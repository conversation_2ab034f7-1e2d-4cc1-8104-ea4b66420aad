/**
 * SmoLAgents 服务集成模块（重构版）
 *
 * 重构原则：
 * 1. 前端只负责API调用和数据展示
 * 2. 所有业务逻辑由后端SmoLAgents微服务处理
 * 3. 移除前端的数据处理和提示词构建逻辑
 * 4. 保持接口兼容性，简化内部实现
 */

import type { ChatMessage, TrainingCustomer } from '@/types/salesTraining';

export interface SmoLAgentsConfig {
  baseUrl: string;
  model: string;
  timeout?: number;
  enableWebSearch?: boolean;
  maxSearchResults?: number;
}



export interface AnalysisRequest {
  product: string;
  query: string;
  analysisType?: string;
}

export interface StrategyGenerationRequest {
  product: string;
  query: string;
  strategyCount?: number;
  analysisType?: string;
  language?: string;
}

export interface SearchStrategy {
  id: string;
  query: string;
  category: string;
  priority: number;
  description: string;
}

export interface StrategyGenerationResponse {
  success: boolean;
  strategies: SearchStrategy[];
  total_count: number;
  generation_time?: number;
  error?: string;
}

export interface AnalysisExecutionRequest {
  product: string;
  query: string;
  strategies: SearchStrategy[];
  analysisType?: string;
}

export interface MarketDataSource {
  title: string;
  url: string;
  content: string;
  dataType: string;
  relevanceScore?: number;
}

export interface AnalysisExecutionResponse {
  success: boolean;
  data?: string;
  dataSources?: MarketDataSource[];
  generatedStrategies?: SearchStrategy[];
  execution_time?: number; // 后端返回的执行时间（秒）
  error?: string;
}

export interface HealthCheckResponse {
  service: string;
  model: string;
  tools: string;
  redis: string;
  timestamp: string;
  version: string;
  error?: string;
}

export type ProgressCallback = (message: string) => void;

// ==================== 销售训练专用接口定义 ====================

// 销售训练通用请求接口
export interface SalesTrainingRequest {
  query: string;
  maxSteps?: number;
  temperature?: number;
  signal?: AbortSignal;
}

// 销售训练通用响应接口
export interface SalesTrainingResponse {
  success: boolean;
  result?: string;
  completion?: string;
  error?: string;
  execution_time?: number;
}

// 智能评分请求接口
export interface IntelligentEvaluationRequest extends SalesTrainingRequest {
  conversationHistory: string;
  sessionId: string;
  messageCount: number;
}

// AI指导分析请求接口
export interface AIGuidanceRequest extends SalesTrainingRequest {
  messages: ChatMessage[];
  customer: TrainingCustomer;
}

// AI答复生成请求接口
export interface AIReplyGenerationRequest extends SalesTrainingRequest {
  direction: string;
  context?: string;
}

// AI指导分析响应接口
export interface AIGuidanceResponse extends SalesTrainingResponse {
  overallAnalysis?: any;
  replyAnalyses?: any[];
}

/**
 * SmoLAgents服务类（重构版）
 *
 * 职责：
 * - 提供前端与SmoLAgents微服务的接口
 * - 处理API调用和错误处理
 * - 数据格式转换
 *
 * 不再负责：
 * - 业务逻辑处理
 * - 数据收集和处理
 * - 提示词构建
 */
class SmoLAgentsService {
  private config: SmoLAgentsConfig;

  constructor(config: SmoLAgentsConfig) {
    this.config = config;
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<HealthCheckResponse> {
    try {
      const response = await fetch(`${this.config.baseUrl}/api/v1/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`健康检查失败: HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('健康检查失败:', error);
      throw error;
    }
  }

  /**
   * 生成智能搜索策略
   */
  async generateSearchStrategies(
    request: StrategyGenerationRequest
  ): Promise<StrategyGenerationResponse> {
    try {
      const { product, query, strategyCount = 4, analysisType = 'market_analysis', language = 'zh-CN' } = request;

      // 根据策略数量动态设置超时时间
      const strategyTimeout = Math.max(120000, (strategyCount || 4) * 20000); // 每个策略20秒，最少2分钟

      const response = await fetch(`${this.config.baseUrl}/api/v1/agent/strategies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          product,
          query,
          full_topic: `${product} ${query}`.trim(),
          analysis_type: analysisType,
          strategy_count: strategyCount,
          region: 'global',
          time_period: `${new Date().getFullYear()}-${new Date().getFullYear() + 1}`,
          language: language
        }),
      });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`策略生成失败 (${response.status}): ${errorText}`);
        }

        const result = await response.json();

        if (result.success && result.strategies) {
          return {
            success: true,
            strategies: result.strategies,
            total_count: result.total_count,
            generation_time: result.generation_time
          };
        } else {
          throw new Error(result.error || '策略生成失败');
        }

    } catch (error) {
      console.error('策略生成失败:', error);

      // 提供更友好的错误提示
      let friendlyMessage = '策略生成失败，请稍后重试';

      if (error instanceof Error) {
        const errorMsg = error.message.toLowerCase();
        if (errorMsg.includes('ai生成流程有效策略') || errorMsg.includes('500')) {
          friendlyMessage = '🤖 AI正在努力分析您的查询，但遇到了一些困难。\n\n💡 建议尝试：\n' +
                          '• 使用更通用的产品名称（如"战术背包"代替具体型号）\n' +
                          '• 简化搜索关键词，使用行业常用术语\n' +
                          '• 确保产品在市场上有一定知名度\n\n' +
                          '如果问题持续，请联系技术支持。';
        } else if (errorMsg.includes('timeout') || errorMsg.includes('超时')) {
          friendlyMessage = '⏱️ 请求超时，AI服务响应较慢。请稍后重试或简化查询内容。';
        } else if (errorMsg.includes('network') || errorMsg.includes('网络')) {
          friendlyMessage = '🌐 网络连接异常，请检查网络状态后重试。';
        } else if (errorMsg.includes('detail') && errorMsg.includes('ai生成')) {
          friendlyMessage = '🔍 AI无法为当前查询生成有效的市场分析策略。\n\n' +
                          '这可能是因为：\n' +
                          '• 产品名称过于具体或小众\n' +
                          '• 缺乏足够的市场数据支持\n' +
                          '• 搜索关键词需要优化\n\n' +
                          '建议使用更通用的产品类别进行分析。';
        } else if (errorMsg.includes('code parsing failed') || errorMsg.includes('syntaxerror')) {
          friendlyMessage = '🔧 AI在处理过程中遇到了技术问题。\n\n' +
                          '建议尝试：\n' +
                          '• 重新生成策略\n' +
                          '• 使用更简单的产品名称\n' +
                          '• 稍后重试\n\n' +
                          '如果问题持续，请联系技术支持。';
        }
      }

      return {
        success: false,
        strategies: [],
        total_count: 0,
        error: friendlyMessage
      };
    }
  }

  /**
   * 统一市场分析方法（优化版 - 支持策略生成+分析一体化）
   */
  async executeUnifiedMarketAnalysis(
    product: string,
    query: string,
    strategies?: SearchStrategy[],
    strategyCount?: number,
    onProgress?: ProgressCallback
  ): Promise<AnalysisExecutionResponse> {
    try {


      if (onProgress) {
        onProgress(strategies?.length ? '正在基于已有策略进行完整市场分析...' : '正在生成策略并执行完整市场分析...');
      }

      // 调用统一的市场分析API
      const response = await fetch(`${this.config.baseUrl}/api/v1/agent/market-analysis`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          product,
          query,
          strategies: strategies || [], // 如果为空，后端会自动生成策略
          analysis_type: 'market_analysis',
          region: 'global',
          time_period: `${new Date().getFullYear()}-${new Date().getFullYear() + 1}`,
          strategy_count: strategyCount || 4, // 传递策略数量
          // max_steps 和 timeout 由后端统一配置管理器提供，不再硬编码
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`统一分析API调用失败 (${response.status}): ${errorText}`);
      }

      const result = await response.json();

      if (result.success && result.analysis_report) {
        // 转换数据源格式
        const dataSources: MarketDataSource[] = (result.data_sources || []).map((source: any) => ({
          title: source.title,
          url: source.url,
          content: source.content,
          dataType: source.data_type,
          relevanceScore: source.relevance_score
        }));

        if (onProgress) {
          onProgress(`分析完成！执行了${result.strategies_executed}个策略，收集了${result.total_data_points}个数据点`);
        }

        return {
          success: true,
          data: result.analysis_report,
          dataSources,
          generatedStrategies: result.generated_strategies || [],
          execution_time: result.execution_time // 传递后端的执行时间
        };
      } else {
        throw new Error(result.error || '统一分析执行失败');
      }

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return {
          success: false,
          error: '分析已取消'
        };
      }

      console.error('统一市场分析执行失败:', error);

      let errorMessage = '统一市场分析执行失败，请稍后重试';
      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('超时')) {
          errorMessage = 'AI服务响应超时，请稍后重试或简化查询内容';
        } else if (error.message.includes('network') || error.message.includes('网络')) {
          errorMessage = '网络连接异常，请检查网络状态后重试';
        } else if (error.message.includes('500')) {
          errorMessage = 'AI服务暂时不可用，请稍后重试';
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * 执行市场分析（重构版 - 直接调用后端API）
   * @deprecated 建议使用 executeUnifiedMarketAnalysis 方法
   */
  async executeMarketAnalysis(
    request: AnalysisExecutionRequest,
    onProgress?: ProgressCallback
  ): Promise<AnalysisExecutionResponse> {
    try {


      const { product, query, strategies } = request;

      if (onProgress) {
        onProgress('正在进行全面数据分析，请稍候...');
      }

      // 直接调用后端市场分析API
      const response = await fetch(`${this.config.baseUrl}/api/v1/agent/analysis`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          product,
          query,
          strategies,
          analysis_type: request.analysisType || 'market_analysis',
          region: 'global',
          time_period: `${new Date().getFullYear()}-${new Date().getFullYear() + 1}`,
          // max_steps 和 timeout 由后端统一配置管理器提供，不再硬编码
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`分析API调用失败 (${response.status}): ${errorText}`);
      }

      const result = await response.json();

      if (result.success && result.analysis_report) {
        // 转换数据源格式
        const dataSources: MarketDataSource[] = (result.data_sources || []).map((source: any) => ({
          title: source.title,
          url: source.url,
          content: source.content,
          dataType: source.data_type,
          relevanceScore: source.relevance_score
        }));

        if (onProgress) {
          onProgress(`分析完成！执行了${result.strategies_executed}个策略，收集了${result.total_data_points}个数据点`);
        }

        return {
          success: true,
          data: result.analysis_report,
          dataSources,
          execution_time: result.execution_time // 传递后端的执行时间
        };
      } else {
        throw new Error(result.error || '分析执行失败');
      }

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return {
          success: false,
          error: '分析已取消'
        };
      }

      console.error('市场分析执行失败:', error);

      let errorMessage = '市场分析执行失败，请稍后重试';
      if (error instanceof Error) {
        const msg = error.message.toLowerCase();
        if (msg.includes('network') || msg.includes('fetch')) {
          errorMessage = '🌐 AI服务连接失败，请检查网络状态或服务是否正常运行';
        } else if (msg.includes('timeout')) {
          errorMessage = '⏱️ 分析超时，AI处理时间较长。建议简化查询内容或稍后重试';
        } else if (msg.includes('500') && msg.includes('ai生成')) {
          errorMessage = '🤖 AI分析遇到困难，可能是查询内容过于复杂或缺乏相关数据。\n\n' +
                        '建议：\n' +
                        '• 使用更通用的产品名称\n' +
                        '• 简化搜索策略\n' +
                        '• 确保产品具有市场知名度';
        } else if (msg.includes('api调用失败')) {
          errorMessage = `🔧 ${error.message}\n\n请检查AI服务状态或联系技术支持`;
        } else if (msg.includes('code parsing failed') || msg.includes('syntaxerror')) {
          errorMessage = '🔧 AI在处理过程中遇到了技术问题。\n\n' +
                        '建议尝试：\n' +
                        '• 重新执行分析\n' +
                        '• 使用更简单的策略\n' +
                        '• 稍后重试\n\n' +
                        '如果问题持续，请联系技术支持。';
        } else {
          errorMessage = `❌ ${error.message}`;
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.config.baseUrl}/api/v1/agent/task/${taskId}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return {
        success: true
      };
    } catch (error) {
      console.error('取消任务失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '取消任务失败'
      };
    }
  }

  // ==================== 销售训练专用方法 ====================

  /**
   * 执行销售训练智能评分
   */
  async performIntelligentEvaluation(
    request: IntelligentEvaluationRequest
  ): Promise<SalesTrainingResponse> {
    try {
      const { query, maxSteps = 3, temperature = 0.3, signal } = request;

      console.log('开始调用SmoLAgents服务进行智能评分...');

      const response = await fetch(`${this.config.baseUrl}/api/v1/agent/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          max_steps: maxSteps,
          temperature
        }),
        signal
      });

      if (!response.ok) {
        throw new Error(`SmoLAgents API调用失败: ${response.status} ${response.statusText}`);
      }

      const responseData = await response.json();
      console.log('SmoLAgents智能评分API响应成功');

      const completionText = responseData.result || responseData.completion || '';

      if (!completionText) {
        throw new Error('AI服务返回空响应');
      }

      return {
        success: true,
        result: responseData.result,
        completion: responseData.completion,
        execution_time: responseData.execution_time
      };

    } catch (error) {
      console.error('智能评分失败:', error);

      let errorMessage = 'AI评分服务暂时不可用';
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = '评分已取消';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'AI服务响应超时，请稍后重试';
        } else if (error.message.includes('network')) {
          errorMessage = '网络连接异常，请检查网络状态';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * 执行销售训练AI指导分析
   */
  async performAIGuidanceAnalysis(
    request: AIGuidanceRequest
  ): Promise<AIGuidanceResponse> {
    try {
      const { query, maxSteps = 3, temperature = 0.3, signal } = request;

      console.log('开始调用SmoLAgents服务进行AI指导分析...');

      const response = await fetch(`${this.config.baseUrl}/api/v1/agent/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          max_steps: maxSteps,
          temperature
        }),
        signal
      });

      if (!response.ok) {
        throw new Error(`SmoLAgents API调用失败: ${response.status} ${response.statusText}`);
      }

      const responseData = await response.json();
      console.log('SmoLAgents AI指导API响应成功');

      const completionText = responseData.result || responseData.completion || '';

      if (!completionText) {
        throw new Error('AI服务返回空响应');
      }

      return {
        success: true,
        result: responseData.result,
        completion: responseData.completion,
        execution_time: responseData.execution_time
      };

    } catch (error) {
      console.error('AI指导分析失败:', error);

      let errorMessage = 'AI指导服务暂时不可用';
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = '分析已取消';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'AI服务响应超时，请稍后重试';
        } else if (error.message.includes('network')) {
          errorMessage = '网络连接异常，请检查网络状态';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * 执行销售训练AI答复生成
   */
  async generateAIReply(
    request: AIReplyGenerationRequest
  ): Promise<SalesTrainingResponse> {
    try {
      const { query, maxSteps = 2, temperature = 0.6, signal } = request;

      console.log('开始调用SmoLAgents服务生成AI答复...');

      const response = await fetch(`${this.config.baseUrl}/api/v1/agent/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          max_steps: maxSteps,
          temperature
        }),
        signal
      });

      if (!response.ok) {
        throw new Error(`SmoLAgents API调用失败: ${response.status} ${response.statusText}`);
      }

      const responseData = await response.json();
      console.log('SmoLAgents AI答复生成API响应成功');

      const completionText = responseData.result || responseData.completion || '';

      if (!completionText) {
        throw new Error('AI服务返回空响应');
      }

      return {
        success: true,
        result: responseData.result,
        completion: responseData.completion,
        execution_time: responseData.execution_time
      };

    } catch (error) {
      console.error('AI答复生成失败:', error);

      let errorMessage = 'AI答复生成服务暂时不可用';
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = '生成已取消';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'AI服务响应超时，请稍后重试';
        } else if (error.message.includes('network')) {
          errorMessage = '网络连接异常，请检查网络状态';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

}

// 创建服务实例
const smolagentsService = new SmoLAgentsService({
  baseUrl: import.meta.env.VITE_SMOLAGENTS_API_URL || 'http://localhost:8002',
  model: 'default'
});

export { smolagentsService };
export default SmoLAgentsService;
