/**
 * 头像上传组件
 */
import React, { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Loader2, Upload, Trash2, Camera } from 'lucide-react'
import { useAuthStore } from '@/store/auth'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface AvatarUploadProps {
  currentAvatarUrl?: string
  onAvatarUpdate: (avatarUrl: string | null) => void
}

export const AvatarUpload: React.FC<AvatarUploadProps> = ({
  currentAvatarUrl,
  onAvatarUpdate,
}) => {
  const { token, user } = useAuthStore()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file || !token) return

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      setError('只支持 JPG、PNG、GIF、WebP 格式的图片')
      return
    }

    // 验证文件大小 (2MB)
    if (file.size > 2 * 1024 * 1024) {
      setError('文件大小不能超过 2MB')
      return
    }

    setIsUploading(true)
    setError('')
    setSuccess('')

    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch(`${API_BASE_URL}/auth/upload-avatar`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      })

      if (response.ok) {
        const data = await response.json()
        setSuccess('头像上传成功')
        onAvatarUpdate(data.avatar_url)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '上传失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsUploading(false)
      // 清空文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleDeleteAvatar = async () => {
    if (!token) return

    setIsDeleting(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch(`${API_BASE_URL}/auth/avatar`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        setSuccess('头像删除成功')
        onAvatarUpdate(null)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '删除失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsDeleting(false)
    }
  }

  // 获取用户显示名称的首字母
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const displayName = user?.full_name || user?.username || 'User'
  const initials = getInitials(displayName)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Camera className="mr-2 h-5 w-5" />
          头像设置
        </CardTitle>
        <CardDescription>
          上传您的个人头像，支持 JPG、PNG、GIF、WebP 格式，最大 2MB
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {success && (
          <Alert>
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}
        
        <div className="flex items-center space-x-4">
          <Avatar className="h-20 w-20">
            <AvatarImage 
              src={currentAvatarUrl ? `${API_BASE_URL.replace('/api/v1', '')}${currentAvatarUrl}` : undefined} 
              alt={displayName} 
            />
            <AvatarFallback className="text-lg">{initials}</AvatarFallback>
          </Avatar>
          
          <div className="flex flex-col space-y-2">
            <Button
              onClick={handleFileSelect}
              disabled={isUploading || isDeleting}
              variant="outline"
            >
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  上传中...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  {currentAvatarUrl ? '更换头像' : '上传头像'}
                </>
              )}
            </Button>
            
            {currentAvatarUrl && (
              <Button
                onClick={handleDeleteAvatar}
                disabled={isUploading || isDeleting}
                variant="outline"
                size="sm"
              >
                {isDeleting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    删除中...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" />
                    删除头像
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/png,image/gif,image/webp"
          onChange={handleFileChange}
          className="hidden"
        />
        
        <div className="text-xs text-muted-foreground">
          <p>• 支持的格式：JPG、PNG、GIF、WebP</p>
          <p>• 文件大小：最大 2MB</p>
          <p>• 建议尺寸：200x200 像素</p>
        </div>
      </CardContent>
    </Card>
  )
}
