/**
 * 角色管理组件
 */
import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Loader2, Plus, Edit, Trash2, Shield, Users, Search, Filter } from 'lucide-react'
import { useAuthStore } from '@/store/auth'
import { CreateRoleDialog } from './CreateRoleDialog'
import { EditRoleDialog } from './EditRoleDialog'
import { DeleteRoleConfirmDialog } from './DeleteRoleConfirmDialog'
import { RoleStatusToggle } from './RoleStatusToggle'
import { RoleStatsCard } from './RoleStatsCard'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface Permission {
  id: number
  name: string
  description: string
  category: string
}

interface Role {
  id: number
  name: string
  description: string
  is_active: boolean
  created_at: string
  permissions: Permission[]
}

export const RoleManagement: React.FC = () => {
  const { token } = useAuthStore()
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // 对话框状态
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)

  // 搜索和筛选状态
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all')
  const [typeFilter, setTypeFilter] = useState<'all' | 'system' | 'custom'>('all')

  // 获取角色列表
  const fetchRoles = async () => {
    if (!token) return

    try {
      const response = await fetch(`${API_BASE_URL}/admin/roles`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data: Role[] = await response.json()
        setRoles(data)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '获取角色列表失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    }
  }

  // 获取权限列表
  const fetchPermissions = async () => {
    if (!token) return

    try {
      const response = await fetch(`${API_BASE_URL}/admin/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data: Permission[] = await response.json()
        setPermissions(data)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '获取权限列表失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    }
  }

  // 处理创建角色成功
  const handleCreateSuccess = () => {
    setSuccess('角色创建成功')
    fetchRoles() // 刷新角色列表
    // 3秒后清除成功消息
    setTimeout(() => setSuccess(''), 3000)
  }

  // 处理编辑角色成功
  const handleEditSuccess = () => {
    setSuccess('角色更新成功')
    fetchRoles() // 刷新角色列表
    // 3秒后清除成功消息
    setTimeout(() => setSuccess(''), 3000)
  }

  // 处理删除角色成功
  const handleDeleteSuccess = () => {
    setSuccess('角色删除成功')
    fetchRoles() // 刷新角色列表
    // 3秒后清除成功消息
    setTimeout(() => setSuccess(''), 3000)
  }

  // 打开编辑对话框
  const handleEditRole = (role: Role) => {
    setSelectedRole(role)
    setShowEditDialog(true)
  }

  // 打开删除确认对话框
  const handleDeleteRole = (role: Role) => {
    setSelectedRole(role)
    setShowDeleteDialog(true)
  }

  // 处理角色状态变更
  const handleRoleStatusChange = (updatedRole: Role, newStatus: boolean) => {
    setRoles(prevRoles =>
      prevRoles.map(role =>
        role.id === updatedRole.id ? { ...role, is_active: newStatus } : role
      )
    )
    setSuccess(`角色 ${updatedRole.name} 状态已${newStatus ? '激活' : '禁用'}`)
    // 3秒后清除成功消息
    setTimeout(() => setSuccess(''), 3000)
  }

  // 筛选角色
  const filteredRoles = roles.filter(role => {
    // 搜索筛选
    const matchesSearch = searchTerm === '' ||
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description?.toLowerCase().includes(searchTerm.toLowerCase())

    // 状态筛选
    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'active' && role.is_active) ||
      (statusFilter === 'inactive' && !role.is_active)

    // 类型筛选
    const isSystemRole = ['superadmin', 'admin', 'user'].includes(role.name)
    const matchesType = typeFilter === 'all' ||
      (typeFilter === 'system' && isSystemRole) ||
      (typeFilter === 'custom' && !isSystemRole)

    return matchesSearch && matchesStatus && matchesType
  })

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      await Promise.all([fetchRoles(), fetchPermissions()])
      setIsLoading(false)
    }
    loadData()
  }, [token])

  // 按分类分组权限
  const groupPermissionsByCategory = (permissions: Permission[]) => {
    const groups: { [key: string]: Permission[] } = {}
    permissions.forEach(permission => {
      const category = permission.category || '其他'
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push(permission)
    })
    return groups
  }

  const permissionGroups = groupPermissionsByCategory(permissions)

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>加载中...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="mr-2 h-5 w-5" />
            角色管理
          </CardTitle>
          <CardDescription>
            管理系统角色和权限分配
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mb-4">
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          {/* 角色统计 */}
          <RoleStatsCard roles={roles} permissions={permissions} />

          {/* 搜索和筛选 */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索角色名称或描述..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={(value: 'all' | 'active' | 'inactive') => setStatusFilter(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">激活</SelectItem>
                  <SelectItem value="inactive">禁用</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={(value: 'all' | 'system' | 'custom') => setTypeFilter(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="system">系统角色</SelectItem>
                  <SelectItem value="custom">自定义</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 筛选结果统计 */}
          {(searchTerm || statusFilter !== 'all' || typeFilter !== 'all') && (
            <div className="flex items-center justify-between mb-4 p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  筛选结果：显示 {filteredRoles.length} / {roles.length} 个角色
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchTerm('')
                  setStatusFilter('all')
                  setTypeFilter('all')
                }}
              >
                清除筛选
              </Button>
            </div>
          )}

          {/* 角色列表 */}
          <div className="space-y-4">
            {filteredRoles.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-8">
                  <Shield className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">没有找到角色</h3>
                  <p className="text-muted-foreground text-center">
                    {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                      ? '尝试调整搜索条件或筛选器'
                      : '还没有创建任何角色，点击上方按钮创建第一个角色'
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              filteredRoles.map((role) => (
              <Card key={role.id} className="border-l-4 border-l-blue-500">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="text-lg font-semibold">{role.name}</h3>
                        <RoleStatusToggle
                          role={role}
                          onStatusChange={handleRoleStatusChange}
                        />
                      </div>
                      
                      <p className="text-muted-foreground mb-3">
                        {role.description}
                      </p>
                      
                      <div className="space-y-2">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Users className="mr-1 h-4 w-4" />
                          权限数量: {role.permissions.length}
                        </div>
                        
                        {role.permissions.length > 0 && (
                          <div>
                            <p className="text-sm font-medium mb-2">权限列表:</p>
                            <div className="flex flex-wrap gap-1">
                              {role.permissions.slice(0, 10).map((permission) => (
                                <Badge key={permission.id} variant="outline" className="text-xs">
                                  {permission.name}
                                </Badge>
                              ))}
                              {role.permissions.length > 10 && (
                                <Badge variant="outline" className="text-xs">
                                  +{role.permissions.length - 10} 更多
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditRole(role)}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteRole(role)}
                        className={['superadmin', 'admin', 'user'].includes(role.name) ? 'opacity-50 cursor-not-allowed' : ''}
                        disabled={['superadmin', 'admin', 'user'].includes(role.name)}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        删除
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
              ))
            )}
          </div>

          <div className="mt-6 pt-6 border-t">
            <Button
              onClick={() => setShowCreateDialog(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              创建新角色
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 权限总览 */}
      <Card>
        <CardHeader>
          <CardTitle>系统权限总览</CardTitle>
          <CardDescription>
            查看系统中所有可用的权限
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(permissionGroups).map(([category, categoryPermissions]) => (
              <Card key={category} className="border">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">{category}</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-1">
                    {categoryPermissions.map((permission) => (
                      <div key={permission.id} className="text-sm">
                        <span className="font-mono text-xs bg-muted px-1 py-0.5 rounded">
                          {permission.name}
                        </span>
                        <p className="text-muted-foreground text-xs mt-1">
                          {permission.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 创建角色对话框 */}
      <CreateRoleDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={handleCreateSuccess}
      />

      {/* 编辑角色对话框 */}
      <EditRoleDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSuccess={handleEditSuccess}
        role={selectedRole}
      />

      {/* 删除角色确认对话框 */}
      <DeleteRoleConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onSuccess={handleDeleteSuccess}
        role={selectedRole}
      />
    </div>
  )
}
