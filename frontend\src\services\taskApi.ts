/**
 * 任务管理API服务
 * 提供与后端任务API的交互功能
 */
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';

// 创建axios实例
const taskApi = axios.create({
  baseURL: `${API_BASE_URL}/api/v1/tasks`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

// 任务优先级枚举
export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

// 任务数据类型
export interface Task {
  id: number;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  assignee?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// 创建任务请求类型
export interface CreateTaskRequest {
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  assignee?: string;
  is_active?: boolean;
}

// 更新任务请求类型
export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  assignee?: string;
  is_active?: boolean;
}

// 任务列表响应类型
export interface TaskListResponse {
  items: Task[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 任务统计响应类型
export interface TaskStatsResponse {
  total: number;
  pending: number;
  in_progress: number;
  completed: number;
  cancelled: number;
  by_priority: Record<string, number>;
}

// 查询参数类型
export interface TaskQueryParams {
  skip?: number;
  limit?: number;
  status?: TaskStatus;
  priority?: TaskPriority;
  assignee?: string;
  is_active?: boolean;
}

// API函数
export const taskApiService = {
  // 创建任务
  async createTask(data: CreateTaskRequest): Promise<Task> {
    const response = await taskApi.post<Task>('/', data);
    return response.data;
  },

  // 获取任务列表
  async getTasks(params?: TaskQueryParams): Promise<TaskListResponse> {
    const response = await taskApi.get<TaskListResponse>('/', { params });
    return response.data;
  },

  // 获取单个任务
  async getTask(id: number): Promise<Task> {
    const response = await taskApi.get<Task>(`/${id}`);
    return response.data;
  },

  // 更新任务
  async updateTask(id: number, data: UpdateTaskRequest): Promise<Task> {
    const response = await taskApi.put<Task>(`/${id}`, data);
    return response.data;
  },

  // 删除任务
  async deleteTask(id: number): Promise<Task> {
    const response = await taskApi.delete<Task>(`/${id}`);
    return response.data;
  },

  // 获取任务统计
  async getTaskStats(): Promise<TaskStatsResponse> {
    const response = await taskApi.get<TaskStatsResponse>('/stats');
    return response.data;
  },
};

// 状态和优先级的显示标签
export const statusLabels: Record<TaskStatus, string> = {
  [TaskStatus.PENDING]: '待处理',
  [TaskStatus.IN_PROGRESS]: '进行中',
  [TaskStatus.COMPLETED]: '已完成',
  [TaskStatus.CANCELLED]: '已取消',
};

export const priorityLabels: Record<TaskPriority, string> = {
  [TaskPriority.LOW]: '低',
  [TaskPriority.MEDIUM]: '中',
  [TaskPriority.HIGH]: '高',
  [TaskPriority.URGENT]: '紧急',
};

// 状态颜色映射
export const statusColors: Record<TaskStatus, string> = {
  [TaskStatus.PENDING]: 'bg-yellow-100 text-yellow-800',
  [TaskStatus.IN_PROGRESS]: 'bg-blue-100 text-blue-800',
  [TaskStatus.COMPLETED]: 'bg-green-100 text-green-800',
  [TaskStatus.CANCELLED]: 'bg-gray-100 text-gray-800',
};

// 优先级颜色映射
export const priorityColors: Record<TaskPriority, string> = {
  [TaskPriority.LOW]: 'bg-gray-100 text-gray-800',
  [TaskPriority.MEDIUM]: 'bg-blue-100 text-blue-800',
  [TaskPriority.HIGH]: 'bg-orange-100 text-orange-800',
  [TaskPriority.URGENT]: 'bg-red-100 text-red-800',
};
