/**
 * 知识库主题提供者
 * 统一管理知识库相关的主题、样式和视觉一致性
 */
import React, { createContext, useContext, useEffect } from 'react';
import { useKnowledgeBaseSettings } from '@/store/knowledgeBase';

// 主题上下文
interface ThemeContextType {
  theme: 'light' | 'dark' | 'system';
  resolvedTheme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  compactMode: boolean;
  setCompactMode: (compact: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// 主题提供者组件
interface KnowledgeBaseThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: 'light' | 'dark' | 'system';
}

export function KnowledgeBaseThemeProvider({
  children,
  defaultTheme = 'system'
}: KnowledgeBaseThemeProviderProps) {
  const { settings, update } = useKnowledgeBaseSettings();

  // 解析系统主题
  const getSystemTheme = (): 'light' | 'dark' => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  };

  const resolvedTheme = settings.theme === 'system' ? getSystemTheme() : settings.theme;

  // 设置主题
  const setTheme = (theme: 'light' | 'dark' | 'system') => {
    update({ theme });
  };

  // 设置紧凑模式
  const setCompactMode = (compact: boolean) => {
    update({ compactMode: compact });
  };

  // 应用主题到DOM
  useEffect(() => {
    const root = window.document.documentElement;

    // 移除之前的主题类
    root.classList.remove('light', 'dark');

    // 添加当前主题类
    root.classList.add(resolvedTheme);

    // 设置CSS变量
    if (resolvedTheme === 'dark') {
      root.style.setProperty('--knowledge-base-bg', 'hsl(222.2 84% 4.9%)');
      root.style.setProperty('--knowledge-base-fg', 'hsl(210 40% 98%)');
      root.style.setProperty('--knowledge-base-primary', 'hsl(217.2 91.2% 59.8%)');
      root.style.setProperty('--knowledge-base-secondary', 'hsl(217.2 32.6% 17.5%)');
      root.style.setProperty('--knowledge-base-accent', 'hsl(217.2 32.6% 17.5%)');
      root.style.setProperty('--knowledge-base-muted', 'hsl(217.2 32.6% 17.5%)');
    } else {
      root.style.setProperty('--knowledge-base-bg', 'hsl(0 0% 100%)');
      root.style.setProperty('--knowledge-base-fg', 'hsl(222.2 84% 4.9%)');
      root.style.setProperty('--knowledge-base-primary', 'hsl(221.2 83.2% 53.3%)');
      root.style.setProperty('--knowledge-base-secondary', 'hsl(210 40% 96%)');
      root.style.setProperty('--knowledge-base-accent', 'hsl(210 40% 96%)');
      root.style.setProperty('--knowledge-base-muted', 'hsl(210 40% 96%)');
    }

    // 设置紧凑模式
    if (settings.compactMode) {
      root.classList.add('compact-mode');
      root.style.setProperty('--knowledge-base-spacing', '0.5rem');
      root.style.setProperty('--knowledge-base-text-sm', '0.75rem');
      root.style.setProperty('--knowledge-base-text-base', '0.875rem');
    } else {
      root.classList.remove('compact-mode');
      root.style.setProperty('--knowledge-base-spacing', '1rem');
      root.style.setProperty('--knowledge-base-text-sm', '0.875rem');
      root.style.setProperty('--knowledge-base-text-base', '1rem');
    }
  }, [resolvedTheme, settings.compactMode]);

  // 监听系统主题变化
  useEffect(() => {
    if (settings.theme !== 'system') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = () => {
      // 触发重新渲染以更新resolvedTheme
      update({ theme: 'system' });
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [settings.theme, update]);

  const value: ThemeContextType = {
    theme: settings.theme,
    resolvedTheme,
    setTheme,
    compactMode: settings.compactMode,
    setCompactMode,
  };

  return (
    <ThemeContext.Provider value={value}>
      <div className="knowledge-base-theme-root">
        {children}
      </div>
    </ThemeContext.Provider>
  );
}

// 主题钩子
export function useKnowledgeBaseTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useKnowledgeBaseTheme must be used within a KnowledgeBaseThemeProvider');
  }
  return context;
}

// 主题切换组件已删除
// 紧凑模式切换组件已删除

// 知识库专用样式类
export const knowledgeBaseStyles = {
  // 容器样式
  container: 'knowledge-base-container bg-knowledge-base-bg text-knowledge-base-fg',

  // 卡片样式
  card: 'knowledge-base-card bg-background border border-border rounded-lg shadow-sm',

  // 按钮样式
  primaryButton: 'bg-knowledge-base-primary text-primary-foreground hover:bg-knowledge-base-primary/90',
  secondaryButton: 'bg-knowledge-base-secondary text-secondary-foreground hover:bg-knowledge-base-secondary/80',

  // 输入框样式
  input: 'bg-background border border-border focus:border-knowledge-base-primary',

  // 文本样式
  heading: 'text-knowledge-base-fg font-semibold',
  body: 'text-knowledge-base-fg/80',
  muted: 'text-muted-foreground',

  // 状态样式
  success: 'text-green-600 bg-green-50 border-green-200',
  warning: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  error: 'text-red-600 bg-red-50 border-red-200',
  info: 'text-blue-600 bg-blue-50 border-blue-200',

  // 间距样式
  spacing: {
    xs: 'p-2 gap-2',
    sm: 'p-3 gap-3',
    md: 'p-4 gap-4',
    lg: 'p-6 gap-6',
    xl: 'p-8 gap-8',
  },

  // 响应式样式
  responsive: {
    mobile: 'block md:hidden',
    tablet: 'hidden md:block lg:hidden',
    desktop: 'hidden lg:block',
  },
};

// CSS-in-JS样式对象
export const knowledgeBaseCSS = `
  .knowledge-base-theme-root {
    --kb-transition: all 0.2s ease-in-out;
    --kb-border-radius: 0.5rem;
    --kb-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .knowledge-base-container {
    transition: var(--kb-transition);
  }

  .knowledge-base-card {
    transition: var(--kb-transition);
    border-radius: var(--kb-border-radius);
    box-shadow: var(--kb-shadow);
  }

  .knowledge-base-card:hover {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  }

  .compact-mode .knowledge-base-card {
    padding: var(--knowledge-base-spacing);
  }

  .compact-mode .text-sm {
    font-size: var(--knowledge-base-text-sm);
  }

  .compact-mode .text-base {
    font-size: var(--knowledge-base-text-base);
  }

  /* 知识库专用动画 */
  @keyframes kb-fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes kb-slide-in {
    from { opacity: 0; transform: translateX(-20px); }
    to { opacity: 1; transform: translateX(0); }
  }

  .kb-fade-in {
    animation: kb-fade-in 0.3s ease-out;
  }

  .kb-slide-in {
    animation: kb-slide-in 0.3s ease-out;
  }
`;

export default KnowledgeBaseThemeProvider;
