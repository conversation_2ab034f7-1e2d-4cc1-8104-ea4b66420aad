"""
管理员API路由
提供角色、权限、用户管理等管理员功能
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.core.permissions import require_admin, require_permissions, Permissions
from app.core.auth import get_current_active_user
from app.crud.rbac import role_crud, permission_crud, user_role_crud
from app.crud.user import user_crud
from app.schemas.rbac import (
    Role, RoleCreate, RoleUpdate,
    Permission, PermissionCreate, PermissionUpdate,
    UserRoleAssign, UserPermissionResponse, AdminUserResponse,
    PermissionCheckResponse
)
from app.schemas.user import UserCreate, UserUpdate, User as UserResponse
from app.models.user import User

router = APIRouter()


# 角色管理API
@router.get("/roles", response_model=List[Role], summary="获取所有角色")
async def get_roles(
    *,
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    current_user: User = Depends(require_permissions([Permissions.ROLE_READ]))
) -> List[Role]:
    """
    获取所有角色列表
    
    需要权限：role:read
    """
    roles = await role_crud.get_multi(db, skip=skip, limit=limit)
    
    # 手动构建响应以包含权限信息
    result = []
    for role in roles:
        permissions = [rp.permission for rp in role.role_permissions]
        role_dict = {
            "id": role.id,
            "name": role.name,
            "description": role.description,
            "is_active": role.is_active,
            "created_at": role.created_at,
            "permissions": permissions
        }
        result.append(Role(**role_dict))
    
    return result


@router.post("/roles", response_model=Role, summary="创建角色")
async def create_role(
    *,
    db: AsyncSession = Depends(get_db),
    role_in: RoleCreate,
    current_user: User = Depends(require_permissions([Permissions.ROLE_CREATE]))
) -> Role:
    """
    创建新角色
    
    需要权限：role:create
    """
    # 检查角色名是否已存在
    existing_role = await role_crud.get_by_name(db, name=role_in.name)
    if existing_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="角色名已存在"
        )
    
    role = await role_crud.create(db, obj_in=role_in)
    
    # 重新获取角色以包含权限信息
    role_with_permissions = await role_crud.get(db, id=role.id)
    permissions = [rp.permission for rp in role_with_permissions.role_permissions]
    
    return Role(
        id=role.id,
        name=role.name,
        description=role.description,
        is_active=role.is_active,
        created_at=role.created_at,
        permissions=permissions
    )


@router.put("/roles/{role_id}", response_model=Role, summary="更新角色")
async def update_role(
    *,
    db: AsyncSession = Depends(get_db),
    role_id: int,
    role_in: RoleUpdate,
    current_user: User = Depends(require_permissions([Permissions.ROLE_UPDATE]))
) -> Role:
    """
    更新角色信息
    
    需要权限：role:update
    """
    role = await role_crud.get(db, id=role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 检查角色名是否已被其他角色使用
    if role_in.name and role_in.name != role.name:
        existing_role = await role_crud.get_by_name(db, name=role_in.name)
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色名已存在"
            )
    
    updated_role = await role_crud.update(db, db_obj=role, obj_in=role_in)
    
    # 重新获取角色以包含权限信息
    role_with_permissions = await role_crud.get(db, id=updated_role.id)
    permissions = [rp.permission for rp in role_with_permissions.role_permissions]
    
    return Role(
        id=updated_role.id,
        name=updated_role.name,
        description=updated_role.description,
        is_active=updated_role.is_active,
        created_at=updated_role.created_at,
        permissions=permissions
    )


@router.delete("/roles/{role_id}", summary="删除角色")
async def delete_role(
    *,
    db: AsyncSession = Depends(get_db),
    role_id: int,
    current_user: User = Depends(require_permissions([Permissions.ROLE_DELETE]))
) -> dict:
    """
    删除角色
    
    需要权限：role:delete
    """
    role = await role_crud.get(db, id=role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 防止删除系统内置角色
    if role.name in ["superadmin", "admin", "user"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除系统内置角色"
        )
    
    success = await role_crud.remove(db, id=role_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除角色失败"
        )
    
    return {"message": "角色删除成功"}


# 权限管理API
@router.get("/permissions", response_model=List[Permission], summary="获取所有权限")
async def get_permissions(
    *,
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    category: str = Query(None, description="权限分类"),
    current_user: User = Depends(require_permissions([Permissions.PERMISSION_READ]))
) -> List[Permission]:
    """
    获取所有权限列表
    
    需要权限：permission:read
    """
    if category:
        permissions = await permission_crud.get_by_category(db, category=category)
    else:
        permissions = await permission_crud.get_multi(db, skip=skip, limit=limit)
    
    return permissions


# 用户管理API
@router.get("/users", response_model=List[AdminUserResponse], summary="获取用户列表")
async def get_users(
    *,
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    current_user: User = Depends(require_permissions([Permissions.USER_READ]))
) -> List[AdminUserResponse]:
    """
    获取用户列表（管理员视图）
    
    需要权限：user:read
    """
    users = await user_role_crud.get_users_with_roles(db, skip=skip, limit=limit)
    
    result = []
    for user in users:
        role_names = [ur.role.name for ur in user.user_roles if ur.role.is_active]
        user_dict = {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "created_at": user.created_at,
            "roles": role_names
        }
        result.append(AdminUserResponse(**user_dict))
    
    return result


@router.post("/users", response_model=UserResponse, summary="创建用户")
async def create_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_in: UserCreate,
    current_user: User = Depends(require_permissions([Permissions.USER_CREATE]))
) -> UserResponse:
    """
    创建新用户（管理员功能）

    需要权限：user:create
    """
    try:
        user = await user_crud.create(db=db, obj_in=user_in)
        return user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/users/{user_id}", response_model=UserResponse, summary="更新用户信息")
async def update_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int,
    user_in: UserUpdate,
    current_user: User = Depends(require_permissions([Permissions.USER_UPDATE]))
) -> UserResponse:
    """
    更新用户信息（管理员功能）

    需要权限：user:update
    """
    # 验证用户存在
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    try:
        updated_user = await user_crud.update(db=db, db_obj=user, obj_in=user_in)
        return updated_user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/users/{user_id}", summary="删除用户")
async def delete_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int,
    current_user: User = Depends(require_permissions([Permissions.USER_DELETE]))
) -> dict:
    """
    删除用户（管理员功能）

    需要权限：user:delete
    """
    # 验证用户存在
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 防止删除自己
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户"
        )

    # 检查用户依赖关系
    dependencies = await user_crud.check_user_dependencies(db, id=user_id)

    # 防止删除超级用户
    if dependencies["is_superuser"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除超级用户账户"
        )

    # 执行删除
    success = await user_crud.remove(db, id=user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除用户失败"
        )

    return {
        "message": "用户删除成功",
        "deleted_user_id": user_id,
        "deleted_roles": dependencies["role_count"]
    }


@router.get("/users/{user_id}/dependencies", summary="获取用户依赖关系")
async def get_user_dependencies(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int,
    current_user: User = Depends(require_permissions([Permissions.USER_READ]))
) -> dict:
    """
    获取用户的依赖关系信息

    需要权限：user:read
    """
    # 验证用户存在
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    dependencies = await user_crud.check_user_dependencies(db, id=user_id)

    return {
        "user_id": user_id,
        "username": user.username,
        "email": user.email,
        "is_superuser": user.is_superuser,
        "is_active": user.is_active,
        "dependencies": dependencies
    }


@router.post("/users/{user_id}/roles", summary="分配用户角色")
async def assign_user_roles(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int,
    role_assign: UserRoleAssign,
    current_user: User = Depends(require_permissions([Permissions.USER_UPDATE]))
) -> dict:
    """
    为用户分配角色
    
    需要权限：user:update
    """
    # 验证用户存在
    from app.crud.user import user_crud
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 验证角色存在
    for role_id in role_assign.role_ids:
        role = await role_crud.get(db, id=role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"角色ID {role_id} 不存在"
            )
    
    await user_role_crud.assign_roles(
        db, 
        user_id=user_id, 
        role_ids=role_assign.role_ids,
        assigned_by=current_user.id
    )
    
    return {"message": "角色分配成功"}


@router.get("/users/{user_id}/permissions", response_model=UserPermissionResponse, summary="获取用户权限")
async def get_user_permissions(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int,
    current_user: User = Depends(get_current_active_user)
) -> UserPermissionResponse:
    """
    获取用户的所有权限

    允许用户查看自己的权限，或者超级用户/有权限的用户查看其他用户权限
    """
    # 检查权限：用户只能查看自己的权限，除非是超级用户或有user:read权限
    if current_user.id != user_id and not current_user.is_superuser:
        # 检查是否有user:read权限
        has_permission = await user_role_crud.check_permission(db, current_user.id, Permissions.USER_READ)
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权限查看其他用户的权限信息"
            )

    # 验证用户存在
    from app.crud.user import user_crud
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 获取用户角色
    user_roles = await user_role_crud.get_user_roles(db, user_id)
    roles = []
    for ur in user_roles:
        if ur.role.is_active:
            role_with_permissions = await role_crud.get(db, id=ur.role.id)
            permissions = [rp.permission for rp in role_with_permissions.role_permissions]
            roles.append(Role(
                id=ur.role.id,
                name=ur.role.name,
                description=ur.role.description,
                is_active=ur.role.is_active,
                created_at=ur.role.created_at,
                permissions=permissions
            ))
    
    # 获取用户所有权限
    permissions = await user_role_crud.get_user_permissions(db, user_id)
    
    return UserPermissionResponse(
        user_id=user_id,
        roles=roles,
        permissions=permissions
    )


@router.post("/check-permission", response_model=PermissionCheckResponse, summary="检查用户权限")
async def check_user_permission(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int = Query(..., description="用户ID"),
    permission_name: str = Query(..., description="权限名称"),
    current_user: User = Depends(require_admin())
) -> PermissionCheckResponse:
    """
    检查用户是否有指定权限
    
    需要管理员权限
    """
    # 验证用户存在
    from app.crud.user import user_crud
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 检查权限
    has_permission = await user_role_crud.check_permission(db, user_id, permission_name)
    
    # 获取用户角色
    user_roles = await user_role_crud.get_user_roles(db, user_id)
    role_names = [ur.role.name for ur in user_roles if ur.role.is_active]
    
    return PermissionCheckResponse(
        has_permission=has_permission,
        user_id=user_id,
        permission_name=permission_name,
        roles=role_names
    )
