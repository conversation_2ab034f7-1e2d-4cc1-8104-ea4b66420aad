/**
 * 任务表单组件
 * 用于创建和编辑任务
 */
import React from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import {
  TaskStatus,
  TaskPriority,
  CreateTaskRequest,
  UpdateTaskRequest,
  Task,
  statusLabels,
  priorityLabels,
} from '../../services/taskApi';

interface TaskFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task?: Task; // 如果提供了task，则为编辑模式
  onSubmit: (data: CreateTaskRequest | UpdateTaskRequest) => void;
  isLoading?: boolean;
}

export function TaskForm({
  open,
  onOpenChange,
  task,
  onSubmit,
  isLoading = false,
}: TaskFormProps) {
  const isEdit = !!task;

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<CreateTaskRequest>({
    defaultValues: task
      ? {
          title: task.title,
          description: task.description || '',
          status: task.status,
          priority: task.priority,
          assignee: task.assignee || '',
          is_active: task.is_active,
        }
      : {
          title: '',
          description: '',
          status: TaskStatus.PENDING,
          priority: TaskPriority.MEDIUM,
          assignee: '',
          is_active: true,
        },
  });

  const watchedStatus = watch('status');
  const watchedPriority = watch('priority');

  // 重置表单当对话框关闭时
  React.useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open, reset]);

  // 当task变化时更新表单
  React.useEffect(() => {
    if (task) {
      reset({
        title: task.title,
        description: task.description || '',
        status: task.status,
        priority: task.priority,
        assignee: task.assignee || '',
        is_active: task.is_active,
      });
    }
  }, [task, reset]);

  const handleFormSubmit = (data: CreateTaskRequest) => {
    onSubmit(data);
    if (!isEdit) {
      reset(); // 只在创建模式下重置表单
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {isEdit ? '编辑任务' : '创建新任务'}
          </DialogTitle>
          <DialogDescription>
            {isEdit
              ? '修改任务信息并保存更改。'
              : '填写任务信息并创建新任务。'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">任务标题 *</Label>
            <Input
              id="title"
              {...register('title', {
                required: '任务标题是必填的',
                minLength: {
                  value: 1,
                  message: '任务标题不能为空',
                },
                maxLength: {
                  value: 200,
                  message: '任务标题不能超过200个字符',
                },
              })}
              placeholder="输入任务标题"
            />
            {errors.title && (
              <p className="text-sm text-red-600">{errors.title.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">任务描述</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="输入任务描述（可选）"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>任务状态</Label>
              <Select
                value={watchedStatus}
                onValueChange={(value) => setValue('status', value as TaskStatus)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(statusLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>优先级</Label>
              <Select
                value={watchedPriority}
                onValueChange={(value) => setValue('priority', value as TaskPriority)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择优先级" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(priorityLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="assignee">负责人</Label>
            <Input
              id="assignee"
              {...register('assignee', {
                maxLength: {
                  value: 100,
                  message: '负责人名称不能超过100个字符',
                },
              })}
              placeholder="输入负责人姓名（可选）"
            />
            {errors.assignee && (
              <p className="text-sm text-red-600">{errors.assignee.message}</p>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? '保存中...' : isEdit ? '更新任务' : '创建任务'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
