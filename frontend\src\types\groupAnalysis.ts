/**
 * 特定群体需求分析历史记录相关类型定义
 */

export interface SearchStrategy {
  id: string;
  query: string;
  category: string;
  priority: number;
  description: string;
}

export interface GroupDataSource {
  title: string;
  url: string;
  content: string;
  dataType: string;
  relevanceScore?: number;
}

export interface GroupAnalysisRecord {
  id: string;
  productType: string;
  productLabel: string;
  searchStrategies: SearchStrategy[];
  analysisResult: string;
  dataSources: GroupDataSource[];
  timestamp: string;
  status: 'success' | 'failed' | 'cancelled';
  strategyCount: number;
  analysisType: string;
  duration?: number; // 分析耗时（秒）
  errorMessage?: string; // 失败时的错误信息
}

export interface AnalysisHistoryStats {
  totalAnalyses: number;
  successfulAnalyses: number;
  failedAnalyses: number;
  averageDuration: number;
  mostAnalyzedProduct: string;
  recentAnalysisDate: string;
}

export interface HistoryFilterOptions {
  status?: 'all' | 'success' | 'failed' | 'cancelled';
  productType?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  sortBy?: 'timestamp' | 'productType' | 'status';
  sortOrder?: 'asc' | 'desc';
}
