/**
 * 增强版销冠实战训练演示页面
 * 展示新版本对话训练窗口的所有功能
 */

import { useState } from 'react'
import {
  Sparkles,
  Rocket,
  Target,
  Trophy,
  Star,
  Zap,
  Brain,
  Heart,
  Globe,
  MessageSquare,
  BarChart3,
  Award,
  Play,
  ArrowRight,
  CheckCircle,
  Clock,
  Users,
  TrendingUp
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { EnhancedTrainingDialog } from '@/components/sales-training/EnhancedTrainingDialog'
import type { TrainingCustomer } from '@/types/salesTraining'

export default function EnhancedTrainingDemoPage() {
  const [showTrainingDialog, setShowTrainingDialog] = useState(false)
  const [selectedDemo, setSelectedDemo] = useState<'basic' | 'advanced' | 'expert'>('basic')

  // 演示客户数据
  const demoCustomers: Record<string, TrainingCustomer> = {
    basic: {
      id: 'demo-1',
      name: 'John Smith',
      country: { id: 1, name: '美国', code: 'US', flag: '🇺🇸' },
      product: { id: 1, name: '企业管理软件', category: '软件', description: '全面的企业资源规划解决方案' },
      createdAt: new Date(),
      updatedAt: new Date(),
      background: {
        company: '科技创新公司',
        position: '采购经理',
        experience: '5年以上',
        preferences: ['性价比', '技术支持', '快速部署']
      }
    },
    advanced: {
      id: 'demo-2',
      name: 'Maria Rodriguez',
      country: { id: 2, name: '西班牙', code: 'ES', flag: '🇪🇸' },
      product: { id: 2, name: '工业设备', category: '制造业', description: '高精度自动化生产设备' },
      createdAt: new Date(),
      updatedAt: new Date(),
      background: {
        company: '欧洲制造集团',
        position: '技术总监',
        experience: '10年以上',
        preferences: ['技术先进性', '质量保证', '长期合作']
      }
    },
    expert: {
      id: 'demo-3',
      name: 'Hiroshi Tanaka',
      country: { id: 3, name: '日本', code: 'JP', flag: '🇯🇵' },
      product: { id: 3, name: '医疗设备', category: '医疗', description: '先进的医疗诊断设备' },
      createdAt: new Date(),
      updatedAt: new Date(),
      background: {
        company: '日本医疗科技',
        position: '首席执行官',
        experience: '15年以上',
        preferences: ['创新技术', '安全认证', '精确度']
      }
    }
  }

  const features = [
    {
      icon: Target,
      title: '多样化训练场景',
      description: '涵盖不同行业、客户类型和难度级别的丰富训练场景',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20'
    },
    {
      icon: MessageSquare,
      title: '智能对话交互',
      description: '打字动画、消息状态、快捷回复、语音输入等增强功能',
      color: 'text-green-500',
      bgColor: 'bg-green-50 dark:bg-green-900/20'
    },
    {
      icon: BarChart3,
      title: '实时评分反馈',
      description: '7个维度实时评分，动态反馈和详细改进建议',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20'
    },
    {
      icon: Trophy,
      title: '成就系统',
      description: '徽章收集、等级提升、技能树和进度追踪',
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50 dark:bg-yellow-900/20'
    },
    {
      icon: Brain,
      title: '智能学习提示',
      description: '基于对话内容的情境化学习建议和知识点推荐',
      color: 'text-indigo-500',
      bgColor: 'bg-indigo-50 dark:bg-indigo-900/20'
    },
    {
      icon: TrendingUp,
      title: '进度分析',
      description: '详细的能力分析报告和个性化提升路径',
      color: 'text-red-500',
      bgColor: 'bg-red-50 dark:bg-red-900/20'
    }
  ]

  const demoLevels = [
    {
      id: 'basic',
      name: '基础演示',
      description: '体验基本的对话训练功能',
      difficulty: '初级',
      duration: '10-15分钟',
      features: ['基础对话', '简单评分', '基本提示'],
      color: 'bg-green-500'
    },
    {
      id: 'advanced',
      name: '进阶演示',
      description: '探索高级功能和复杂场景',
      difficulty: '中级',
      duration: '20-25分钟',
      features: ['复杂场景', '详细评分', '智能建议', '成就系统'],
      color: 'bg-blue-500'
    },
    {
      id: 'expert',
      name: '专家演示',
      description: '挑战最高难度的训练场景',
      difficulty: '高级',
      duration: '30-35分钟',
      features: ['专家场景', '全面评估', '高级技巧', '完整体验'],
      color: 'bg-purple-500'
    }
  ]

  const handleStartDemo = () => {
    setShowTrainingDialog(true)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* 头部标题 */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg">
              <Sparkles className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              增强版销冠实战训练
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            全新升级的AI驱动销售训练平台，提供沉浸式对话体验、实时智能评分和个性化学习路径
          </p>
          <div className="flex items-center justify-center space-x-4 mt-6">
            <Badge className="bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 px-3 py-1">
              <Rocket className="w-4 h-4 mr-1" />
              全新升级
            </Badge>
            <Badge className="bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 px-3 py-1">
              <Star className="w-4 h-4 mr-1" />
              AI驱动
            </Badge>
            <Badge className="bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400 px-3 py-1">
              <Heart className="w-4 h-4 mr-1" />
              智能体验
            </Badge>
          </div>
        </div>

        {/* 功能特性 */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            核心功能特性
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Card
                  key={index}
                  className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-gray-200 dark:border-gray-700 opacity-0 animate-fade-in-up"
                  style={{animationDelay: `${index * 0.1}s`, animationFillMode: 'forwards'}}
                >
                  <CardContent className="p-6">
                    <div className={`w-12 h-12 rounded-lg ${feature.bgColor} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className={`w-6 h-6 ${feature.color}`} />
                    </div>
                    <h3 className="font-semibold text-lg mb-2 text-gray-900 dark:text-white">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* 演示级别选择 */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            选择演示级别
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {demoLevels.map((level, index) => (
              <Card
                key={level.id}
                className={`cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border-2 opacity-0 animate-fade-in-up ${
                  selectedDemo === level.id
                    ? 'border-blue-500 dark:border-blue-400 ring-2 ring-blue-500/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600'
                }`}
                style={{animationDelay: `${index * 0.1}s`, animationFillMode: 'forwards'}}
                onClick={() => setSelectedDemo(level.id as any)}
              >
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className={`w-10 h-10 rounded-lg ${level.color} flex items-center justify-center text-white font-bold`}>
                      {index + 1}
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
                        {level.name}
                      </h3>
                      <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                        <Badge variant="outline" className="text-xs">
                          {level.difficulty}
                        </Badge>
                        <span>·</span>
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{level.duration}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                    {level.description}
                  </p>
                  <div className="space-y-2">
                    {level.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-2 text-sm">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-400">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* 开始演示按钮 */}
        <div className="text-center">
          <Card className="max-w-md mx-auto shadow-lg border-gray-200 dark:border-gray-700">
            <CardContent className="p-6">
              <div className="mb-4">
                <h3 className="font-semibold text-lg mb-2 text-gray-900 dark:text-white">
                  准备开始训练
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  选择的演示级别：<span className="font-medium text-blue-600 dark:text-blue-400">
                    {demoLevels.find(l => l.id === selectedDemo)?.name}
                  </span>
                </p>
              </div>
              <Button
                onClick={handleStartDemo}
                className="w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              >
                <Play className="w-5 h-5 mr-2" />
                开始演示训练
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* 统计信息 */}
        <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className="text-center shadow-sm border-gray-200 dark:border-gray-700">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">50+</div>
              <p className="text-sm text-gray-600 dark:text-gray-400">训练场景</p>
            </CardContent>
          </Card>
          <Card className="text-center shadow-sm border-gray-200 dark:border-gray-700">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">7</div>
              <p className="text-sm text-gray-600 dark:text-gray-400">评估维度</p>
            </CardContent>
          </Card>
          <Card className="text-center shadow-sm border-gray-200 dark:border-gray-700">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">30+</div>
              <p className="text-sm text-gray-600 dark:text-gray-400">成就徽章</p>
            </CardContent>
          </Card>
          <Card className="text-center shadow-sm border-gray-200 dark:border-gray-700">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">∞</div>
              <p className="text-sm text-gray-600 dark:text-gray-400">学习提升</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 训练对话弹窗 */}
      {showTrainingDialog && (
        <EnhancedTrainingDialog
          customer={demoCustomers[selectedDemo]}
          open={showTrainingDialog}
          onClose={() => setShowTrainingDialog(false)}
        />
      )}
    </div>
  )
}
