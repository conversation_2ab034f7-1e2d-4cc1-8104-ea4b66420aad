import asyncio
import json
import logging
import time  # Add time import for profiling
from datetime import UTC, datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import arq
import jwt
import tomli
from fastapi import Depends, FastAPI, Form, Header, HTTPException, Query, UploadFile
from fastapi.middleware.cors import CORSMiddleware  # Import CORSMiddleware
from fastapi.responses import StreamingResponse, Response
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from starlette.middleware.sessions import SessionMiddleware

from core.agent import MorphikAgent
from core.app_factory import lifespan
from core.auth_utils import verify_token
from core.config import get_settings
from core.logging_config import setup_logging
from core.database.postgres_database import PostgresDatabase
from core.dependencies import get_redis_pool
from core.limits_utils import check_and_increment_limits
from core.models.auth import AuthContext, EntityType
from core.models.chat import ChatMessage
from core.models.completion import ChunkSource, CompletionResponse
from core.models.documents import <PERSON><PERSON><PERSON><PERSON><PERSON>, Document, DocumentResult
from core.models.folders import Folder, FolderCreate
from core.models.graph import Graph
from core.models.prompts import validate_prompt_overrides_with_http_exception
from core.models.request import (
    AgentQueryRequest,
    CompletionQueryRequest,
    CreateGraphRequest,
    GenerateUriRequest,
    IngestTextRequest,
    RetrieveRequest,
    SetFolderRuleRequest,
    UpdateGraphRequest,
)
from core.routes.ingest import router as ingest_router
from core.services.telemetry import TelemetryService
from core.services_init import document_service

# 为Docker环境设置日志配置
setup_logging()

# 初始化FastAPI应用
logger = logging.getLogger(__name__)


# 性能跟踪类
class PerformanceTracker:
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = time.time()
        self.phases = {}
        self.current_phase = None
        self.phase_start = None

    def start_phase(self, phase_name: str):
        # 如果有正在运行的阶段，则结束当前阶段
        if self.current_phase and self.phase_start:
            self.phases[self.current_phase] = time.time() - self.phase_start

        # 开始新阶段
        self.current_phase = phase_name
        self.phase_start = time.time()

    def end_phase(self):
        if self.current_phase and self.phase_start:
            self.phases[self.current_phase] = time.time() - self.phase_start
            self.current_phase = None
            self.phase_start = None

    def add_suboperation(self, name: str, duration: float):
        """添加子操作计时"""
        self.phases[name] = duration

    def log_summary(self, additional_info: str = ""):
        total_time = time.time() - self.start_time

        # 如果仍在运行，则结束当前阶段
        if self.current_phase and self.phase_start:
            self.phases[self.current_phase] = time.time() - self.phase_start

        logger.info(f"=== {self.operation_name} 性能摘要 ===")
        logger.info(f"总时间: {total_time:.2f}s")

        # 按持续时间排序阶段（最长的优先）
        for phase, duration in sorted(self.phases.items(), key=lambda x: x[1], reverse=True):
            percentage = (duration / total_time) * 100 if total_time > 0 else 0
            logger.info(f"  - {phase}: {duration:.2f}s ({percentage:.1f}%)")

        if additional_info:
            logger.info(additional_info)
        logger.info("=" * (len(self.operation_name) + 31))


# ---------------------------------------------------------------------------
# 应用实例和核心初始化（移动了lifespan，其余不变）
# ---------------------------------------------------------------------------

app = FastAPI(lifespan=lifespan)

# 添加CORS中间件（与重构前行为相同）
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化遥测服务
telemetry = TelemetryService()

# OpenTelemetry仪表化 – 排除嘈杂的跨度/头部
FastAPIInstrumentor.instrument_app(
    app,
    excluded_urls="health,health/.*",
    exclude_spans=["send", "receive"],
    http_capture_headers_server_request=None,
    http_capture_headers_server_response=None,
    tracer_provider=None,
)

# 全局设置对象
settings = get_settings()

# ---------------------------------------------------------------------------
# 会话cookie行为在云端/自托管之间有所不同
# ---------------------------------------------------------------------------

if settings.MODE == "cloud":
    app.add_middleware(
        SessionMiddleware,
        secret_key=settings.SESSION_SECRET_KEY,
        same_site="none",
        https_only=True,
    )
else:
    app.add_middleware(SessionMiddleware, secret_key=settings.SESSION_SECRET_KEY)


# 简单的健康检查端点
@app.get("/ping")
async def ping_health():
    """简单的健康检查端点，返回200 OK状态。"""
    return {"status": "ok", "message": "Server is running"}


# ---------------------------------------------------------------------------
# 核心单例（数据库、向量存储、存储、解析器、模型...）
# ---------------------------------------------------------------------------


# 存储在app.state中以供后续访问
app.state.document_service = document_service
logger.info("文档服务已初始化并存储在app.state中")

# 注册摄取路由器
app.include_router(ingest_router)

# 单个MorphikAgent实例（工具定义已缓存）
morphik_agent = MorphikAgent(document_service=document_service)


# 规范化文件夹名称参数的辅助函数
def normalize_folder_name(folder_name: Optional[Union[str, List[str]]]) -> Optional[Union[str, List[str]]]:
    """将字符串'null'转换为None，用于folder_name参数。"""
    if folder_name is None:
        return None
    if isinstance(folder_name, str):
        return None if folder_name.lower() == "null" else folder_name
    if isinstance(folder_name, list):
        return [None if f.lower() == "null" else f for f in folder_name]
    return folder_name


# 企业版专用路由（可选）
try:
    from ee.routers import init_app as _init_ee_app  # type: ignore  # noqa: E402

    _init_ee_app(app)  # noqa: SLF001 – 运行时扩展
except ModuleNotFoundError as exc:
    logger.debug("未找到企业版包 – 以社区版模式运行。")
    logger.error("ModuleNotFoundError: %s", exc, exc_info=True)
except ImportError as exc:
    logger.error("从ee.routers导入init_app失败: %s", exc, exc_info=True)
except Exception as exc:  # noqa: BLE001
    logger.error("企业版应用初始化过程中发生意外错误: %s", exc, exc_info=True)


@app.post("/retrieve/chunks", response_model=List[ChunkResult])
@telemetry.track(operation_type="retrieve_chunks", metadata_resolver=telemetry.retrieve_chunks_metadata)
async def retrieve_chunks(request: RetrieveRequest, auth: AuthContext = Depends(verify_token)):
    """
    检索相关的文档块。

    参数:
        request: RetrieveRequest包含:
            - query: 搜索查询文本
            - filters: 可选的元数据过滤器
            - k: 结果数量（默认: 4）
            - min_score: 最小相似度阈值（默认: 0.0）
            - use_reranking: 是否使用重排序
            - use_colpali: 是否使用ColPali风格的嵌入模型
            - folder_name: 可选的文件夹范围限制
            - end_user_id: 可选的终端用户ID范围限制
        auth: 身份验证上下文

    返回:
        List[ChunkResult]: 相关文档块列表
    """
    # 初始化性能跟踪器
    perf = PerformanceTracker(f"检索文档块: '{request.query[:50]}...'")

    try:
        # 主要检索操作
        perf.start_phase("document_service_retrieve_chunks")
        results = await document_service.retrieve_chunks(
            request.query,
            auth,
            request.filters,
            request.k,
            request.min_score,
            request.use_reranking,
            request.use_colpali,
            request.folder_name,
            request.end_user_id,
            perf,  # 传递性能跟踪器
        )

        # 记录合并的性能摘要
        perf.log_summary(f"检索到 {len(results)} 个文档块")

        return results
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.post("/retrieve/docs", response_model=List[DocumentResult])
@telemetry.track(operation_type="retrieve_docs", metadata_resolver=telemetry.retrieve_docs_metadata)
async def retrieve_documents(request: RetrieveRequest, auth: AuthContext = Depends(verify_token)):
    """
    检索相关的文档。

    参数:
        request: RetrieveRequest包含:
            - query: 搜索查询文本
            - filters: 可选的元数据过滤器
            - k: 结果数量（默认: 4）
            - min_score: 最小相似度阈值（默认: 0.0）
            - use_reranking: 是否使用重排序
            - use_colpali: 是否使用ColPali风格的嵌入模型
            - folder_name: 可选的文件夹范围限制
            - end_user_id: 可选的终端用户ID范围限制
        auth: 身份验证上下文

    返回:
        List[DocumentResult]: 相关文档列表
    """
    # 初始化性能跟踪器
    perf = PerformanceTracker(f"检索文档: '{request.query[:50]}...'")

    try:
        # 主要检索操作
        perf.start_phase("document_service_retrieve_docs")
        results = await document_service.retrieve_docs(
            request.query,
            auth,
            request.filters,
            request.k,
            request.min_score,
            request.use_reranking,
            request.use_colpali,
            request.folder_name,
            request.end_user_id,
        )

        # 记录合并的性能摘要
        perf.log_summary(f"检索到 {len(results)} 个文档")

        return results
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.post("/batch/documents", response_model=List[Document])
@telemetry.track(operation_type="batch_get_documents", metadata_resolver=telemetry.batch_documents_metadata)
async def batch_get_documents(request: Dict[str, Any], auth: AuthContext = Depends(verify_token)):
    """
    通过文档ID在单个批量操作中检索多个文档。

    参数:
        request: 包含以下内容的字典:
            - document_ids: 要检索的文档ID列表
            - folder_name: 可选的文件夹范围限制
            - end_user_id: 可选的终端用户ID范围限制
        auth: 身份验证上下文

    返回:
        List[Document]: 匹配ID的文档列表
    """
    # 初始化性能跟踪器
    perf = PerformanceTracker("批量获取文档")

    try:
        # 从请求中提取document_ids
        perf.start_phase("request_extraction")
        document_ids = request.get("document_ids", [])
        folder_name = request.get("folder_name")
        end_user_id = request.get("end_user_id")

        if not document_ids:
            perf.log_summary("未提供文档ID")
            return []

        # 为文件夹和用户范围创建系统过滤器
        perf.start_phase("filter_creation")
        system_filters = {}
        if folder_name is not None:
            normalized_folder_name = normalize_folder_name(folder_name)
            system_filters["folder_name"] = normalized_folder_name
        if end_user_id:
            system_filters["end_user_id"] = end_user_id
        if auth.app_id:
            system_filters["app_id"] = auth.app_id

        # 主要批量检索操作
        perf.start_phase("batch_retrieve_documents")
        results = await document_service.batch_retrieve_documents(document_ids, auth, folder_name, end_user_id)

        # 记录合并的性能摘要
        perf.log_summary(f"检索到 {len(results)}/{len(document_ids)} 个文档")

        return results
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.post("/batch/chunks", response_model=List[ChunkResult])
@telemetry.track(operation_type="batch_get_chunks", metadata_resolver=telemetry.batch_chunks_metadata)
async def batch_get_chunks(request: Dict[str, Any], auth: AuthContext = Depends(verify_token)):
    """
    通过文档ID和块编号在单个批量操作中检索特定的文档块。

    参数:
        request: 包含以下内容的字典:
            - sources: ChunkSource对象列表（包含document_id和chunk_number）
            - folder_name: 可选的文件夹范围限制
            - end_user_id: 可选的终端用户ID范围限制
            - use_colpali: 是否使用ColPali风格的嵌入
        auth: 身份验证上下文

    返回:
        List[ChunkResult]: 文档块结果列表
    """
    # 初始化性能跟踪器
    perf = PerformanceTracker("批量获取文档块")

    try:
        # 从请求中提取sources
        perf.start_phase("request_extraction")
        sources = request.get("sources", [])
        folder_name = request.get("folder_name")
        end_user_id = request.get("end_user_id")
        use_colpali = request.get("use_colpali")

        if not sources:
            perf.log_summary("未提供数据源")
            return []

        # 如果需要，将sources转换为ChunkSource对象
        perf.start_phase("source_conversion")
        chunk_sources = []
        for source in sources:
            if isinstance(source, dict):
                chunk_sources.append(ChunkSource(**source))
            else:
                chunk_sources.append(source)

        # 为文件夹和用户范围创建系统过滤器
        perf.start_phase("filter_creation")
        system_filters = {}
        if folder_name is not None:
            normalized_folder_name = normalize_folder_name(folder_name)
            system_filters["folder_name"] = normalized_folder_name
        if end_user_id:
            system_filters["end_user_id"] = end_user_id
        if auth.app_id:
            system_filters["app_id"] = auth.app_id

        # 主要批量检索操作
        perf.start_phase("batch_retrieve_chunks")
        results = await document_service.batch_retrieve_chunks(
            chunk_sources, auth, folder_name, end_user_id, use_colpali
        )

        # 记录合并的性能摘要
        perf.log_summary(f"检索到 {len(results)}/{len(sources)} 个文档块")

        return results
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.post("/query", response_model=CompletionResponse)
@telemetry.track(operation_type="query", metadata_resolver=telemetry.query_metadata)
async def query_completion(
    request: CompletionQueryRequest,
    auth: AuthContext = Depends(verify_token),
    redis: arq.ArqRedis = Depends(get_redis_pool),
):
    """
    使用相关文档块作为上下文生成完成响应。

    当提供graph_name时，查询将利用知识图谱通过查找相关实体及其连接的文档来增强检索。

    参数:
        request: CompletionQueryRequest包含:
            - query: 查询文本
            - filters: 可选的元数据过滤器
            - k: 用作上下文的文档块数量（默认: 4）
            - min_score: 最小相似度阈值（默认: 0.0）
            - max_tokens: 完成响应的最大令牌数
            - temperature: 模型温度
            - use_reranking: 是否使用重排序
            - use_colpali: 是否使用ColPali风格的嵌入模型
            - graph_name: 用于知识图谱增强检索的图谱名称（可选）
            - hop_depth: 在图谱中遍历的关系跳数（1-3）
            - include_paths: 是否在响应中包含关系路径
            - prompt_overrides: 实体提取、解析和查询提示的可选自定义
            - folder_name: 可选的文件夹范围限制
            - end_user_id: 可选的终端用户ID范围限制
            - schema: 结构化输出的可选模式
            - chat_id: 维护历史记录的可选聊天会话标识符
        auth: 身份验证上下文

    返回:
        CompletionResponse: 生成的文本完成或结构化输出
    """
    # 初始化性能跟踪器
    perf = PerformanceTracker(f"查询: '{request.query[:50]}...'")

    try:
        # 在继续之前验证提示覆盖
        perf.start_phase("prompt_validation")
        if request.prompt_overrides:
            validate_prompt_overrides_with_http_exception(request.prompt_overrides, operation_type="query")

        # 聊天历史检索
        perf.start_phase("chat_history_retrieval")
        history_key = None
        history: List[Dict[str, Any]] = []
        if request.chat_id:
            history_key = f"chat:{request.chat_id}"
            stored = await redis.get(history_key)
            if stored:
                try:
                    history = json.loads(stored)
                except Exception:
                    history = []
            else:
                db_hist = await document_service.db.get_chat_history(request.chat_id, auth.user_id, auth.app_id)
                if db_hist:
                    history = db_hist

            history.append(
                {
                    "role": "user",
                    "content": request.query,
                    "timestamp": datetime.now(UTC).isoformat(),
                }
            )

        # 如果在云模式下检查查询限制
        perf.start_phase("limits_check")
        if settings.MODE == "cloud" and auth.user_id:
            # 在继续之前检查限制
            await check_and_increment_limits(auth, "query", 1)

        # 动态模型切换支持
        original_completion_model = None
        if request.model:
            # 如果请求指定了模型，临时切换completion_model
            from core.completion.litellm_completion import LiteLLMCompletionModel
            try:
                # 保存原始模型
                original_completion_model = document_service.completion_model
                # 创建新的completion_model实例
                new_completion_model = LiteLLMCompletionModel(model_key=request.model)
                document_service.completion_model = new_completion_model
                logger.info(f"🔄 临时切换到模型: {request.model}")
            except Exception as e:
                logger.error(f"❌ 模型切换失败: {e}")
                # 如果切换失败，继续使用原始模型
                pass

        try:
            # 主要查询处理
            perf.start_phase("document_service_query")
            result = await document_service.query(
                request.query,
                auth,
                request.filters,
                request.k,
                request.min_score,
                request.max_tokens,
                request.temperature,
                request.use_reranking,
                request.use_colpali,
                request.graph_name,
                request.hop_depth,
                request.include_paths,
                request.prompt_overrides,
                request.folder_name,
                request.end_user_id,
                request.schema,
                history,
                perf,  # 传递性能跟踪器
                request.stream_response,
            )
        finally:
            # 恢复原始模型
            if original_completion_model:
                document_service.completion_model = original_completion_model
                logger.info("🔄 已恢复原始模型")

        # 处理流式与非流式响应
        if request.stream_response:
            # 对于流式响应，解包元组
            response_stream, sources = result

            async def generate_stream():
                full_content = ""
                first_token_time = None

                async for chunk in response_stream:
                    # 跟踪首个令牌的时间
                    if first_token_time is None:
                        first_token_time = time.time()
                        completion_start_to_first_token = first_token_time - perf.start_time
                        perf.add_suboperation("completion_start_to_first_token", completion_start_to_first_token)
                        logger.info(f"完成开始到首个令牌: {completion_start_to_first_token:.2f}s")

                    full_content += chunk
                    yield f"data: {json.dumps({'content': chunk})}\n\n"

                # 将sources转换为前端期望的格式
                sources_info = [
                    {"document_id": source.document_id, "chunk_number": source.chunk_number, "score": source.score}
                    for source in sources
                ]

                # 发送带有sources的完成信号
                yield f"data: {json.dumps({'done': True, 'sources': sources_info})}\n\n"

                # 流式传输完成后处理聊天历史
                if history_key:
                    history.append(
                        {
                            "role": "assistant",
                            "content": full_content,
                            "timestamp": datetime.now(UTC).isoformat(),
                        }
                    )
                    await redis.set(history_key, json.dumps(history))
                    await document_service.db.upsert_chat_history(
                        request.chat_id,
                        auth.user_id,
                        auth.app_id,
                        history,
                    )

                # 记录流式传输的合并性能摘要
                streaming_time = time.time() - first_token_time if first_token_time else 0
                perf.add_suboperation("streaming_duration", streaming_time)
                perf.log_summary(f"生成了包含 {len(sources)} 个来源的流式完成")

            headers = {
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
            return StreamingResponse(generate_stream(), media_type="text/event-stream", headers=headers)
        else:
            # 对于非流式响应，结果就是CompletionResponse
            response = result

            # 非流式响应的聊天历史存储
            perf.start_phase("chat_history_storage")
            if history_key:
                history.append(
                    {
                        "role": "assistant",
                        "content": response.completion,
                        "timestamp": datetime.now(UTC).isoformat(),
                    }
                )
                await redis.set(history_key, json.dumps(history))
                await document_service.db.upsert_chat_history(
                    request.chat_id,
                    auth.user_id,
                    auth.app_id,
                    history,
                )

            # 记录合并的性能摘要
            perf.log_summary(f"生成了包含 {len(response.sources) if response.sources else 0} 个来源的完成")

            return response
    except ValueError as e:
        validate_prompt_overrides_with_http_exception(operation_type="query", error=e)
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.get("/chat/{chat_id}", response_model=List[ChatMessage])
async def get_chat_history(
    chat_id: str,
    auth: AuthContext = Depends(verify_token),
    redis: arq.ArqRedis = Depends(get_redis_pool),
):
    """检索聊天会话的消息历史记录。

    参数:
        chat_id: 要加载历史记录的会话标识符。
        auth: 用于验证会话访问权限的身份验证上下文。
        redis: 存储聊天消息的Redis连接。

    返回:
        :class:`ChatMessage` 对象列表，如果没有历史记录则返回空列表。
    """
    history_key = f"chat:{chat_id}"
    stored = await redis.get(history_key)
    if not stored:
        db_hist = await document_service.db.get_chat_history(chat_id, auth.user_id, auth.app_id)
        if not db_hist:
            return []
        return [ChatMessage(**m) for m in db_hist]
    try:
        data = json.loads(stored)
        return [ChatMessage(**m) for m in data]
    except Exception:
        return []


@app.post("/agent", response_model=Dict[str, Any])
@telemetry.track(operation_type="agent_query")
async def agent_query(
    request: AgentQueryRequest,
    auth: AuthContext = Depends(verify_token),
    redis: arq.ArqRedis = Depends(get_redis_pool),
):
    """使用 :class:`MorphikAgent` 执行智能代理风格的查询。

    参数:
        request: 包含自然语言问题和可选chat_id的查询载荷。
        auth: 用于执行限制和访问控制的身份验证上下文。
        redis: 用于聊天历史存储的Redis连接。

    返回:
        包含代理完整响应的字典。
    """
    # 聊天历史检索
    history_key = None
    history: List[Dict[str, Any]] = []
    if request.chat_id:
        history_key = f"chat:{request.chat_id}"
        stored = await redis.get(history_key)
        if stored:
            try:
                history = json.loads(stored)
            except Exception:
                history = []
        else:
            db_hist = await document_service.db.get_chat_history(request.chat_id, auth.user_id, auth.app_id)
            if db_hist:
                history = db_hist

        history.append(
            {
                "role": "user",
                "content": request.query,
                "timestamp": datetime.now(UTC).isoformat(),
            }
        )

    # 在云模式下检查免费层代理调用限制
    if settings.MODE == "cloud" and auth.user_id:
        await check_and_increment_limits(auth, "agent", 1)

    # 使用共享的MorphikAgent实例；每次运行的状态现在在内部隔离
    response = await morphik_agent.run(request.query, auth, history)

    # 聊天历史存储
    if history_key:
        # 存储包含结构化数据的完整代理响应
        agent_message = {
            "role": "assistant",
            "content": response.get("response", ""),
            "timestamp": datetime.now(UTC).isoformat(),
            # 存储代理特定的结构化数据
            "agent_data": {
                "display_objects": response.get("display_objects", []),
                "tool_history": response.get("tool_history", []),
                "sources": response.get("sources", []),
            },
        }
        history.append(agent_message)
        await redis.set(history_key, json.dumps(history))
        await document_service.db.upsert_chat_history(
            request.chat_id,
            auth.user_id,
            auth.app_id,
            history,
        )

    # 返回完整的响应字典
    return response


@app.post("/documents", response_model=List[Document])
async def list_documents(
    auth: AuthContext = Depends(verify_token),
    skip: int = 0,
    limit: int = 10000,
    filters: Optional[Dict[str, Any]] = None,
    folder_name: Optional[Union[str, List[str]]] = Query(None),
    end_user_id: Optional[str] = None,
):
    """
    列出可访问的文档。

    参数:
        auth: 身份验证上下文
        skip: 要跳过的文档数量
        limit: 返回的最大文档数量
        filters: 可选的元数据过滤器
        folder_name: 可选的文件夹范围限制
        end_user_id: 可选的终端用户ID范围限制

    返回:
        List[Document]: 可访问的文档列表
    """
    # 为文件夹和用户范围创建系统过滤器
    system_filters = {}

    # 规范化folder_name参数（将字符串"null"转换为None）
    if folder_name is not None:
        normalized_folder_name = normalize_folder_name(folder_name)
        system_filters["folder_name"] = normalized_folder_name
    if end_user_id:
        system_filters["end_user_id"] = end_user_id
    if auth.app_id:
        system_filters["app_id"] = auth.app_id

    return await document_service.db.get_documents(auth, skip, limit, filters, system_filters)


@app.get("/documents/{document_id}", response_model=Document)
async def get_document(document_id: str, auth: AuthContext = Depends(verify_token)):
    """通过外部标识符检索单个文档。

    参数:
        document_id: 要获取的文档的外部ID。
        auth: 用于验证访问权限的身份验证上下文。

    返回:
        如果找到，返回 :class:`Document` 元数据。
    """
    try:
        doc = await document_service.db.get_document(document_id, auth)
        logger.debug(f"找到文档: {doc}")
        if not doc:
            raise HTTPException(status_code=404, detail="Document not found")
        return doc
    except HTTPException as e:
        logger.error(f"获取文档时出错: {e}")
        raise e


@app.get("/documents/{document_id}/status", response_model=Dict[str, Any])
async def get_document_status(document_id: str, auth: AuthContext = Depends(verify_token)):
    """
    获取文档的处理状态。

    参数:
        document_id: 要检查的文档ID
        auth: 身份验证上下文

    返回:
        包含文档状态信息的字典
    """
    try:
        doc = await document_service.db.get_document(document_id, auth)
        if not doc:
            raise HTTPException(status_code=404, detail="Document not found")

        # 提取状态信息
        status = doc.system_metadata.get("status", "unknown")

        response = {
            "document_id": doc.external_id,
            "status": status,
            "filename": doc.filename,
            "created_at": doc.system_metadata.get("created_at"),
            "updated_at": doc.system_metadata.get("updated_at"),
        }

        # 如果失败则添加错误信息
        if status == "failed":
            response["error"] = doc.system_metadata.get("error", "Unknown error")

        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档状态时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting document status: {str(e)}")


@app.delete("/documents/{document_id}")
@telemetry.track(operation_type="delete_document", metadata_resolver=telemetry.document_delete_metadata)
async def delete_document(document_id: str, auth: AuthContext = Depends(verify_token)):
    """
    删除文档及其所有关联数据。

    此端点删除文档及其所有关联数据，包括：
    - 文档元数据
    - 存储中的文档内容
    - 向量存储中的文档块和嵌入

    参数:
        document_id: 要删除的文档ID
        auth: 身份验证上下文（必须具有文档的写入权限）

    返回:
        删除状态
    """
    try:
        success = await document_service.delete_document(document_id, auth)
        if not success:
            raise HTTPException(status_code=404, detail="Document not found or delete failed")
        return {"status": "success", "message": f"Document {document_id} deleted successfully"}
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.get("/storage/{key:path}")
async def download_file(
    key: str,
    auth: AuthContext = Depends(verify_token)
):
    """
    下载存储中的文件。

    参数:
        key: 文件路径（可能包含子目录）
        auth: 身份验证上下文

    返回:
        文件内容
    """
    try:
        # 对于本地存储，我们需要使用空的 bucket 参数
        # 因为 LocalStorage 会根据 bucket 是否为空来决定路径构建方式
        file_content = await document_service.storage.download_file("", key)

        # 根据文件扩展名确定MIME类型
        import mimetypes
        content_type, _ = mimetypes.guess_type(key)
        if not content_type:
            content_type = 'application/octet-stream'

        # 从路径中提取文件名
        filename = key.split('/')[-1]

        # 处理中文文件名的编码问题
        import urllib.parse
        encoded_filename = urllib.parse.quote(filename, safe='')

        return Response(
            content=file_content,
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
            }
        )
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
    except Exception as e:
        logger.error(f"下载文件时出错: {e}")
        raise HTTPException(status_code=500, detail="Error downloading file")


@app.get("/storage/{bucket}/{key:path}")
async def download_file_with_bucket(
    bucket: str,
    key: str,
    auth: AuthContext = Depends(verify_token)
):
    """
    下载存储中的文件（带 bucket 参数）。

    参数:
        bucket: 存储桶名称（对于本地存储通常是存储根路径）
        key: 文件路径（可能包含子目录）
        auth: 身份验证上下文

    返回:
        文件内容
    """
    try:
        # 对于本地存储，如果 bucket 是存储根路径，我们使用空 bucket
        # 这样 LocalStorage 会直接使用 key 作为相对路径
        if bucket.endswith('storage') or bucket == 'storage':
            file_content = await document_service.storage.download_file("", key)
        else:
            file_content = await document_service.storage.download_file(bucket, key)

        # 根据文件扩展名确定MIME类型
        import mimetypes
        content_type, _ = mimetypes.guess_type(key)
        if not content_type:
            content_type = 'application/octet-stream'

        # 从路径中提取文件名
        filename = key.split('/')[-1]

        # 处理中文文件名的编码问题
        import urllib.parse
        encoded_filename = urllib.parse.quote(filename, safe='')

        return Response(
            content=file_content,
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
            }
        )
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="File not found")
    except Exception as e:
        logger.error(f"下载文件时出错: {e}")
        raise HTTPException(status_code=500, detail="Error downloading file")


@app.get("/documents/filename/{filename}", response_model=Document)
async def get_document_by_filename(
    filename: str,
    auth: AuthContext = Depends(verify_token),
    folder_name: Optional[Union[str, List[str]]] = None,
    end_user_id: Optional[str] = None,
):
    """
    通过文件名获取文档。

    参数:
        filename: 要检索的文档的文件名
        auth: 身份验证上下文
        folder_name: 可选的文件夹范围限制
        end_user_id: 可选的终端用户ID范围限制

    返回:
        Document: 如果找到且可访问，返回文档元数据
    """
    try:
        # 为文件夹和用户范围创建系统过滤器
        system_filters = {}
        if folder_name is not None:
            normalized_folder_name = normalize_folder_name(folder_name)
            system_filters["folder_name"] = normalized_folder_name
        if end_user_id:
            system_filters["end_user_id"] = end_user_id

        doc = await document_service.db.get_document_by_filename(filename, auth, system_filters)
        logger.debug(f"通过文件名找到文档: {doc}")
        if not doc:
            raise HTTPException(status_code=404, detail=f"Document with filename '{filename}' not found")
        return doc
    except HTTPException as e:
        logger.error(f"通过文件名获取文档时出错: {e}")
        raise e


@app.post("/documents/{document_id}/update_text", response_model=Document)
@telemetry.track(operation_type="update_document_text", metadata_resolver=telemetry.document_update_text_metadata)
async def update_document_text(
    document_id: str,
    request: IngestTextRequest,
    update_strategy: str = "add",
    auth: AuthContext = Depends(verify_token),
):
    """
    使用指定策略更新文档的新文本内容。

    参数:
        document_id: 要更新的文档ID
        request: 更新的文本内容和元数据
        update_strategy: 更新文档的策略（默认: 'add'）
        auth: 身份验证上下文

    返回:
        Document: 更新后的文档元数据
    """
    try:
        doc = await document_service.update_document(
            document_id=document_id,
            auth=auth,
            content=request.content,
            file=None,
            filename=request.filename,
            metadata=request.metadata,
            rules=request.rules,
            update_strategy=update_strategy,
            use_colpali=request.use_colpali,
        )

        if not doc:
            raise HTTPException(status_code=404, detail="Document not found or update failed")

        return doc
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.post("/documents/{document_id}/update_file", response_model=Document)
@telemetry.track(operation_type="update_document_file", metadata_resolver=telemetry.document_update_file_metadata)
async def update_document_file(
    document_id: str,
    file: UploadFile,
    metadata: str = Form("{}"),
    rules: str = Form("[]"),
    update_strategy: str = Form("add"),
    use_colpali: Optional[bool] = Form(None),
    auth: AuthContext = Depends(verify_token),
):
    """
    使用指定策略通过文件内容更新文档。

    参数:
        document_id: 要更新的文档ID
        file: 要添加到文档的文件
        metadata: 与现有元数据合并的元数据JSON字符串
        rules: 应用于内容的规则JSON字符串
        update_strategy: 更新文档的策略（默认: 'add'）
        use_colpali: 是否使用多向量嵌入
        auth: 身份验证上下文

    返回:
        Document: 更新后的文档元数据
    """
    try:
        metadata_dict = json.loads(metadata)
        rules_list = json.loads(rules)

        doc = await document_service.update_document(
            document_id=document_id,
            auth=auth,
            content=None,
            file=file,
            filename=file.filename,
            metadata=metadata_dict,
            rules=rules_list,
            update_strategy=update_strategy,
            use_colpali=use_colpali,
        )

        if not doc:
            raise HTTPException(status_code=404, detail="Document not found or update failed")

        return doc
    except json.JSONDecodeError as e:
        raise HTTPException(status_code=400, detail=f"Invalid JSON: {str(e)}")
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.post("/documents/{document_id}/update_metadata", response_model=Document)
@telemetry.track(
    operation_type="update_document_metadata",
    metadata_resolver=telemetry.document_update_metadata_resolver,
)
async def update_document_metadata(
    document_id: str, metadata: Dict[str, Any], auth: AuthContext = Depends(verify_token)
):
    """
    仅更新文档的元数据。

    参数:
        document_id: 要更新的文档ID
        metadata: 与现有元数据合并的新元数据
        auth: 身份验证上下文

    返回:
        Document: 更新后的文档元数据
    """
    try:
        doc = await document_service.update_document(
            document_id=document_id,
            auth=auth,
            content=None,
            file=None,
            filename=None,
            metadata=metadata,
            rules=[],
            update_strategy="add",
            use_colpali=None,
        )

        if not doc:
            raise HTTPException(status_code=404, detail="Document not found or update failed")

        return doc
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


# 使用统计端点
@app.get("/usage/stats")
@telemetry.track(operation_type="get_usage_stats", metadata_resolver=telemetry.usage_stats_metadata)
async def get_usage_stats(auth: AuthContext = Depends(verify_token)) -> Dict[str, int]:
    """获取已认证用户的使用统计信息。

    参数:
        auth: 标识调用者的身份验证上下文。

    返回:
        操作类型到令牌使用计数的映射。
    """
    if not auth.permissions or "admin" not in auth.permissions:
        return telemetry.get_user_usage(auth.entity_id)
    return telemetry.get_user_usage(auth.entity_id)


@app.get("/usage/recent")
@telemetry.track(operation_type="get_recent_usage", metadata_resolver=telemetry.recent_usage_metadata)
async def get_recent_usage(
    auth: AuthContext = Depends(verify_token),
    operation_type: Optional[str] = None,
    since: Optional[datetime] = None,
    status: Optional[str] = None,
) -> List[Dict]:
    """检索用户或应用程序的最近遥测记录。

    参数:
        auth: 身份验证上下文；管理员用户接收全局记录。
        operation_type: 可选的操作类型过滤器。
        since: 仅返回比此时间戳更新的记录。
        status: 可选的状态过滤器（例如 ``success`` 或 ``error``）。

    返回:
        按时间戳排序的使用条目列表，每个条目表示为字典。
    """
    if not auth.permissions or "admin" not in auth.permissions:
        records = telemetry.get_recent_usage(
            user_id=auth.entity_id, operation_type=operation_type, since=since, status=status
        )
    else:
        records = telemetry.get_recent_usage(operation_type=operation_type, since=since, status=status)

    return [
        {
            "timestamp": record.timestamp,
            "operation_type": record.operation_type,
            "tokens_used": record.tokens_used,
            "user_id": record.user_id,
            "duration_ms": record.duration_ms,
            "status": record.status,
            "metadata": record.metadata,
        }
        for record in records
    ]


# 缓存端点
@app.post("/cache/create")
@telemetry.track(operation_type="create_cache", metadata_resolver=telemetry.cache_create_metadata)
async def create_cache(
    name: str,
    model: str,
    gguf_file: str,
    filters: Optional[Dict[str, Any]] = None,
    docs: Optional[List[str]] = None,
    auth: AuthContext = Depends(verify_token),
) -> Dict[str, Any]:
    """为低延迟完成创建持久缓存。

    参数:
        name: 缓存的唯一标识符。
        model: 生成完成时使用的模型名称。
        gguf_file: 要加载的 ``gguf`` 权重文件路径。
        filters: 用于选择文档的可选元数据过滤器。
        docs: 要包含在缓存中的文档ID的显式列表。
        auth: 用于权限检查的身份验证上下文。

    返回:
        描述已创建缓存的字典。
    """
    try:
        # 如果在云模式下检查缓存创建限制
        if settings.MODE == "cloud" and auth.user_id:
            # 在继续之前检查限制
            await check_and_increment_limits(auth, "cache", 1)

        filter_docs = set(await document_service.db.get_documents(auth, filters=filters))
        additional_docs = (
            {await document_service.db.get_document(document_id=doc_id, auth=auth) for doc_id in docs}
            if docs
            else set()
        )
        docs_to_add = list(filter_docs.union(additional_docs))
        if not docs_to_add:
            raise HTTPException(status_code=400, detail="No documents to add to cache")
        response = await document_service.create_cache(name, model, gguf_file, docs_to_add, filters)
        return response
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.get("/cache/{name}")
@telemetry.track(operation_type="get_cache", metadata_resolver=telemetry.cache_get_metadata)
async def get_cache(name: str, auth: AuthContext = Depends(verify_token)) -> Dict[str, Any]:
    """检索特定缓存的信息。

    参数:
        name: 要检查的缓存名称。
        auth: 用于授权请求的身份验证上下文。

    返回:
        包含布尔值 ``exists`` 字段的字典，指示缓存是否已加载。
    """
    try:
        exists = await document_service.load_cache(name)
        return {"exists": exists}
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.post("/cache/{name}/update")
@telemetry.track(operation_type="update_cache", metadata_resolver=telemetry.cache_update_metadata)
async def update_cache(name: str, auth: AuthContext = Depends(verify_token)) -> Dict[str, bool]:
    """使用新可用的文档刷新现有缓存。

    参数:
        name: 要更新的缓存标识符。
        auth: 用于权限检查的身份验证上下文。

    返回:
        指示是否添加了任何文档的字典。
    """
    try:
        if name not in document_service.active_caches:
            exists = await document_service.load_cache(name)
            if not exists:
                raise HTTPException(status_code=404, detail=f"Cache '{name}' not found")
        cache = document_service.active_caches[name]
        docs = await document_service.db.get_documents(auth, filters=cache.filters)
        docs_to_add = [doc for doc in docs if doc.id not in cache.docs]
        return cache.add_docs(docs_to_add)
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.post("/cache/{name}/add_docs")
@telemetry.track(operation_type="add_docs_to_cache", metadata_resolver=telemetry.cache_add_docs_metadata)
async def add_docs_to_cache(name: str, docs: List[str], auth: AuthContext = Depends(verify_token)) -> Dict[str, bool]:
    """手动将文档添加到现有缓存。

    参数:
        name: 目标缓存的名称。
        docs: 要插入的文档ID列表。
        auth: 用于授权的身份验证上下文。

    返回:
        指示文档是否已排队添加的字典。
    """
    try:
        cache = document_service.active_caches[name]
        docs_to_add = [
            await document_service.db.get_document(doc_id, auth) for doc_id in docs if doc_id not in cache.docs
        ]
        return cache.add_docs(docs_to_add)
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.post("/cache/{name}/query")
@telemetry.track(operation_type="query_cache", metadata_resolver=telemetry.cache_query_metadata)
async def query_cache(
    name: str,
    query: str,
    max_tokens: Optional[int] = None,
    temperature: Optional[float] = None,
    auth: AuthContext = Depends(verify_token),
) -> CompletionResponse:
    """使用预填充的缓存生成完成。

    参数:
        name: 要查询的缓存名称。
        query: 发送给模型的提示文本。
        max_tokens: 可选的生成令牌最大数量。
        temperature: 可选的模型采样温度。
        auth: 用于权限检查的身份验证上下文。

    返回:
        包含模型输出的 :class:`CompletionResponse` 对象。
    """
    try:
        # 如果在云模式下检查缓存查询限制
        if settings.MODE == "cloud" and auth.user_id:
            # 在继续之前检查限制
            await check_and_increment_limits(auth, "cache_query", 1)

        cache = document_service.active_caches[name]
        logger.info(f"缓存状态: {cache.state.n_tokens}")
        return cache.query(query)  # , max_tokens, temperature)
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))


@app.post("/graph/create", response_model=Graph)
@telemetry.track(operation_type="create_graph", metadata_resolver=telemetry.create_graph_metadata)
async def create_graph(
    request: CreateGraphRequest,
    auth: AuthContext = Depends(verify_token),
) -> Graph:
    """基于文档内容创建新的图谱。

    图谱是异步创建的。返回一个状态为 ``status = "processing"`` 的存根图谱记录，
    同时后台任务提取实体和关系。

    参数:
        request: 图谱创建参数，包括名称和可选过滤器。
        auth: 授权操作的身份验证上下文。

    返回:
        客户端可以轮询状态的占位符 :class:`Graph` 对象。
    """
    try:
        # 在继续之前验证提示覆盖
        if request.prompt_overrides:
            validate_prompt_overrides_with_http_exception(request.prompt_overrides, operation_type="graph")

        # 执行使用限制（云模式）
        if settings.MODE == "cloud" and auth.user_id:
            await check_and_increment_limits(auth, "graph", 1)

        # --------------------
        # 构建系统过滤器
        # --------------------
        system_filters: Dict[str, Any] = {}
        if request.folder_name is not None:
            normalized_folder_name = normalize_folder_name(request.folder_name)
            system_filters["folder_name"] = normalized_folder_name
        if request.end_user_id:
            system_filters["end_user_id"] = request.end_user_id

        # 开发者令牌：始终按app_id范围限制以防止跨应用泄漏
        if auth.app_id:
            system_filters["app_id"] = auth.app_id

        # --------------------
        # 创建存根图谱
        # --------------------
        import uuid
        from datetime import UTC, datetime

        from core.models.graph import Graph

        access_control = {
            "readers": [auth.entity_id],
            "writers": [auth.entity_id],
            "admins": [auth.entity_id],
        }
        if auth.user_id:
            access_control["user_id"] = [auth.user_id]

        graph_stub = Graph(
            id=str(uuid.uuid4()),
            name=request.name,
            filters=request.filters,
            owner={"type": auth.entity_type.value, "id": auth.entity_id},
            access_control=access_control,
        )

        # 在系统元数据中持久化范围信息
        if system_filters.get("folder_name"):
            graph_stub.system_metadata["folder_name"] = system_filters["folder_name"]
        if system_filters.get("end_user_id"):
            graph_stub.system_metadata["end_user_id"] = system_filters["end_user_id"]
        if auth.app_id:
            graph_stub.system_metadata["app_id"] = auth.app_id

        # 将图谱标记为处理中
        graph_stub.system_metadata["status"] = "processing"
        graph_stub.system_metadata["created_at"] = datetime.now(UTC)
        graph_stub.system_metadata["updated_at"] = datetime.now(UTC)

        # 存储存根图谱以便客户端可以轮询状态
        success = await document_service.db.store_graph(graph_stub)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to create graph stub")

        # --------------------
        # 后台处理
        # --------------------
        async def _build_graph_async():
            try:
                await document_service.update_graph(
                    name=request.name,
                    auth=auth,
                    additional_filters=None,  # 原始过滤器已在存根上
                    additional_documents=request.documents,
                    prompt_overrides=request.prompt_overrides,
                    system_filters=system_filters,
                    is_initial_build=True,  # 指示这是初始构建
                )
            except Exception as e:
                logger.error(f"图谱创建失败 {request.name}: {e}")
                # 将图谱状态更新为失败
                existing = await document_service.db.get_graph(request.name, auth, system_filters=system_filters)
                if existing:
                    existing.system_metadata["status"] = "failed"
                    existing.system_metadata["error"] = str(e)
                    existing.system_metadata["updated_at"] = datetime.now(UTC)
                    await document_service.db.update_graph(existing)

        import asyncio

        asyncio.create_task(_build_graph_async())

        # 立即返回存根图谱
        return graph_stub
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except ValueError as e:
        validate_prompt_overrides_with_http_exception(operation_type="graph", error=e)


@app.post("/folders", response_model=Folder)
@telemetry.track(operation_type="create_folder", metadata_resolver=telemetry.create_folder_metadata)
async def create_folder(
    folder_create: FolderCreate,
    auth: AuthContext = Depends(verify_token),
) -> Folder:
    """
    创建新文件夹。

    参数:
        folder_create: 包含名称和可选描述的文件夹创建请求
        auth: 身份验证上下文

    返回:
        Folder: 已创建的文件夹
    """
    try:
        # 创建具有显式ID的文件夹对象
        import uuid

        folder_id = str(uuid.uuid4())
        logger.info(f"创建文件夹，ID: {folder_id}, auth.user_id: {auth.user_id}")

        # 设置包含user_id的访问控制
        access_control = {
            "readers": [auth.entity_id],
            "writers": [auth.entity_id],
            "admins": [auth.entity_id],
        }

        if auth.user_id:
            access_control["user_id"] = [auth.user_id]
            logger.info(f"将user_id {auth.user_id} 添加到文件夹访问控制")

        folder = Folder(
            id=folder_id,
            name=folder_create.name,
            description=folder_create.description,
            owner={
                "type": auth.entity_type.value,
                "id": auth.entity_id,
            },
            access_control=access_control,
        )

        # 为开发者令牌将文件夹范围限制到应用程序ID
        if auth.app_id:
            folder.system_metadata["app_id"] = auth.app_id

        # 存储到数据库
        success = await document_service.db.create_folder(folder)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to create folder")

        return folder
    except Exception as e:
        logger.error(f"创建文件夹时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/folders", response_model=List[Folder])
@telemetry.track(operation_type="list_folders", metadata_resolver=telemetry.list_folders_metadata)
async def list_folders(
    auth: AuthContext = Depends(verify_token),
) -> List[Folder]:
    """
    列出用户有权访问的所有文件夹。

    参数:
        auth: 身份验证上下文

    返回:
        List[Folder]: 文件夹列表
    """
    try:
        folders = await document_service.db.list_folders(auth)
        return folders
    except Exception as e:
        logger.error(f"列出文件夹时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/folders/{folder_id}", response_model=Folder)
@telemetry.track(operation_type="get_folder", metadata_resolver=telemetry.get_folder_metadata)
async def get_folder(
    folder_id: str,
    auth: AuthContext = Depends(verify_token),
) -> Folder:
    """
    通过ID获取文件夹。

    参数:
        folder_id: 文件夹的ID
        auth: 身份验证上下文

    返回:
        Folder: 如果找到且可访问，返回文件夹
    """
    try:
        folder = await document_service.db.get_folder(folder_id, auth)

        if not folder:
            raise HTTPException(status_code=404, detail=f"Folder {folder_id} not found")

        return folder
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件夹时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/folders/{folder_name}")
@telemetry.track(operation_type="delete_folder", metadata_resolver=telemetry.delete_folder_metadata)
async def delete_folder(
    folder_name: str,
    auth: AuthContext = Depends(verify_token),
):
    """
    删除文件夹及其所有关联文档。

    参数:
        folder_name: 要删除的文件夹名称
        auth: 身份验证上下文（必须具有文件夹的写入权限）

    返回:
        删除状态
    """
    try:
        folder = await document_service.db.get_folder_by_name(folder_name, auth)
        folder_id = folder.id
        if not folder:
            raise HTTPException(status_code=404, detail="Folder not found")

        document_ids = folder.document_ids
        tasks = [remove_document_from_folder(folder_id, document_id, auth) for document_id in document_ids]
        results = await asyncio.gather(*tasks)
        stati = [res.get("status", False) for res in results]
        if not all(stati):
            failed = [doc for doc, stat in zip(document_ids, stati) if not stat]
            msg = "Failed to remove the following documents from folder: " + ", ".join(failed)
            logger.error(msg)
            raise HTTPException(status_code=500, detail=msg)

        # 文件夹现在为空
        delete_tasks = [document_service.db.delete_document(document_id, auth) for document_id in document_ids]
        stati = await asyncio.gather(*delete_tasks)
        if not all(stati):
            failed = [doc for doc, stat in zip(document_ids, stati) if not stat]
            msg = "Failed to delete the following documents: " + ", ".join(failed)
            logger.error(msg)
            raise HTTPException(status_code=500, detail=msg)

        db: PostgresDatabase = document_service.db
        # 现在也删除文件夹
        status = await db.delete_folder(folder_id, auth)
        if not status:
            logger.error(f"删除文件夹失败 {folder_id}")
            raise HTTPException(status_code=500, detail=f"Failed to delete folder {folder_id}")
        return {"status": "success", "message": f"Folder {folder_id} deleted successfully"}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"删除文件夹时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/folders/{folder_id}/documents/{document_id}")
@telemetry.track(operation_type="add_document_to_folder", metadata_resolver=telemetry.add_document_to_folder_metadata)
async def add_document_to_folder(
    folder_id: str,
    document_id: str,
    auth: AuthContext = Depends(verify_token),
):
    """
    将文档添加到文件夹。

    参数:
        folder_id: 文件夹的ID
        document_id: 文档的ID
        auth: 身份验证上下文

    返回:
        成功状态
    """
    try:
        success = await document_service.db.add_document_to_folder(folder_id, document_id, auth)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to add document to folder")

        return {"status": "success"}
    except Exception as e:
        logger.error(f"将文档添加到文件夹时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/folders/{folder_id}/documents/{document_id}")
@telemetry.track(
    operation_type="remove_document_from_folder", metadata_resolver=telemetry.remove_document_from_folder_metadata
)
async def remove_document_from_folder(
    folder_id: str,
    document_id: str,
    auth: AuthContext = Depends(verify_token),
):
    """
    从文件夹中移除文档。

    参数:
        folder_id: 文件夹的ID
        document_id: 文档的ID
        auth: 身份验证上下文

    返回:
        成功状态
    """
    try:
        success = await document_service.db.remove_document_from_folder(folder_id, document_id, auth)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to remove document from folder")

        return {"status": "success"}
    except Exception as e:
        logger.error(f"从文件夹移除文档时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/graph/{name}", response_model=Graph)
@telemetry.track(operation_type="get_graph", metadata_resolver=telemetry.get_graph_metadata)
async def get_graph(
    name: str,
    auth: AuthContext = Depends(verify_token),
    folder_name: Optional[Union[str, List[str]]] = None,
    end_user_id: Optional[str] = None,
) -> Graph:
    """
    通过名称获取图谱。

    此端点通过名称检索图谱（如果用户有权访问）。

    参数:
        name: 要检索的图谱名称
        auth: 身份验证上下文
        folder_name: 可选的文件夹范围限制
        end_user_id: 可选的终端用户ID范围限制

    返回:
        Graph: 请求的图谱对象
    """
    try:
        # 为文件夹和用户范围创建系统过滤器
        system_filters = {}
        if folder_name is not None:
            normalized_folder_name = normalize_folder_name(folder_name)
            system_filters["folder_name"] = normalized_folder_name
        if end_user_id:
            system_filters["end_user_id"] = end_user_id

        # 开发者令牌：始终按app_id范围限制以防止跨应用泄漏
        if auth.app_id:
            system_filters["app_id"] = auth.app_id

        graph = await document_service.db.get_graph(name, auth, system_filters)
        if not graph:
            raise HTTPException(status_code=404, detail=f"Graph '{name}' not found")
        return graph
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/graphs", response_model=List[Graph])
@telemetry.track(operation_type="list_graphs", metadata_resolver=telemetry.list_graphs_metadata)
async def list_graphs(
    auth: AuthContext = Depends(verify_token),
    folder_name: Optional[Union[str, List[str]]] = None,
    end_user_id: Optional[str] = None,
) -> List[Graph]:
    """
    列出用户有权访问的所有图谱。

    此端点检索用户有权访问的所有图谱。

    参数:
        auth: 身份验证上下文
        folder_name: 可选的文件夹范围限制
        end_user_id: 可选的终端用户ID范围限制

    返回:
        List[Graph]: 图谱对象列表
    """
    try:
        # 为文件夹和用户范围创建系统过滤器
        system_filters = {}
        if folder_name is not None:
            normalized_folder_name = normalize_folder_name(folder_name)
            system_filters["folder_name"] = normalized_folder_name
        if end_user_id:
            system_filters["end_user_id"] = end_user_id

        # 开发者令牌：始终按app_id范围限制以防止跨应用泄漏
        if auth.app_id:
            system_filters["app_id"] = auth.app_id

        return await document_service.db.list_graphs(auth, system_filters)
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/graph/{name}/visualization", response_model=Dict[str, Any])
@telemetry.track(operation_type="get_graph_visualization", metadata_resolver=telemetry.get_graph_metadata)
async def get_graph_visualization(
    name: str,
    auth: AuthContext = Depends(verify_token),
    folder_name: Optional[Union[str, List[str]]] = None,
    end_user_id: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取图谱可视化数据。

    此端点检索图谱可视化所需的节点和链接数据。
    它适用于本地和基于API的图谱服务。

    参数:
        name: 要可视化的图谱名称
        auth: 身份验证上下文
        folder_name: 可选的文件夹范围限制
        end_user_id: 可选的终端用户ID范围限制

    返回:
        Dict: 包含节点和链接数组的可视化数据
    """
    try:
        return await document_service.get_graph_visualization_data(
            name=name,
            auth=auth,
            folder_name=folder_name,
            end_user_id=end_user_id,
        )
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        logger.error(f"获取图谱可视化数据时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/graph/{name}/update", response_model=Graph)
@telemetry.track(operation_type="update_graph", metadata_resolver=telemetry.update_graph_metadata)
async def update_graph(
    name: str,
    request: UpdateGraphRequest,
    auth: AuthContext = Depends(verify_token),
) -> Graph:
    """
    使用新文档更新现有图谱。

    此端点基于原始图谱过滤器和/或新过滤器/文档ID处理额外文档，
    提取实体和关系，并使用新信息更新图谱。

    参数:
        name: 要更新的图谱名称
        request: UpdateGraphRequest包含:
            - additional_filters: 可选的额外元数据过滤器，用于确定要包含的新文档
            - additional_documents: 可选的额外文档ID列表
            - prompt_overrides: 实体提取和解析提示的可选自定义
            - folder_name: 可选的文件夹范围限制
            - end_user_id: 可选的终端用户ID范围限制
        auth: 身份验证上下文

    返回:
        Graph: 更新后的图谱对象
    """
    try:
        # 在继续之前验证提示覆盖
        if request.prompt_overrides:
            validate_prompt_overrides_with_http_exception(request.prompt_overrides, operation_type="graph")

        # 为文件夹和用户范围创建系统过滤器
        system_filters = {}
        if request.folder_name:
            system_filters["folder_name"] = request.folder_name
        if request.end_user_id:
            system_filters["end_user_id"] = request.end_user_id

        # 开发者令牌：始终按app_id范围限制以防止跨应用泄漏
        if auth.app_id:
            system_filters["app_id"] = auth.app_id

        return await document_service.update_graph(
            name=name,
            auth=auth,
            additional_filters=request.additional_filters,
            additional_documents=request.additional_documents,
            prompt_overrides=request.prompt_overrides,
            system_filters=system_filters,
        )
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except ValueError as e:
        validate_prompt_overrides_with_http_exception(operation_type="graph", error=e)
    except Exception as e:
        logger.error(f"更新图谱时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/graph/{name}")
@telemetry.track(operation_type="delete_graph", metadata_resolver=telemetry.get_graph_metadata)
async def delete_graph(
    name: str,
    auth: AuthContext = Depends(verify_token),
    folder_name: Optional[Union[str, List[str]]] = None,
    end_user_id: Optional[str] = None,
):
    """
    删除指定的知识图谱。

    此端点删除指定名称的知识图谱及其所有关联数据，包括：
    - 图谱元数据
    - 实体和关系数据
    - 图谱配置信息

    参数:
        name: 要删除的图谱名称
        auth: 身份验证上下文（必须具有图谱的写入权限）
        folder_name: 可选的文件夹范围限制
        end_user_id: 可选的终端用户ID范围限制

    返回:
        删除状态
    """
    try:
        # 为文件夹和用户范围创建系统过滤器
        system_filters = {}
        if folder_name is not None:
            normalized_folder_name = normalize_folder_name(folder_name)
            system_filters["folder_name"] = normalized_folder_name
        if end_user_id:
            system_filters["end_user_id"] = end_user_id

        # 开发者令牌：始终按app_id范围限制以防止跨应用泄漏
        if auth.app_id:
            system_filters["app_id"] = auth.app_id

        success = await document_service.db.delete_graph(name, auth, system_filters)
        if not success:
            raise HTTPException(status_code=404, detail=f"Graph '{name}' not found or delete failed")

        return {"status": "success", "message": f"Graph '{name}' deleted successfully"}
    except PermissionError as e:
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        logger.error(f"删除图谱时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/graph/workflow/{workflow_id}/status", response_model=Dict[str, Any])
@telemetry.track(operation_type="check_workflow_status", metadata_resolver=telemetry.workflow_status_metadata)
async def check_workflow_status(
    workflow_id: str,
    run_id: Optional[str] = None,
    auth: AuthContext = Depends(verify_token),
) -> Dict[str, Any]:
    """检查图谱构建/更新工作流的状态。

    此端点轮询外部图谱API以检查异步操作的状态。

    参数:
        workflow_id: 从构建/更新操作返回的工作流ID
        run_id: 特定工作流运行的可选运行ID
        auth: 身份验证上下文

    返回:
        包含状态（'running'、'completed'或'failed'）和可选结果的字典
    """
    try:
        # 获取图谱服务（本地或基于API的）
        graph_service = document_service.graph_service

        # 检查是否为MorphikGraphService
        from core.services.morphik_graph_service import MorphikGraphService

        if isinstance(graph_service, MorphikGraphService):
            # 使用新的check_workflow_status方法
            result = await graph_service.check_workflow_status(workflow_id=workflow_id, run_id=run_id, auth=auth)

            # 如果工作流已完成，更新相应的图谱状态
            if result.get("status") == "completed":
                # 从workflow_id中提取graph_id（格式："build-update-{graph_name}-..."）
                # 这是一个简单的启发式方法，根据实际workflow_id格式调整
                parts = workflow_id.split("-")
                if len(parts) >= 3:
                    graph_name = parts[2]
                    try:
                        # 查找并更新图谱
                        graphs = await document_service.db.list_graphs(auth)
                        for graph in graphs:
                            if graph.name == graph_name or workflow_id in graph.system_metadata.get("workflow_id", ""):
                                graph.system_metadata["status"] = "completed"
                                # 清除工作流跟踪数据
                                graph.system_metadata.pop("workflow_id", None)
                                graph.system_metadata.pop("run_id", None)
                                await document_service.db.update_graph(graph)
                                break
                    except Exception as e:
                        logger.warning(f"工作流完成后更新图谱状态失败: {e}")

            return result
        else:
            # 对于本地图谱服务，工作流同步完成
            return {"status": "completed", "result": {"message": "Local graph operations complete synchronously"}}

    except Exception as e:
        logger.error(f"检查工作流状态时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/local/generate_uri", include_in_schema=True)
async def generate_local_uri(
    name: str = Form("admin"),
    expiry_days: int = Form(30),
) -> Dict[str, str]:
    """为本地运行Morphik生成开发URI。

    参数:
        name: 要嵌入到令牌载荷中的开发者名称。
        expiry_days: 生成的令牌应保持有效的天数。

    返回:
        包含可用于连接到本地实例的 ``uri`` 的字典。
    """
    try:
        # 清理名称
        name = name.replace(" ", "_").lower()

        # 创建载荷
        payload = {
            "type": "developer",
            "entity_id": name,
            "permissions": ["read", "write", "admin"],
            "exp": datetime.now(UTC) + timedelta(days=expiry_days),
        }

        # 生成令牌
        token = jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)

        # 读取主机/端口配置
        with open("morphik.toml", "rb") as f:
            config = tomli.load(f)
        base_url = f"{config['api']['host']}:{config['api']['port']}".replace("localhost", "127.0.0.1")

        # 生成URI
        uri = f"morphik://{name}:{token}@{base_url}"
        return {"uri": uri}
    except Exception as e:
        logger.error(f"生成本地URI时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/cloud/generate_uri", include_in_schema=True)
async def generate_cloud_uri(
    request: GenerateUriRequest,
    authorization: str = Header(None),
) -> Dict[str, str]:
    """为云托管的Morphik应用程序生成认证URI。

    参数:
        request: URI生成参数，包括 ``app_id`` 和 ``name``。
        authorization: 请求URI的用户的Bearer令牌。

    返回:
        包含生成的 ``uri`` 和关联的 ``app_id`` 的字典。
    """
    try:
        app_id = request.app_id
        name = request.name
        user_id = request.user_id
        expiry_days = request.expiry_days

        logger.debug(f"为app_id={app_id}, name={name}, user_id={user_id}生成云URI")

        # 在继续之前验证授权头
        if not authorization:
            logger.warning("缺少授权头")
            raise HTTPException(
                status_code=401,
                detail="Missing authorization header",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 验证令牌是否有效
        if not authorization.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid authorization header")

        token = authorization[7:]  # 移除"Bearer "

        try:
            # 解码令牌以确保其有效
            payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])

            # 只允许用户为自己创建应用（或管理员）
            token_user_id = payload.get("user_id")
            logger.debug(f"Token user ID: {token_user_id}")
            logger.debug(f"User ID: {user_id}")
            if not (token_user_id == user_id or "admin" in payload.get("permissions", [])):
                raise HTTPException(
                    status_code=403,
                    detail="You can only create apps for your own account unless you have admin permissions",
                )
        except jwt.InvalidTokenError as e:
            raise HTTPException(status_code=401, detail=str(e))

        # 在此处导入UserService以避免循环导入
        from core.services.user_service import UserService

        user_service = UserService()

        # 如果需要，初始化用户服务
        await user_service.initialize()

        # 清理名称
        name = name.replace(" ", "_").lower()

        # 检查用户是否在应用限制内并生成URI
        uri = await user_service.generate_cloud_uri(user_id, app_id, name, expiry_days)

        if not uri:
            logger.debug("此账户层级的应用限制已达到，user_id: %s", user_id)
            raise HTTPException(status_code=403, detail="Application limit reached for this account tier")

        return {"uri": uri, "app_id": app_id}
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"生成云URI时出错: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/folders/{folder_id}/set_rule")
@telemetry.track(operation_type="set_folder_rule", metadata_resolver=telemetry.set_folder_rule_metadata)
async def set_folder_rule(
    folder_id: str,
    request: SetFolderRuleRequest,
    auth: AuthContext = Depends(verify_token),
    apply_to_existing: bool = True,
):
    """
    为文件夹设置提取规则。

    参数:
        folder_id: 要设置规则的文件夹ID
        request: 包含元数据提取规则的SetFolderRuleRequest
        auth: 身份验证上下文
        apply_to_existing: 是否将规则应用于文件夹中的现有文档

    返回:
        包含处理结果的成功状态
    """
    # 在此处导入text以确保在此函数范围内可用
    from sqlalchemy import text

    try:
        # 记录有关规则的详细信息
        logger.debug(f"为文件夹 {folder_id} 设置规则")
        logger.debug(f"规则数量: {len(request.rules)}")

        for i, rule in enumerate(request.rules):
            logger.debug(f"\nRule {i + 1}:")
            logger.debug(f"Type: {rule.type}")
            logger.debug("Schema:")
            for field_name, field_config in rule.schema.items():
                logger.debug(f"  Field: {field_name}")
                logger.debug(f"    Type: {field_config.get('type', 'unknown')}")
                logger.debug(f"    Description: {field_config.get('description', 'No description')}")
                if "schema" in field_config:
                    logger.debug("    Has JSON schema: Yes")
                    logger.debug(f"    Schema: {field_config['schema']}")

        # Get the folder
        folder = await document_service.db.get_folder(folder_id, auth)
        if not folder:
            raise HTTPException(status_code=404, detail=f"Folder {folder_id} not found")

        # Check if user has write access to the folder
        if not document_service.db._check_folder_access(folder, auth, "write"):
            raise HTTPException(status_code=403, detail="You don't have write access to this folder")

        # Update folder with rules
        # Convert rules to dicts for JSON serialization
        rules_dicts = [rule.model_dump() for rule in request.rules]

        # Update the folder in the database
        async with document_service.db.async_session() as session:
            # Execute update query
            await session.execute(
                text(
                    """
                    UPDATE folders
                    SET rules = :rules
                    WHERE id = :folder_id
                    """
                ),
                {"folder_id": folder_id, "rules": json.dumps(rules_dicts)},
            )
            await session.commit()

        logger.info(f"Successfully updated folder {folder_id} with {len(request.rules)} rules")

        # Get updated folder
        updated_folder = await document_service.db.get_folder(folder_id, auth)

        # If apply_to_existing is True, apply these rules to all existing documents in the folder
        processing_results = {"processed": 0, "errors": []}

        if apply_to_existing and folder.document_ids:
            logger.info(f"Applying rules to {len(folder.document_ids)} existing documents in folder")

            # Import rules processor

            # Get all documents in the folder
            documents = await document_service.db.get_documents_by_id(folder.document_ids, auth)

            # Process each document
            for doc in documents:
                try:
                    # Get document content
                    logger.info(f"Processing document {doc.external_id}")

                    # For each document, apply the rules from the folder
                    doc_content = None

                    # Get content from system_metadata if available
                    if doc.system_metadata and "content" in doc.system_metadata:
                        doc_content = doc.system_metadata["content"]
                        logger.info(f"Retrieved content from system_metadata for document {doc.external_id}")

                    # If we still have no content, log error and continue
                    if not doc_content:
                        error_msg = f"No content found in system_metadata for document {doc.external_id}"
                        logger.error(error_msg)
                        processing_results["errors"].append({"document_id": doc.external_id, "error": error_msg})
                        continue

                    # Process document with rules
                    try:
                        # Convert request rules to actual rule models and apply them
                        from core.models.rules import MetadataExtractionRule

                        for rule_request in request.rules:
                            if rule_request.type == "metadata_extraction":
                                # Create the actual rule model
                                rule = MetadataExtractionRule(type=rule_request.type, schema=rule_request.schema)

                                # Apply the rule with retries
                                max_retries = 3
                                base_delay = 1  # seconds
                                extracted_metadata = None
                                last_error = None

                                for retry_count in range(max_retries):
                                    try:
                                        if retry_count > 0:
                                            # Exponential backoff
                                            delay = base_delay * (2 ** (retry_count - 1))
                                            logger.info(f"Retry {retry_count}/{max_retries} after {delay}s delay")
                                            await asyncio.sleep(delay)

                                        extracted_metadata, _ = await rule.apply(doc_content, {})
                                        logger.info(
                                            f"Successfully extracted metadata on attempt {retry_count + 1}: "
                                            f"{extracted_metadata}"
                                        )
                                        break  # Success, exit retry loop

                                    except Exception as rule_apply_error:
                                        last_error = rule_apply_error
                                        logger.warning(
                                            f"Metadata extraction attempt {retry_count + 1} failed: "
                                            f"{rule_apply_error}"
                                        )
                                        if retry_count == max_retries - 1:  # Last attempt
                                            logger.error(f"All {max_retries} metadata extraction attempts failed")
                                            processing_results["errors"].append(
                                                {
                                                    "document_id": doc.external_id,
                                                    "error": f"Failed to extract metadata after {max_retries} "
                                                    f"attempts: {str(last_error)}",
                                                }
                                            )
                                            continue  # Skip to next document

                                # Update document metadata if extraction succeeded
                                if extracted_metadata:
                                    # Merge new metadata with existing
                                    doc.metadata.update(extracted_metadata)

                                    # Create an updates dict that only updates metadata
                                    # We need to create system_metadata with all preserved fields
                                    # Note: In the database, metadata is stored as 'doc_metadata', not 'metadata'
                                    updates = {
                                        "doc_metadata": doc.metadata,  # Use doc_metadata for the database
                                        "system_metadata": {},  # Will be merged with existing in update_document
                                    }

                                    # Explicitly preserve the content field in system_metadata
                                    if "content" in doc.system_metadata:
                                        updates["system_metadata"]["content"] = doc.system_metadata["content"]

                                    # Log the updates we're making
                                    logger.info(
                                        f"Updating document {doc.external_id} with metadata: {extracted_metadata}"
                                    )
                                    logger.info(f"Full metadata being updated: {doc.metadata}")
                                    logger.info(f"Update object being sent to database: {updates}")
                                    logger.info(
                                        f"Preserving content in system_metadata: {'content' in doc.system_metadata}"
                                    )

                                    # Update document in database
                                    app_db = document_service.db
                                    success = await app_db.update_document(doc.external_id, updates, auth)

                                    if success:
                                        logger.info(f"Updated metadata for document {doc.external_id}")
                                        processing_results["processed"] += 1
                                    else:
                                        logger.error(f"Failed to update metadata for document {doc.external_id}")
                                        processing_results["errors"].append(
                                            {
                                                "document_id": doc.external_id,
                                                "error": "Failed to update document metadata",
                                            }
                                        )
                    except Exception as rule_error:
                        logger.error(f"Error processing rules for document {doc.external_id}: {rule_error}")
                        processing_results["errors"].append(
                            {
                                "document_id": doc.external_id,
                                "error": f"Error processing rules: {str(rule_error)}",
                            }
                        )

                except Exception as doc_error:
                    logger.error(f"Error processing document {doc.external_id}: {doc_error}")
                    processing_results["errors"].append({"document_id": doc.external_id, "error": str(doc_error)})

            return {
                "status": "success",
                "message": "Rules set successfully",
                "folder_id": folder_id,
                "rules": updated_folder.rules,
                "processing_results": processing_results,
            }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error setting folder rules: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ---------------------------------------------------------------------------
# 云端 – 删除应用程序（仅限控制平面）
# ---------------------------------------------------------------------------


@app.delete("/cloud/apps")
async def delete_cloud_app(
    app_name: str = Query(..., description="Name of the application to delete"),
    auth: AuthContext = Depends(verify_token),
) -> Dict[str, Any]:
    """删除与给定云应用程序关联的所有资源。

    参数:
        app_name: 要删除其数据的应用程序名称。
        auth: 请求用户的身份验证上下文。

    返回:
        描述删除了多少文档和文件夹的摘要。
    """

    user_id = auth.user_id or auth.entity_id
    logger.info(f"为用户 {user_id} 删除应用 {app_name}")

    from sqlalchemy import delete as sa_delete
    from sqlalchemy import select

    from core.models.apps import AppModel
    from core.services.user_service import UserService

    # 1) 从应用表中解析app_id ----------------------------------
    async with document_service.db.async_session() as session:
        stmt = select(AppModel).where(AppModel.user_id == user_id, AppModel.name == app_name)
        res = await session.execute(stmt)
        app_row = res.scalar_one_or_none()

    if app_row is None:
        raise HTTPException(status_code=404, detail="Application not found")

    app_id = app_row.app_id

    # ------------------------------------------------------------------
    # 创建一个范围限定为*此*应用程序的AuthContext，以便数据库层中的
    # 底层访问控制过滤器允许我们查看和删除属于该应用的资源 – 即使用于
    # 调用此端点的JWT范围限定为*不同*的应用。
    # ------------------------------------------------------------------

    if auth.entity_type == EntityType.DEVELOPER:
        app_auth = AuthContext(
            entity_type=auth.entity_type,
            entity_id=auth.entity_id,
            app_id=app_id,
            permissions=auth.permissions or {"read", "write", "admin"},
            user_id=auth.user_id,
        )
    else:
        app_auth = auth

    # 2) 删除此应用的所有文档 ------------------------------
    # ------------------------------------------------------------------
    # 使用应用范围的身份验证获取*此*应用的所有文档。
    # ------------------------------------------------------------------
    doc_ids = await document_service.db.find_authorized_and_filtered_documents(app_auth)

    deleted = 0
    for doc_id in doc_ids:
        try:
            await document_service.delete_document(doc_id, app_auth)
            deleted += 1
        except Exception as exc:
            logger.warning("删除应用 %s 的文档 %s 失败: %s", app_id, doc_id, exc)

    # 3) 删除与此应用关联的文件夹 -----------------------
    # ------------------------------------------------------------------
    # 使用相同的应用范围身份验证获取*此*应用的所有文件夹。
    # ------------------------------------------------------------------
    folder_ids_deleted = 0
    folders = await document_service.db.list_folders(app_auth)

    for folder in folders:
        try:
            await document_service.db.delete_folder(folder.id, app_auth)
            folder_ids_deleted += 1
        except Exception as f_exc:  # noqa: BLE001
            logger.warning("删除应用 %s 的文件夹 %s 失败: %s", app_id, folder.id, f_exc)

    # 4) 移除应用表条目 ---------------------------------------
    async with document_service.db.async_session() as session:
        await session.execute(sa_delete(AppModel).where(AppModel.app_id == app_id))
        await session.commit()

    # 5) 更新用户限制 --------------------------------------------
    user_service = UserService()
    await user_service.initialize()
    await user_service.unregister_app(user_id, app_id)

    return {
        "app_name": app_name,
        "status": "deleted",
        "documents_deleted": deleted,
        "folders_deleted": folder_ids_deleted,
    }


@app.get("/chats", response_model=List[Dict[str, Any]])
async def list_chat_conversations(
    auth: AuthContext = Depends(verify_token),
    limit: int = Query(100, ge=1, le=500),
):
    """列出当前用户可用的聊天会话。

    参数:
        auth: 包含用户和应用标识符的身份验证上下文。
        limit: 返回的最大会话数量（1-500）

    返回:
        描述每个会话的字典列表，按最近活动排序。
    """
    try:
        convos = await document_service.db.list_chat_conversations(
            user_id=auth.user_id,
            app_id=auth.app_id,
            limit=limit,
        )
        return convos
    except Exception as exc:  # noqa: BLE001
        logger.error("列出聊天会话时出错: %s", exc)
        raise HTTPException(status_code=500, detail="Failed to list chat conversations")
