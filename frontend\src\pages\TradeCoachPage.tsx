import { useNavigate } from 'react-router-dom'

export default function TradeCoachPage() {
  const navigate = useNavigate()

  const handleCardClick = (path: string) => {
    navigate(path)
  }

  return (
    <div className="flex-1 p-8 pt-6 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* 头部区域 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          {/* 头像 */}
          <div className="w-20 h-20 bg-gray-300 rounded-lg flex items-center justify-center">
            <div className="w-full h-full bg-blue-500 rounded-lg flex items-center justify-center text-white text-2xl font-bold">
              AI
            </div>
          </div>

          {/* 问候语和介绍 */}
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
              你好，小兔子
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              外贸教官：全方位助你快速成长
            </p>
          </div>
        </div>
      </div>

      {/* 功能卡片网格 */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* 企业知识库 */}
        <div
          className="group relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 cursor-pointer"
           onClick={() => handleCardClick('/trade-coach/company-knowledge')}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              <span className="text-2xl">🏢</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              企业知识库
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
              公司知识、产品知识、产业知识等上传的知识，完善公司知识库，以便自己和同事学习
            </p>
            <div className="flex items-center text-blue-600 dark:text-blue-400 text-sm font-medium">
              进入知识库
              <span className="ml-1 group-hover:translate-x-1 transition-transform duration-300">→</span>
            </div>
          </div>
        </div>

        {/* 外贸知识库 */}
        <div
          className="group relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 cursor-pointer"
          onClick={() => handleCardClick('/trade-coach/trade-knowledge')}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-teal-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              <span className="text-2xl">🌍</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              外贸知识库
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
              外贸知识：外贸流程、外贸术语、国际商业文化、谈判策略、报关流程等各种知识：各种知识、销售话术、谈判技巧
            </p>
            <div className="flex items-center text-green-600 dark:text-green-400 text-sm font-medium">
              智能对话
              <span className="ml-1 group-hover:translate-x-1 transition-transform duration-300">→</span>
            </div>
          </div>
        </div>

        {/* 全球市场分析及学习 */}
        <div
          className="group relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 cursor-pointer"
          onClick={() => handleCardClick('/trade-coach/market-analysis')}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              <span className="text-2xl">🌐</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              全球市场分析
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
              分析行业及产品竞争趋势，全球采购情况，买家分析情况等
            </p>
            <div className="flex items-center text-purple-600 dark:text-purple-400 text-sm font-medium">
              市场分析
              <span className="ml-1 group-hover:translate-x-1 transition-transform duration-300">→</span>
            </div>
          </div>
        </div>

        {/* 特定群体需求分析及学习 */}
        <div
          className="group relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 cursor-pointer"
          onClick={() => handleCardClick('/trade-coach/group-analysis')}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-teal-500/10 to-cyan-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 bg-teal-100 dark:bg-teal-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              <span className="text-2xl">👥</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              特定群体需求分析
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
              不同国家、地理环境不同、文化习俗不同、消费习惯不同，对产品的需求不同，深入了解需求
            </p>
            <div className="flex items-center text-teal-600 dark:text-teal-400 text-sm font-medium">
              需求分析
              <span className="ml-1 group-hover:translate-x-1 transition-transform duration-300">→</span>
            </div>
          </div>
        </div>

        {/* 销冠实战训练 */}
        <div
          className="group relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 cursor-pointer"
          onClick={() => handleCardClick('/trade-coach/sales-training')}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              <span className="text-2xl">🏆</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              销冠实战训练
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
              以真实案例演练，模拟场景，实时对话，实战练习，提升销售能力，快速成长
            </p>
            <div className="flex items-center text-indigo-600 dark:text-indigo-400 text-sm font-medium">
              开始训练
              <span className="ml-1 group-hover:translate-x-1 transition-transform duration-300">→</span>
            </div>
          </div>
        </div>

         {/* 个性化学习计划制定 */}
        <div
          className="group relative overflow-hidden rounded-xl bg-white dark:bg-gray-800 p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 cursor-pointer"
          onClick={() => handleCardClick('/trade-coach/learning-plan')}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              <span className="text-2xl">✏️</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              个性化学习计划制定
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
              按企业的特点及个人的具体情况，做个性化的规划发展规划，年度学习计划，单项学习方案
            </p>
            <div className="flex items-center text-orange-600 dark:text-orange-400 text-sm font-medium">
              制定计划
              <span className="ml-1 group-hover:translate-x-1 transition-transform duration-300">→</span>
            </div>
          </div>
        </div>

       
      </div>
    </div>
  )
}
