import { useState, useEffect } from 'react'
import {
  Building2,
  Upload,
  Plus,
  Package,
  TrendingUp,
  FileText,
  Download,
  Trash2,
  Loader2,
  AlertCircle,
  CheckCircle,
  Clock,
  File,
  FileImage,
  FileVideo,
  FileAudio,
  Archive,
  Code,
  Sheet,
  Presentation
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { KnowledgeCategoryCard } from '@/components/enterprise-knowledge/KnowledgeCategoryCard'
// import { DocumentListItem } from '@/components/enterprise-knowledge/DocumentListItem'
import { SearchAndFilter } from '@/components/enterprise-knowledge/SearchAndFilter'
import { EnterpriseUploadDialog } from '@/components/enterprise-knowledge/EnterpriseUploadDialog'
import { DeleteDocumentDialog } from '@/components/enterprise/DeleteDocumentDialog'
import { useDocuments, useDocumentContent } from '@/hooks/useDocuments'
import { useDeleteDocument } from '@/hooks/useMorphik'
import { filterEnterpriseDocuments, countDocumentsByCategory, type EnterpriseCategory } from '@/utils/enterpriseKnowledge'
import { toast } from 'sonner'

// 知识库分类数据
const knowledgeCategories = [
  {
    id: 'enterprise',
    title: '企业知识',
    icon: Building2,
    color: 'bg-blue-500',
    description: '公司内部知识和资料',
    count: 7
  },
  {
    id: 'product',
    title: '产品知识',
    icon: Package,
    color: 'bg-green-500',
    description: '产品相关信息和文档',
    count: 7
  },
  {
    id: 'industry',
    title: '产业知识',
    icon: TrendingUp,
    color: 'bg-purple-500',
    description: '行业趋势和市场信息',
    count: 7
  }
]





export default function EnterpriseKnowledgeBasePage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [activeCategory, setActiveCategory] = useState<EnterpriseCategory>('enterprise')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [sortBy, setSortBy] = useState('date-desc')
  const [filters, setFilters] = useState({
    fileTypes: [],
    dateRange: 'all',
    author: ''
  })
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [uploadDialogDefaultTab, setUploadDialogDefaultTab] = useState<'file' | 'text'>('file')
  const [filteredDocuments, setFilteredDocuments] = useState<any[]>([])
  const [selectedDocument, setSelectedDocument] = useState<any>(null)
  const [showPreview, setShowPreview] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [documentToDelete, setDocumentToDelete] = useState<any>(null)
  const [showAllDocuments, setShowAllDocuments] = useState(false)

  // 文档显示限制
  const DOCUMENTS_LIMIT = 6

  // 获取真实文档数据
  const { data: documentsData, refetch: refetchDocuments } = useDocuments()

  // 文档操作hooks
  const deleteDocument = useDeleteDocument()

  // 搜索和筛选功能
  useEffect(() => {
    if (!documentsData?.items) {
      setFilteredDocuments([])
      return
    }

    // 重置显示状态
    setShowAllDocuments(false)

    // 首先过滤出企业知识库文档
    let documents = filterEnterpriseDocuments(documentsData.items, activeCategory)

    // 搜索过滤
    if (searchQuery.trim()) {
      documents = documents.filter(doc =>
        doc.filename?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.metadata?.title?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // 文件类型过滤
    if (filters.fileTypes.length > 0) {
      documents = documents.filter(doc => {
        // 1. 从文件名获取扩展名
        let extension = ''
        if (doc.filename) {
          const parts = doc.filename.split('.')
          if (parts.length > 1) {
            extension = parts.pop()?.toLowerCase() || ''
          }
        }

        // 2. 从content_type获取类型
        let mimeType = doc.content_type || ''

        // 3. 从metadata获取类型
        let metadataType = doc.metadata?.fileType || ''

        // 检查是否匹配任何选定的文件类型
        return filters.fileTypes.some(type => {
          // 检查扩展名匹配
          if (extension === type.toLowerCase()) {
            return true
          }

          // 检查MIME类型匹配
          if (mimeType) {
            if (type === 'txt' && mimeType.includes('text/plain')) return true
            if (type === 'pdf' && mimeType.includes('application/pdf')) return true
            if (type === 'doc' && (
              mimeType.includes('application/msword') ||
              mimeType.includes('application/doc')
            )) return true
            if (type === 'docx' && (
              mimeType.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ||
              mimeType.includes('application/docx')
            )) return true
          }

          // 检查metadata类型匹配
          if (metadataType && metadataType.toLowerCase() === type.toLowerCase()) {
            return true
          }

          return false
        })
      })
    }

    // 日期范围过滤
    if (filters.dateRange !== 'all') {
      const now = new Date()
      let startDate = new Date(0) // 1970年1月1日

      // 计算开始日期
      switch (filters.dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'week':
          // 获取本周一
          const day = now.getDay() || 7 // 将周日的0转换为7
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - day + 1)
          break
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          break
        case 'quarter':
          const quarter = Math.floor(now.getMonth() / 3)
          startDate = new Date(now.getFullYear(), quarter * 3, 1)
          break
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1)
          break
      }

      documents = documents.filter(doc => {
        const docDate = new Date(doc.system_metadata?.created_at || doc.metadata?.created_at || 0)
        return docDate >= startDate
      })
    }

    // 作者过滤
    // if (filters.author.trim()) {
    //   const authorSearch = filters.author.trim().toLowerCase()
    //   documents = documents.filter(doc => {
    //     // 检查多个可能包含作者信息的字段
    //     const createdBy = doc.metadata?.created_by || ''
    //     const owner = doc.owner?.id || ''
    //     const authorField = doc.metadata?.author || ''

    //     return createdBy.toLowerCase().includes(authorSearch) ||
    //            owner.toLowerCase().includes(authorSearch) ||
    //            authorField.toLowerCase().includes(authorSearch)
    //   })
    // }

    // 排序
    documents = [...documents].sort((a, b) => {
      switch (sortBy) {
        case 'name-asc':
          return (a.filename || '').localeCompare(b.filename || '')
        case 'name-desc':
          return (b.filename || '').localeCompare(a.filename || '')
        case 'date-desc':
          return new Date(b.system_metadata?.created_at || 0).getTime() - new Date(a.system_metadata?.created_at || 0).getTime()
        case 'date-asc':
          return new Date(a.system_metadata?.created_at || 0).getTime() - new Date(b.system_metadata?.created_at || 0).getTime()
        case 'size-desc':
          return (b.metadata?.fileSize || 0) - (a.metadata?.fileSize || 0)
        case 'size-asc':
          return (a.metadata?.fileSize || 0) - (b.metadata?.fileSize || 0)
        default:
          return 0
      }
    })

    setFilteredDocuments(documents)
  }, [documentsData, searchQuery, activeCategory, filters, sortBy])

  // 处理分类切换
  const handleCategoryChange = (categoryId: string) => {
    setActiveCategory(categoryId as EnterpriseCategory)
    setSearchQuery('') // 清空搜索
    setFilters({ fileTypes: [], dateRange: 'all', author: '' }) // 清空筛选
  }

  // 计算各分类的文档数量
  const categoryCounts = documentsData?.items ? countDocumentsByCategory(documentsData.items) : {
    enterprise: 0,
    product: 0,
    industry: 0
  }

  // 更新知识库分类数据
  const updatedKnowledgeCategories = knowledgeCategories.map(category => ({
    ...category,
    count: categoryCounts[category.id as EnterpriseCategory]
  }))

  // 上传完成处理
  const handleUploadComplete = (documentIds: string[]) => {
    console.log('上传完成:', documentIds)
    // 刷新文档列表
    refetchDocuments()
  }

  // 状态轮询 - 检查处理中的文档
  useEffect(() => {
    if (!documentsData?.items) return

    const processingDocuments = documentsData.items.filter(
      doc => doc.system_metadata?.status === 'processing'
    )

    if (processingDocuments.length === 0) return

    const pollInterval = setInterval(async () => {
      try {
        // 检查每个处理中文档的状态
        const statusChecks = processingDocuments.map(async (doc) => {
          try {
            const response = await fetch(`http://localhost:8000/documents/${doc.external_id}`)
            if (!response.ok) throw new Error('Failed to fetch document status')

            const updatedDoc = await response.json()
            const status = updatedDoc.system_metadata?.status || 'completed'

            return {
              documentId: doc.external_id,
              status: status
            }
          } catch (error) {
            console.error(`Failed to check status for document ${doc.external_id}:`, error)
            return null
          }
        })

        const results = await Promise.all(statusChecks)

        // 如果有状态变化，刷新文档列表
        const hasStatusChange = results.some(result =>
          result && result.status !== 'processing'
        )

        if (hasStatusChange) {
          refetchDocuments()
        }
      } catch (error) {
        console.error('Error polling document status:', error)
      }
    }, 3000) // 每3秒检查一次

    return () => clearInterval(pollInterval)
  }, [documentsData?.items, refetchDocuments])

  // 格式化文件大小
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '-'
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  // 格式化日期
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 根据文件类型获取对应图标
  const getFileIcon = (doc: any) => {
    const filename = doc.filename || ''
    const contentType = doc.content_type || ''
    const extension = filename.split('.').pop()?.toLowerCase() || ''

    // PDF 文件
    if (contentType.includes('pdf') || extension === 'pdf') {
      return <FileText className="h-5 w-5 text-red-500" />
    }

    // Word 文档
    if (contentType.includes('word') || contentType.includes('document') ||
        ['doc', 'docx'].includes(extension)) {
      return <FileText className="h-5 w-5 text-blue-500" />
    }

    // Excel 表格
    if (contentType.includes('sheet') || contentType.includes('excel') ||
        ['xls', 'xlsx', 'csv'].includes(extension)) {
      return <Sheet className="h-5 w-5 text-green-500" />
    }

    // PowerPoint 演示文稿
    if (contentType.includes('presentation') || contentType.includes('powerpoint') ||
        ['ppt', 'pptx'].includes(extension)) {
      return <Presentation className="h-5 w-5 text-orange-500" />
    }

    // 图片文件
    if (contentType.includes('image') ||
        ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
      return <FileImage className="h-5 w-5 text-purple-500" />
    }

    // 视频文件
    if (contentType.includes('video') ||
        ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(extension)) {
      return <FileVideo className="h-5 w-5 text-pink-500" />
    }

    // 音频文件
    if (contentType.includes('audio') ||
        ['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(extension)) {
      return <FileAudio className="h-5 w-5 text-indigo-500" />
    }

    // 压缩文件
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
      return <Archive className="h-5 w-5 text-yellow-500" />
    }

    // 代码文件
    if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'html', 'css', 'json', 'xml'].includes(extension)) {
      return <Code className="h-5 w-5 text-cyan-500" />
    }

    // 文本文件
    if (contentType.includes('text') || ['txt', 'md', 'rtf'].includes(extension)) {
      return <File className="h-5 w-5 text-gray-500" />
    }

    // 默认文件图标
    return <FileText className="h-5 w-5 text-primary" />
  }

  // 获取文档状态显示
  const getDocumentStatusBadge = (document: any) => {
    const status = document.system_metadata?.status || 'completed'

    switch (status) {
      case 'processing':
        return (
          <Badge className="flex items-center gap-1 px-1.5 py-0.5 text-xs font-medium bg-yellow-50 text-yellow-700 border border-yellow-200">
            <Clock className="h-2.5 w-2.5 animate-pulse" />
            处理中
          </Badge>
        )
      case 'completed':
        return (
          <Badge className="flex items-center gap-1 px-1.5 py-0.5 text-xs font-medium bg-green-50 text-green-700 border border-green-200">
            <CheckCircle className="h-2.5 w-2.5" />
            已完成
          </Badge>
        )
      case 'error':
        return (
          <Badge className="flex items-center gap-1 px-1.5 py-0.5 text-xs font-medium bg-red-50 text-red-700 border border-red-200">
            <AlertCircle className="h-2.5 w-2.5" />
            失败
          </Badge>
        )
      default:
        return (
          <Badge className="px-1.5 py-0.5 text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200">
            {status}
          </Badge>
        )
    }
  }

  // 文档操作处理
  const handleDocumentView = (doc: any) => {
    setSelectedDocument(doc)
    setShowPreview(true)
    toast.info(`查看文档: ${doc.filename || '未命名文档'}`)
  }

  const handleDocumentDownload = async (doc: any, event?: React.MouseEvent) => {
    // 阻止事件冒泡，避免触发行点击
    if (event) {
      event.stopPropagation()
    }

    try {
      const filename = doc.filename || doc.metadata?.title || `document-${doc.external_id}`

      // 检查是否为文本文档（创作知识）
      if (doc.content_type === 'text/plain' && doc.system_metadata?.content) {
        // 对于文本文档，从 system_metadata.content 获取内容并创建下载
        const textContent = doc.system_metadata.content
        const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' })
        const url = URL.createObjectURL(blob)

        // 创建临时链接触发下载
        const link = window.document.createElement('a')
        link.href = url
        link.download = filename.endsWith('.txt') ? filename : `${filename}.txt`
        link.style.display = 'none'

        // 添加到DOM，点击，然后移除
        window.document.body.appendChild(link)
        link.click()

        // 清理资源
        setTimeout(() => {
          if (link.parentNode) {
            window.document.body.removeChild(link)
          }
          URL.revokeObjectURL(url)
        }, 100)

        toast.success(`开始下载: ${filename}`)
        return
      }

      // 检查文档是否有存储信息（文件文档）
      if (!doc.storage_info || !doc.storage_info.key) {
        toast.error('该文档没有可下载的文件')
        return
      }

      const { key } = doc.storage_info
      const downloadUrl = `http://localhost:8000/storage/${key}`

      // 创建临时链接触发下载
      const link = window.document.createElement('a')
      link.href = downloadUrl
      link.download = filename
      link.target = '_blank'
      link.style.display = 'none'

      // 添加到DOM，点击，然后移除
      window.document.body.appendChild(link)
      link.click()

      // 延迟移除链接以确保下载开始
      setTimeout(() => {
        if (link.parentNode) {
          window.document.body.removeChild(link)
        }
      }, 100)

      toast.success(`开始下载: ${filename}`)
    } catch (error) {
      console.error('下载失败:', error)
      toast.error('文档下载失败，请稍后重试')
    }
  }

  const handleDocumentDelete = (doc: any, event?: React.MouseEvent) => {
    // 阻止事件冒泡，避免触发行点击
    if (event) {
      event.stopPropagation()
    }

    setDocumentToDelete(doc)
    setShowDeleteDialog(true)
  }

  const confirmDeleteDocument = async () => {
    if (!documentToDelete) return

    const filename = documentToDelete.filename || documentToDelete.metadata?.title || '未命名文档'

    try {
      await deleteDocument.mutateAsync(documentToDelete.external_id)
      toast.success(`文档 "${filename}" 删除成功`)
      setShowDeleteDialog(false)
      setDocumentToDelete(null)
    } catch (error) {
      console.error('删除失败:', error)
      toast.error(`删除文档 "${filename}" 失败，请稍后重试`)
    }
  }

  const cancelDeleteDocument = () => {
    setShowDeleteDialog(false)
    setDocumentToDelete(null)
  }

  return (
    <div className="flex-1 space-y-6 md:space-y-8 p-4 md:p-8 pt-4 md:pt-6 bg-gradient-to-br from-gray-50/50 to-white dark:from-gray-900/50 dark:to-gray-900 min-h-screen">
      {/* 页面头部 */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-6 bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-xl shadow-lg">
            <Building2 className="h-7 w-7 text-white" />
          </div>
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-200 bg-clip-text text-transparent">企业知识库</h1>
            <p className="text-muted-foreground text-sm md:text-base mt-1">管理公司内部知识和资料</p>
          </div>
        </div>

        <div className="flex items-center space-x-3 w-full sm:w-auto">
          <Button
            size="sm"
            className="flex-1 sm:flex-none h-10 px-4 font-medium bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 transition-all duration-200 shadow-md hover:shadow-lg"
            onClick={() => {
              setUploadDialogDefaultTab('file')
              setUploadDialogOpen(true)
            }}
          >
            <Upload className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">上传知识</span>
            <span className="sm:hidden">上传</span>
          </Button>
          <Button
            size="sm"
            className="flex-1 sm:flex-none h-10 px-4 font-medium bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 transition-all duration-200 shadow-md hover:shadow-lg"
            onClick={() => {
              setUploadDialogDefaultTab('text')
              setUploadDialogOpen(true)
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">创作知识</span>
            <span className="sm:hidden">创作</span>
          </Button>
        </div>
      </div>

      {/* 搜索和筛选区域 */}
      <SearchAndFilter
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        sortBy={sortBy}
        onSortChange={setSortBy}
        filters={filters}
        onFiltersChange={setFilters}
        totalResults={filteredDocuments.length}
      />

      {/* 知识库分类和内容 */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 左侧分类导航 */}
        <div className="lg:col-span-1">
          <Card className="shadow-sm border-gray-100 dark:border-gray-700 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
            <CardHeader className="pb-4 border-b border-gray-100 dark:border-gray-700">
              <CardTitle className="text-base md:text-lg font-semibold text-gray-900 dark:text-gray-100">知识分类</CardTitle>
            </CardHeader>
            <CardContent className="space-y-1 p-4">
              {updatedKnowledgeCategories.map((category) => (
                <KnowledgeCategoryCard
                  key={category.id}
                  id={category.id}
                  title={category.title}
                  icon={category.icon}
                  color={category.color}
                  description={category.description}
                  count={category.count}
                  isActive={activeCategory === category.id}
                  onClick={handleCategoryChange}
                />
              ))}
            </CardContent>
          </Card>
        </div>

        {/* 右侧文档列表 */}
        <div className="lg:col-span-3">
          <Card className="shadow-sm border-gray-100 dark:border-gray-700 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
            <CardHeader className="pb-4 border-b border-gray-100 dark:border-gray-700">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
                <div>
                  <CardTitle className="text-base md:text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {updatedKnowledgeCategories.find(cat => cat.id === activeCategory)?.title}
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    共 {filteredDocuments.length} 个文档
                  </CardDescription>
                </div>
                {filteredDocuments.length > DOCUMENTS_LIMIT && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowAllDocuments(!showAllDocuments)}
                    className="text-xs px-3 py-1 h-auto text-primary hover:text-primary/80 hover:bg-primary/10"
                  >
                    {showAllDocuments ? '收起' : '更多'}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent className="p-6">
              {filteredDocuments.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-16 text-center">
                  <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6">
                    <FileText className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">暂无文档</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 max-w-sm">
                    {searchQuery ? '没有找到匹配的文档，请尝试其他关键词' : '该分类下暂无文档，请上传相关文档'}
                  </p>
                </div>
              ) : viewMode === 'list' ? (
                <div className="space-y-2">
                  {(showAllDocuments ? filteredDocuments : filteredDocuments.slice(0, DOCUMENTS_LIMIT)).map((doc) => (
                    <div key={doc.external_id || doc.id} className="group relative bg-white dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md hover:border-primary/30 transition-all duration-200 overflow-hidden">
                      <div className="flex items-center p-3 gap-3">
                        {/* 文件图标和类型标识 */}
                        <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg flex items-center justify-center group-hover:from-gray-100 group-hover:to-gray-200 dark:group-hover:from-gray-600 dark:group-hover:to-gray-500 transition-colors">
                          {getFileIcon(doc)}
                        </div>

                        {/* 主要内容区域 */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3
                              className="font-medium text-gray-900 dark:text-gray-100 truncate cursor-pointer hover:text-primary transition-colors text-sm"
                              onClick={() => handleDocumentView(doc)}
                              title="点击查看文档"
                            >
                              {doc.filename || doc.metadata?.title || '未命名文档'}
                            </h3>
                            {getDocumentStatusBadge(doc)}
                          </div>

                          <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
                            <span className="flex items-center gap-1">
                              <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                              {formatFileSize(doc.metadata?.fileSize)}
                            </span>
                            <Badge variant="secondary" className="text-xs px-1.5 py-0.5 h-auto font-normal bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border-0">
                              {doc.content_type?.includes('pdf') ? 'PDF' :
                               doc.content_type?.includes('word') ? 'WORD' :
                               doc.content_type?.includes('text') ? 'TEXT' :
                               doc.type?.toUpperCase() || 'FILE'}
                            </Badge>
                            <span className="flex items-center gap-1">
                              <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                              {formatDate(doc.system_metadata?.created_at || doc.created_at)}
                            </span>
                          </div>
                        </div>

                        {/* 操作按钮 */}
                        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => handleDocumentDownload(doc, e)}
                            title="下载文档"
                            disabled={!doc.storage_info && !(doc.content_type === 'text/plain' && doc.system_metadata?.content)}
                            className="h-7 w-7 p-0 text-blue-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-950/20 transition-all duration-200"
                          >
                            <Download className="h-3.5 w-3.5" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => handleDocumentDelete(doc, e)}
                            disabled={deleteDocument.isPending}
                            title="删除文档"
                            className="h-7 w-7 p-0 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/20 transition-all duration-200"
                          >
                            {deleteDocument.isPending ? (
                              <Loader2 className="h-3.5 w-3.5 animate-spin" />
                            ) : (
                              <Trash2 className="h-3.5 w-3.5" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* 列表视图底部更多按钮 */}
                  {filteredDocuments.length > DOCUMENTS_LIMIT && !showAllDocuments && (
                    <div className="flex justify-center pt-4">
                      <Button
                        variant="outline"
                        onClick={() => setShowAllDocuments(true)}
                        className="text-primary hover:text-primary/80 hover:bg-primary/10"
                      >
                        显示更多 ({filteredDocuments.length - DOCUMENTS_LIMIT} 个)
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {(showAllDocuments ? filteredDocuments : filteredDocuments.slice(0, DOCUMENTS_LIMIT)).map((doc) => (
                    <Card key={doc.external_id || doc.id} className="h-full flex flex-col hover:shadow-lg hover:border-primary/30 transition-all duration-300 group bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
                      <CardContent className="p-5 flex-1 flex flex-col">
                        <div className="space-y-4 flex-1">
                          <div className="flex items-start gap-3">
                            <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg flex items-center justify-center group-hover:from-gray-100 group-hover:to-gray-200 dark:group-hover:from-gray-600 dark:group-hover:to-gray-500 transition-colors">
                              {getFileIcon(doc)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <h3
                                className="font-semibold text-sm line-clamp-2 mb-2 cursor-pointer text-gray-900 dark:text-gray-100 hover:text-primary transition-colors leading-relaxed"
                                onClick={() => handleDocumentView(doc)}
                                title="点击查看文档"
                              >
                                {doc.filename || doc.metadata?.title || '未命名文档'}
                              </h3>
                              <div className="flex items-center">
                                {getDocumentStatusBadge(doc)}
                              </div>
                            </div>
                          </div>

                          <div className="bg-gray-50/80 dark:bg-gray-800/50 rounded-lg p-3 space-y-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                                <span className="font-medium">大小:</span>
                                <span>{formatFileSize(doc.metadata?.fileSize)}</span>
                              </div>
                              <Badge
                                variant="secondary"
                                className="text-xs font-medium bg-primary/10 text-primary border-primary/20 hover:bg-primary/20"
                              >
                                {doc.content_type?.includes('pdf') ? 'PDF' :
                                 doc.content_type?.includes('word') ? 'WORD' :
                                 doc.content_type?.includes('text') ? 'TEXT' :
                                 doc.type?.toUpperCase() || 'FILE'}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                              <span className="font-medium">时间:</span>
                              <span>{formatDate(doc.system_metadata?.created_at || doc.created_at)}</span>
                            </div>
                          </div>

                          {doc.metadata?.description && (
                            <div className="bg-blue-50/50 dark:bg-blue-900/20 rounded-md p-2 border-l-2 border-blue-200 dark:border-blue-700">
                              <p className="text-xs text-gray-700 dark:text-gray-300 line-clamp-2 leading-relaxed">
                                {doc.metadata.description}
                              </p>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center gap-2 pt-4 mt-auto border-t border-gray-100 dark:border-gray-700">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 h-9 text-xs font-medium text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300 dark:text-blue-400 dark:border-blue-700 dark:hover:bg-blue-900/20 transition-all duration-200 hover:shadow-sm"
                            onClick={(e) => handleDocumentDownload(doc, e)}
                            disabled={!doc.storage_info && !(doc.content_type === 'text/plain' && doc.system_metadata?.content)}
                            title="下载文档"
                          >
                            <Download className="h-3 w-3 mr-1.5" />
                            下载
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => handleDocumentDelete(doc, e)}
                            disabled={deleteDocument.isPending}
                            title="删除文档"
                            className="h-9 w-9 p-0 text-red-500 border-red-200 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-700 dark:hover:bg-red-900/20 transition-all duration-200 hover:shadow-sm"
                          >
                            {deleteDocument.isPending ? (
                              <Loader2 className="h-3 w-3 animate-spin" />
                            ) : (
                              <Trash2 className="h-3 w-3" />
                            )}
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {/* 网格视图底部更多按钮 */}
                  {filteredDocuments.length > DOCUMENTS_LIMIT && !showAllDocuments && (
                    <div className="col-span-full flex justify-center pt-4">
                      <Button
                        variant="outline"
                        onClick={() => setShowAllDocuments(true)}
                        className="text-primary hover:text-primary/80 hover:bg-primary/10"
                      >
                        显示更多 ({filteredDocuments.length - DOCUMENTS_LIMIT} 个)
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 上传对话框 */}
      <EnterpriseUploadDialog
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
        category={activeCategory}
        onUploadComplete={handleUploadComplete}
        defaultTab={uploadDialogDefaultTab}
      />

      {/* 文档预览模态框 */}
      <DocumentPreviewModal
        document={selectedDocument}
        isOpen={showPreview}
        onClose={() => {
          setShowPreview(false)
          setSelectedDocument(null)
        }}
      />

      {/* 删除确认对话框 */}
      <DeleteDocumentDialog
        isOpen={showDeleteDialog}
        onClose={cancelDeleteDocument}
        onConfirm={confirmDeleteDocument}
        document={documentToDelete}
        isDeleting={deleteDocument.isPending}
      />
    </div>
  )
}

// 文档预览模态框组件
function DocumentPreviewModal({
  document,
  isOpen,
  onClose
}: {
  document: any
  isOpen: boolean
  onClose: () => void
}) {
  const { data: content, isLoading } = useDocumentContent(
    document?.external_id || '',
    isOpen && !!document?.external_id
  )

  if (!document) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
        <DialogHeader className="border-b border-gray-100 dark:border-gray-700 pb-6">
          <DialogTitle className="flex items-center gap-3 text-xl">
            <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-xl flex items-center justify-center shadow-lg">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <h2 className="font-bold text-gray-900 dark:text-gray-100 truncate text-lg">
                {document.filename || document.metadata?.title || '未命名文档'}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 font-mono">
                ID: {document.external_id}
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-2">
          {/* 文档信息 */}
          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
            <div className="px-6 py-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 border-b border-gray-100 dark:border-gray-600">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                文档信息
              </h3>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="space-y-2">
                  <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">文档大小</div>
                  <div className="text-base font-medium text-gray-900 dark:text-gray-100">
                    {document.metadata?.fileSize ?
                      `${(document.metadata.fileSize / 1024 / 1024).toFixed(2)} MB` :
                      '0.00 MB'
                    }
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">文件类型</div>
                  <div>
                    <Badge className="bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 border border-blue-200 font-medium px-3 py-1">
                      {document.content_type?.includes('pdf') ? 'PDF' :
                       document.content_type?.includes('word') ? 'WORD' :
                       document.content_type?.includes('text') ? 'TEXT' :
                       document.content_type || 'FILE'}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">上传时间</div>
                  <div className="text-base font-medium text-gray-900 dark:text-gray-100">
                    {document.system_metadata?.created_at ?
                      new Date(document.system_metadata.created_at).toLocaleDateString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                      }) :
                      '2025/7/15'
                    }
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">状态</div>
                  <div>
                    {document.system_metadata?.status === 'completed' ? (
                      <Badge className="bg-gradient-to-r from-green-100 to-green-50 text-green-800 border border-green-200 font-medium px-3 py-1">
                        已完成
                      </Badge>
                    ) : document.system_metadata?.status === 'processing' ? (
                      <Badge className="bg-gradient-to-r from-yellow-100 to-yellow-50 text-yellow-800 border border-yellow-200 font-medium px-3 py-1">
                        处理中
                      </Badge>
                    ) : document.system_metadata?.status === 'failed' ? (
                      <Badge className="bg-gradient-to-r from-red-100 to-red-50 text-red-800 border border-red-200 font-medium px-3 py-1">
                        失败
                      </Badge>
                    ) : (
                      <Badge className="bg-gradient-to-r from-gray-100 to-gray-50 text-gray-800 border border-gray-200 font-medium px-3 py-1">
                        {document.system_metadata?.status || '已完成'}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 文档内容预览 */}
          <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
            <div className="px-6 py-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 border-b border-gray-100 dark:border-gray-600">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                文档内容预览
              </h3>
            </div>
            <div className="relative">
              <ScrollArea className="h-[400px]">
                <div className="p-6">
                  {isLoading ? (
                    <div className="flex flex-col items-center justify-center h-40 space-y-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                        <Loader2 className="h-6 w-6 animate-spin text-primary" />
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">加载中...</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">正在获取文档内容</p>
                      </div>
                    </div>
                  ) : content ? (
                    <div className="bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                      <pre className="whitespace-pre-wrap text-sm leading-relaxed text-gray-800 dark:text-gray-200 font-mono overflow-x-auto">
                        {content}
                      </pre>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-40 space-y-4">
                      <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                        <FileText className="h-8 w-8 text-gray-400" />
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">暂无内容预览</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">该文档可能不支持预览或内容为空</p>
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
