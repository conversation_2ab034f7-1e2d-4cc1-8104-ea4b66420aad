/**
 * 任务统计组件
 * 显示任务的统计信息
 */
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { TaskStatsResponse } from '../../services/taskApi';

interface TaskStatsProps {
  stats: TaskStatsResponse;
  isLoading?: boolean;
}

export function TaskStats({ stats, isLoading = false }: TaskStatsProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const statusStats = [
    {
      title: '待处理',
      value: stats.pending,
      color: 'bg-yellow-100 text-yellow-800',
    },
    {
      title: '进行中',
      value: stats.in_progress,
      color: 'bg-blue-100 text-blue-800',
    },
    {
      title: '已完成',
      value: stats.completed,
      color: 'bg-green-100 text-green-800',
    },
    {
      title: '已取消',
      value: stats.cancelled,
      color: 'bg-gray-100 text-gray-800',
    },
  ];

  const priorityStats = [
    {
      title: '低优先级',
      value: stats.by_priority.low || 0,
      color: 'bg-gray-100 text-gray-800',
    },
    {
      title: '中优先级',
      value: stats.by_priority.medium || 0,
      color: 'bg-blue-100 text-blue-800',
    },
    {
      title: '高优先级',
      value: stats.by_priority.high || 0,
      color: 'bg-orange-100 text-orange-800',
    },
    {
      title: '紧急',
      value: stats.by_priority.urgent || 0,
      color: 'bg-red-100 text-red-800',
    },
  ];

  return (
    <div className="space-y-6">
      {/* 总体统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">总体统计</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-blue-600">{stats.total}</div>
          <p className="text-sm text-gray-600">总任务数</p>
        </CardContent>
      </Card>

      {/* 按状态统计 */}
      <div>
        <h3 className="text-lg font-semibold mb-4">按状态统计</h3>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {statusStats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <Badge variant="secondary" className={stat.color}>
                    {stats.total > 0
                      ? `${Math.round((stat.value / stats.total) * 100)}%`
                      : '0%'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* 按优先级统计 */}
      <div>
        <h3 className="text-lg font-semibold mb-4">按优先级统计</h3>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {priorityStats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <Badge variant="outline" className={stat.color}>
                    {stats.total > 0
                      ? `${Math.round((stat.value / stats.total) * 100)}%`
                      : '0%'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
