import { useState } from 'react'
import { Search, Filter, Grid3X3, List, SortAsc, SortDesc, Calendar, FileType } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'

interface SearchAndFilterProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  viewMode: 'grid' | 'list'
  onViewModeChange: (mode: 'grid' | 'list') => void
  sortBy: string
  onSortChange: (sort: string) => void
  filters: {
    fileTypes: string[]
    dateRange: string
    author: string
  }
  onFiltersChange: (filters: any) => void
  totalResults: number
}

const fileTypeOptions = [
  { value: 'txt', label: 'TXT文档', color: 'bg-green-100 text-green-800' },
  { value: 'pdf', label: 'PDF文档', color: 'bg-red-100 text-red-800' },
  { value: 'docx', label: 'Word文档', color: 'bg-blue-100 text-blue-800' },
  { value: 'png', label: 'PNG图片', color: 'bg-gray-100 text-gray-800' },
  { value: 'jpg', label: 'JPG图片', color: 'bg-gray-100 text-gray-800' },
]

const dateRangeOptions = [
  { value: 'all', label: '全部时间' },
  { value: 'today', label: '今天' },
  { value: 'week', label: '本周' },
  { value: 'month', label: '本月' },
  { value: 'quarter', label: '本季度' },
  { value: 'year', label: '本年' },
]

const sortOptions = [
  { value: 'name-asc', label: '名称 A-Z', icon: SortAsc },
  { value: 'name-desc', label: '名称 Z-A', icon: SortDesc },
  { value: 'date-desc', label: '最新优先', icon: Calendar },
  { value: 'date-asc', label: '最旧优先', icon: Calendar },
  { value: 'size-desc', label: '大小降序', icon: FileType },
  { value: 'size-asc', label: '大小升序', icon: FileType },
]

export function SearchAndFilter({
  searchQuery,
  onSearchChange,
  viewMode,
  onViewModeChange,
  sortBy,
  onSortChange,
  filters,
  onFiltersChange,
  totalResults
}: SearchAndFilterProps) {
  const [isFilterOpen, setIsFilterOpen] = useState(false)

  const handleFileTypeChange = (fileType: string, checked: boolean) => {
    const newFileTypes = checked
      ? [...filters.fileTypes, fileType]
      : filters.fileTypes.filter(type => type !== fileType)
    
    onFiltersChange({
      ...filters,
      fileTypes: newFileTypes
    })
  }

  const clearFilters = () => {
    onFiltersChange({
      fileTypes: [],
      dateRange: 'all',
      author: ''
    })
  }

  const hasActiveFilters = filters.fileTypes.length > 0 || filters.dateRange !== 'all' || filters.author

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          {/* 搜索栏 */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索文档名称、内容或标签..."
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              {/* 筛选按钮 */}
              <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="relative">
                    <Filter className="h-4 w-4 mr-2" />
                    筛选
                    {hasActiveFilters && (
                      <div className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full shadow-sm border border-white animate-pulse"></div>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80" align="end">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">筛选条件</h4>
                      {hasActiveFilters && (
                        <Button variant="ghost" size="sm" onClick={clearFilters}>
                          清除
                        </Button>
                      )}
                    </div>
                    
                    <Separator />
                    
                    {/* 文件类型筛选 */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium flex items-center">
                        <FileType className="h-4 w-4 mr-2" />
                        文件类型
                      </Label>
                      <div className="space-y-2">
                        {fileTypeOptions.map((option) => (
                          <div key={option.value} className="flex items-center space-x-2">
                            <Checkbox
                              id={option.value}
                              checked={filters.fileTypes.includes(option.value)}
                              onCheckedChange={(checked) => 
                                handleFileTypeChange(option.value, checked as boolean)
                              }
                            />
                            <Label htmlFor={option.value} className="text-sm">
                              {option.label}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <Separator />
                    
                    {/* 时间范围筛选 */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium flex items-center">
                        <Calendar className="h-4 w-4 mr-2" />
                        时间范围
                      </Label>
                      <Select
                        value={filters.dateRange}
                        onValueChange={(value) => onFiltersChange({ ...filters, dateRange: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {dateRangeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <Separator />
                    
                   
                  </div>
                </PopoverContent>
              </Popover>
              
              {/* 排序选择 */}
              <Select value={sortBy} onValueChange={onSortChange}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => {
                    const IconComponent = option.icon
                    return (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center">
                          <IconComponent className="h-4 w-4 mr-2" />
                          {option.label}
                        </div>
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
              
              {/* 视图模式切换 */}
              <div className="flex items-center border rounded-md">
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => onViewModeChange('list')}
                  className="rounded-r-none"
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => onViewModeChange('grid')}
                  className="rounded-l-none"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          
          {/* 活动筛选器显示 */}
          {hasActiveFilters && (
            <div className="flex items-center space-x-2 flex-wrap">
              <span className="text-sm text-muted-foreground">活动筛选:</span>
              {filters.fileTypes.map((type) => {
                const option = fileTypeOptions.find(opt => opt.value === type)
                return option ? (
                  <Badge key={type} variant="secondary" className="text-xs">
                    {option.label}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 ml-1"
                      onClick={() => handleFileTypeChange(type, false)}
                    >
                      ×
                    </Button>
                  </Badge>
                ) : null
              })}
              {filters.dateRange !== 'all' && (
                <Badge variant="secondary" className="text-xs">
                  {dateRangeOptions.find(opt => opt.value === filters.dateRange)?.label}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 ml-1"
                    onClick={() => onFiltersChange({ ...filters, dateRange: 'all' })}
                  >
                    ×
                  </Button>
                </Badge>
              )}
             
            </div>
          )}
          
          {/* 结果统计 */}
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>共找到 {totalResults} 个文档</span>
            {searchQuery && (
              <span>搜索关键词: "{searchQuery}"</span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
