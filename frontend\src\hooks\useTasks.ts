/**
 * 任务管理相关的React Hooks
 * 使用TanStack Query管理服务端状态
 */
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  taskApiService,
  Task,
  CreateTaskRequest,
  UpdateTaskRequest,
  TaskQueryParams,
  TaskListResponse,
  TaskStatsResponse,
} from '../services/taskApi';

// 查询键常量
export const TASK_QUERY_KEYS = {
  all: ['tasks'] as const,
  lists: () => [...TASK_QUERY_KEYS.all, 'list'] as const,
  list: (params?: TaskQueryParams) => [...TASK_QUERY_KEYS.lists(), params] as const,
  details: () => [...TASK_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...TASK_QUERY_KEYS.details(), id] as const,
  stats: () => [...TASK_QUERY_KEYS.all, 'stats'] as const,
};

// 获取任务列表Hook
export function useTasks(params?: TaskQueryParams) {
  return useQuery({
    queryKey: TASK_QUERY_KEYS.list(params),
    queryFn: () => taskApiService.getTasks(params),
    staleTime: 30 * 1000, // 30秒内认为数据是新鲜的
    gcTime: 5 * 60 * 1000, // 5分钟后清理缓存
  });
}

// 获取单个任务Hook
export function useTask(id: number) {
  return useQuery({
    queryKey: TASK_QUERY_KEYS.detail(id),
    queryFn: () => taskApiService.getTask(id),
    enabled: !!id, // 只有当id存在时才执行查询
    staleTime: 30 * 1000,
    gcTime: 5 * 60 * 1000,
  });
}

// 获取任务统计Hook
export function useTaskStats() {
  return useQuery({
    queryKey: TASK_QUERY_KEYS.stats(),
    queryFn: () => taskApiService.getTaskStats(),
    staleTime: 60 * 1000, // 1分钟内认为数据是新鲜的
    gcTime: 5 * 60 * 1000,
  });
}

// 创建任务Hook
export function useCreateTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTaskRequest) => taskApiService.createTask(data),
    onSuccess: (newTask) => {
      // 使所有任务列表查询失效，触发重新获取
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.stats() });
      
      toast.success('任务创建成功', {
        description: `任务"${newTask.title}"已成功创建`,
      });
    },
    onError: (error: any) => {
      toast.error('创建任务失败', {
        description: error.response?.data?.detail || error.message || '未知错误',
      });
    },
  });
}

// 更新任务Hook
export function useUpdateTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateTaskRequest }) =>
      taskApiService.updateTask(id, data),
    onSuccess: (updatedTask) => {
      // 更新缓存中的任务详情
      queryClient.setQueryData(
        TASK_QUERY_KEYS.detail(updatedTask.id),
        updatedTask
      );
      
      // 使任务列表查询失效
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.stats() });
      
      toast.success('任务更新成功', {
        description: `任务"${updatedTask.title}"已成功更新`,
      });
    },
    onError: (error: any) => {
      toast.error('更新任务失败', {
        description: error.response?.data?.detail || error.message || '未知错误',
      });
    },
  });
}

// 删除任务Hook
export function useDeleteTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => taskApiService.deleteTask(id),
    onSuccess: (deletedTask) => {
      // 从缓存中移除任务详情
      queryClient.removeQueries({ queryKey: TASK_QUERY_KEYS.detail(deletedTask.id) });
      
      // 使任务列表查询失效
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.stats() });
      
      toast.success('任务删除成功', {
        description: `任务"${deletedTask.title}"已成功删除`,
      });
    },
    onError: (error: any) => {
      toast.error('删除任务失败', {
        description: error.response?.data?.detail || error.message || '未知错误',
      });
    },
  });
}

// 批量操作Hook
export function useBatchUpdateTasks() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (updates: Array<{ id: number; data: UpdateTaskRequest }>) => {
      const results = await Promise.all(
        updates.map(({ id, data }) => taskApiService.updateTask(id, data))
      );
      return results;
    },
    onSuccess: (updatedTasks) => {
      // 使所有相关查询失效
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.stats() });
      
      toast.success('批量更新成功', {
        description: `已成功更新 ${updatedTasks.length} 个任务`,
      });
    },
    onError: (error: any) => {
      toast.error('批量更新失败', {
        description: error.response?.data?.detail || error.message || '未知错误',
      });
    },
  });
}
