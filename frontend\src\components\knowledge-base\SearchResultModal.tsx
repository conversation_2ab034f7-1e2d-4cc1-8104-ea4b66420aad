import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { 
  FileText, 
  Star, 
  Copy, 
  ExternalLink, 
  Download,
  Clock,
  Hash,
  Tag
} from 'lucide-react';
import { toast } from 'sonner';
import type { SearchResult } from '@/types/morphik';

interface SearchResultModalProps {
  result: SearchResult | null;
  isOpen: boolean;
  onClose: () => void;
}

function SearchResultModal({ result, isOpen, onClose }: SearchResultModalProps) {
  if (!result) return null;

  // 格式化相似度分数
  const formatScore = (score: number) => {
    return (score * 100).toFixed(1);
  };

  // 复制内容到剪贴板
  const handleCopyContent = async () => {
    try {
      await navigator.clipboard.writeText(result.content);
      toast.success('内容已复制到剪贴板');
    } catch (error) {
      toast.error('复制失败');
    }
  };

  // 获取文档图标
  const getDocumentIcon = () => {
    const contentType = result.metadata?.fileType || 'text';
    
    if (contentType.includes('pdf')) {
      return <FileText className="h-5 w-5 text-red-500" />;
    } else if (contentType.includes('word') || contentType.includes('doc')) {
      return <FileText className="h-5 w-5 text-blue-500" />;
    } else if (contentType.includes('image')) {
      return <FileText className="h-5 w-5 text-green-500" />;
    } else {
      return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (!bytes) return 'N/A';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getDocumentIcon()}
            <span className="truncate">
              {result.metadata?.filename || result.metadata?.title || '搜索结果详情'}
            </span>
          </DialogTitle>
          <DialogDescription className="flex items-center gap-4 text-sm">
            <span className="flex items-center gap-1">
              <Star className="h-4 w-4" />
              相似度: {formatScore(result.similarity_score)}%
            </span>
            {result.document_id && (
              <span className="flex items-center gap-1">
                <Hash className="h-4 w-4" />
                文档ID: {result.document_id.substring(0, 8)}...
              </span>
            )}
            {result.chunk_index !== undefined && (
              <span className="flex items-center gap-1">
                <Tag className="h-4 w-4" />
                块索引: {result.chunk_index}
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyContent}
              className="flex items-center gap-2"
            >
              <Copy className="h-4 w-4" />
              复制内容
            </Button>
            
            {result.metadata?.download_url && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(result.metadata.download_url, '_blank')}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                下载文档
              </Button>
            )}
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // 这里可以添加跳转到文档详情的逻辑
                console.log('Navigate to document:', result.document_id);
              }}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              查看文档
            </Button>
          </div>

          <Separator />

          {/* 内容区域 */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">内容</h3>
              <ScrollArea className="h-[300px] w-full rounded-md border p-4">
                <div className="text-sm leading-relaxed whitespace-pre-wrap">
                  {result.content}
                </div>
              </ScrollArea>
            </div>

            {/* 基本信息 */}
            <div>
              <h3 className="text-lg font-semibold mb-2">基本信息</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">文件名:</span>
                    <span className="font-medium">
                      {result.metadata?.filename || 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">文件类型:</span>
                    <Badge variant="outline">
                      {result.metadata?.fileType || 'text/plain'}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">文件大小:</span>
                    <span className="font-medium">
                      {formatFileSize(result.metadata?.fileSize)}
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">相似度分数:</span>
                    <Badge variant="secondary">
                      {formatScore(result.similarity_score)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">文档ID:</span>
                    <span className="font-mono text-xs">
                      {result.document_id}
                    </span>
                  </div>
                  {result.chunk_index !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">块索引:</span>
                      <span className="font-medium">
                        {result.chunk_index}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 详细元数据 */}
            {result.metadata && Object.keys(result.metadata).length > 0 && (
              <Accordion type="single" collapsible>
                <AccordionItem value="metadata">
                  <AccordionTrigger className="text-lg font-semibold">
                    详细元数据
                  </AccordionTrigger>
                  <AccordionContent>
                    <ScrollArea className="h-[200px] w-full">
                      <pre className="text-xs bg-muted rounded p-3 overflow-x-auto">
                        {JSON.stringify(result.metadata, null, 2)}
                      </pre>
                    </ScrollArea>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default SearchResultModal;
