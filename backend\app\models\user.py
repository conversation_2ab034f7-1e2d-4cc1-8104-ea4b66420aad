"""
用户管理数据模型
定义用户的数据库表结构
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class User(Base):
    """
    用户数据模型
    用于用户认证和管理系统
    """
    __tablename__ = "users"
    
    # 主键ID
    id = Column(Integer, primary_key=True, index=True, comment="用户ID")
    
    # 用户名（唯一）
    username = Column(
        String(50), 
        unique=True, 
        nullable=False, 
        index=True, 
        comment="用户名"
    )
    
    # 邮箱（唯一）
    email = Column(
        String(100), 
        unique=True, 
        nullable=False, 
        index=True, 
        comment="邮箱地址"
    )
    
    # 密码哈希
    hashed_password = Column(
        String(255), 
        nullable=False, 
        comment="密码哈希"
    )
    
    # 全名
    full_name = Column(String(100), nullable=True, comment="全名")
    
    # 头像URL
    avatar_url = Column(String(255), nullable=True, comment="头像URL")
    
    # 电话号码
    phone = Column(String(20), nullable=True, comment="电话号码")
    
    # 是否激活
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 是否超级用户
    is_superuser = Column(Boolean, default=False, comment="是否超级用户")
    
    # 创建时间
    created_at = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        comment="创建时间"
    )
    
    # 更新时间
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )

    # 关联密码重置令牌
    password_reset_tokens = relationship("PasswordResetToken", back_populates="user", cascade="all, delete-orphan")

    # 关联用户角色
    user_roles = relationship("UserRole", foreign_keys="UserRole.user_id", back_populates="user", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
