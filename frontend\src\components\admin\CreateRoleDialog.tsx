/**
 * 创建角色对话框组件
 */
import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Loader2, Shield, Check, AlertCircle } from 'lucide-react'
import { useAuthStore } from '@/store/auth'
import { PermissionSelector } from './PermissionSelector'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface Permission {
  id: number
  name: string
  description: string
  category: string
}

interface CreateRoleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export const CreateRoleDialog: React.FC<CreateRoleDialogProps> = ({
  open,
  onOpenChange,
  onSuccess,
}) => {
  const { token } = useAuthStore()
  
  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true,
  })
  
  // 权限相关状态
  const [allPermissions, setAllPermissions] = useState<Permission[]>([])
  const [selectedPermissionIds, setSelectedPermissionIds] = useState<number[]>([])
  
  // UI状态
  const [isLoading, setIsLoading] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // 获取所有权限
  const fetchPermissions = async () => {
    if (!token) return

    setIsLoading(true)
    try {
      const response = await fetch(`${API_BASE_URL}/admin/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data: Permission[] = await response.json()
        setAllPermissions(data)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '获取权限列表失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 对话框打开时获取权限
  useEffect(() => {
    if (open) {
      fetchPermissions()
    }
  }, [open, token])

  // 处理表单输入
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  // 处理权限选择
  const handlePermissionToggle = (permissionId: number, checked: boolean) => {
    setSelectedPermissionIds(prev => {
      if (checked) {
        return [...prev, permissionId]
      } else {
        return prev.filter(id => id !== permissionId)
      }
    })
  }



  // 创建角色
  const handleCreate = async () => {
    if (!token) return

    // 表单验证
    if (!formData.name.trim()) {
      setError('角色名称不能为空')
      return
    }

    setIsCreating(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch(`${API_BASE_URL}/admin/roles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || null,
          is_active: formData.is_active,
          permission_ids: selectedPermissionIds,
        }),
      })

      if (response.ok) {
        setSuccess('角色创建成功')
        onSuccess()
        setTimeout(() => {
          handleClose()
        }, 1500)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '创建角色失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsCreating(false)
    }
  }

  // 关闭对话框
  const handleClose = () => {
    setFormData({
      name: '',
      description: '',
      is_active: true,
    })
    setSelectedPermissionIds([])
    setError('')
    setSuccess('')
    onOpenChange(false)
  }

  // 获取选中权限的统计信息
  const selectedPermissions = allPermissions.filter(p => selectedPermissionIds.includes(p.id))
  const selectedByCategory = selectedPermissions.reduce((groups, permission) => {
    const category = permission.category || '其他'
    groups[category] = (groups[category] || 0) + 1
    return groups
  }, {} as Record<string, number>)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>创建新角色</span>
          </DialogTitle>
          <DialogDescription>
            创建新的系统角色并分配相应权限
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-4">
          {/* 左侧：基本信息 */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">基本信息</CardTitle>
                <CardDescription>
                  设置角色的基本信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="role-name">角色名称 *</Label>
                  <Input
                    id="role-name"
                    placeholder="输入角色名称"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    disabled={isCreating}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role-description">角色描述</Label>
                  <Textarea
                    id="role-description"
                    placeholder="输入角色描述（可选）"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    disabled={isCreating}
                    rows={3}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="role-active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => handleInputChange('is_active', checked as boolean)}
                    disabled={isCreating}
                  />
                  <Label htmlFor="role-active">启用角色</Label>
                </div>
              </CardContent>
            </Card>

            {/* 权限预览 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">权限预览</CardTitle>
                <CardDescription>
                  已选择 {selectedPermissionIds.length} 个权限
                </CardDescription>
              </CardHeader>
              <CardContent>
                {selectedPermissionIds.length === 0 ? (
                  <p className="text-muted-foreground text-sm">尚未选择任何权限</p>
                ) : (
                  <div className="space-y-2">
                    {Object.entries(selectedByCategory).map(([category, count]) => (
                      <div key={category} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{category}</span>
                        <Badge variant="secondary">{count}</Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 右侧：权限选择 */}
          <div className="space-y-4">
            {isLoading ? (
              <Card>
                <CardContent className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">加载权限中...</span>
                </CardContent>
              </Card>
            ) : (
              <PermissionSelector
                permissions={allPermissions}
                selectedPermissionIds={selectedPermissionIds}
                onPermissionToggle={handlePermissionToggle}
                disabled={isCreating}
                className="flex-1"
              />
            )}
          </div>
        </div>

        {/* 错误和成功消息 */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <Check className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isCreating}
          >
            取消
          </Button>
          <Button
            onClick={handleCreate}
            disabled={isCreating || !formData.name.trim()}
          >
            {isCreating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            创建角色
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
