"""Add RBAC tables

Revision ID: 004_add_rbac_tables
Revises: 93cebe17795d
Create Date: 2025-07-04 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '004_add_rbac_tables'
down_revision = '93cebe17795d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 创建角色表
    op.create_table('roles',
        sa.<PERSON>umn('id', sa.Integer(), nullable=False, comment='角色ID'),
        sa.Column('name', sa.String(length=50), nullable=False, comment='角色名称'),
        sa.Column('description', sa.Text(), nullable=True, comment='角色描述'),
        sa.Column('is_active', sa.<PERSON>(), nullable=True, comment='是否激活'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.<PERSON>ey<PERSON>onstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_roles_id'), 'roles', ['id'], unique=False)
    op.create_index(op.f('ix_roles_name'), 'roles', ['name'], unique=False)

    # 创建权限表
    op.create_table('permissions',
        sa.Column('id', sa.Integer(), nullable=False, comment='权限ID'),
        sa.Column('name', sa.String(length=100), nullable=False, comment='权限名称'),
        sa.Column('description', sa.Text(), nullable=True, comment='权限描述'),
        sa.Column('category', sa.String(length=50), nullable=True, comment='权限分类'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_permissions_category'), 'permissions', ['category'], unique=False)
    op.create_index(op.f('ix_permissions_id'), 'permissions', ['id'], unique=False)
    op.create_index(op.f('ix_permissions_name'), 'permissions', ['name'], unique=False)

    # 创建用户角色关联表
    op.create_table('user_roles',
        sa.Column('id', sa.Integer(), nullable=False, comment='关联ID'),
        sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
        sa.Column('role_id', sa.Integer(), nullable=False, comment='角色ID'),
        sa.Column('assigned_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='分配时间'),
        sa.Column('assigned_by', sa.Integer(), nullable=True, comment='分配者ID'),
        sa.ForeignKeyConstraint(['assigned_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'role_id', name='uq_user_role')
    )
    op.create_index(op.f('ix_user_roles_id'), 'user_roles', ['id'], unique=False)
    op.create_index(op.f('ix_user_roles_role_id'), 'user_roles', ['role_id'], unique=False)
    op.create_index(op.f('ix_user_roles_user_id'), 'user_roles', ['user_id'], unique=False)

    # 创建角色权限关联表
    op.create_table('role_permissions',
        sa.Column('id', sa.Integer(), nullable=False, comment='关联ID'),
        sa.Column('role_id', sa.Integer(), nullable=False, comment='角色ID'),
        sa.Column('permission_id', sa.Integer(), nullable=False, comment='权限ID'),
        sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('role_id', 'permission_id', name='uq_role_permission')
    )
    op.create_index(op.f('ix_role_permissions_id'), 'role_permissions', ['id'], unique=False)
    op.create_index(op.f('ix_role_permissions_permission_id'), 'role_permissions', ['permission_id'], unique=False)
    op.create_index(op.f('ix_role_permissions_role_id'), 'role_permissions', ['role_id'], unique=False)

    # 插入默认角色
    op.execute("""
        INSERT INTO roles (name, description, is_active) VALUES
        ('superadmin', '超级管理员', true),
        ('admin', '管理员', true),
        ('moderator', '版主', true),
        ('user', '普通用户', true),
        ('guest', '访客', true);
    """)

    # 插入默认权限
    op.execute("""
        INSERT INTO permissions (name, description, category) VALUES
        -- 用户管理权限
        ('user:read', '查看用户', '用户管理'),
        ('user:create', '创建用户', '用户管理'),
        ('user:update', '更新用户', '用户管理'),
        ('user:delete', '删除用户', '用户管理'),
        
        -- 角色管理权限
        ('role:read', '查看角色', '角色管理'),
        ('role:create', '创建角色', '角色管理'),
        ('role:update', '更新角色', '角色管理'),
        ('role:delete', '删除角色', '角色管理'),
        
        -- 权限管理权限
        ('permission:read', '查看权限', '权限管理'),
        ('permission:create', '创建权限', '权限管理'),
        ('permission:update', '更新权限', '权限管理'),
        ('permission:delete', '删除权限', '权限管理'),
        
        -- 系统管理权限
        ('system:config', '系统配置', '系统管理'),
        ('system:monitor', '系统监控', '系统管理'),
        
        -- 内容管理权限
        ('content:read', '查看内容', '内容管理'),
        ('content:create', '创建内容', '内容管理'),
        ('content:update', '更新内容', '内容管理'),
        ('content:delete', '删除内容', '内容管理');
    """)

    # 为超级管理员分配所有权限
    op.execute("""
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r, permissions p
        WHERE r.name = 'superadmin';
    """)

    # 为管理员分配部分权限
    op.execute("""
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r, permissions p
        WHERE r.name = 'admin' AND p.name IN (
            'user:read', 'user:create', 'user:update',
            'role:read', 'content:read', 'content:create', 'content:update', 'content:delete'
        );
    """)

    # 为版主分配内容管理权限
    op.execute("""
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r, permissions p
        WHERE r.name = 'moderator' AND p.name IN (
            'content:read', 'content:create', 'content:update'
        );
    """)

    # 为普通用户分配基本权限
    op.execute("""
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r, permissions p
        WHERE r.name = 'user' AND p.name IN (
            'content:read'
        );
    """)

    # 为现有的超级用户分配超级管理员角色
    op.execute("""
        INSERT INTO user_roles (user_id, role_id)
        SELECT u.id, r.id
        FROM users u, roles r
        WHERE u.is_superuser = true AND r.name = 'superadmin';
    """)

    # 为其他用户分配普通用户角色
    op.execute("""
        INSERT INTO user_roles (user_id, role_id)
        SELECT u.id, r.id
        FROM users u, roles r
        WHERE u.is_superuser = false AND r.name = 'user'
        AND NOT EXISTS (
            SELECT 1 FROM user_roles ur WHERE ur.user_id = u.id
        );
    """)


def downgrade() -> None:
    # 删除表（按依赖关系逆序）
    op.drop_table('role_permissions')
    op.drop_table('user_roles')
    op.drop_table('permissions')
    op.drop_table('roles')
