/**
 * Morphik Core 主要操作 Hooks
 * 封装 Morphik API 调用逻辑，集成 TanStack Query 进行状态管理
 */
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import morphikApi from '@/services/morphikApi';
import type {
  DocumentUploadRequest,
  DocumentUploadResponse,
  SearchRequest,
  SearchResponse,
  RAGQueryRequest,
  RAGQueryResponse,
  ChunkRetrievalRequest,
  DocumentChunk,
  KnowledgeGraph,
  DocumentStats,
  SystemHealth,
  BatchUploadRequest,
  BatchUploadResponse,
  MorphikApiError,
  ChatMessage,
  ChatSessionMeta
} from '@/types/morphik';

// ==================== Query Keys ====================
export const morphikQueryKeys = {
  all: ['morphik'] as const,
  documents: () => [...morphikQueryKeys.all, 'documents'] as const,
  documentsList: (params?: any) => [...morphikQueryKeys.documents(), 'list', params] as const,
  document: (id: string) => [...morphikQueryKeys.documents(), 'detail', id] as const,
  stats: () => [...morphikQueryKeys.all, 'stats'] as const,
  health: () => [...morphikQueryKeys.all, 'health'] as const,
  knowledgeGraph: (filters?: any) => [...morphikQueryKeys.all, 'knowledge-graph', filters] as const,
  search: (query: string) => [...morphikQueryKeys.all, 'search', query] as const,
  // 聊天会话相关查询键
  chatSessions: () => [...morphikQueryKeys.all, 'chat', 'sessions'] as const,
  chatHistory: (chatId: string) => [...morphikQueryKeys.all, 'chat', 'history', chatId] as const,
  // 新增图谱相关查询键
  graphs: () => [...morphikQueryKeys.all, 'graphs'] as const,
  graph: (name: string) => [...morphikQueryKeys.all, 'graph', name] as const,
  graphVisualization: (name: string) => [...morphikQueryKeys.all, 'graph', name, 'visualization'] as const,
  graphWorkflowStatus: (workflowId: string, runId?: string) => [...morphikQueryKeys.all, 'graph', 'workflow', workflowId, runId] as const,
};

// ==================== 系统状态 Hooks ====================

/**
 * 获取系统健康状态
 */
export function useSystemHealth() {
  return useQuery({
    queryKey: morphikQueryKeys.health(),
    queryFn: () => morphikApi.getSystemHealth(),
    staleTime: 30 * 1000, // 30秒
    gcTime: 5 * 60 * 1000, // 5分钟
    retry: 2,
    retryDelay: 1000,
  });
}

/**
 * 获取文档统计
 */
export function useDocumentStats() {
  return useQuery({
    queryKey: morphikQueryKeys.stats(),
    queryFn: () => morphikApi.getDocumentStats(),
    staleTime: 60 * 1000, // 1分钟
    gcTime: 10 * 60 * 1000, // 10分钟
    retry: 1,
  });
}

/**
 * 测试 Morphik 连接
 */
export function useTestConnection() {
  return useMutation({
    mutationFn: () => morphikApi.testConnection(),
    onSuccess: (isConnected) => {
      if (isConnected) {
        toast.success('✅ Morphik 连接成功');
      } else {
        toast.error('❌ Morphik 连接失败');
      }
    },
    onError: (error: MorphikApiError) => {
      toast.error(`❌ 连接测试失败: ${error.message}`);
    },
  });
}

// ==================== 文档上传 Hooks ====================

/**
 * 单文档上传
 */
export function useUploadDocument() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: DocumentUploadRequest) => morphikApi.uploadDocument(request),
    onSuccess: (response: DocumentUploadResponse) => {
      // 刷新文档列表和统计
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.documents() });
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.stats() });

      toast.success(`📄 文档上传成功: ${response.document_id}`);
    },
    onError: (error: MorphikApiError) => {
      toast.error(`❌ 文档上传失败: ${error.message}`);
    },
  });
}

/**
 * 批量文档上传
 */
export function useBatchUploadDocuments() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: BatchUploadRequest) => morphikApi.batchUploadDocuments(request),
    onSuccess: (response: BatchUploadResponse) => {
      // 刷新文档列表和统计
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.documents() });
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.stats() });

      const { summary } = response;
      toast.success(
        `📄 批量上传完成: ${summary.successful}/${summary.total} 成功`
      );
    },
    onError: (error: MorphikApiError) => {
      toast.error(`❌ 批量上传失败: ${error.message}`);
    },
  });
}

// ==================== 搜索和检索 Hooks ====================

/**
 * 语义搜索
 */
export function useSearch() {
  return useMutation({
    mutationFn: (request: SearchRequest) => morphikApi.search(request),
    onError: (error: MorphikApiError) => {
      toast.error(`❌ 搜索失败: ${error.message}`);
    },
  });
}

/**
 * 文档块检索
 */
export function useRetrieveChunks() {
  return useMutation({
    mutationFn: (request: ChunkRetrievalRequest) => morphikApi.retrieveChunks(request),
    onError: (error: MorphikApiError) => {
      toast.error(`❌ 检索失败: ${error.message}`);
    },
  });
}

// ==================== RAG 问答 Hooks ====================

/**
 * RAG 查询 - 支持话术模板集成
 */
export function useRAGQuery() {
  return useMutation({
    mutationFn: ({ request, context }: {
      request: RAGQueryRequest;
      context?: 'knowledge-base' | 'chat' | 'general'
    }) => morphikApi.ragQuery(request, context || 'general'),
    onError: (error: MorphikApiError) => {
      toast.error(`❌ 查询失败: ${error.message}`);
    },
  });
}

// ==================== 聊天会话 Hooks ====================

/**
 * 获取聊天历史
 */
export function useChatHistory(chatId: string, enabled = true) {
  return useQuery({
    queryKey: morphikQueryKeys.chatHistory(chatId),
    queryFn: () => morphikApi.getChatHistory(chatId),
    staleTime: 1 * 60 * 1000, // 1分钟
    gcTime: 5 * 60 * 1000, // 5分钟
    enabled: enabled && !!chatId,
    retry: 1,
  });
}

/**
 * 获取聊天会话列表
 */
export function useChatSessions(limit = 100) {
  return useQuery({
    queryKey: morphikQueryKeys.chatSessions(),
    queryFn: () => morphikApi.getChatSessions(limit),
    staleTime: 2 * 60 * 1000, // 2分钟
    gcTime: 10 * 60 * 1000, // 10分钟
    retry: 1,
  });
}

// ==================== 知识图谱 Hooks ====================

/**
 * 创建知识图谱
 */
export function useCreateGraph() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: { name: string; filters?: Record<string, any>; documents?: string[] }) =>
      morphikApi.createGraph(request),
    onSuccess: () => {
      // 刷新图谱列表
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.graphs() });
      toast.success('✅ 知识图谱创建成功');
    },
    onError: (error: any) => {
      console.error('创建图谱失败 - 完整错误:', error);
      let errorMessage = '未知错误';

      if (error?.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // 特殊处理重复名称错误
      if (errorMessage.includes('duplicate key') || errorMessage.includes('already exists')) {
        errorMessage = '图谱名称已存在，请使用不同的名称';
      }

      toast.error(`❌ 创建图谱失败: ${errorMessage}`);
    },
  });
}

/**
 * 获取图谱列表
 */
export function useGraphs() {
  return useQuery({
    queryKey: morphikQueryKeys.graphs(),
    queryFn: () => morphikApi.getGraphs(),
    staleTime: 0, // 总是获取最新数据
    gcTime: 1 * 60 * 1000, // 1分钟
    retry: 1,
    refetchOnWindowFocus: true, // 窗口聚焦时重新获取
    refetchOnMount: true, // 组件挂载时重新获取
  });
}

/**
 * 获取指定图谱
 */
export function useGraph(name: string, enabled = true) {
  return useQuery({
    queryKey: morphikQueryKeys.graph(name),
    queryFn: () => morphikApi.getGraph(name),
    staleTime: 5 * 60 * 1000, // 5分钟
    gcTime: 15 * 60 * 1000, // 15分钟
    enabled: enabled && !!name,
    retry: 1,
  });
}

/**
 * 获取图谱可视化数据
 */
export function useGraphVisualization(name: string, enabled = true) {
  return useQuery({
    queryKey: morphikQueryKeys.graphVisualization(name),
    queryFn: () => morphikApi.getGraphVisualization(name),
    staleTime: 5 * 60 * 1000, // 5分钟
    gcTime: 15 * 60 * 1000, // 15分钟
    enabled: enabled && !!name,
    retry: 1,
  });
}

/**
 * 更新图谱
 */
export function useUpdateGraph() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ name, ...request }: { name: string; additional_filters?: Record<string, any>; additional_documents?: string[] }) =>
      morphikApi.updateGraph(name, request),
    onSuccess: (_, variables) => {
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.graph(variables.name) });
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.graphVisualization(variables.name) });
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.graphs() });
      toast.success('✅ 知识图谱更新成功');
    },
    onError: (error: MorphikApiError) => {
      toast.error(`❌ 更新图谱失败: ${error.message}`);
    },
  });
}

/**
 * 删除图谱
 */
export function useDeleteGraph() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (name: string) => morphikApi.deleteGraph(name),
    onSuccess: (_, name) => {
      // 刷新图谱列表
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.graphs() });
      // 移除特定图谱的缓存
      queryClient.removeQueries({ queryKey: morphikQueryKeys.graph(name) });
      queryClient.removeQueries({ queryKey: morphikQueryKeys.graphVisualization(name) });
      toast.success('🗑️ 知识图谱删除成功');
    },
    onError: (error: MorphikApiError) => {
      toast.error(`❌ 删除图谱失败: ${error.message}`);
    },
  });
}

/**
 * 获取图谱构建状态
 */
export function useGraphWorkflowStatus(workflowId: string, runId?: string, enabled = true) {
  return useQuery({
    queryKey: morphikQueryKeys.graphWorkflowStatus(workflowId, runId),
    queryFn: () => morphikApi.getGraphWorkflowStatus(workflowId, runId),
    staleTime: 0, // 总是获取最新状态
    gcTime: 1 * 60 * 1000, // 1分钟
    enabled: enabled && !!workflowId,
    refetchInterval: (query) => {
      // 如果状态是处理中，每5秒轮询一次
      return query.state.data?.status === 'processing' ? 5000 : false;
    },
    retry: 1,
  });
}

/**
 * 增强的图谱状态轮询Hook
 * 支持多个图谱同时轮询，自动停止已完成的轮询
 */
export function useGraphStatusPolling(graphs: any[] = [], options: {
  enabled?: boolean;
  pollInterval?: number;
  onStatusChange?: (graphName: string, oldStatus: string, newStatus: string) => void;
} = {}) {
  const { enabled = true, pollInterval = 3000, onStatusChange } = options;
  const queryClient = useQueryClient();

  // 获取处理中的图谱
  const processingGraphs = graphs.filter(graph =>
    graph.status === 'processing'
  );

  return useQuery({
    queryKey: ['graph-status-polling', processingGraphs.map(g => g.name).sort()],
    queryFn: async () => {
      if (processingGraphs.length === 0) return [];

      const statusChecks = processingGraphs.map(async (graph) => {
        try {
          // 如果有workflow_id，检查工作流状态
          if (graph.system_metadata?.workflow_id) {
            const workflowStatus = await morphikApi.getGraphWorkflowStatus(
              graph.system_metadata.workflow_id,
              graph.system_metadata.run_id
            );

            const newStatus = workflowStatus.status === 'completed' ? 'completed' :
                             workflowStatus.status === 'failed' ? 'failed' : 'processing';

            const oldStatus = graph.status || 'unknown';
            const changed = oldStatus !== newStatus;

            // 如果状态改变，触发回调并刷新图谱列表
            if (changed) {
              onStatusChange?.(graph.name, oldStatus, newStatus);
              queryClient.invalidateQueries({ queryKey: morphikQueryKeys.graphs() });
            }

            return {
              name: graph.name,
              status: newStatus,
              changed,
              progress: workflowStatus.progress,
              message: workflowStatus.message
            };
          } else {
            // 没有workflow_id，直接检查图谱状态
            const updatedGraph = await morphikApi.getGraph(graph.name);
            const newStatus = updatedGraph.status || 'unknown';
            const oldStatus = graph.status || 'unknown';
            const changed = oldStatus !== newStatus;

            if (changed) {
              onStatusChange?.(graph.name, oldStatus, newStatus);
              queryClient.invalidateQueries({ queryKey: morphikQueryKeys.graphs() });
            }

            return {
              name: graph.name,
              status: newStatus,
              changed
            };
          }
        } catch (error) {
          console.error(`Failed to check status for graph ${graph.name}:`, error);
          return {
            name: graph.name,
            status: 'error',
            changed: true,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      });

      return Promise.all(statusChecks);
    },
    enabled: enabled && processingGraphs.length > 0,
    refetchInterval: pollInterval,
    staleTime: 0,
    gcTime: 30 * 1000, // 30秒
  });
}

/**
 * 获取知识图谱 (兼容旧接口)
 */
export function useKnowledgeGraph(filters?: any) {
  return useQuery({
    queryKey: morphikQueryKeys.knowledgeGraph(filters),
    queryFn: () => morphikApi.getKnowledgeGraph(filters),
    staleTime: 5 * 60 * 1000, // 5分钟
    gcTime: 15 * 60 * 1000, // 15分钟
    enabled: true,
    retry: 1,
  });
}

// ==================== 文档删除 Hook ====================

/**
 * 删除文档
 */
export function useDeleteDocument() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (documentId: string) => morphikApi.deleteDocument(documentId),
    onSuccess: (_, documentId) => {
      // 刷新相关查询
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.documents() });
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.stats() });

      // 移除特定文档的缓存
      queryClient.removeQueries({ queryKey: morphikQueryKeys.document(documentId) });

      toast.success('🗑️ 文档删除成功');
    },
    onError: (error: MorphikApiError) => {
      toast.error(`❌ 删除失败: ${error.message}`);
    },
  });
}

// ==================== 复合 Hooks ====================

/**
 * 智能搜索 Hook (结合搜索和RAG)
 */
export function useSmartSearch() {
  const searchMutation = useSearch();
  const ragMutation = useRAGQuery();

  const performSmartSearch = async (query: string, options?: {
    includeRAG?: boolean;
    searchFilters?: any;
    ragOptions?: Partial<RAGQueryRequest>;
  }) => {
    const { includeRAG = false, searchFilters, ragOptions } = options || {};

    try {
      // 执行语义搜索
      const searchRequest: SearchRequest = {
        query,
        filters: searchFilters,
        limit: 10,
        similarity_threshold: 0.7,
        include_metadata: true,
      };

      const searchResults = await searchMutation.mutateAsync(searchRequest);

      // 如果需要RAG回答
      let ragResponse: RAGQueryResponse | undefined;
      if (includeRAG) {
        const ragRequest: RAGQueryRequest = {
          query,
          filters: searchFilters,
          max_chunks: 5,
          include_sources: true,
          ...ragOptions,
        };

        ragResponse = await ragMutation.mutateAsync({
          request: ragRequest,
          context: 'knowledge-base'
        });
      }

      return {
        searchResults,
        ragResponse,
      };
    } catch (error) {
      throw error;
    }
  };

  return {
    performSmartSearch,
    isSearching: searchMutation.isPending,
    isGeneratingAnswer: ragMutation.isPending,
    isLoading: searchMutation.isPending || ragMutation.isPending,
    searchError: searchMutation.error,
    ragError: ragMutation.error,
    reset: () => {
      searchMutation.reset();
      ragMutation.reset();
    },
  };
}

// ==================== 实用 Hooks ====================

/**
 * Morphik 配置更新
 */
export function useMorphikConfig() {
  const updateConfig = (newConfig: any) => {
    morphikApi.updateConfig(newConfig);
    toast.success('⚙️ Morphik 配置已更新');
  };

  return { updateConfig };
}

/**
 * 批量操作状态管理
 */
export function useBatchOperations() {
  const queryClient = useQueryClient();

  const invalidateAll = () => {
    queryClient.invalidateQueries({ queryKey: morphikQueryKeys.all });
  };

  const clearCache = () => {
    queryClient.clear();
    toast.success('🧹 缓存已清理');
  };

  return {
    invalidateAll,
    clearCache,
  };
}

// morphikQueryKeys 已在文件开头导出
