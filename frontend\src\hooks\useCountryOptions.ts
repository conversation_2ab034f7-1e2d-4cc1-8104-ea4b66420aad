import { useState, useEffect } from 'react'
import { Flag, Globe } from 'lucide-react'
import type { SelectorOption } from '@/components/ProductSelector'

// 默认国家选项 - 使用固定时间戳避免重新渲染问题
const FIXED_TIMESTAMP = '2024-01-01T00:00:00.000Z'

const DEFAULT_COUNTRIES: SelectorOption[] = [
  {
    id: 'country_1',
    value: 'usa',
    label: '美国',
    icon: Flag,
    iconName: 'Flag',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'country_2',
    value: 'france',
    label: '法国',
    icon: Flag,
    iconName: 'Flag',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'country_3',
    value: 'italy',
    label: '意大利',
    icon: Flag,
    iconName: 'Flag',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'country_4',
    value: 'saudi-arabia',
    label: '沙特',
    icon: Flag,
    iconName: 'Flag',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'country_5',
    value: 'brazil',
    label: '巴西',
    icon: Flag,
    iconName: 'Flag',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'country_6',
    value: 'russia',
    label: '俄罗斯',
    icon: Flag,
    iconName: 'Flag',
    createdAt: FIXED_TIMESTAMP
  }
]

const STORAGE_KEY = 'group-analysis-country-options'

export interface UseCountryOptionsReturn {
  countryOptions: SelectorOption[]
  addCountryOption: (label: string) => Promise<{ success: boolean; error?: string; value?: string }>
  removeCountryOption: (countryId: string) => Promise<{ success: boolean; error?: string }>
  isLoading: boolean
}

export const useCountryOptions = (): UseCountryOptionsReturn => {
  const [countryOptions, setCountryOptions] = useState<SelectorOption[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // 初始化加载选项
  useEffect(() => {
    const loadOptions = () => {
      try {
        const stored = localStorage.getItem(STORAGE_KEY)
        if (stored) {
          const parsedOptions = JSON.parse(stored)
          // 验证数据结构并恢复图标引用
          if (Array.isArray(parsedOptions) && parsedOptions.length > 0) {
            const optionsWithIcons = parsedOptions.map((option: any) => ({
              ...option,
              icon: option.iconName === 'Flag' ? Flag : Globe
            }))
            setCountryOptions(optionsWithIcons)
          } else {
            // 如果存储的数据无效，使用默认选项
            setCountryOptions(DEFAULT_COUNTRIES)
            localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_COUNTRIES))
          }
        } else {
          // 首次使用，设置默认选项
          setCountryOptions(DEFAULT_COUNTRIES)
          localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_COUNTRIES))
        }
      } catch (error) {
        console.error('加载国家选项失败:', error)
        // 出错时使用默认选项
        setCountryOptions(DEFAULT_COUNTRIES)
        localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_COUNTRIES))
      } finally {
        setIsLoading(false)
      }
    }

    loadOptions()
  }, [])

  // 保存选项到localStorage
  const saveOptions = (options: SelectorOption[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(options))
      return true
    } catch (error) {
      console.error('保存国家选项失败:', error)
      return false
    }
  }

  // 添加新的国家选项
  const addCountryOption = async (label: string): Promise<{ success: boolean; error?: string; value?: string }> => {
    try {
      // 检查是否已存在相同标签
      const existingOption = countryOptions.find(option => 
        option.label.toLowerCase() === label.toLowerCase()
      )
      
      if (existingOption) {
        return { success: false, error: '该国家选项已存在' }
      }

      // 生成新选项
      const newOption: SelectorOption = {
        id: `country_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        value: label.toLowerCase().replace(/\s+/g, '-'),
        label: label,
        icon: Globe, // 新添加的使用Globe图标
        iconName: 'Globe',
        createdAt: new Date().toISOString()
      }

      const updatedOptions = [...countryOptions, newOption]
      
      if (saveOptions(updatedOptions)) {
        setCountryOptions(updatedOptions)
        return { success: true, value: newOption.value }
      } else {
        return { success: false, error: '保存失败' }
      }
    } catch (error) {
      console.error('添加国家选项失败:', error)
      return { success: false, error: '添加过程中发生错误' }
    }
  }

  // 删除国家选项
  const removeCountryOption = async (countryId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // 防止删除最后一个选项
      if (countryOptions.length <= 1) {
        return { success: false, error: '至少需要保留一个国家选项' }
      }

      const updatedOptions = countryOptions.filter(option => option.id !== countryId)
      
      if (saveOptions(updatedOptions)) {
        setCountryOptions(updatedOptions)
        return { success: true }
      } else {
        return { success: false, error: '保存失败' }
      }
    } catch (error) {
      console.error('删除国家选项失败:', error)
      return { success: false, error: '删除过程中发生错误' }
    }
  }

  return {
    countryOptions,
    addCountryOption,
    removeCountryOption,
    isLoading
  }
}

export default useCountryOptions
