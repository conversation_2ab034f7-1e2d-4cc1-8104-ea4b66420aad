"""
文件上传服务
处理用户头像等文件的上传、存储和管理
"""
import os
import uuid
import shutil
from pathlib import Path
from typing import Optional, Tuple
from fastapi import UploadFile, HTTPException, status
import logging

logger = logging.getLogger(__name__)


class FileUploadService:
    """文件上传服务类"""
    
    def __init__(self):
        # 上传目录配置
        self.upload_dir = Path("/tmp/uploads")
        self.avatar_dir = self.upload_dir / "avatars"
        
        # 创建上传目录
        self.avatar_dir.mkdir(parents=True, exist_ok=True)
        
        # 允许的文件类型和大小
        self.allowed_image_types = {
            "image/jpeg": ".jpg",
            "image/png": ".png", 
            "image/gif": ".gif",
            "image/webp": ".webp"
        }
        self.max_file_size = 2 * 1024 * 1024  # 2MB
    
    def validate_image_file(self, file: UploadFile) -> None:
        """验证图片文件"""
        # 检查文件类型
        if file.content_type not in self.allowed_image_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的文件类型。支持的类型：{', '.join(self.allowed_image_types.keys())}"
            )
        
        # 检查文件大小
        if file.size and file.size > self.max_file_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"文件大小超过限制。最大允许：{self.max_file_size // (1024*1024)}MB"
            )
    
    def generate_filename(self, user_id: int, content_type: str) -> str:
        """生成唯一的文件名"""
        extension = self.allowed_image_types[content_type]
        unique_id = str(uuid.uuid4())
        return f"user_{user_id}_{unique_id}{extension}"
    
    async def save_avatar(self, user_id: int, file: UploadFile) -> str:
        """
        保存用户头像
        
        Args:
            user_id: 用户ID
            file: 上传的文件
            
        Returns:
            str: 头像文件的相对路径
        """
        try:
            # 验证文件
            self.validate_image_file(file)
            
            # 创建用户专属目录
            user_avatar_dir = self.avatar_dir / str(user_id)
            user_avatar_dir.mkdir(exist_ok=True)
            
            # 删除用户的旧头像
            await self.delete_user_avatars(user_id)
            
            # 生成新文件名
            filename = self.generate_filename(user_id, file.content_type)
            file_path = user_avatar_dir / filename
            
            # 保存文件
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            # 返回相对路径
            relative_path = f"/uploads/avatars/{user_id}/{filename}"
            logger.info(f"头像保存成功: {relative_path}")
            
            return relative_path
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"保存头像失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="保存头像失败"
            )
    
    async def delete_user_avatars(self, user_id: int) -> None:
        """删除用户的所有头像文件"""
        try:
            user_avatar_dir = self.avatar_dir / str(user_id)
            if user_avatar_dir.exists():
                # 删除目录中的所有文件
                for file_path in user_avatar_dir.iterdir():
                    if file_path.is_file():
                        file_path.unlink()
                        logger.info(f"删除旧头像: {file_path}")
        except Exception as e:
            logger.warning(f"删除旧头像失败: {e}")
    
    async def delete_avatar(self, user_id: int, avatar_url: Optional[str] = None) -> bool:
        """
        删除用户头像
        
        Args:
            user_id: 用户ID
            avatar_url: 头像URL（可选）
            
        Returns:
            bool: 删除是否成功
        """
        try:
            if avatar_url:
                # 从URL中提取文件路径
                if avatar_url.startswith("/uploads/avatars/"):
                    file_path = self.upload_dir / avatar_url.lstrip("/")
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"删除头像: {file_path}")
                        return True
            else:
                # 删除用户的所有头像
                await self.delete_user_avatars(user_id)
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"删除头像失败: {e}")
            return False
    
    def get_avatar_url(self, user_id: int) -> Optional[str]:
        """获取用户头像URL"""
        try:
            user_avatar_dir = self.avatar_dir / str(user_id)
            if user_avatar_dir.exists():
                # 查找用户的头像文件
                for file_path in user_avatar_dir.iterdir():
                    if file_path.is_file():
                        return f"/uploads/avatars/{user_id}/{file_path.name}"
            return None
        except Exception as e:
            logger.error(f"获取头像URL失败: {e}")
            return None


# 创建全局实例
file_upload_service = FileUploadService()
