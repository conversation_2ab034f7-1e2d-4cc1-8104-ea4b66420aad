/**
 * Morphik Core API 类型定义
 * 基于 Morphik Core 官方文档和 API 规范
 */

// ==================== 基础类型 ====================

export interface MorphikResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  skip?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  skip: number;
  limit: number;
}

// ==================== 文档相关类型 ====================

export interface Document {
  external_id: string;
  owner?: {
    id: string;
    type: string;
  };
  content_type: string;
  filename?: string;
  metadata: DocumentMetadata;
  storage_info?: Record<string, any>;
  storage_files?: Array<{
    bucket: string;
    key: string;
    version: number;
    filename: string;
    content_type: string;
    timestamp: string;
  }>;
  system_metadata?: {
    status?: DocumentStatus;
    content?: string;
    version?: number;
    created_at?: string;
    updated_at?: string;
    end_user_id?: string;
    folder_name?: string;
    [key: string]: any;
  };
  additional_metadata?: Record<string, any>;
  access_control?: Record<string, any>;
  chunk_ids?: string[];
}

export interface DocumentMetadata {
  title?: string;
  author?: string;
  category?: string;
  tags?: string[];
  source?: string;
  language?: string;
  [key: string]: any;
}

export type DocumentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'deleted';

export interface DocumentUploadRequest {
  file?: File;
  content?: string;
  metadata?: DocumentMetadata;
  title?: string;
}

export interface DocumentUploadResponse {
  document_id: string;
  status: DocumentStatus;
  message?: string;
}

// ==================== 搜索和检索类型 ====================

export interface SearchRequest {
  query: string;
  filters?: SearchFilters;
  limit?: number;
  similarity_threshold?: number;
  include_metadata?: boolean;
}

export interface SearchFilters {
  category?: string;
  tags?: string[];
  date_range?: {
    start?: string;
    end?: string;
  };
  file_type?: string;
  [key: string]: any;
}

export interface SearchResult {
  id: string;
  content: string;
  metadata: DocumentMetadata;
  similarity_score: number;
  document_id: string;
  chunk_index?: number;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  query: string;
  execution_time: number;
}

// ==================== RAG 问答类型 ====================

export interface RAGQueryRequest {
  query: string;
  filters?: SearchFilters;
  max_chunks?: number;
  temperature?: number;
  model_name?: string;
  include_sources?: boolean;
  graph_name?: string; // 知识图谱名称，用于图谱增强检索
  chat_id?: string; // 聊天会话ID，用于多轮对话上下文关联
  prompt_overrides?: any; // 话术模板提示词覆盖
  max_tokens?: number; // 最大令牌数
}

export interface RAGQueryResponse {
  completion: string;
  sources?: SearchResult[];
  model_used?: string;
  execution_time: number;
  token_usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// ==================== 聊天会话类型 ====================

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  agent_data?: {
    display_objects?: Array<{
      type: string;
      content: string;
      source?: string;
      caption?: string;
    }>;
    tool_history?: any[];
    sources?: any[];
  };
}

export interface ChatSession {
  chat_id: string;
  messages: ChatMessage[];
  created_at: string;
  updated_at: string;
  title?: string; // 会话标题，可以从第一条消息生成
}

export interface ChatSessionMeta {
  conversation_id: string;
  updated_at: string;
  last_message?: {
    role: string;
    content: string;
    agent_data?: any;
  };
}

// ==================== 文档块类型 ====================

export interface DocumentChunk {
  id: string;
  document_id: string;
  content: string;
  metadata: DocumentMetadata;
  chunk_index: number;
  embedding?: number[];
  created_at: string;
}

export interface ChunkRetrievalRequest {
  query: string;
  filters?: SearchFilters;
  limit?: number;
  similarity_threshold?: number;
}

// ==================== 知识图谱类型 ====================

export interface KnowledgeGraphNode {
  id: string;
  label: string;
  type: string;
  properties: Record<string, any>;
  document_ids: string[];
}

export interface KnowledgeGraphEdge {
  id: string;
  source: string;
  target: string;
  relationship: string;
  weight: number;
  properties: Record<string, any>;
}

export interface KnowledgeGraph {
  nodes: KnowledgeGraphNode[];
  edges: KnowledgeGraphEdge[];
  metadata: {
    total_nodes: number;
    total_edges: number;
    created_at: string;
    updated_at: string;
  };
}

// ==================== 统计和监控类型 ====================

export interface DocumentStats {
  total_documents: number;
  total_chunks: number;
  processing_queue: number;
  failed_documents: number;
  storage_used: number;
  by_status: Record<DocumentStatus, number>;
  by_type: Record<string, number>;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  services: {
    api: boolean;
    database: boolean;
    vector_store: boolean;
    embedding_service: boolean;
    completion_service: boolean;
  };
  metrics: {
    response_time: number;
    memory_usage: number;
    cpu_usage: number;
    disk_usage: number;
  };
}

// ==================== 错误类型 ====================

export interface MorphikError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

export class MorphikApiError extends Error {
  constructor(
    public code: string,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'MorphikApiError';
  }
}

// ==================== API 配置类型 ====================

export interface MorphikApiConfig {
  baseUrl: string;
  timeout?: number;
  retries?: number;
  apiKey?: string;
  headers?: Record<string, string>;
}

// ==================== 批量操作类型 ====================

export interface BatchUploadRequest {
  files: File[];
  metadata?: DocumentMetadata;
  parallel_processing?: boolean;
}

export interface BatchUploadResponse {
  results: DocumentUploadResponse[];
  summary: {
    total: number;
    successful: number;
    failed: number;
    processing: number;
  };
}

// ==================== 导出所有类型 ====================
// 所有类型已在上面定义时直接导出，无需重复导出
