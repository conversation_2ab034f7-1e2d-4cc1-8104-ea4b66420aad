"""
用户资料管理Pydantic模式
定义用户资料相关API请求和响应的数据结构
"""
from pydantic import BaseModel, Field, EmailStr
from typing import Optional


class ProfileUpdate(BaseModel):
    """更新用户资料的请求模式"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    phone: Optional[str] = Field(None, max_length=20, description="电话号码")


class ProfileResponse(BaseModel):
    """用户资料响应模式"""
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, description="全名")
    phone: Optional[str] = Field(None, description="电话号码")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    is_active: bool = Field(..., description="是否激活")
    is_superuser: bool = Field(..., description="是否超级用户")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")


class AvatarUploadResponse(BaseModel):
    """头像上传响应模式"""
    message: str = Field(..., description="响应消息")
    avatar_url: str = Field(..., description="头像URL")


class AvatarDeleteResponse(BaseModel):
    """头像删除响应模式"""
    message: str = Field(..., description="响应消息")


class PasswordChangeRequest(BaseModel):
    """修改密码请求模式"""
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=6, max_length=50, description="新密码")
    confirm_password: str = Field(..., description="确认新密码")


class PasswordChangeResponse(BaseModel):
    """修改密码响应模式"""
    message: str = Field(..., description="响应消息")
