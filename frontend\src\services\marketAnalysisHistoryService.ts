/**
 * 全球市场分析历史记录存储服务
 * 使用 localStorage 进行本地存储
 */

import { MarketAnalysisRecord, AnalysisHistoryStats, HistoryFilterOptions } from '@/types/marketAnalysis';

const STORAGE_KEY = 'market_analysis_history';
const MAX_RECORDS = 100; // 最大存储记录数

export class MarketAnalysisHistoryService {
  /**
   * 保存分析记录
   */
  static saveAnalysisRecord(record: MarketAnalysisRecord): void {
    try {
      // 临时调试：检查保存的记录
      console.log('💾 保存历史记录调试:', {
        recordId: record.id,
        duration: record.duration,
        durationType: typeof record.duration,
        status: record.status,
        productType: record.productType
      });

      const existingRecords = this.getAllRecords();

      // 检查是否已存在相同ID的记录，如果存在则更新
      const existingIndex = existingRecords.findIndex(r => r.id === record.id);
      if (existingIndex >= 0) {
        existingRecords[existingIndex] = record;
      } else {
        // 添加新记录到开头
        existingRecords.unshift(record);
      }

      // 限制记录数量
      if (existingRecords.length > MAX_RECORDS) {
        existingRecords.splice(MAX_RECORDS);
      }

      localStorage.setItem(STORAGE_KEY, JSON.stringify(existingRecords));

      // 验证保存后的数据
      const savedRecords = this.getAllRecords();
      const savedRecord = savedRecords.find(r => r.id === record.id);
      console.log('💾 保存后验证:', {
        found: !!savedRecord,
        savedDuration: savedRecord?.duration,
        savedDurationType: typeof savedRecord?.duration
      });
    } catch (error) {
      console.error('保存分析记录失败:', error);
    }
  }

  /**
   * 获取所有记录
   */
  static getAllRecords(): MarketAnalysisRecord[] {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('获取历史记录失败:', error);
      return [];
    }
  }

  /**
   * 根据ID获取单条记录
   */
  static getRecordById(id: string): MarketAnalysisRecord | null {
    const records = this.getAllRecords();
    return records.find(record => record.id === id) || null;
  }

  /**
   * 删除单条记录
   */
  static deleteRecord(id: string): boolean {
    try {
      const records = this.getAllRecords();
      const filteredRecords = records.filter(record => record.id !== id);
      
      if (filteredRecords.length !== records.length) {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredRecords));
        return true;
      }
      return false;
    } catch (error) {
      console.error('删除记录失败:', error);
      return false;
    }
  }

  /**
   * 批量删除记录
   */
  static deleteMultipleRecords(ids: string[]): number {
    try {
      const records = this.getAllRecords();
      const filteredRecords = records.filter(record => !ids.includes(record.id));
      const deletedCount = records.length - filteredRecords.length;
      
      localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredRecords));
      return deletedCount;
    } catch (error) {
      console.error('批量删除记录失败:', error);
      return 0;
    }
  }

  /**
   * 清空所有记录
   */
  static clearAllRecords(): void {
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('清空记录失败:', error);
    }
  }

  /**
   * 获取过滤后的记录
   */
  static getFilteredRecords(options: HistoryFilterOptions): MarketAnalysisRecord[] {
    let records = this.getAllRecords();

    // 状态过滤
    if (options.status && options.status !== 'all') {
      records = records.filter(record => record.status === options.status);
    }

    // 产品类型过滤
    if (options.productType) {
      records = records.filter(record => 
        record.productType.toLowerCase().includes(options.productType!.toLowerCase()) ||
        record.productLabel.toLowerCase().includes(options.productType!.toLowerCase())
      );
    }

    // 日期范围过滤
    if (options.dateRange) {
      const startDate = new Date(options.dateRange.start);
      const endDate = new Date(options.dateRange.end);
      records = records.filter(record => {
        const recordDate = new Date(record.timestamp);
        return recordDate >= startDate && recordDate <= endDate;
      });
    }

    // 排序
    const sortBy = options.sortBy || 'timestamp';
    const sortOrder = options.sortOrder || 'desc';
    
    records.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'timestamp':
          aValue = new Date(a.timestamp);
          bValue = new Date(b.timestamp);
          break;
        case 'productType':
          aValue = a.productLabel;
          bValue = b.productLabel;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        default:
          aValue = a.timestamp;
          bValue = b.timestamp;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return records;
  }

  /**
   * 获取历史统计信息
   */
  static getHistoryStats(): AnalysisHistoryStats {
    const records = this.getAllRecords();
    
    if (records.length === 0) {
      return {
        totalAnalyses: 0,
        successfulAnalyses: 0,
        failedAnalyses: 0,
        averageDuration: 0,
        mostAnalyzedProduct: '',
        recentAnalysisDate: ''
      };
    }

    const successfulRecords = records.filter(r => r.status === 'success');
    const failedRecords = records.filter(r => r.status === 'failed');
    
    // 计算平均耗时
    const recordsWithDuration = records.filter(r => r.duration);
    const averageDuration = recordsWithDuration.length > 0 
      ? recordsWithDuration.reduce((sum, r) => sum + (r.duration || 0), 0) / recordsWithDuration.length
      : 0;

    // 找出最常分析的产品
    const productCounts: { [key: string]: number } = {};
    records.forEach(record => {
      productCounts[record.productLabel] = (productCounts[record.productLabel] || 0) + 1;
    });
    
    const mostAnalyzedProduct = Object.keys(productCounts).reduce((a, b) => 
      productCounts[a] > productCounts[b] ? a : b, ''
    );

    // 最近分析日期
    const recentAnalysisDate = records.length > 0 ? records[0].timestamp : '';

    return {
      totalAnalyses: records.length,
      successfulAnalyses: successfulRecords.length,
      failedAnalyses: failedRecords.length,
      averageDuration: Math.round(averageDuration),
      mostAnalyzedProduct,
      recentAnalysisDate
    };
  }

  /**
   * 导出历史记录为JSON
   */
  static exportRecords(): string {
    const records = this.getAllRecords();
    return JSON.stringify(records, null, 2);
  }

  /**
   * 从JSON导入历史记录
   */
  static importRecords(jsonData: string): boolean {
    try {
      const records = JSON.parse(jsonData) as MarketAnalysisRecord[];
      
      // 验证数据格式
      if (!Array.isArray(records)) {
        throw new Error('数据格式不正确');
      }

      // 简单验证每条记录的必要字段
      records.forEach(record => {
        if (!record.id || !record.productType || !record.timestamp) {
          throw new Error('记录缺少必要字段');
        }
      });

      localStorage.setItem(STORAGE_KEY, JSON.stringify(records));
      return true;
    } catch (error) {
      console.error('导入记录失败:', error);
      return false;
    }
  }
}

export default MarketAnalysisHistoryService;
