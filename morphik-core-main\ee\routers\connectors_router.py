import json
import logging
from typing import Any, Dict, List, Optional, Union

import arq  # Added for Redis
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import HTMLResponse, RedirectResponse
from pydantic import BaseModel

from core.auth_utils import verify_token

# 尝试从core.api导入全局document_service和redis_pool依赖
# 这是一个简化方案；更健壮的解决方案可能使用app.state或专用的依赖模块
from core.dependencies import get_document_service, get_redis_pool
from core.models.auth import AuthContext

# 导入DocumentService类型用于依赖注入提示
from core.services.document_service import DocumentService
from ee.services.connector_service import ConnectorService
from ee.services.connectors.base_connector import ConnectorAuthStatus, ConnectorFile  # 导入特定模型

# from starlette.datastructures import URL  # oauth2callback需要使用


logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/ee/connectors",
    tags=["EE - Connectors"],
)


# 获取ConnectorService的依赖
async def get_connector_service(auth: AuthContext = Depends(verify_token)) -> ConnectorService:
    # 应该被verify_token捕获，但作为保护措施
    if not auth.user_id and not auth.entity_id:
        logger.error("get_connector_service中AuthContext缺少user_id和entity_id。")
        raise HTTPException(status_code=401, detail="Invalid authentication context.")
    try:
        return ConnectorService(auth_context=auth)
    except ValueError as e:
        logger.error(f"初始化ConnectorService失败: {e}")
        # 用户友好的错误消息
        raise HTTPException(status_code=500, detail="Connector service initialization error.")


# IngestFromConnectorRequest Pydantic模型的占位符
class IngestFromConnectorRequest(BaseModel):
    file_id: str
    morphik_folder_name: Optional[str] = None
    morphik_end_user_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None  # 自定义元数据的新字段
    rules: Optional[List[Dict[str, Any]]] = None  # 自定义规则的新字段


# 添加手动凭据的请求模型
class ManualCredentialsRequest(BaseModel):
    """手动凭据提交的请求模型。"""

    credentials: Dict[str, Any]


# 认证启动响应的模型
class CredentialFieldOption(BaseModel):
    value: str
    label: str


class CredentialField(BaseModel):
    name: str
    label: str
    description: str
    type: str  # "text", "password", "select"
    required: bool
    options: Optional[List[CredentialFieldOption]] = None


class ManualCredentialsAuthResponse(BaseModel):
    auth_type: str  # "manual_credentials"
    required_fields: List[CredentialField]
    instructions: Optional[str] = None


class OAuthAuthResponse(BaseModel):
    authorization_url: str


# 认证响应的联合类型
AuthInitiateResponse = Union[ManualCredentialsAuthResponse, OAuthAuthResponse]


# 端点将在下面添加


@router.get("/{connector_type}/auth_status", response_model=ConnectorAuthStatus)
async def get_auth_status_for_connector(
    connector_type: str, connector_service: ConnectorService = Depends(get_connector_service)
):
    """检查给定连接器类型的当前身份验证状态。"""
    try:
        connector = await connector_service.get_connector(connector_type)
        status = await connector.get_auth_status()
        return status
    except ValueError as e:
        # 处理连接器类型不受支持或其他初始化错误的情况
        logger.error(
            f"为用户 {connector_service.user_identifier} 获取 {connector_type} 认证状态时出现值错误: {e}"
        )
        raise HTTPException(status_code=404, detail=str(e))
    except ConnectionError as e:
        # 处理连接器本身连接问题的情况（例如，如果提前检查外部服务）
        logger.error(f"用户 {connector_service.user_identifier} 的 {connector_type} 连接错误: {e}")
        raise HTTPException(status_code=503, detail=f"Connector service unavailable: {str(e)}")
    except Exception as e:
        logger.exception(
            f"为用户 {connector_service.user_identifier} 获取 {connector_type} 认证状态时发生意外错误: {e}"
        )
        raise HTTPException(status_code=500, detail="An internal server error occurred.")


@router.get("/{connector_type}/auth/initiate_url", response_model=AuthInitiateResponse)
async def get_initiate_auth_url(
    request: Request,
    connector_type: str,
    app_redirect_uri: Optional[str] = None,
    service: ConnectorService = Depends(get_connector_service),
):
    """返回给定连接器的提供商*authorization_url*。

    该方法镜像了 `/auth/initiate` 端点的逻辑，但发送JSON载荷而不是重定向，
    以便浏览器可以保持在同一源上，直到它们有意导航离开。

    对于基于OAuth的连接器，这返回authorization_url。
    对于手动凭据连接器，这返回凭据表单规范。
    """

    try:
        connector = await service.get_connector(connector_type)
        auth_details = await connector.initiate_auth()

        # 检查这是否是手动凭据流程
        if auth_details.get("auth_type") == "manual_credentials":
            # 对于手动凭据，直接返回表单规范
            return ManualCredentialsAuthResponse(**auth_details)

        # 对于OAuth流程，继续现有逻辑
        authorization_url = auth_details.get("authorization_url")
        state = auth_details.get("state")

        if not authorization_url or not state:
            logger.error(
                "连接器 '%s' 没有为用户 '%s' 返回授权URL或状态。",
                connector_type,
                service.user_identifier,
            )
            raise HTTPException(status_code=500, detail="Failed to initiate authentication with the provider.")

        # 在会话中存储状态和连接器类型以供后续验证。
        request.session["oauth_state"] = state
        request.session["connector_type_for_callback"] = connector_type
        if app_redirect_uri:
            request.session["app_redirect_uri"] = app_redirect_uri

        # 将AuthContext作为JSON字符串存储以供回调使用
        auth_context_json_str = service.auth_context.model_dump_json()  # 使用 .model_dump_json()
        request.session["oauth_auth_context_json"] = auth_context_json_str  # 作为JSON字符串存储

        logger.info("为用户 '%s' 准备了 '%s' 的认证URL。", service.user_identifier, connector_type)

        return OAuthAuthResponse(authorization_url=authorization_url)

    except ValueError as ve:
        logger.warning("为 '%s' 准备认证URL失败: %s", connector_type, ve)
        if "Unsupported connector type" in str(ve):
            raise HTTPException(status_code=404, detail=str(ve))
        raise HTTPException(status_code=500, detail=str(ve))
    except NotImplementedError:
        raise HTTPException(status_code=501, detail=f"Connector '{connector_type}' not fully implemented.")
    except Exception as exc:
        logger.exception("为 '%s' 准备认证URL时出错: %s", connector_type, exc)
        raise HTTPException(status_code=500, detail="Internal server error preparing authentication URL.")


@router.get("/{connector_type}/oauth2callback")
async def connector_oauth_callback(
    request: Request,  # For accessing session and query parameters
    connector_type: str,  # From path, to verify against session
    code: Optional[str] = None,  # OAuth code from query parameters
    state: Optional[str] = None,  # State from query parameters
    error: Optional[str] = None,  # Optional error from OAuth provider
    error_description: Optional[str] = None,  # Optional error description
):
    """
    处理来自身份验证提供商的OAuth 2.0回调。
    验证状态，完成身份验证，并存储凭据。
    """
    logger.info(
        f"收到 '{connector_type}' 的OAuth回调。Code: {'SET' if code else 'NOT_SET'}, "
        f"State: {'SET' if state else 'NOT_SET'}, Error: {error}"
    )

    if error:
        logger.error(f"OAuth提供商为 '{connector_type}' 返回错误: {error} - {error_description}")
        raise HTTPException(status_code=400, detail=f"OAuth provider error: {error_description or error}")

    # --- 会话数据检索和验证 ---
    stored_state = request.session.pop("oauth_state", None)
    stored_connector_type = request.session.pop("connector_type_for_callback", None)
    auth_context_json_str = request.session.pop("oauth_auth_context_json", None)

    if not stored_state or not state or stored_state != state:
        logger.error(
            f"'{connector_type}' 的OAuth状态不匹配。期望: '{stored_state}', "
            f"收到: '{state}'。IP: {request.client.host if request.client else 'unknown'}"
        )
        raise HTTPException(status_code=400, detail="Invalid OAuth state. Authentication failed.")

    if not stored_connector_type or stored_connector_type != connector_type:
        logger.error(
            f"OAuth回调中连接器类型不匹配。期望: '{stored_connector_type}', 路径: '{connector_type}'。"
        )
        raise HTTPException(status_code=400, detail="Connector type mismatch during OAuth callback.")

    if not auth_context_json_str:
        logger.error(f"在 '{connector_type}' 的OAuth回调期间会话中未找到AuthContext。")
        raise HTTPException(status_code=400, detail="Authentication context missing. Please restart the auth flow.")

    # --- 服务和连接器初始化 ---
    try:
        auth_context = AuthContext(**json.loads(auth_context_json_str))
        service = ConnectorService(auth_context=auth_context)
        # connector变量将通过调用service.get_connector定义
        # 但我们需要确保在可以处理连接器特定错误的try块中完成。
    except Exception as e:  # 涵盖AuthContext重建和ConnectorService实例化
        logger.error(f"重建AuthContext或实例化ConnectorService失败: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error during authentication setup.")

    # --- 代码验证（现在可以在日志中使用service.user_identifier如果需要） ---
    if not code:
        user_id_for_log = service.user_identifier if "service" in locals() else "unknown user"
        logger.error(
            f"在用户 '{user_id_for_log}' 的 '{connector_type}' OAuth回调中未找到授权代码。"
        )
        raise HTTPException(status_code=400, detail="Authorization code missing from provider callback.")

    # 重建提供商重定向到的完整授权响应URL。
    authorization_response_url = str(request.url)
    logger.debug(f"'{connector_type}' 的完整authorization_response_url: {authorization_response_url}")

    try:
        # 现在获取连接器，因为服务已初始化
        connector = await service.get_connector(connector_type)

        auth_data = {
            "authorization_response_url": authorization_response_url,
            "state": state,
        }

        success = await connector.finalize_auth(auth_data)  # 正确地在连接器上调用

        if success:
            logger.info(
                f"成功为用户 '{service.user_identifier}' 完成 '{connector_type}' 的身份验证。"
            )
            app_redirect_uri = request.session.pop("app_redirect_uri", None)
            if app_redirect_uri:
                logger.info(f"重定向到前端app_redirect_uri: {app_redirect_uri}")
                return RedirectResponse(url=app_redirect_uri)
            else:
                logger.info("未找到app_redirect_uri，显示通用成功页面。")
                html_content = """
                <html><head><title>Authentication Successful</title></head>
                <body><h1>Authentication Successful</h1>
                <p>You have successfully authenticated. You can now close this window and return to the application.</p>
                </body></html>
                """
                return HTMLResponse(content=html_content)
        else:
            logger.error(
                f"为用户 '{service.user_identifier}' 完成 '{connector_type}' 认证失败 "
                f"（连接器返回False）。"
            )
            raise HTTPException(status_code=500, detail="Failed to finalize authentication with the provider.")

    except ValueError as ve:
        logger.error(f"用户 '{service.user_identifier}' 的 '{connector_type}' OAuth回调期间出错: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))
    except NotImplementedError:
        logger.error(f"连接器 '{connector_type}' 在认证完成方面未完全实现。")
        raise HTTPException(status_code=501, detail=f"Connector '{connector_type}' not fully implemented.")
    except Exception as e:
        user_id_for_log = service.user_identifier if "service" in locals() else "unknown user"
        logger.exception(
            f"用户 '{user_id_for_log}' 的 '{connector_type}' OAuth回调期间发生意外错误: {e}"
        )
        raise HTTPException(status_code=500, detail="Internal server error during authentication callback.")


@router.post("/{connector_type}/auth/finalize", response_model=Dict[str, Any])
async def finalize_manual_auth(
    connector_type: str,
    credentials_request: ManualCredentialsRequest,
    service: ConnectorService = Depends(get_connector_service),
):
    """使用手动凭据完成身份验证。

    此端点用于需要手动凭据输入（如Zotero）而不是OAuth流程的连接器。
    """
    try:
        connector = await service.get_connector(connector_type)

        # 尝试使用提供的凭据完成身份验证
        success = await connector.finalize_auth(credentials_request.credentials)

        if success:
            logger.info(
                f"成功为用户 '{service.user_identifier}' 完成 '{connector_type}' 的手动身份验证。"
            )
            return {"status": "success", "message": f"Successfully authenticated with {connector_type}."}
        else:
            logger.error(
                f"为用户 '{service.user_identifier}' 完成 '{connector_type}' 手动认证失败 "
                f"（连接器返回False）。"
            )
            raise HTTPException(status_code=400, detail="Invalid credentials provided.")

    except ValueError as ve:
        logger.error(f"用户 '{service.user_identifier}' 的 '{connector_type}' 手动认证期间出错: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))
    except NotImplementedError:
        logger.error(f"连接器 '{connector_type}' 在手动认证完成方面未完全实现。")
        raise HTTPException(status_code=501, detail=f"Connector '{connector_type}' not fully implemented.")
    except Exception as e:
        user_id_for_log = service.user_identifier if "service" in locals() else "unknown user"
        logger.exception(
            f"用户 '{user_id_for_log}' 的 '{connector_type}' 手动认证期间发生意外错误: {e}"
        )
        raise HTTPException(status_code=500, detail="Internal server error during manual authentication.")


# list_files的响应模型
class FileListResponse(BaseModel):
    files: List[ConnectorFile]
    next_page_token: Optional[str] = None


@router.get("/{connector_type}/files", response_model=FileListResponse)
async def list_files_for_connector(
    connector_type: str,
    path: Optional[str] = None,  # 连接器特定路径（例如，folder_id）
    page_token: Optional[str] = None,
    q_filter: Optional[str] = None,  # 连接器特定搜索/过滤查询字符串
    page_size: int = 100,  # 默认页面大小，可以通过查询参数覆盖
    service: ConnectorService = Depends(get_connector_service),
):
    """列出指定连接器的文件和文件夹。"""
    try:
        connector = await service.get_connector(connector_type)
        # 将所有相关参数传递给连接器的list_files方法
        # 连接器本身将决定如何使用它们（例如在**kwargs或命名参数中）
        file_listing = await connector.list_files(
            path=path,
            page_token=page_token,
            q_filter=q_filter,  # 传递过滤查询
            page_size=page_size,  # 传递页面大小
        )
        # 确保连接器的响应与FileListResponse模型匹配。
        # connector.list_files方法应该返回如下字典:
        # {"files": [ConnectorFile, ...], "next_page_token": "..."}
        return file_listing
    except ValueError as ve:  # 由get_connector引发或连接器有问题时
        logger.error(f"为用户 '{service.user_identifier}' 列出 '{connector_type}' 文件时出错: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))
    except ConnectionError as ce:  # 如果connector.list_files引发连接问题
        logger.error(
            f"为用户 '{service.user_identifier}' 列出 '{connector_type}' 文件时连接错误: {ce}"
        )
        raise HTTPException(status_code=503, detail=f"Connector service unavailable: {str(ce)}")
    except NotImplementedError:
        logger.error(f"连接器 '{connector_type}' 不支持列出文件或未完全实现。")
        raise HTTPException(status_code=501, detail=f"File listing not implemented for connector '{connector_type}'.")
    except Exception as e:
        logger.exception(
            f"为用户 '{service.user_identifier}' 列出 '{connector_type}' 文件时发生意外错误: {e}"
        )
        raise HTTPException(status_code=500, detail="Internal server error listing files.")


@router.post("/{connector_type}/ingest", response_model=Dict[str, Any])
async def ingest_file_from_connector(
    connector_type: str,
    ingest_request: IngestFromConnectorRequest,
    auth_context: AuthContext = Depends(verify_token),  # For DocumentService & connector
    connector_service: ConnectorService = Depends(get_connector_service),
    doc_service: DocumentService = Depends(get_document_service),
    redis_pool_instance: arq.ArqRedis = Depends(get_redis_pool),  # Retained as per original router structure
):
    """从连接器下载文件并通过DocumentService将其摄取到Morphik中。"""
    logger.info(
        f"从连接器摄取文件: {connector_type} 的file_id: {ingest_request.file_id} "
        f"用户: {auth_context.user_id or auth_context.entity_id}"
    )
    try:
        connector = await connector_service.get_connector(connector_type)

        # 1. 从连接器获取文件元数据
        file_metadata = await connector.get_file_metadata_by_id(ingest_request.file_id)
        if not file_metadata:
            logger.error(f"通过连接器未找到文件: {ingest_request.file_id}")
            raise HTTPException(status_code=404, detail="File not found via connector.")

        # 2. 从连接器下载文件内容
        file_content_stream = await connector.download_file_by_id(ingest_request.file_id)
        if not file_content_stream:
            logger.error(f"从连接器下载文件失败: {ingest_request.file_id}")
            raise HTTPException(status_code=500, detail="Failed to download file from connector.")

        file_content_bytes = file_content_stream.getvalue()  # Assuming BytesIO

        # ----------------------------------------------------------
        # 从文件字节检测实际MIME类型（回退到API）
        # 并在缺少时修复文件名扩展名。
        # ----------------------------------------------------------
        import filetype as _ft

        detected_kind = _ft.guess(file_content_bytes)
        if detected_kind:
            # 当可用时使用检测到的mime/扩展名
            actual_mime_type = detected_kind.mime
            actual_extension = detected_kind.extension
        else:
            # 回退到连接器报告的mime
            actual_mime_type = file_metadata.mime_type
            # 如果可能，从mime派生扩展名
            import mimetypes as _mtypes

            guessed_ext = _mtypes.guess_extension(actual_mime_type or "")
            actual_extension = guessed_ext.lstrip(".") if guessed_ext else None

        # 确保文件名有扩展名，以便下游解析器工作
        filename_to_use = file_metadata.name
        if actual_extension and "." not in filename_to_use:
            filename_to_use = f"{filename_to_use}.{actual_extension}"

        # 清理元数据 – 只保留可能对用户有用的连接器特定字段，
        # 但删除UI/布尔助手。
        cleaned_metadata = {}
        if file_metadata.modified_date:
            cleaned_metadata["modified_date"] = file_metadata.modified_date
        # 如果需要，您可以在此处添加更多白名单字段

        # 将用户提供的元数据与清理的连接器元数据合并
        final_metadata = cleaned_metadata.copy()
        if ingest_request.metadata:  # 用户提供的元数据
            final_metadata.update(ingest_request.metadata)

        # 3. 使用DocumentService摄取到Morphik
        morphik_doc = await doc_service.ingest_file_content(
            file_content_bytes=file_content_bytes,
            filename=filename_to_use,
            content_type=actual_mime_type,
            metadata=final_metadata,  # 使用合并的元数据
            auth=auth_context,
            redis=redis_pool_instance,
            folder_name=ingest_request.morphik_folder_name,
            end_user_id=ingest_request.morphik_end_user_id,
            rules=ingest_request.rules,  # 传递用户提供的规则
            use_colpali=True,  # 根据之前的请求
        )

        return {
            "message": f"文件 '{file_metadata.name}' 已成功加入摄取队列。",
            "morphik_document_id": morphik_doc.external_id,
            "status_path": f"/documents/{morphik_doc.external_id}/status",
        }

    except HTTPException:  # 直接重新抛出HTTPExceptions
        raise
    except ValueError as ve:  # 例如，不支持的连接器类型
        logger.error(f"从 {connector_type} 摄取期间出现值错误: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        logger.exception(f"从连接器 '{connector_type}' 摄取时发生意外错误: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during file ingestion.")


@router.post("/{connector_type}/disconnect", response_model=Dict[str, Any])
async def disconnect_from_connector(
    connector_type: str,
    service: ConnectorService = Depends(get_connector_service),
):
    """通过删除存储的凭据断开用户与指定连接器的连接。"""
    user_log_id = service.user_identifier
    logger.info(f"尝试为用户 '{user_log_id}' 断开与 '{connector_type}' 的连接。")
    try:
        connector = await service.get_connector(connector_type)
        success = await connector.disconnect()

        if success:
            logger.info(f"成功为用户 '{user_log_id}' 断开与 '{connector_type}' 的连接。")
            return {"status": "success", "message": f"Successfully disconnected from {connector_type}."}
        else:
            logger.warning(
                f"用户 '{user_log_id}' 与 '{connector_type}' 的断开连接 "
                f"被连接器指示为失败（返回False）。"
            )
            raise HTTPException(
                status_code=500, detail=f"Failed to disconnect from {connector_type}. An issue occurred on the server."
            )

    except ValueError as ve:  # 来自get_connector
        logger.error(f"用户 '{user_log_id}' 断开与 '{connector_type}' 连接时出现ValueError: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))
    except ConnectionError as ce:  # 如果connector.disconnect有问题
        logger.error(f"用户 '{user_log_id}' 断开与 '{connector_type}' 连接时出现ConnectionError: {ce}")
        raise HTTPException(status_code=503, detail=f"Connector service error during disconnect: {str(ce)}")
    except Exception as e:
        logger.exception(f"用户 '{user_log_id}' 断开与 '{connector_type}' 连接时发生意外错误: {e}")
        raise HTTPException(status_code=500, detail="Internal server error during disconnect operation.")
