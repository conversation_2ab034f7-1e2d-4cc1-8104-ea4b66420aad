/**
 * AI训练聊天Hook
 * 专门用于销售培训的AI对话管理
 */

import { useState, useCallback, useRef } from 'react'
import { useRAGQuery } from '@/hooks/useMorphik'
import type { TrainingCustomer } from '@/types/salesTraining'

// AI训练消息类型
export interface AITrainingMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  isStreaming?: boolean
  sources?: any[]
  metadata?: {
    type: 'training' | 'feedback' | 'suggestion'
    confidence?: number
    evaluation?: {
      score: number
      feedback: string
    }
  }
}

// 训练会话状态
export interface TrainingSession {
  id: string
  startTime: Date
  endTime?: Date
  status: 'active' | 'paused' | 'completed'
  messageCount: number
  duration: number
  score?: number
}

// 训练配置
export interface TrainingConfig {
  enableRealTimeEvaluation: boolean
  enableSuggestions: boolean
  enableAIGuidance: boolean
  enableIntelligentScoring: boolean // 新增：智能评分开关
  showAISuggestions: boolean // 新增：控制AI建议显示和生成
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  focusAreas: string[]
}

// 增强的评分结构
interface DetailedScore {
  needsUnderstanding: number      // 需求理解与把握
  professionalism: number         // 专业度
  problemSolving: number          // 问题处理能力
  persuasiveness: number          // 说服力
  culturalSensitivity: number     // 跨文化敏感度
  termControl: number             // 条款把控
  customerSatisfaction: number    // 客户满意度
  overall: number                 // 总体得分
  timestamp: Date
}

interface ScoreReason {
  dimension: string
  score: number
  reason: string
  suggestion: string
  keyStrengths: string[]
  improvementAreas: string[]
}

interface IntelligentEvaluation {
  id: string
  sessionId: string
  overallScore: number
  detailedScores: DetailedScore
  scoreReasons: ScoreReason[]
  overallFeedback: string
  keyStrengths: string[]
  improvementSuggestions: string[]
  nextStepRecommendations: string[]
  evaluatedAt: Date
  evaluatedMessageCount: number
}



interface AIGuidance {
  id: string
  suggestions: string[]
  keyPoints: string[]
  warnings: string[]
  recommendedResponse: string
  timestamp: Date
}



interface UseAITrainingChatProps {
  customer: TrainingCustomer
  config?: Partial<TrainingConfig>
}

export function useAITrainingChat({ customer, config = {} }: UseAITrainingChatProps) {
  // 默认配置
  const defaultConfig: TrainingConfig = {
    enableRealTimeEvaluation: true,
    enableSuggestions: true,
    enableAIGuidance: false,
    enableIntelligentScoring: true, // 默认开启智能评分
    showAISuggestions: false, // 默认不显示AI建议，需要手动激活
    difficulty: 'intermediate',
    focusAreas: ['communication', 'negotiation', 'product_knowledge'],
    ...config
  }

  // 状态管理
  const [session, setSession] = useState<TrainingSession | null>(null)
  const [messages, setMessages] = useState<AITrainingMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isEvaluating, setIsEvaluating] = useState(false) // 新增：评分加载状态
  const [trainingConfig, setTrainingConfig] = useState<TrainingConfig>(defaultConfig)
  const [currentGuidance, setCurrentGuidance] = useState<AIGuidance | null>(null)
  const [isGeneratingGuidance, setIsGeneratingGuidance] = useState(false) // 新增：AI建议生成状态
  const [currentEvaluation, setCurrentEvaluation] = useState<IntelligentEvaluation | null>(null) // 新增：当前智能评估
  const [evaluationError, setEvaluationError] = useState<string | null>(null) // 新增：评分错误状态

  // Refs
  const sessionRef = useRef<TrainingSession | null>(null)

  // AI服务
  const ragQuery = useRAGQuery()

  // 生成客户主动开场消息
  const generateCustomerOpening = useCallback(() => {
    // 获取客户信息，提供合理的默认值
    const customerName = customer.name || '客户'
    const countryName = customer.country?.name || '海外'
    const companyName = customer.background?.company || `${countryName}某公司`
    const position = customer.background?.position || '负责人'
    const productName = customer.product?.name || '产品'
    const primaryPreference = customer.background?.preferences?.[0] || '产品质量'

    // 根据国家文化背景调整开场风格
    const getCulturalGreeting = () => {
      const country = customer.country?.name?.toLowerCase()
      if (country?.includes('日本')) {
        return '您好'  // 日本商务文化偏向正式
      } else if (country?.includes('美国') || country?.includes('英国') || country?.includes('澳大利亚')) {
        return '你好'  // 英语国家相对随意
      } else if (country?.includes('德国') || country?.includes('法国')) {
        return '您好'  // 欧洲商务文化较正式
      }
      return '您好'  // 默认正式
    }

    const greeting = getCulturalGreeting()

    // 优化后的开场模板，更自然和具体
    const openingTemplates = [
      // 模板1：正式介绍 + 解决方案导向
      `${greeting}！我是${companyName}的${customerName}，担任${position}。我们公司正在评估${productName}的解决方案，听说贵公司在这个领域有很好的口碑。我想了解一下你们的产品能否满足我们的需求。`,

      // 模板2：直接需求 + 关注点明确（原图中使用的优化版）
      `${greeting}，我是来自${countryName}的${customerName}。我们公司目前需要${productName}，特别关注${primaryPreference}方面的表现。能否详细介绍一下你们的产品特点？`,

      // 模板3：网络了解 + 进一步沟通
      `${greeting}！我在网上了解到贵公司的${productName}产品，我是${companyName}的${position}${customerName}。我们对这类产品很感兴趣，希望能进一步沟通了解具体的功能和优势。`
    ]

    // 根据客户背景智能选择模板
    const selectTemplate = () => {
      const experience = customer.background?.experience?.toLowerCase() || ''
      const preferences = customer.background?.preferences || []

      // 有经验的客户倾向于直接表达需求
      if (experience.includes('年') && preferences.length > 0) {
        return 1  // 使用直接需求模板
      }
      // 技术导向的客户偏向正式介绍
      if (preferences.some(p => p.includes('技术') || p.includes('先进') || p.includes('创新'))) {
        return 0  // 使用正式介绍模板
      }
      // 默认随机选择
      return Math.floor(Math.random() * openingTemplates.length)
    }

    const templateIndex = selectTemplate()
    return openingTemplates[templateIndex]
  }, [customer])

  // 初始化训练会话
  const initializeSession = useCallback(async () => {
    const newSession: TrainingSession = {
      id: `training_${Date.now()}`,
      startTime: new Date(),
      status: 'active',
      messageCount: 0,
      duration: 0
    }

    setSession(newSession)
    sessionRef.current = newSession

    // 生成客户主动开场消息
    const customerOpening = generateCustomerOpening()
    const openingMessage: AITrainingMessage = {
      id: 'customer_opening',
      role: 'assistant',
      content: customerOpening,
      timestamp: new Date(),
      metadata: { type: 'training' }
    }

    setMessages([openingMessage])
    return newSession
  }, [customer, generateCustomerOpening])

  // 构建训练提示词
  const buildTrainingPrompt = useCallback((userMessage: string) => {
    // 获取客户基本信息
    const customerName = customer.name || '客户'
    const countryName = customer.country?.name || '海外'
    const companyName = customer.background?.company || `${countryName}某公司`
    const position = customer.background?.position || '决策者'
    const productName = customer.product?.name || '产品'
    const experience = customer.background?.experience || '有一定行业经验'
    const preferences = customer.background?.preferences || ['质量', '价格', '服务']

    // 增强的难度级别描述，更具体和可操作
    const difficultyPrompts = {
      beginner: {
        behavior: '表现出对产品的浓厚兴趣，相对容易被说服',
        questions: '主要询问产品基本功能、价格、使用方法等基础问题',
        attitude: '积极配合，偶尔提出简单的疑虑，但容易接受解释',
        decision: '决策相对快速，主要关注直接利益'
      },
      intermediate: {
        behavior: '保持适度的商务谨慎，需要充分的信息才能做决定',
        questions: '会提出关于ROI、实施周期、技术支持、竞品对比等商务问题',
        attitude: '理性分析，会表达一些合理的担忧和异议',
        decision: '需要多轮沟通，会要求提供案例和数据支撑'
      },
      advanced: {
        behavior: '表现得非常专业和谨慎，具有丰富的行业经验',
        questions: '提出深度的技术问题、战略层面的考虑、风险评估等尖锐问题',
        attitude: '挑战性强，会质疑产品声明，要求详细证据',
        decision: '决策过程复杂，需要全面评估，可能涉及多个决策维度'
      }
    }

    // 根据国家文化背景调整沟通风格
    const getCulturalStyle = () => {
      const country = countryName.toLowerCase()
      if (country.includes('日本')) {
        return {
          style: '礼貌而谨慎，注重细节和长期关系',
          concerns: '特别关注产品质量、服务稳定性和供应商信誉',
          communication: '倾向于间接表达，会通过提问来表达疑虑'
        }
      } else if (country.includes('美国')) {
        return {
          style: '直接而高效，注重结果和ROI',
          concerns: '关注成本效益、实施速度和竞争优势',
          communication: '直接表达需求和疑虑，喜欢数据和案例'
        }
      } else if (country.includes('德国')) {
        return {
          style: '严谨而专业，注重技术细节和标准',
          concerns: '特别关注技术规格、质量标准和合规性',
          communication: '逻辑性强，会深入询问技术细节'
        }
      } else if (country.includes('英国')) {
        return {
          style: '礼貌而保守，注重传统和稳定性',
          concerns: '关注风险控制、服务质量和长期价值',
          communication: '委婉表达，通过礼貌的质疑来评估'
        }
      }
      return {
        style: '专业而平衡，综合考虑各方面因素',
        concerns: '关注产品性能、价格合理性和服务支持',
        communication: '理性沟通，会提出合理的问题和要求'
      }
    }

    const culturalStyle = getCulturalStyle()
    const difficultyConfig = difficultyPrompts[trainingConfig.difficulty]

    const prompt = `请用中文作答。

【角色设定】
你是一位来自${countryName}的客户${customerName}，在${companyName}担任${position}。
你对${productName}感兴趣，但作为专业的决策者，需要销售人员充分说服你才会购买。

【个人背景】
- 姓名：${customerName}
- 公司：${companyName}
- 职位：${position}
- 经验：${experience}
- 主要关注点：${preferences.join('、')}

【文化背景】
- 国家：${countryName}
- 沟通风格：${culturalStyle.style}
- 主要关切：${culturalStyle.concerns}
- 表达方式：${culturalStyle.communication}

【行为模式】（难度：${trainingConfig.difficulty}）
- 基本态度：${difficultyConfig.behavior}
- 提问类型：${difficultyConfig.questions}
- 互动方式：${difficultyConfig.attitude}
- 决策特点：${difficultyConfig.decision}

【当前情况】
销售人员刚才说："${userMessage}"

【回复要求】
请以${customerName}的身份回复，必须：
1. 完全符合角色设定和文化背景
2. 体现${trainingConfig.difficulty}难度级别的特点
3. 根据你的关注点（${preferences.join('、')}）提出相应问题或表达关切
4. 保持对话的自然流畅和商务专业性
5. 回复长度控制在50-150字
6. 不要包含任何角色外的解释或分析

请直接以客户身份回复：`

    return prompt
  }, [customer, trainingConfig.difficulty])

  // 生成智能备用回复
  const generateIntelligentFallback = useCallback((userMessage: string) => {
    const customerName = customer.name || '客户'
    const countryName = customer.country?.name || '海外'
    const companyName = customer.background?.company || `${countryName}某公司`
    const productName = customer.product?.name || '产品'
    const preferences = customer.background?.preferences || ['质量', '价格', '服务']
    const primaryPreference = preferences[0]

    // 分析用户消息内容，选择合适的回复类型
    const analyzeUserMessage = (message: string) => {
      const lowerMessage = message.toLowerCase()

      if (lowerMessage.includes('价格') || lowerMessage.includes('费用') || lowerMessage.includes('成本')) {
        return 'price'
      } else if (lowerMessage.includes('功能') || lowerMessage.includes('特点') || lowerMessage.includes('优势')) {
        return 'features'
      } else if (lowerMessage.includes('案例') || lowerMessage.includes('客户') || lowerMessage.includes('成功')) {
        return 'cases'
      } else if (lowerMessage.includes('技术') || lowerMessage.includes('规格') || lowerMessage.includes('参数')) {
        return 'technical'
      } else if (lowerMessage.includes('服务') || lowerMessage.includes('支持') || lowerMessage.includes('售后')) {
        return 'service'
      } else if (lowerMessage.includes('实施') || lowerMessage.includes('部署') || lowerMessage.includes('上线')) {
        return 'implementation'
      }
      return 'general'
    }

    const messageType = analyzeUserMessage(userMessage)

    // 根据难度级别和消息类型生成回复
    const generateResponseByType = () => {
      const difficulty = trainingConfig.difficulty

      const responses = {
        price: {
          beginner: `价格听起来还可以，不过我需要了解一下具体的费用构成。`,
          intermediate: `关于价格，我需要和财务部门讨论一下。能否提供详细的报价单？`,
          advanced: `价格只是考虑因素之一。我更关心总体拥有成本和ROI分析。`
        },
        features: {
          beginner: `这些功能听起来不错，能否演示一下具体的操作流程？`,
          intermediate: `功能介绍很详细，但我想了解一下与我们现有系统的兼容性如何？`,
          advanced: `功能确实丰富，但我需要评估这些功能的实际业务价值和技术可行性。`
        },
        cases: {
          beginner: `这些案例很有说服力，我们公司的情况和这些客户类似吗？`,
          intermediate: `案例不错，但我想了解一下在${countryName}市场的具体应用情况。`,
          advanced: `案例有参考价值，但每个企业情况不同。能否提供更详细的实施数据？`
        },
        technical: {
          beginner: `技术方面我不太懂，但听起来很先进。实施起来复杂吗？`,
          intermediate: `技术规格需要我们IT部门评估，能否提供技术文档？`,
          advanced: `技术架构需要深入评估。关于数据安全和系统集成有什么保障？`
        },
        service: {
          beginner: `服务支持很重要，你们的响应时间是多长？`,
          intermediate: `服务体系看起来完善，但我们需要本地化的支持服务。`,
          advanced: `服务质量是关键因素。能否提供SLA协议和服务等级保证？`
        },
        implementation: {
          beginner: `实施周期多长？会不会影响我们正常的业务运营？`,
          intermediate: `实施计划需要详细规划，我们需要分阶段推进。`,
          advanced: `实施风险控制是重点。项目管理和变更管理如何保障？`
        },
        general: {
          beginner: `听起来不错，但我还需要更多信息才能做决定。`,
          intermediate: `我需要和团队讨论一下，能否提供更详细的资料？`,
          advanced: `这需要全面评估，我们会从多个维度来考虑这个方案。`
        }
      }

      return responses[messageType]?.[difficulty] || responses.general[difficulty]
    }

    // 添加个性化元素
    const baseResponse = generateResponseByType()

    // 根据客户偏好添加相关关注点
    const addPersonalizedConcern = () => {
      if (primaryPreference.includes('技术')) {
        return `另外，我特别关注${primaryPreference}方面的表现。`
      } else if (primaryPreference.includes('成本') || primaryPreference.includes('价格')) {
        return `当然，${primaryPreference}也是我们重要的考虑因素。`
      } else if (primaryPreference.includes('服务')) {
        return `我们对${primaryPreference}的要求比较高。`
      }
      return `我们特别重视${primaryPreference}。`
    }

    // 组合最终回复
    const shouldAddConcern = Math.random() > 0.5 // 50%概率添加个性化关注点
    const finalResponse = shouldAddConcern
      ? `${baseResponse} ${addPersonalizedConcern()}`
      : baseResponse

    return finalResponse
  }, [customer, trainingConfig.difficulty])

  // 检查是否应该生成AI建议
  const shouldGenerateAIGuidance = useCallback(() => {
    // 1. 检查灯泡是否开启
    if (!trainingConfig.enableAIGuidance || !trainingConfig.showAISuggestions) {
      return false
    }

    // 2. 检查最后一条消息是否是AI发的
    if (messages.length === 0) {
      return false
    }

    const lastMessage = messages[messages.length - 1]
    if (lastMessage.role !== 'assistant') {
      return false
    }

    return true
  }, [trainingConfig.enableAIGuidance, trainingConfig.showAISuggestions, messages])

  // 生成AI指导建议
  const generateAIGuidance = useCallback(async (customerMessage: string) => {
    // 防止重复请求
    if (isGeneratingGuidance) {
      return
    }

    setIsGeneratingGuidance(true) // 开始生成动效

    try {
      const guidancePrompt = `
作为销售培训专家，请分析客户的话语并提供针对性的回复建议：

客户说："${customerMessage}"

客户背景：
- 来自：${customer.country.name}
- 职位：${customer.background?.position || '决策者'}
- 关注点：${customer.background?.preferences?.join('、') || '质量和价格'}

请提供：
1. 2个推荐回复方向（简洁明了）
2. 2个关键话术要点
3. 1个需要注意的事项

请以JSON格式回复：
{
  "suggestions": ["建议1", "建议2"],
  "keyPoints": ["要点1", "要点2"],
  "warnings": ["注意事项"],
  "recommendedResponse": "推荐的具体回复示例"
}
`

      const response = await ragQuery.mutateAsync({
        request: {
          query: guidancePrompt,
          max_chunks: 1,  // 优化：减少检索块数量以提升速度
          temperature: 0.6,  // 优化：降低温度值加快生成
          include_sources: false
        },
        context: 'general'
      })

      // 检查响应是否有效
      if (!response || !response.completion) {
        return
      }

      try {
        const guidanceData = JSON.parse(response.completion || '{}')
        const guidance: AIGuidance = {
          id: `guidance_${Date.now()}`,
          suggestions: Array.isArray(guidanceData.suggestions) ? guidanceData.suggestions : [],
          keyPoints: Array.isArray(guidanceData.keyPoints) ? guidanceData.keyPoints : [],
          warnings: Array.isArray(guidanceData.warnings) ? guidanceData.warnings : [],
          recommendedResponse: guidanceData.recommendedResponse || '',
          timestamp: new Date()
        }

        // 只有在有有效建议时才更新状态
        if (guidance.suggestions.length > 0) {
          setCurrentGuidance(guidance)
        }
      } catch (parseError) {
        // 解析失败时不清除现有建议，保持用户体验
      }
    } catch (error) {
      // 网络错误时不清除现有建议，保持用户体验
    } finally {
      setIsGeneratingGuidance(false)
    }
  }, [customer, ragQuery, isGeneratingGuidance])

  // 清除AI建议
  const clearAIGuidance = useCallback(() => {
    setCurrentGuidance(null)
    setIsGeneratingGuidance(false)
  }, [])

  // 发送消息
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isLoading || !session) return

    const userMessage: AITrainingMessage = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: content.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)

    try {
      // 构建训练提示词
      const trainingPrompt = buildTrainingPrompt(content)

      // 调用AI服务
      const response = await ragQuery.mutateAsync({
        request: {
          query: trainingPrompt,
          max_chunks: 2,
          temperature: 0.7,
          include_sources: false,
          chat_id: session.id
        },
        context: 'chat'
      })

      // 验证AI回复内容
      let aiContent = response.completion || ''

      // 检查是否是异常回复
      if (!aiContent ||
        aiContent.length < 10 ||
        aiContent.includes('发现心理问题') ||
        aiContent.includes('异常') ||
        /^(.+?)：\1$/.test(aiContent.trim())) { // 检查重复模式
        console.warn('检测到异常AI回复，使用备用回复:', aiContent)

        // 生成智能备用回复
        aiContent = generateIntelligentFallback(content)

        console.log('使用备用回复:', aiContent)
      }

      // 添加AI回复
      const aiMessage: AITrainingMessage = {
        id: `ai_${Date.now()}`,
        role: 'assistant',
        content: aiContent,
        timestamp: new Date(),
        sources: response.sources,
        metadata: {
          type: 'training',
          confidence: aiContent === response.completion ? 0.9 : 0.5 // 备用回复置信度较低
        }
      }

      setMessages(prev => {
        const newMessages = [...prev, aiMessage]

        // 在状态更新后检查是否应该生成AI建议
        // 检查条件：1. 灯泡开启 2. 最后一条消息是AI发的 3. 当前没有正在生成的建议
        if (trainingConfig.enableAIGuidance &&
          trainingConfig.showAISuggestions &&
          aiMessage.role === 'assistant' &&
          !isGeneratingGuidance) {
          // 使用setTimeout确保状态更新完成，并增加延迟避免竞态条件
          setTimeout(() => {
            // 再次检查状态，确保条件仍然满足
            if (trainingConfig.enableAIGuidance && trainingConfig.showAISuggestions && !isGeneratingGuidance) {
              generateAIGuidance(response.completion)
            }
          }, 100)
        }

        return newMessages
      })



      // 更新会话统计
      setSession(prev => prev ? {
        ...prev,
        messageCount: prev.messageCount + 1,
        duration: Math.floor((Date.now() - prev.startTime.getTime()) / 1000)
      } : null)

    } catch (error) {
      // 生成智能备用回复
      const generateSmartFallback = (userInput: string) => {
        const fallbackTemplates = [
          `谢谢您的介绍。关于${customer.product.name}，我想了解更多细节。`,
          `听起来很有趣。不过我需要考虑一下成本效益，能详细说明吗？`,
          `我们公司对新产品比较谨慎，您能提供一些客户案例吗？`,
          `价格方面如何？我们需要在预算范围内做决定。`,
          `这个解决方案适合我们${customer.country.name}的业务环境吗？`,
          `我需要和团队讨论一下，您能发一份详细资料吗？`
        ]

        // 根据用户输入选择更合适的回复
        if (userInput.includes('价格') || userInput.includes('费用') || userInput.includes('成本')) {
          return `价格确实是我们考虑的重要因素。能否提供一个大概的报价范围？`
        } else if (userInput.includes('功能') || userInput.includes('特点') || userInput.includes('优势')) {
          return `功能介绍很详细，但我想了解一下与竞品相比的核心优势在哪里？`
        } else if (userInput.includes('服务') || userInput.includes('支持') || userInput.includes('培训')) {
          return `服务支持很重要，你们的售后服务体系是怎样的？`
        } else {
          const randomIndex = Math.floor(Math.random() * fallbackTemplates.length)
          return fallbackTemplates[randomIndex]
        }
      }

      // 添加智能备用回复
      const fallbackContent = generateSmartFallback(content.trim())
      const fallbackMessage: AITrainingMessage = {
        id: `fallback_${Date.now()}`,
        role: 'assistant',
        content: fallbackContent,
        timestamp: new Date(),
        metadata: {
          type: 'training',
          confidence: 0.3
        }
      }

      setMessages(prev => [...prev, fallbackMessage])

      // 显示错误提示但不中断对话
      console.warn('使用备用AI回复，原因:', error.message)
    } finally {
      setIsLoading(false)
    }
  }, [session, isLoading, buildTrainingPrompt, ragQuery])

  // 重置会话 - 保留AI建议状态以避免突然消失
  const resetSession = useCallback(() => {
    setMessages([])
    setSession(null)
    sessionRef.current = null
    // 清除评分相关状态
    setCurrentEvaluation(null)
    setEvaluationError(null)
    setIsEvaluating(false)
    // 注意：不清除AI建议状态，避免用户体验中断
    // setCurrentGuidance(null)
    // setIsGeneratingGuidance(false)
    initializeSession()
  }, [initializeSession])

  // 暂停/继续会话
  const toggleSession = useCallback(() => {
    if (session) {
      const newStatus = session.status === 'active' ? 'paused' : 'active'
      setSession(prev => prev ? { ...prev, status: newStatus } : null)

      // 当会话状态变为active（继续训练）时，清除评分状态以允许新的评分
      if (newStatus === 'active') {
        setCurrentEvaluation(null)
        setEvaluationError(null)
        setIsEvaluating(false)
      }
    }
  }, [session])

  // 完成会话
  const completeSession = useCallback(() => {
    if (session) {
      setSession(prev => prev ? {
        ...prev,
        status: 'completed',
        endTime: new Date(),
        duration: Math.floor((Date.now() - prev.startTime.getTime()) / 1000)
      } : null)
    }
  }, [session])

  // 保存会话
  const saveSession = useCallback(() => {
    if (session && messages.length > 0) {
      const sessionData = {
        session,
        messages,
        customer,
        config: trainingConfig,
        savedAt: new Date()
      }

      // 保存到localStorage（实际项目中应该保存到后端）
      const savedSessions = JSON.parse(localStorage.getItem('ai_training_sessions') || '[]')
      savedSessions.push(sessionData)
      localStorage.setItem('ai_training_sessions', JSON.stringify(savedSessions))

      return sessionData
    }
    return null
  }, [session, messages, customer, trainingConfig])

  // 获取会话时长
  const getSessionDuration = useCallback(() => {
    if (!session) return '00:00'
    const duration = session.status === 'active'
      ? Math.floor((Date.now() - session.startTime.getTime()) / 1000)
      : session.duration
    const minutes = Math.floor(duration / 60)
    const seconds = duration % 60
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }, [session])





  // 动态权重计算：根据对话阶段调整权重
  const calculateDynamicWeights = useCallback((messageCount: number) => {
    if (messageCount <= 2) {
      // 对话初期：重点关注需求理解和专业度
      return {
        needsUnderstanding: 35,
        professionalism: 30,
        problemSolving: 10,
        persuasiveness: 10,
        culturalSensitivity: 10,
        termControl: 3,
        customerSatisfaction: 2
      }
    } else if (messageCount <= 5) {
      // 对话中期：平衡各维度
      return {
        needsUnderstanding: 30,
        professionalism: 25,
        problemSolving: 20,
        persuasiveness: 15,
        culturalSensitivity: 7,
        termControl: 2,
        customerSatisfaction: 1
      }
    } else {
      // 对话后期：重点关注说服力和客户满意度
      return {
        needsUnderstanding: 25,
        professionalism: 20,
        problemSolving: 15,
        persuasiveness: 20,
        culturalSensitivity: 10,
        termControl: 5,
        customerSatisfaction: 5
      }
    }
  }, [])

  // 构建优化的智能评分提示词模板
  const buildIntelligentScoringPrompt = useCallback((conversationHistory: string) => {
    const userMessageCount = messages.filter(m => m.role === 'user').length
    const weights = calculateDynamicWeights(userMessageCount)

    // 根据对话阶段确定评分重点
    const getStageDescription = (count: number) => {
      if (count <= 2) return "对话初期，重点评估需求理解和专业基础"
      if (count <= 5) return "对话中期，全面评估各项销售技能"
      return "对话深入阶段，重点评估说服力和客户体验"
    }

    return `作为资深销售培训专家，请对以下销售对话进行专业、客观的智能评分。

**评分背景：**
- 评分阶段：${getStageDescription(userMessageCount)}（共${userMessageCount}轮用户回复）
- 客户信息：${customer.name}（${customer.country.name}）
- 产品需求：${customer.product.name}
- 客户特点：${customer.background?.preferences?.join('、') || '注重质量和价格'}

**动态评分维度（权重会根据对话阶段调整）：**

1. **需求理解与把握（${weights.needsUnderstanding}%）**
   - 评估要点：需求挖掘深度、痛点识别、确认技巧
   - 优秀表现：主动询问、深入了解、准确把握客户真实需求

2. **专业度（${weights.professionalism}%）**
   - 评估要点：产品知识、行业理解、专业表达
   - 优秀表现：术语准确、知识丰富、回答专业

3. **问题处理能力（${weights.problemSolving}%）**
   - 评估要点：异议处理、问题解决、应变能力
   - 优秀表现：冷静应对、逻辑清晰、有效解决

4. **说服力（${weights.persuasiveness}%）**
   - 评估要点：逻辑论证、价值呈现、引导决策
   - 优秀表现：论据充分、价值突出、引导自然

5. **跨文化敏感度（${weights.culturalSensitivity}%）**
   - 评估要点：文化理解、沟通调整、礼仪规范
   - 优秀表现：尊重文化、调整风格、得体表达

6. **条款把控（${weights.termControl}%）**
   - 评估要点：商务条款、价格谈判、风险控制
   - 优秀表现：条款清晰、谈判技巧、风险意识

7. **客户满意度（${weights.customerSatisfaction}%）**
   - 评估要点：服务态度、响应速度、体验质量
   - 优秀表现：态度友好、响应及时、体验良好

**对话记录：**
${conversationHistory}

**评分标准（严格执行）：**
- 90-100分：卓越表现，超出预期，专业度极高
- 80-89分：优秀表现，达到专业标准，处理得当
- 70-79分：良好表现，基本合格，有改进空间
- 60-69分：一般表现，勉强及格，存在明显不足
- 50-59分：较差表现，低于标准，需要改进
- 30-49分：差劲表现，严重不足，急需培训
- 0-29分：极差表现，完全不合格，重新开始

**特别注意：**
1. 严格基于对话内容评分，禁止主观臆断
2. 简短无意义回复（如单字、数字、"好"等）必须给低分
3. 考虑对话的连贯性和上下文关联
4. 重视客户反馈和互动质量
5. 总分为加权平均，确保计算准确

**输出格式（仅返回JSON，无其他内容）：**

{
  "overallScore": 0,
  "detailedScores": {
    "needsUnderstanding": 0,
    "professionalism": 0,
    "problemSolving": 0,
    "persuasiveness": 0,
    "culturalSensitivity": 0,
    "termControl": 0,
    "customerSatisfaction": 0
  },
  "scoreReasons": [
    {
      "dimension": "维度名称",
      "score": 0,
      "reason": "基于对话内容的具体评分理由",
      "suggestion": "针对性的改进建议",
      "keyStrengths": ["具体优势表现"],
      "improvementAreas": ["具体需要改进的方面"]
    }
  ],
  "overallFeedback": "基于整体表现的综合评价",
  "keyStrengths": ["整体优势"],
  "improvementSuggestions": ["具体改进建议"],
  "nextStepRecommendations": ["下一步行动建议"]
}`
  }, [customer, messages, calculateDynamicWeights])

  // 生成智能fallback评分
  const generateFallbackEvaluation = useCallback((
    sessionId: string,
    messages: AITrainingMessage[],
    aiResponse: string,
    error: Error
  ): IntelligentEvaluation => {
    const userMessages = messages.filter(m => m.role === 'user')
    const messageCount = userMessages.length

    // 基于对话内容进行基础分析
    const basicAnalysis = {
      hasContent: userMessages.some(m => m.content.trim().length > 10),
      hasQuestions: userMessages.some(m => m.content.includes('?') || m.content.includes('？')),
      hasProfessionalTerms: userMessages.some(m => m.content.length > 30),
      averageLength: userMessages.reduce((sum, m) => sum + m.content.length, 0) / Math.max(messageCount, 1),
      hasNeedsInquiry: userMessages.some(m =>
        m.content.includes('需求') || m.content.includes('要求') || m.content.includes('需要')
      )
    }

    // 基于分析结果计算基础分数
    const calculateBasicScore = (baseScore: number) => {
      let score = baseScore

      if (basicAnalysis.hasContent) score += 10
      if (basicAnalysis.hasQuestions) score += 5
      if (basicAnalysis.hasProfessionalTerms) score += 8
      if (basicAnalysis.hasNeedsInquiry) score += 12
      if (basicAnalysis.averageLength > 20) score += 5
      if (messageCount > 3) score += 5

      return Math.min(85, Math.max(25, score)) // 限制在25-85分之间
    }

    const baseScores = {
      needsUnderstanding: calculateBasicScore(40),
      professionalism: calculateBasicScore(45),
      problemSolving: calculateBasicScore(50),
      persuasiveness: calculateBasicScore(45),
      culturalSensitivity: calculateBasicScore(55),
      termControl: calculateBasicScore(50),
      customerSatisfaction: calculateBasicScore(60)
    }

    // 计算加权总分 - 修复计算逻辑
    // 各项分数已经是百分制，权重也是百分比，直接按权重计算加权平均
    const weights = calculateDynamicWeights(messageCount)
    const overallScore = Math.round(
      (baseScores.needsUnderstanding * weights.needsUnderstanding +
        baseScores.professionalism * weights.professionalism +
        baseScores.problemSolving * weights.problemSolving +
        baseScores.persuasiveness * weights.persuasiveness +
        baseScores.culturalSensitivity * weights.culturalSensitivity +
        baseScores.termControl * weights.termControl +
        baseScores.customerSatisfaction * weights.customerSatisfaction) / 100
    )

    return {
      id: `evaluation_${Date.now()}`,
      sessionId,
      overallScore,
      detailedScores: {
        needsUnderstanding: baseScores.needsUnderstanding,
        professionalism: baseScores.professionalism,
        problemSolving: baseScores.problemSolving,
        persuasiveness: baseScores.persuasiveness,
        culturalSensitivity: baseScores.culturalSensitivity,
        termControl: baseScores.termControl,
        customerSatisfaction: baseScores.customerSatisfaction,
        overall: overallScore,
        timestamp: new Date()
      },
      scoreReasons: [{
        dimension: "系统评估",
        score: overallScore,
        reason: `AI评分服务暂时不可用，基于对话基础分析给出评估。错误信息：${error.message}`,
        suggestion: "建议稍后重试AI智能评分以获得更准确的分析",
        keyStrengths: [
          ...(basicAnalysis.hasContent ? ["积极参与对话"] : []),
          ...(basicAnalysis.hasQuestions ? ["主动询问"] : []),
          ...(basicAnalysis.hasProfessionalTerms ? ["使用专业表达"] : [])
        ],
        improvementAreas: [
          ...(!basicAnalysis.hasNeedsInquiry ? ["需要更多了解客户需求"] : []),
          ...(basicAnalysis.averageLength < 20 ? ["回复内容可以更详细"] : []),
          "建议使用AI智能评分获得详细分析"
        ]
      }],
      overallFeedback: `基于${messageCount}轮对话的基础分析。${aiResponse ? `AI原始回复：${aiResponse.substring(0, 100)}...` : ''}`,
      keyStrengths: [
        ...(messageCount > 3 ? ["对话轮次充足"] : []),
        ...(basicAnalysis.hasContent ? ["有实质性内容"] : [])
      ],
      improvementSuggestions: [
        "重试AI智能评分以获得专业分析",
        "继续练习销售对话技巧",
        "关注客户需求挖掘"
      ],
      nextStepRecommendations: [
        "稍后重新进行AI评分",
        "分析当前对话的改进点",
        "继续销售技能训练"
      ],
      evaluatedAt: new Date(),
      evaluatedMessageCount: messageCount
    }
  }, [calculateDynamicWeights])

  // 执行智能评分 - 使用SmoLAgents服务
  const performIntelligentEvaluation = useCallback(async () => {
    console.log('开始执行智能评分', {
      session: !!session,
      messagesLength: messages.length,
      enableIntelligentScoring: trainingConfig.enableIntelligentScoring,
      userMessagesCount: messages.filter(m => m.role === 'user').length
    })

    if (!session) {
      console.error('评分失败：没有活动会话')
      throw new Error('没有活动会话')
    }

    if (messages.length < 2) {
      console.error('评分失败：对话内容不足')
      throw new Error('至少需要2条消息才能进行评分')
    }

    if (!trainingConfig.enableIntelligentScoring) {
      console.error('评分失败：智能评分功能未启用')
      throw new Error('智能评分功能未启用')
    }

    const userMessages = messages.filter(m => m.role === 'user')
    if (userMessages.length < 1) {
      console.error('评分失败：没有用户消息')
      throw new Error('没有用户消息可供评分')
    }

    setIsEvaluating(true)
    setEvaluationError(null) // 清除之前的错误

    try {
      // 构建对话历史
      const conversationHistory = messages
        .filter(msg => msg.role !== 'system')
        .map(msg => `${msg.role === 'user' ? '销售人员' : '客户'}：${msg.content}`)
        .join('\n\n')

      console.log('对话历史构建完成', {
        conversationLength: conversationHistory.length,
        messageCount: messages.length
      })

      // 构建评分提示词
      const scoringPrompt = buildIntelligentScoringPrompt(conversationHistory)

      console.log('评分提示词构建完成，长度:', scoringPrompt.length)

      // 使用SmoLAgents服务进行评分
      console.log('开始调用SmoLAgents服务进行评分...')

      const { smolagentsService } = await import('@/services/smolagentsService')

      const result = await smolagentsService.performIntelligentEvaluation({
        query: scoringPrompt,
        conversationHistory,
        sessionId: session.id,
        messageCount: messages.length,
        maxSteps: 3,
        temperature: 0.3
      })

      if (!result.success) {
        throw new Error(result.error || 'AI评分服务调用失败')
      }

      console.log('SmoLAgents API调用成功，原始响应:', result)

      const completionText = result.result || result.completion || ''

      try {
        console.log('开始解析AI响应结果...')
        let jsonString = completionText

        // 清理可能的markdown标记和多余文字
        if (jsonString.includes('```json')) {
          const jsonMatch = jsonString.match(/```json\s*([\s\S]*?)\s*```/)
          if (jsonMatch) {
            jsonString = jsonMatch[1]
          }
        } else if (jsonString.includes('```')) {
          const jsonMatch = jsonString.match(/```\s*([\s\S]*?)\s*```/)
          if (jsonMatch) {
            jsonString = jsonMatch[1]
          }
        }

        // 尝试提取JSON部分
        const jsonMatch = jsonString.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          jsonString = jsonMatch[0]
        }

        console.log('清理后的JSON字符串长度:', jsonString.length)
        console.log('清理后的JSON字符串:', jsonString.substring(0, 200) + '...')

        const evaluationData = JSON.parse(jsonString)

        console.log('AI响应解析成功', {
          hasOverallScore: !!evaluationData.overallScore,
          hasDetailedScores: !!evaluationData.detailedScores,
          hasScoreReasons: !!evaluationData.scoreReasons
        })

        // 检查回复质量并调整评分
        const userMessages = messages.filter(m => m.role === 'user')
        const lastUserMessage = userMessages[userMessages.length - 1]?.content || ''
        const shouldAdjustScore = shouldAdjustScoreForLowQuality(userMessages)

        let adjustedEvaluationData = evaluationData
        if (shouldAdjustScore) {
          console.log('检测到低质量回复，调整评分:', lastUserMessage)
          adjustedEvaluationData = adjustScoreForLowQuality(evaluationData, lastUserMessage)
        }

        // 验证并修复总分计算
        const messageCount = userMessages.length
        const weights = calculateDynamicWeights(messageCount)
        const detailedScores = adjustedEvaluationData.detailedScores || {}

        // 重新计算正确的总分
        const recalculatedOverallScore = Math.round(
          ((detailedScores.needsUnderstanding || 0) * weights.needsUnderstanding +
            (detailedScores.professionalism || 0) * weights.professionalism +
            (detailedScores.problemSolving || 0) * weights.problemSolving +
            (detailedScores.persuasiveness || 0) * weights.persuasiveness +
            (detailedScores.culturalSensitivity || 0) * weights.culturalSensitivity +
            (detailedScores.termControl || 0) * weights.termControl +
            (detailedScores.customerSatisfaction || 0) * weights.customerSatisfaction) / 100
        )

        console.log('总分计算验证:', {
          原始总分: adjustedEvaluationData.overallScore,
          重新计算总分: recalculatedOverallScore,
          各项分数: detailedScores,
          权重: weights
        })

        // 使用重新计算的总分
        adjustedEvaluationData.overallScore = recalculatedOverallScore

        // 构建详细评估结果
        const evaluation: IntelligentEvaluation = {
          id: `evaluation_${Date.now()}`,
          sessionId: session.id,
          overallScore: adjustedEvaluationData.overallScore || 0,
          detailedScores: {
            needsUnderstanding: adjustedEvaluationData.detailedScores?.needsUnderstanding || 0,
            professionalism: adjustedEvaluationData.detailedScores?.professionalism || 0,
            problemSolving: adjustedEvaluationData.detailedScores?.problemSolving || 0,
            persuasiveness: adjustedEvaluationData.detailedScores?.persuasiveness || 0,
            culturalSensitivity: adjustedEvaluationData.detailedScores?.culturalSensitivity || 0,
            termControl: adjustedEvaluationData.detailedScores?.termControl || 0,
            customerSatisfaction: adjustedEvaluationData.detailedScores?.customerSatisfaction || 0,
            overall: adjustedEvaluationData.overallScore || 0,
            timestamp: new Date()
          },
          scoreReasons: adjustedEvaluationData.scoreReasons || [],
          overallFeedback: adjustedEvaluationData.overallFeedback || '',
          keyStrengths: adjustedEvaluationData.keyStrengths || [],
          improvementSuggestions: adjustedEvaluationData.improvementSuggestions || [],
          nextStepRecommendations: adjustedEvaluationData.nextStepRecommendations || [],
          evaluatedAt: new Date(),
          evaluatedMessageCount: messages.filter(m => m.role === 'user').length
        }

        console.log('评估结果构建完成', evaluation)
        setCurrentEvaluation(evaluation)
        return evaluation

      } catch (parseError) {

        // 智能fallback评分：基于对话内容进行基础分析
        const fallbackEvaluation = generateFallbackEvaluation(
          session.id,
          messages,
          completionText,
          parseError as Error
        )

        setCurrentEvaluation(fallbackEvaluation)
        return fallbackEvaluation
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      setEvaluationError(`AI评分服务暂时不可用：${errorMessage}`)
      throw error
    } finally {
      setIsEvaluating(false)
    }
  }, [session, messages, trainingConfig.enableIntelligentScoring, buildIntelligentScoringPrompt])

  // 检测低质量回复的辅助函数
  const shouldAdjustScoreForLowQuality = useCallback((userMessages: AITrainingMessage[]) => {
    if (userMessages.length === 0) return false

    const lastMessage = userMessages[userMessages.length - 1]?.content || ''

    // 检测低质量回复的条件
    const isVeryShort = lastMessage.trim().length < 10 // 少于10个字符
    const isNumberOnly = /^\d+$/.test(lastMessage.trim()) // 纯数字
    const isSingleWord = lastMessage.trim().split(/\s+/).length === 1 && lastMessage.trim().length < 5 // 单个短词
    const isNoMeaning = /^[^\u4e00-\u9fa5a-zA-Z]*$/.test(lastMessage.trim()) // 无意义字符
    const isSimpleResponse = ['是', '不是', '好', '不好', '行', '不行', '可以', '不可以'].includes(lastMessage.trim())

    return isVeryShort || isNumberOnly || isSingleWord || isNoMeaning || isSimpleResponse
  }, [])

  // 调整低质量回复评分的函数
  const adjustScoreForLowQuality = useCallback((originalData: any, lastMessage: string) => {
    const lowScores = {
      needsUnderstanding: Math.min(originalData.detailedScores?.needsUnderstanding || 0, 30),
      professionalism: Math.min(originalData.detailedScores?.professionalism || 0, 25),
      problemSolving: Math.min(originalData.detailedScores?.problemSolving || 0, 30),
      persuasiveness: Math.min(originalData.detailedScores?.persuasiveness || 0, 25),
      culturalSensitivity: Math.min(originalData.detailedScores?.culturalSensitivity || 0, 35),
      termControl: Math.min(originalData.detailedScores?.termControl || 0, 30),
      customerSatisfaction: Math.min(originalData.detailedScores?.customerSatisfaction || 0, 20)
    }

    // 使用动态权重计算调整后的总分
    const userMessages = messages.filter(m => m.role === 'user')
    const messageCount = userMessages.length
    const weights = calculateDynamicWeights(messageCount)

    const adjustedOverallScore = Math.round(
      (lowScores.needsUnderstanding * weights.needsUnderstanding +
        lowScores.professionalism * weights.professionalism +
        lowScores.problemSolving * weights.problemSolving +
        lowScores.persuasiveness * weights.persuasiveness +
        lowScores.culturalSensitivity * weights.culturalSensitivity +
        lowScores.termControl * weights.termControl +
        lowScores.customerSatisfaction * weights.customerSatisfaction) / 100
    )

    console.log('低质量回复评分调整:', {
      原始分数: originalData.detailedScores,
      调整后分数: lowScores,
      权重: weights,
      调整后总分: adjustedOverallScore
    })

    return {
      ...originalData,
      overallScore: adjustedOverallScore,
      detailedScores: lowScores,
      overallFeedback: `销售人员回复"${lastMessage}"过于简单，缺乏专业性和销售技巧。${originalData.overallFeedback || ''}`,
      keyStrengths: [],
      improvementSuggestions: [
        '提供更详细和专业的回复',
        '展现产品知识和销售技巧',
        '主动了解客户需求',
        ...(originalData.improvementSuggestions || [])
      ],
      nextStepRecommendations: [
        '学习基本的销售沟通技巧',
        '准备专业的产品介绍话术',
        '练习客户需求挖掘方法',
        ...(originalData.nextStepRecommendations || [])
      ],
      scoreReasons: [
        {
          dimension: "整体评估",
          score: adjustedOverallScore,
          reason: `回复"${lastMessage}"过于简单，未体现专业的销售能力`,
          suggestion: "需要提供更详细、专业的销售回复",
          keyStrengths: [],
          improvementAreas: ["回复过于简单", "缺乏专业性", "未展现销售技巧"]
        },
        ...(originalData.scoreReasons || [])
      ]
    }
  }, [messages, calculateDynamicWeights])

  // 测试总分计算逻辑的函数
  const testScoreCalculation = useCallback((testScores: any, messageCount: number = 3) => {
    const weights = calculateDynamicWeights(messageCount)
    const calculatedScore = Math.round(
      (testScores.needsUnderstanding * weights.needsUnderstanding +
        testScores.professionalism * weights.professionalism +
        testScores.problemSolving * weights.problemSolving +
        testScores.persuasiveness * weights.persuasiveness +
        testScores.culturalSensitivity * weights.culturalSensitivity +
        testScores.termControl * weights.termControl +
        testScores.customerSatisfaction * weights.customerSatisfaction) / 100
    )

    console.log('评分计算测试:', {
      输入分数: testScores,
      消息轮数: messageCount,
      权重: weights,
      计算结果: calculatedScore
    })

    return calculatedScore
  }, [calculateDynamicWeights])

  // 更新配置
  const updateConfig = useCallback((newConfig: Partial<TrainingConfig>) => {
    setTrainingConfig(prev => ({ ...prev, ...newConfig }))
  }, [])

  return {
    // 状态
    session,
    messages,
    isLoading,
    isEvaluating, // 新增：评分加载状态
    trainingConfig,
    currentGuidance,
    isGeneratingGuidance, // 新增：AI建议生成状态
    currentEvaluation, // 新增：当前智能评估
    evaluationError, // 新增：评分错误状态

    // 操作
    initializeSession,
    sendMessage,
    resetSession,
    toggleSession,
    completeSession,
    saveSession,
    updateConfig,
    performIntelligentEvaluation, // 新增：执行智能评分
    testScoreCalculation, // 新增：测试评分计算
    clearAIGuidance, // 新增：清除AI建议

    // 计算属性
    getSessionDuration
  }
}
