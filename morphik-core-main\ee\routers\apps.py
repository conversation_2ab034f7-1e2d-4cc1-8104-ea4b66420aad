"""企业版路由器，允许**专用Morphik实例**的*客户端*配置隔离的Neon支持的数据库（"应用"）。
该端点隐藏了所有Neon复杂性，并返回其他Databridge API可以使用的Morphik特定连接URI。

此路由仅在*企业版*部署中可用，因此位于 `ee` 包内。
"""

from __future__ import annotations

import logging
from pathlib import Path
from typing import Dict

import toml
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field

from core.auth_utils import verify_token
from core.models.auth import AuthContext
from core.models.tiers import AccountTier, get_tier_limits

# 配置逻辑位于*core*中，因为它在后台作业和潜在的社区部署中也很有用。路由器仅限企业版。
from core.services.app_provisioning_service import AppProvisioningService, ProvisionResult
from core.services.user_service import UserService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ee", tags=["Enterprise"])


# ---------------------------------------------------------------------------
# 请求/响应模型（有意保持最小化）
# ---------------------------------------------------------------------------


class CreateAppRequest(BaseModel):  # noqa: D101 – 简单模式
    app_name: str = Field(..., description="要创建的应用程序的用户友好名称")
    region: str | None = Field(
        default=None,
        description="可选的Neon区域标识符（默认为 `aws-us-east-1`）",
    )


class CreateAppResponse(BaseModel):  # noqa: D101 – simple schema
    app_id: str
    app_name: str
    morphik_uri: str
    status: str


class NukeAppResponse(BaseModel):  # noqa: D101 – simple schema
    app_name: str
    status: str = "deleted"


# ---------------------------------------------------------------------------
# 端点实现
# ---------------------------------------------------------------------------


@router.post("/apps", response_model=CreateAppResponse, status_code=status.HTTP_201_CREATED, include_in_schema=True)
async def create_app_route(
    request: CreateAppRequest,
    auth: AuthContext = Depends(verify_token),
) -> Dict[str, str]:
    """为*request.app_name*配置一个**全新的**Neon数据库。

    调用者必须通过专用Morphik实例的身份验证。经过身份验证的用户
    （由JWT的*user_id*表示）成为配置应用的所有者。
    """

    # 确保身份验证具有user_id（在开发模式下生成）
    if not auth.user_id:
        raise HTTPException(status_code=403, detail="Missing user_id in token – cannot provision app")

    # 通过服务层执行繁重的工作
    service = AppProvisioningService()
    await service.initialize()

    # 从ee.toml加载morphik-host
    morphik_host: str | None = None
    try:
        # 假设ee.toml在'ee'目录中，从'ee/routers/apps.py'向上两级父目录然后进入'ee'
        # 如果ee.toml相对于此文件位于其他位置，请调整路径。
        # 对于'ee'是顶级包且此文件是ee/routers/apps.py的典型结构
        # ee_config_path = Path(__file__).resolve().parent.parent / "ee.toml"
        # 如果'ee'是'ee'包内容的根目录，则纠正路径假设
        ee_package_dir = Path(__file__).resolve().parent.parent  # 这应该指向'ee'目录
        ee_config_path = ee_package_dir / "ee.toml"

        if ee_config_path.exists():
            ee_config = toml.load(ee_config_path)
            morphik_host = ee_config.get("morphik-host")
        else:
            logger.error(f"未找到配置文件: {ee_config_path}")
            raise HTTPException(status_code=500, detail="Server configuration error: ee.toml not found.")

    except Exception as e:  # noqa: BLE001
        logger.error(f"加载ee.toml时出错: {e}")
        raise HTTPException(status_code=500, detail=f"Server configuration error: Could not load ee.toml. {e}")

    if not morphik_host:
        logger.error("ee.toml中未配置morphik-host。")
        raise HTTPException(status_code=500, detail="Server configuration error: morphik-host not set in ee.toml.")

    try:
        # 将morphik_host传递给服务
        result: ProvisionResult = await service.provision_new_app(
            auth.user_id,
            request.app_name,
            request.region,
            morphik_host=morphik_host,
        )

        # ------------------------------------------------------------------
        # 代码片段 `user_service = UserService()` 创建了 `UserService` 类的实例，
        # 该类负责与应用程序中的用户服务交互。
        # 在用户限制表中注册新创建的应用
        # ------------------------------------------------------------------
        user_service = UserService()
        await user_service.initialize()

        # ------------------------------------------------------------------
        # 检查计划资格并执行团队应用限制
        # ------------------------------------------------------------------

        user_limits = await user_service.get_user_limits(auth.user_id)

        if not user_limits:
            # 如果不存在，则创建默认限制记录
            await user_service.create_user(auth.user_id)
            user_limits = await user_service.get_user_limits(auth.user_id)

        tier = user_limits.get("tier", AccountTier.FREE)

        # 只有团队或企业版（自托管）客户可以配置隔离应用
        if tier not in (AccountTier.TEAMS, AccountTier.SELF_HOSTED):
            raise HTTPException(
                status_code=403,
                detail="Only Teams or Enterprise plan customers can create isolated applications. "
                "Upgrade your plan to access this feature.",
            )

        # 对于团队层级，确保用户仍在应用限制内
        if tier == AccountTier.TEAMS:
            tier_limits = get_tier_limits(tier, user_limits.get("custom_limits"))
            app_limit = tier_limits.get("app_limit", 50)
            current_apps = user_limits.get("app_ids", []) or []

            if len(current_apps) >= app_limit:
                raise HTTPException(
                    status_code=403,
                    detail=f"Application limit reached for Teams plan (maximum {app_limit}).",
                )

        # 在用户的限制配置文件中记录新应用
        await user_service.register_app(auth.user_id, result.app_id)

    except ValueError as ve:
        # 来自服务的重复名称或其他验证
        raise HTTPException(status_code=400, detail=str(ve)) from ve
    except Exception as exc:  # noqa: BLE001 – 捕获NeonAPIError和其他异常
        logger.exception("配置新应用失败: %s", exc)
        raise HTTPException(status_code=500, detail="Failed to provision app") from exc

    return result.as_dict()


# ---------------------------------------------------------------------------
# 删除/销毁端点
# ---------------------------------------------------------------------------


@router.delete(
    "/apps",
    response_model=NukeAppResponse,
    status_code=status.HTTP_200_OK,
    include_in_schema=True,
)
async def delete_app_route(
    app_name: str = Query(..., description="Name of the application to delete"),
    auth: AuthContext = Depends(verify_token),
) -> Dict[str, str]:
    """销毁与*app_name*关联的Neon项目和元数据。

    只有应用程序的所有者（通过*auth.user_id*标识）才能执行此破坏性操作。
    """

    # 确保身份验证具有user_id（在开发模式下生成）
    if not auth.user_id:
        raise HTTPException(status_code=403, detail="Missing user_id in token – cannot delete app")

    # 通过服务层执行繁重的工作
    service = AppProvisioningService()
    await service.initialize()

    try:
        await service.nuke_app_by_name(auth.user_id, app_name)
    except PermissionError:
        raise HTTPException(status_code=403, detail="Not authorised to delete this app")
    except RuntimeError:
        raise HTTPException(status_code=404, detail="App not found")
    except Exception as exc:  # noqa: BLE001
        logger.exception("删除应用失败: %s", exc)
        raise HTTPException(status_code=500, detail="Failed to delete app") from exc

    return {"app_name": app_name, "status": "deleted"}
