/**
 * 权限选择器组件
 * 提供权限的分组显示、搜索、批量选择等功能
 */
import React, { useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Search, ChevronDown, ChevronRight, CheckSquare, Square, Minus } from 'lucide-react'

interface Permission {
  id: number
  name: string
  description: string
  category: string
}

interface PermissionSelectorProps {
  permissions: Permission[]
  selectedPermissionIds: number[]
  onPermissionToggle: (permissionId: number, checked: boolean) => void
  originalPermissionIds?: number[]
  disabled?: boolean
  showChanges?: boolean
  className?: string
}

export const PermissionSelector: React.FC<PermissionSelectorProps> = ({
  permissions,
  selectedPermissionIds,
  onPermissionToggle,
  originalPermissionIds = [],
  disabled = false,
  showChanges = false,
  className = '',
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())

  // 按分类分组权限
  const groupedPermissions = useMemo(() => {
    return permissions.reduce((groups, permission) => {
      const category = permission.category || '其他'
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push(permission)
      return groups
    }, {} as Record<string, Permission[]>)
  }, [permissions])

  // 过滤权限（搜索）
  const filteredGroupedPermissions = useMemo(() => {
    if (!searchTerm) return groupedPermissions

    return Object.entries(groupedPermissions).reduce((filtered, [category, categoryPermissions]) => {
      const filteredPermissions = categoryPermissions.filter(permission =>
        permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        permission.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
      if (filteredPermissions.length > 0) {
        filtered[category] = filteredPermissions
      }
      return filtered
    }, {} as Record<string, Permission[]>)
  }, [groupedPermissions, searchTerm])

  // 切换分类展开状态
  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(category)) {
      newExpanded.delete(category)
    } else {
      newExpanded.add(category)
    }
    setExpandedCategories(newExpanded)
  }

  // 获取分类的选择状态
  const getCategorySelectionState = (categoryPermissions: Permission[]) => {
    const categoryPermissionIds = categoryPermissions.map(p => p.id)
    const selectedCount = categoryPermissionIds.filter(id => selectedPermissionIds.includes(id)).length
    
    if (selectedCount === 0) return 'none'
    if (selectedCount === categoryPermissionIds.length) return 'all'
    return 'partial'
  }

  // 切换分类的所有权限
  const toggleCategoryPermissions = (categoryPermissions: Permission[]) => {
    const categoryPermissionIds = categoryPermissions.map(p => p.id)
    const selectionState = getCategorySelectionState(categoryPermissions)
    
    if (selectionState === 'all') {
      // 取消选择所有
      categoryPermissionIds.forEach(id => {
        if (selectedPermissionIds.includes(id)) {
          onPermissionToggle(id, false)
        }
      })
    } else {
      // 选择所有
      categoryPermissionIds.forEach(id => {
        if (!selectedPermissionIds.includes(id)) {
          onPermissionToggle(id, true)
        }
      })
    }
  }

  // 全选/取消全选
  const toggleAllPermissions = () => {
    const allPermissionIds = permissions.map(p => p.id)
    const allSelected = allPermissionIds.every(id => selectedPermissionIds.includes(id))
    
    if (allSelected) {
      // 取消全选
      allPermissionIds.forEach(id => {
        if (selectedPermissionIds.includes(id)) {
          onPermissionToggle(id, false)
        }
      })
    } else {
      // 全选
      allPermissionIds.forEach(id => {
        if (!selectedPermissionIds.includes(id)) {
          onPermissionToggle(id, true)
        }
      })
    }
  }

  // 展开所有分类
  const expandAllCategories = () => {
    setExpandedCategories(new Set(Object.keys(filteredGroupedPermissions)))
  }

  // 折叠所有分类
  const collapseAllCategories = () => {
    setExpandedCategories(new Set())
  }

  const totalPermissions = permissions.length
  const selectedCount = selectedPermissionIds.length
  const allSelected = totalPermissions > 0 && selectedCount === totalPermissions

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">权限选择</CardTitle>
            <CardDescription>
              已选择 {selectedCount} / {totalPermissions} 个权限
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={expandAllCategories}
              disabled={disabled}
            >
              展开全部
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={collapseAllCategories}
              disabled={disabled}
            >
              折叠全部
            </Button>
          </div>
        </div>
        
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索权限..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
            disabled={disabled}
          />
        </div>

        {/* 全选控制 */}
        <div className="flex items-center justify-between p-3 border rounded-lg bg-muted/50">
          <div className="flex items-center space-x-3">
            <Checkbox
              checked={allSelected}
              onCheckedChange={toggleAllPermissions}
              disabled={disabled}
            />
            <span className="font-medium">全选权限</span>
          </div>
          <Badge variant="secondary">
            {selectedCount} / {totalPermissions}
          </Badge>
        </div>
      </CardHeader>

      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-2">
            {Object.entries(filteredGroupedPermissions).map(([category, categoryPermissions]) => {
              const selectionState = getCategorySelectionState(categoryPermissions)
              const isExpanded = expandedCategories.has(category)
              
              return (
                <Collapsible
                  key={category}
                  open={isExpanded}
                  onOpenChange={() => toggleCategory(category)}
                >
                  <CollapsibleTrigger asChild>
                    <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                          <span className="font-medium">{category}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {categoryPermissions.length}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            toggleCategoryPermissions(categoryPermissions)
                          }}
                          disabled={disabled}
                          className="h-6 px-2"
                        >
                          {selectionState === 'all' ? (
                            <CheckSquare className="h-3 w-3" />
                          ) : selectionState === 'partial' ? (
                            <Minus className="h-3 w-3" />
                          ) : (
                            <Square className="h-3 w-3" />
                          )}
                        </Button>
                        <Badge 
                          variant={selectionState === 'none' ? 'secondary' : 'default'}
                          className="text-xs"
                        >
                          {categoryPermissions.filter(p => selectedPermissionIds.includes(p.id)).length}
                        </Badge>
                      </div>
                    </div>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent>
                    <div className="ml-6 mt-2 space-y-2">
                      {categoryPermissions.map((permission) => {
                        const isSelected = selectedPermissionIds.includes(permission.id)
                        const wasOriginallySelected = originalPermissionIds.includes(permission.id)
                        const isAdded = showChanges && isSelected && !wasOriginallySelected
                        const isRemoved = showChanges && !isSelected && wasOriginallySelected
                        
                        return (
                          <div key={permission.id} className="flex items-start space-x-3 p-2 border rounded-lg">
                            <Checkbox
                              id={`permission-${permission.id}`}
                              checked={isSelected}
                              onCheckedChange={(checked) => 
                                onPermissionToggle(permission.id, checked as boolean)
                              }
                              disabled={disabled}
                            />
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2">
                                <label
                                  htmlFor={`permission-${permission.id}`}
                                  className="font-medium text-sm cursor-pointer"
                                >
                                  {permission.name}
                                </label>
                                {showChanges && wasOriginallySelected && !isAdded && !isRemoved && (
                                  <Badge variant="outline" className="text-xs">
                                    当前
                                  </Badge>
                                )}
                                {isAdded && (
                                  <Badge variant="default" className="text-xs">
                                    新增
                                  </Badge>
                                )}
                                {isRemoved && (
                                  <Badge variant="destructive" className="text-xs">
                                    移除
                                  </Badge>
                                )}
                              </div>
                              {permission.description && (
                                <p className="text-xs text-muted-foreground mt-1">
                                  {permission.description}
                                </p>
                              )}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              )
            })}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}
