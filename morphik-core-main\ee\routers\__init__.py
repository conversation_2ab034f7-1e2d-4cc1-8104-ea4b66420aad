"""企业版专用FastAPI路由器。

此子包捆绑了**所有**仅在Morphik企业版中可用的额外HTTP API路由。
每个模块都应该公开一个名为 ``router`` 的 ``APIRouter`` 实例，
以便可以通过 :pyfunc:`ee.init_app` 方便地挂载。
"""

import logging
from importlib import import_module
from typing import List

from fastapi import FastAPI

from .apps import router as _apps_router  # noqa: F401 – imported for side effects

__all__: List[str] = []


def init_app(app: FastAPI) -> None:
    """将所有企业版路由器挂载到给定的*app*实例上。"""
    logger = logging.getLogger(__name__)
    logger.info("EE.ROUTERS.INIT_APP: 正在初始化企业版路由器...")

    # 懒加载发现路由器 – 导入注册全局 ``router`` 属性的子模块。
    # 在此处保持列表明确，以避免意外暴露未完成的模块。
    for module_path in [
        "ee.routers.cloud_uri",
        "ee.routers.apps",
        "ee.routers.connectors_router",
    ]:
        try:
            mod = import_module(module_path)

            if hasattr(mod, "router"):
                app.include_router(mod.router)
            else:
                logger.warning(f"EE.ROUTERS.INIT_APP: 模块 {module_path} 没有 'router' 属性。")
        except ImportError as e:
            logger.error(f"EE.ROUTERS.INIT_APP: 导入 {module_path} 失败: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"EE.ROUTERS.INIT_APP: 处理 {module_path} 时发生意外错误: {e}", exc_info=True)
            # 如果关键路由器失败，可能重新抛出或处理，或决定继续
            # 现在，只是记录并继续，看看其他路由器是否加载。
    logger.info("EE.ROUTERS.INIT_APP: 企业版路由器初始化完成。")
