"""
统一配置管理工具
消除重复代码，提供单一配置管理入口
"""

import warnings
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum


class TaskType(Enum):
    """任务类型枚举"""
    STRATEGY_GENERATION = "strategy_generation"
    SINGLE_SEARCH = "single_search"
    MARKET_ANALYSIS = "market_analysis"
    CODE_EXECUTION = "code_execution"
    FINAL_ANALYSIS = "final_analysis"
    AGENT_DEFAULT = "agent_default"


@dataclass
class TaskConfig:
    """任务配置"""
    max_steps: int
    timeout: int
    description: str
    use_cache: bool = True
    enable_web_search: bool = True


@dataclass
class ConfigValidationResult:
    """配置验证结果"""
    is_valid: bool
    message: str
    adjusted_config: Optional[TaskConfig] = None
    original_config: Optional[Dict[str, Any]] = None


class UnifiedConfigManager:
    """统一配置管理器 - 单一配置管理入口"""

    # 基础任务配置 - 所有配置的唯一来源
    BASE_CONFIGS = {
        TaskType.STRATEGY_GENERATION: TaskConfig(
            max_steps=3,
            timeout=90,
            description="策略生成",
            use_cache=False,
            enable_web_search=False
        ),
        TaskType.SINGLE_SEARCH: TaskConfig(
            max_steps=8,
            timeout=160,
            description="单次搜索",
            use_cache=True,
            enable_web_search=True
        ),
        TaskType.MARKET_ANALYSIS: TaskConfig(
            max_steps=12,
            timeout=900,
            description="市场分析",
            use_cache=False,
            enable_web_search=True
        ),
        TaskType.CODE_EXECUTION: TaskConfig(
            max_steps=3,
            timeout=90,
            description="代码执行",
            use_cache=False,
            enable_web_search=False
        ),
        TaskType.FINAL_ANALYSIS: TaskConfig(
            max_steps=12,
            timeout=240,
            description="最终分析",
            use_cache=True,
            enable_web_search=False
        ),
        TaskType.AGENT_DEFAULT: TaskConfig(
            max_steps=8,
            timeout=120,
            description="代理默认",
            use_cache=True,
            enable_web_search=True
        )
    }

    @classmethod
    def get_config(
        cls,
        task_type: TaskType,
        user_overrides: Optional[Dict[str, Any]] = None,
        strategy_count: Optional[int] = None,
        auto_adjust: bool = True
    ) -> TaskConfig:
        """
        统一配置获取方法 - 唯一的配置获取入口

        Args:
            task_type: 任务类型
            user_overrides: 用户覆盖配置
            strategy_count: 策略数量（用于动态调整）
            auto_adjust: 是否自动调整配置到合理范围

        Returns:
            TaskConfig: 最终配置
        """
        if task_type not in cls.BASE_CONFIGS:
            raise ValueError(f"不支持的任务类型: {task_type}")

        # 获取基础配置
        base_config = cls.BASE_CONFIGS[task_type]

        # 创建配置副本
        config = TaskConfig(
            max_steps=base_config.max_steps,
            timeout=base_config.timeout,
            description=base_config.description,
            use_cache=base_config.use_cache,
            enable_web_search=base_config.enable_web_search
        )

        # 根据策略数量动态调整（仅对市场分析有效）
        if task_type == TaskType.MARKET_ANALYSIS and strategy_count:
            config = cls._adjust_for_strategy_count(config, strategy_count)

        # 应用用户覆盖
        if user_overrides:
            for key, value in user_overrides.items():
                if hasattr(config, key) and value is not None:
                    setattr(config, key, value)

        # 验证并调整配置（如果启用自动调整）
        if auto_adjust:
            config = cls._validate_and_adjust_config(config, task_type)

        return config

    @classmethod
    def _adjust_for_strategy_count(cls, config: TaskConfig, strategy_count: int) -> TaskConfig:
        """统一的策略数量调整逻辑 - 更多策略需要更多时间和步骤"""
        if strategy_count <= 4:
            multiplier = 1.0  # 基准配置
        elif strategy_count <= 7:
            multiplier = 1.2  # 增加20%
        else:
            multiplier = 1.5  # 增加50%

        config.max_steps = int(config.max_steps * multiplier)
        config.timeout = int(config.timeout * multiplier)
        config.description = f"{config.description} (已针对{strategy_count}个策略优化)"

        return config

    @classmethod
    def _validate_and_adjust_config(cls, config: TaskConfig, task_type: TaskType) -> TaskConfig:
        """统一的配置验证和调整逻辑"""
        # 确保最小值
        config.max_steps = max(1, config.max_steps)
        config.timeout = max(10, config.timeout)

        # 确保最大值
        config.max_steps = min(50, config.max_steps)
        config.timeout = min(1800, config.timeout)  # 最大30分钟

        # 确保步骤数与超时时间的匹配性
        min_timeout = config.max_steps * 5  # 每步至少5秒
        if config.timeout < min_timeout:
            config.timeout = min_timeout
            warnings.warn(
                f"超时时间已调整为 {config.timeout}秒 以匹配 {config.max_steps} 步执行"
            )

        return config

    @classmethod
    def validate_config(
        cls,
        task_type: TaskType,
        max_steps: Optional[int] = None,
        timeout: Optional[int] = None,
        strategy_count: Optional[int] = None
    ) -> ConfigValidationResult:
        """
        统一的配置验证方法

        Args:
            task_type: 任务类型
            max_steps: 最大步骤数
            timeout: 超时时间
            strategy_count: 策略数量

        Returns:
            ConfigValidationResult: 验证结果
        """
        # 构建用户覆盖配置
        user_overrides = {}
        if max_steps is not None:
            user_overrides['max_steps'] = max_steps
        if timeout is not None:
            user_overrides['timeout'] = timeout

        # 获取原始配置（不自动调整）
        original_config = cls.get_config(
            task_type=task_type,
            user_overrides=user_overrides,
            strategy_count=strategy_count,
            auto_adjust=False
        )

        # 获取调整后的配置
        adjusted_config = cls.get_config(
            task_type=task_type,
            user_overrides=user_overrides,
            strategy_count=strategy_count,
            auto_adjust=True
        )

        # 检查是否需要调整
        is_valid = (
            original_config.max_steps == adjusted_config.max_steps and
            original_config.timeout == adjusted_config.timeout
        )

        message = "配置合理" if is_valid else "配置已自动调整到合理范围"

        return ConfigValidationResult(
            is_valid=is_valid,
            message=message,
            adjusted_config=adjusted_config,
            original_config={
                'max_steps': original_config.max_steps,
                'timeout': original_config.timeout
            }
        )


# ConfigValidator 类已移除，所有功能已整合到 UnifiedConfigManager


# ============================================================================
# 统一配置管理接口 - 所有配置获取的唯一入口
# ============================================================================

def get_task_config(
    task_type: TaskType,
    user_overrides: Optional[Dict[str, Any]] = None,
    strategy_count: Optional[int] = None,
    auto_adjust: bool = True
) -> TaskConfig:
    """
    统一的任务配置获取函数 - 唯一推荐的配置获取方式

    Args:
        task_type: 任务类型
        user_overrides: 用户覆盖配置
        strategy_count: 策略数量（用于动态调整）
        auto_adjust: 是否自动调整配置到合理范围

    Returns:
        TaskConfig: 最终配置
    """
    return UnifiedConfigManager.get_config(
        task_type=task_type,
        user_overrides=user_overrides,
        strategy_count=strategy_count,
        auto_adjust=auto_adjust
    )


def get_agent_request_config(
    task_type: TaskType,
    user_max_steps: Optional[int] = None,
    user_timeout: Optional[int] = None,
    strategy_count: Optional[int] = None
) -> Dict[str, Any]:
    """
    获取 AgentRequest 的配置参数

    Args:
        task_type: 任务类型
        user_max_steps: 用户指定的最大步骤数
        user_timeout: 用户指定的超时时间
        strategy_count: 策略数量

    Returns:
        包含 max_steps, timeout, use_cache, enable_web_search 的字典
    """
    user_overrides = {}
    if user_max_steps is not None:
        user_overrides['max_steps'] = user_max_steps
    if user_timeout is not None:
        user_overrides['timeout'] = user_timeout

    config = get_task_config(task_type, user_overrides, strategy_count)

    return {
        'max_steps': config.max_steps,
        'timeout': config.timeout,
        'use_cache': config.use_cache,
        'enable_web_search': config.enable_web_search
    }


def validate_config(
    task_type: TaskType,
    max_steps: Optional[int] = None,
    timeout: Optional[int] = None,
    strategy_count: Optional[int] = None
) -> ConfigValidationResult:
    """
    统一的配置验证函数

    Args:
        task_type: 任务类型
        max_steps: 最大步骤数
        timeout: 超时时间
        strategy_count: 策略数量

    Returns:
        ConfigValidationResult: 验证结果
    """
    return UnifiedConfigManager.validate_config(
        task_type=task_type,
        max_steps=max_steps,
        timeout=timeout,
        strategy_count=strategy_count
    )


# ============================================================================
# 兼容性函数 - 保持向后兼容，但建议使用上面的统一接口
# ============================================================================

def validate_and_adjust(
    max_steps: int,
    timeout: int,
    task_type: TaskType,
    strategy_count: Optional[int] = None,
    auto_adjust: bool = False
) -> Dict[str, Any]:
    """
    兼容性函数：验证并可选地自动调整配置

    注意：建议使用 validate_config() 函数替代此函数
    """
    validation_result = validate_config(
        task_type=task_type,
        max_steps=max_steps,
        timeout=timeout,
        strategy_count=strategy_count
    )

    result = {
        "is_valid": validation_result.is_valid,
        "message": validation_result.message,
        "original_config": validation_result.original_config
    }

    if validation_result.adjusted_config and auto_adjust:
        result["adjusted_config"] = {
            "max_steps": validation_result.adjusted_config.max_steps,
            "timeout": validation_result.adjusted_config.timeout,
            "adjustment_message": f"配置已从 {max_steps}步/{timeout}秒 调整为 {validation_result.adjusted_config.max_steps}步/{validation_result.adjusted_config.timeout}秒"
        }

    return result
