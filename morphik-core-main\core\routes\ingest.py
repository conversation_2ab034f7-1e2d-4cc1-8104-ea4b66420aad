import base64
import json
import logging
import uuid
from datetime import UTC, datetime
from typing import List, Optional

import arq
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile

from core.auth_utils import verify_token
from core.config import get_settings
from core.dependencies import get_redis_pool
from core.limits_utils import check_and_increment_limits, estimate_pages_by_chars
from core.models.auth import AuthContext
from core.models.documents import Document
from core.models.request import BatchIngestResponse, IngestTextRequest
from core.services.telemetry import TelemetryService
from core.services_init import document_service, storage

# ---------------------------------------------------------------------------
# 路由器初始化和共享单例
# ---------------------------------------------------------------------------

router = APIRouter(prefix="/ingest", tags=["Ingestion"])
logger = logging.getLogger(__name__)
settings = get_settings()
telemetry = TelemetryService()

# ---------------------------------------------------------------------------
# /ingest/text
# ---------------------------------------------------------------------------


@router.post("/text", response_model=Document)
@telemetry.track(operation_type="ingest_text", metadata_resolver=telemetry.ingest_text_metadata)
async def ingest_text(
    request: IngestTextRequest,
    auth: AuthContext = Depends(verify_token),
) -> Document:
    """摄取**文本**文档。

    参数:
        request: IngestTextRequest载荷包含:
            • content – 要摄取的原始文本。
            • filename – 可选的文件名，用于帮助检测MIME类型。
            • metadata – 可选的JSON元数据字典。
            • rules – 可选的提取/自然语言规则列表。
            • folder_name – 可选的文件夹范围。
            • end_user_id – 可选的终端用户范围。
        auth: 解码的JWT上下文（注入）。

    返回:
        表示新摄取文本的文档元数据行。
    """
    try:
        # 免费层使用限制（仅云模式）
        if settings.MODE == "cloud" and auth.user_id:
            pages_est = estimate_pages_by_chars(len(request.content))
            await check_and_increment_limits(
                auth,
                "ingest",
                pages_est,
                verify_only=True,
            )

        return await document_service.ingest_text(
            content=request.content,
            filename=request.filename,
            metadata=request.metadata,
            rules=request.rules,
            use_colpali=request.use_colpali,
            auth=auth,
            folder_name=request.folder_name,
            end_user_id=request.end_user_id,
        )
    except PermissionError as exc:
        raise HTTPException(status_code=403, detail=str(exc))


# ---------------------------------------------------------------------------
# /ingest/file
# ---------------------------------------------------------------------------


@router.post("/file", response_model=Document)
@telemetry.track(operation_type="queue_ingest_file", metadata_resolver=telemetry.ingest_file_metadata)
async def ingest_file(
    file: UploadFile,
    metadata: str = Form("{}"),
    rules: str = Form("[]"),
    auth: AuthContext = Depends(verify_token),
    use_colpali: Optional[bool] = Form(None),
    folder_name: Optional[str] = Form(None),
    end_user_id: Optional[str] = Form(None),
    redis: arq.ArqRedis = Depends(get_redis_pool),
) -> Document:
    """异步摄取**文件**。

    文件被上传到对象存储，持久化一个状态为 ``status='processing'`` 的*文档*存根，
    后台工作进程负责繁重的解析/分块工作。

    参数:
        file: 来自multipart/form-data的上传文件。
        metadata: 表示用户元数据的JSON字符串。
        rules: 包含提取/自然语言规则列表的JSON字符串。
        auth: 调用者上下文 – 必须包含*写入*权限。
        use_colpali: 切换到多向量嵌入。
        folder_name: 可选地将文档范围限制到文件夹。
        end_user_id: 可选地将文档范围限制到终端用户。
        redis: arq redis连接 – 用于将作业加入队列。

    返回:
        状态为 ``status='processing'`` 的文档存根。
    """
    try:
        # ------------------------------------------------------------------
        # 解析和验证输入
        # ------------------------------------------------------------------
        metadata_dict = json.loads(metadata)
        rules_list = json.loads(rules)

        def str2bool(v):
            return v if isinstance(v, bool) else str(v).lower() in {"true", "1", "yes"}

        use_colpali_bool = str2bool(use_colpali)

        if "write" not in auth.permissions:
            raise PermissionError("User does not have write permission")

        logger.debug("将文件摄取加入队列，use_colpali=%s", use_colpali_bool)

        # ------------------------------------------------------------------
        # 创建初始文档存根（状态 = 处理中）
        # ------------------------------------------------------------------
        doc = Document(
            content_type=file.content_type,
            filename=file.filename,
            metadata=metadata_dict,
            owner={"type": auth.entity_type.value, "id": auth.entity_id},
            access_control={
                "readers": [auth.entity_id],
                "writers": [auth.entity_id],
                "admins": [auth.entity_id],
                "user_id": [auth.user_id] if auth.user_id else [],
                "app_access": ([auth.app_id] if auth.app_id else []),
            },
            system_metadata={"status": "processing"},
        )

        # 始终在system_metadata中设置folder_name（如果未提供则为None）
        doc.system_metadata["folder_name"] = folder_name
        if end_user_id:
            doc.system_metadata["end_user_id"] = end_user_id
        if auth.app_id:
            doc.system_metadata["app_id"] = auth.app_id

        # 在应用数据库中存储存根（不是控制平面数据库）
        app_db = document_service.db
        success = await app_db.store_document(doc)
        if not success:
            raise Exception("Failed to store document metadata")

        # 确保文件夹存在（尽力而为）
        if folder_name:
            try:
                await document_service._ensure_folder_exists(folder_name, doc.external_id, auth)
            except Exception as err:  # noqa: BLE001
                logger.error("确保文件夹存在时出错: %s", err)

        # ------------------------------------------------------------------
        # 读取文件内容并预检查存储限制
        # ------------------------------------------------------------------
        file_content = await file.read()

        if settings.MODE == "cloud" and auth.user_id:
            await check_and_increment_limits(auth, "storage_file", 1, verify_only=True)
            await check_and_increment_limits(auth, "storage_size", len(file_content), verify_only=True)

        # ------------------------------------------------------------------
        # 将文件上传到对象存储
        # ------------------------------------------------------------------
        file_key = f"ingest_uploads/{uuid.uuid4()}/{file.filename}"
        file_content_b64 = base64.b64encode(file_content).decode()
        bucket_override = await document_service._get_bucket_for_app(auth.app_id)
        bucket, stored_key = await storage.upload_from_base64(
            file_content_b64,
            file_key,
            file.content_type,
            bucket=bucket_override or "",
        )

        doc.storage_info = {"bucket": bucket, "key": stored_key}

        # 保留存储历史
        from core.models.documents import StorageFileInfo  # 本地导入以避免循环

        doc.storage_files = [
            StorageFileInfo(
                bucket=bucket,
                key=stored_key,
                version=1,
                filename=file.filename,
                content_type=file.content_type,
                timestamp=datetime.now(UTC),
            )
        ]

        await app_db.update_document(
            document_id=doc.external_id,
            updates={"storage_info": doc.storage_info, "storage_files": doc.storage_files},
            auth=auth,
        )

        # 现在记录存储使用情况（云模式）
        if settings.MODE == "cloud" and auth.user_id:
            try:
                await check_and_increment_limits(auth, "storage_file", 1)
                await check_and_increment_limits(auth, "storage_size", len(file_content))
            except Exception as rec_exc:  # noqa: BLE001
                logger.error("记录存储使用情况失败: %s", rec_exc)

        # ------------------------------------------------------------------
        # 将作业推送到摄取工作队列
        # ------------------------------------------------------------------
        auth_dict = {
            "entity_type": auth.entity_type.value,
            "entity_id": auth.entity_id,
            "app_id": auth.app_id,
            "permissions": list(auth.permissions),
            "user_id": auth.user_id,
        }

        job = await redis.enqueue_job(
            "process_ingestion_job",
            document_id=doc.external_id,
            file_key=stored_key,
            bucket=bucket,
            original_filename=file.filename,
            content_type=file.content_type,
            metadata_json=metadata,
            auth_dict=auth_dict,
            rules_list=rules_list,
            use_colpali=use_colpali_bool,
            folder_name=folder_name,
            end_user_id=end_user_id,
        )

        logger.info("文件摄取作业已加入队列 (job_id=%s, doc=%s)", job.job_id, doc.external_id)
        return doc
    except json.JSONDecodeError as exc:
        raise HTTPException(status_code=400, detail=f"Invalid JSON: {str(exc)}")
    except PermissionError as exc:
        raise HTTPException(status_code=403, detail=str(exc))
    except Exception as exc:  # noqa: BLE001
        logger.error("文件摄取过程中出错: %s", exc)
        raise HTTPException(status_code=500, detail=f"Error during file ingestion: {str(exc)}")


# ---------------------------------------------------------------------------
# /ingest/files (批量)
# ---------------------------------------------------------------------------


@router.post("/files", response_model=BatchIngestResponse)
@telemetry.track(operation_type="queue_batch_ingest", metadata_resolver=telemetry.batch_ingest_metadata)
async def batch_ingest_files(
    files: List[UploadFile] = File(...),
    metadata: str = Form("{}"),
    rules: str = Form("[]"),
    use_colpali: Optional[bool] = Form(None),
    folder_name: Optional[str] = Form(None),
    end_user_id: Optional[str] = Form(None),
    auth: AuthContext = Depends(verify_token),
    redis: arq.ArqRedis = Depends(get_redis_pool),
) -> BatchIngestResponse:
    """批量摄取**多个文件**（异步）。

    每个文件的处理方式与 :func:`ingest_file` 相同，但共享同一个请求
    避免了多次往返。所有繁重的工作仍然委托给后台工作进程池。

    参数:
        files: 要上传的文件列表。
        metadata: 单个JSON字符串字典或与文件数量匹配的字典列表。
        rules: 单个规则列表或每个文件的规则列表的列表。
        use_colpali: 启用多向量嵌入。
        folder_name: **所有**文件的可选文件夹范围。
        end_user_id: **所有**文件的可选终端用户范围。
        auth: 具有*写入*权限的调用者上下文。
        redis: 用于将作业加入队列的arq redis连接。

    返回:
        汇总已创建文档和错误的BatchIngestResponse。
    """
    if not files:
        raise HTTPException(status_code=400, detail="No files provided for batch ingestion")

    try:
        metadata_value = json.loads(metadata)
        rules_list = json.loads(rules)

        def str2bool(v):
            return str(v).lower() in {"true", "1", "yes"}

        use_colpali_bool = str2bool(use_colpali)

        if "write" not in auth.permissions:
            raise PermissionError("User does not have write permission")
    except json.JSONDecodeError as exc:
        raise HTTPException(status_code=400, detail=f"Invalid JSON: {str(exc)}")
    except PermissionError as exc:
        raise HTTPException(status_code=403, detail=str(exc))

    # 当提供列表时验证元数据长度
    if isinstance(metadata_value, list) and len(metadata_value) != len(files):
        raise HTTPException(
            status_code=400,
            detail=(f"Number of metadata items ({len(metadata_value)}) must match number of files " f"({len(files)})"),
        )

    # 当提供规则列表的列表时验证规则
    if isinstance(rules_list, list) and rules_list and isinstance(rules_list[0], list):
        if len(rules_list) != len(files):
            raise HTTPException(
                status_code=400,
                detail=(f"Number of rule lists ({len(rules_list)}) must match number of files " f"({len(files)})"),
            )

    auth_dict = {
        "entity_type": auth.entity_type.value,
        "entity_id": auth.entity_id,
        "app_id": auth.app_id,
        "permissions": list(auth.permissions),
        "user_id": auth.user_id,
    }

    created_documents: List[Document] = []

    try:
        for idx, file in enumerate(files):
            metadata_item = metadata_value[idx] if isinstance(metadata_value, list) else metadata_value
            file_rules = (
                rules_list[idx]
                if isinstance(rules_list, list) and rules_list and isinstance(rules_list[0], list)
                else rules_list
            )

            # ------------------------------------------------------------------
            # 创建存根文档（处理中）
            # ------------------------------------------------------------------
            doc = Document(
                content_type=file.content_type,
                filename=file.filename,
                metadata=metadata_item,
                owner={"type": auth.entity_type.value, "id": auth.entity_id},
                access_control={
                    "readers": [auth.entity_id],
                    "writers": [auth.entity_id],
                    "admins": [auth.entity_id],
                    "user_id": [auth.user_id] if auth.user_id else [],
                    "app_access": ([auth.app_id] if auth.app_id else []),
                },
            )

            # 始终在system_metadata中设置folder_name（如果未提供则为None）
            doc.system_metadata["folder_name"] = folder_name
            if end_user_id:
                doc.system_metadata["end_user_id"] = end_user_id
            if auth.app_id:
                doc.system_metadata["app_id"] = auth.app_id
            doc.system_metadata["status"] = "processing"

            app_db = document_service.db
            success = await app_db.store_document(doc)
            if not success:
                raise Exception(f"Failed to store document metadata for {file.filename}")

            if folder_name:
                try:
                    await document_service._ensure_folder_exists(folder_name, doc.external_id, auth)
                except Exception as err:  # noqa: BLE001
                    logger.error("确保文件夹存在时出错: %s", err)

            file_content = await file.read()

            if settings.MODE == "cloud" and auth.user_id:
                await check_and_increment_limits(auth, "storage_file", 1, verify_only=True)
                await check_and_increment_limits(auth, "storage_size", len(file_content), verify_only=True)

            file_key = f"ingest_uploads/{uuid.uuid4()}/{file.filename}"
            file_content_b64 = base64.b64encode(file_content).decode()
            bucket_override = await document_service._get_bucket_for_app(auth.app_id)
            bucket, stored_key = await storage.upload_from_base64(
                file_content_b64,
                file_key,
                file.content_type,
                bucket=bucket_override or "",
            )

            doc.storage_info = {"bucket": bucket, "key": stored_key}
            await app_db.update_document(
                document_id=doc.external_id,
                updates={"storage_info": doc.storage_info},
                auth=auth,
            )

            if settings.MODE == "cloud" and auth.user_id:
                try:
                    await check_and_increment_limits(auth, "storage_file", 1)
                    await check_and_increment_limits(auth, "storage_size", len(file_content))
                except Exception as rec_exc:  # noqa: BLE001
                    logger.error("记录存储使用情况失败: %s", rec_exc)

            metadata_json = json.dumps(metadata_item)

            job = await redis.enqueue_job(
                "process_ingestion_job",
                document_id=doc.external_id,
                file_key=stored_key,
                bucket=bucket,
                original_filename=file.filename,
                content_type=file.content_type,
                metadata_json=metadata_json,
                auth_dict=auth_dict,
                rules_list=file_rules,
                use_colpali=use_colpali_bool,
                folder_name=folder_name,
                end_user_id=end_user_id,
            )

            logger.info("批量摄取已加入队列 (job_id=%s, doc=%s, idx=%s)", job.job_id, doc.external_id, idx)
            created_documents.append(doc)

        return BatchIngestResponse(documents=created_documents, errors=[])
    except Exception as exc:  # noqa: BLE001
        logger.error("批量摄取加入队列时出错: %s", exc)
        raise HTTPException(status_code=500, detail=f"Error queueing batch ingestion: {str(exc)}")
