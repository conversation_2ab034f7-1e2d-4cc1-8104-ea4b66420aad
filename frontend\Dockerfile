# 使用Node.js官方镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 接收构建参数
ARG VITE_DEV_MODE=false
ARG VITE_API_BASE_URL=http://localhost:8001
ARG VITE_MORPHIK_API_URL=http://localhost:8000
ARG VITE_SMOLAGENTS_API_URL=http://localhost:8002
ARG VITE_APP_VERSION=1.0.0

# 设置环境变量
ENV VITE_DEV_MODE=$VITE_DEV_MODE
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_MORPHIK_API_URL=$VITE_MORPHIK_API_URL
ENV VITE_SMOLAGENTS_API_URL=$VITE_SMOLAGENTS_API_URL
ENV VITE_APP_VERSION=$VITE_APP_VERSION

# 配置 npm 国内镜像源
RUN npm config set registry https://registry.npmmirror.com

# 复制依赖文件
COPY package*.json ./

# 清理npm缓存并安装依赖（使用更稳定的安装方式）
RUN npm cache clean --force && \
    npm ci --registry=https://registry.npmmirror.com --legacy-peer-deps --prefer-offline --no-audit

# 关键依赖验证（简化版，适用于Alpine Linux）
RUN echo "🔍 验证关键依赖..." && \
    # 检查关键依赖
    npm list @radix-ui/react-avatar || npm install @radix-ui/react-avatar --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-dialog || npm install @radix-ui/react-dialog --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-dropdown-menu || npm install @radix-ui/react-dropdown-menu --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-popover || npm install @radix-ui/react-popover --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-select || npm install @radix-ui/react-select --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-tabs || npm install @radix-ui/react-tabs --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-tooltip || npm install @radix-ui/react-tooltip --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-switch || npm install @radix-ui/react-switch --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-progress || npm install @radix-ui/react-progress --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-scroll-area || npm install @radix-ui/react-scroll-area --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-accordion || npm install @radix-ui/react-accordion --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-label || npm install @radix-ui/react-label --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-separator || npm install @radix-ui/react-separator --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list @radix-ui/react-slot || npm install @radix-ui/react-slot --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list lucide-react || npm install lucide-react --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list class-variance-authority || npm install class-variance-authority --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list clsx || npm install clsx --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list tailwind-merge || npm install tailwind-merge --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list sonner || npm install sonner --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list react-markdown || npm install react-markdown --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    npm list remark-gfm || npm install remark-gfm --registry=https://registry.npmmirror.com --legacy-peer-deps && \
    echo "✅ 关键依赖验证完成"

# 最终完整性验证
RUN echo "🔍 最终依赖完整性验证..." && \
    npm list --depth=0 && \
    echo "✅ 所有依赖验证通过，构建完成"

# 复制应用代码
COPY . .

# 构建后验证（确保关键文件存在）
RUN echo "🔍 构建后验证..." && \
    test -f package.json && echo "✅ package.json 存在" && \
    test -f vite.config.ts && echo "✅ vite.config.ts 存在" && \
    test -d src && echo "✅ src 目录存在" && \
    test -d node_modules/@radix-ui/react-avatar && echo "✅ @radix-ui/react-avatar 模块存在" && \
    test -d node_modules/@radix-ui/react-popover && echo "✅ @radix-ui/react-popover 模块存在" && \
    echo "✅ 构建验证完成"

# 暴露端口
EXPOSE 5173

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD npm list @radix-ui/react-avatar >/dev/null 2>&1 && npm list @radix-ui/react-popover >/dev/null 2>&1 || exit 1

# 启动命令（将在docker-compose中覆盖）
CMD ["npm", "run", "dev"]
