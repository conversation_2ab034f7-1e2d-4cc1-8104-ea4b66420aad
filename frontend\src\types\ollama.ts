/**
 * Ollama API 类型定义
 * 用于直接调用本地Ollama服务的纯LLM对话功能
 */

// ==================== 基础类型 ====================

export interface OllamaModel {
  name: string;
  model: string;
  size: number;
  digest: string;
  details: {
    parent_model: string;
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
  modified_at: string;
}

export interface OllamaModelsResponse {
  models: OllamaModel[];
}

// ==================== 消息类型 ====================

export interface OllamaChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  images?: string[]; // base64编码的图片
}

export interface OllamaChatRequest {
  model: string;
  messages: OllamaChatMessage[];
  stream?: boolean;
  options?: {
    temperature?: number;
    top_p?: number;
    top_k?: number;
    repeat_penalty?: number;
    seed?: number;
    num_ctx?: number;
    num_predict?: number;
  };
}

export interface OllamaChatResponse {
  model: string;
  created_at: string;
  message: OllamaChatMessage;
  done: boolean;
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

// ==================== 会话管理类型 ====================

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  model: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
  error?: string;
}

// ==================== Hook状态类型 ====================

export interface OllamaChatState {
  sessions: ChatSession[];
  currentSessionId: string | null;
  currentSession: ChatSession | null;
  availableModels: OllamaModel[];
  selectedModel: string;
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;
}

export interface OllamaChatActions {
  // 会话管理
  createNewSession: () => string;
  loadSession: (sessionId: string) => void;
  deleteSession: (sessionId: string) => void;
  updateSessionTitle: (sessionId: string, title: string) => void;
  
  // 消息管理
  sendMessage: (content: string) => Promise<void>;
  regenerateLastMessage: () => Promise<void>;
  deleteMessage: (messageId: string) => void;
  
  // 模型管理
  setSelectedModel: (model: string) => void;
  refreshModels: () => Promise<void>;
  
  // 工具函数
  clearCurrentSession: () => void;
  exportSession: (sessionId: string) => string;
  importSession: (sessionData: string) => void;
}

// ==================== API配置类型 ====================

export interface OllamaApiConfig {
  baseUrl: string;
  timeout: number;
  defaultModel: string;
  defaultOptions: {
    temperature: number;
    top_p: number;
    top_k: number;
    repeat_penalty: number;
    num_ctx: number;
  };
}

// ==================== 错误类型 ====================

export interface OllamaError {
  code: string;
  message: string;
  details?: any;
}

export class OllamaApiError extends Error {
  public code: string;
  public details?: any;

  constructor(code: string, message: string, details?: any) {
    super(message);
    this.name = 'OllamaApiError';
    this.code = code;
    this.details = details;
  }
}

// ==================== 工具类型 ====================

export interface StreamingResponse {
  content: string;
  done: boolean;
  error?: string;
}

export interface ChatExport {
  version: string;
  session: ChatSession;
  exportedAt: Date;
}
