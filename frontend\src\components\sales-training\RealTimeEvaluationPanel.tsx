/**
 * 实时评分和反馈机制组件 - 基于真实AI评分
 * 显示来自Morphik Core AI服务的智能评分结果
 */

import { useState, useEffect, useMemo } from 'react'
import {
  BarChart3,
  Star,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Target,
  Users,
  MessageCircle,
  Zap,
  Globe,
  FileText,
  Heart,
  Award,
  ArrowUp,
  ArrowDown,
  Minus,
  Brain,
  Lightbulb,
  Activity,
  BookOpen,
  Sparkles,
  Clock,
  ThumbsUp,
  ThumbsDown,
  Eye,
  TrendingDown,
  RefreshCw,
  PlayCircle,
  PauseCircle,
  Loader2
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Alert, AlertDescription } from '@/components/ui/alert'
import type { ChatMessage, TrainingCustomer } from '@/types/salesTraining'

interface RealTimeEvaluationPanelProps {
  messages: ChatMessage[]
  customer: TrainingCustomer
  currentEvaluation?: any // AI评分结果
  isEvaluating?: boolean // 评分加载状态
  isSessionActive?: boolean // 会话是否进行中
  evaluationError?: string | null // 评分错误信息
  onStartEvaluation?: () => void // 开始评分回调
}

// 评估维度配置 - 与AI评分维度对应
const evaluationDimensions = [
  {
    id: 'needsUnderstanding',
    name: '需求理解与把握',
    shortName: '需求理解',
    icon: Target,
    color: 'text-blue-600',
    bgColor: 'bg-blue-500',
    lightBg: 'bg-blue-50',
    darkBg: 'bg-blue-900/20',
    borderColor: 'border-blue-200',
    description: '对客户核心需求的理解和准确把握程度',
    weight: 30
  },
  {
    id: 'professionalism',
    name: '专业度',
    shortName: '专业知识',
    icon: Star,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-500',
    lightBg: 'bg-yellow-50',
    darkBg: 'bg-yellow-900/20',
    borderColor: 'border-yellow-200',
    description: '产品知识掌握和行业专业性展现',
    weight: 20
  },
  {
    id: 'problemSolving',
    name: '问题处理能力',
    shortName: '问题处理',
    icon: MessageCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-500',
    lightBg: 'bg-green-50',
    darkBg: 'bg-green-900/20',
    borderColor: 'border-green-200',
    description: '处理客户异议、问题和复杂情况的能力',
    weight: 15
  },
  {
    id: 'persuasiveness',
    name: '说服力',
    shortName: '说服技巧',
    icon: Zap,
    color: 'text-purple-600',
    bgColor: 'bg-purple-500',
    lightBg: 'bg-purple-50',
    darkBg: 'bg-purple-900/20',
    borderColor: 'border-purple-200',
    description: '说服和影响客户决策的能力',
    weight: 15
  },
  {
    id: 'culturalSensitivity',
    name: '跨文化敏感度',
    shortName: '文化适应',
    icon: Globe,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-500',
    lightBg: 'bg-indigo-50',
    darkBg: 'bg-indigo-900/20',
    borderColor: 'border-indigo-200',
    description: '对不同文化背景的理解和沟通适应',
    weight: 10
  },
  {
    id: 'termControl',
    name: '条款把控',
    shortName: '商务谈判',
    icon: FileText,
    color: 'text-orange-600',
    bgColor: 'bg-orange-500',
    lightBg: 'bg-orange-50',
    darkBg: 'bg-orange-900/20',
    borderColor: 'border-orange-200',
    description: '对合同条款和商务细节的把控能力',
    weight: 5
  },
  {
    id: 'customerSatisfaction',
    name: '客户感受与满意度',
    shortName: '客户满意',
    icon: Heart,
    color: 'text-red-600',
    bgColor: 'bg-red-500',
    lightBg: 'bg-red-50',
    darkBg: 'bg-red-900/20',
    borderColor: 'border-red-200',
    description: '客户对整体沟通过程的满意程度',
    weight: 5
  }
]

export function RealTimeEvaluationPanel({
  messages,
  customer,
  currentEvaluation,
  isEvaluating = false,
  isSessionActive = true,
  evaluationError,
  onStartEvaluation
}: RealTimeEvaluationPanelProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'feedback'>('overview')

  // 计算用户消息数量
  const userMessages = useMemo(() =>
    messages.filter(m => m.role === 'user'), [messages]
  )

  // 动态权重计算：根据对话阶段调整权重
  const calculateDynamicWeights = (messageCount: number) => {
    if (messageCount <= 2) {
      // 对话初期：重点关注需求理解和专业度
      return {
        needsUnderstanding: 35,
        professionalism: 30,
        problemSolving: 10,
        persuasiveness: 10,
        culturalSensitivity: 10,
        termControl: 3,
        customerSatisfaction: 2
      }
    } else if (messageCount <= 5) {
      // 对话中期：平衡各维度
      return {
        needsUnderstanding: 30,
        professionalism: 25,
        problemSolving: 20,
        persuasiveness: 15,
        culturalSensitivity: 7,
        termControl: 2,
        customerSatisfaction: 1
      }
    } else {
      // 对话后期：重点关注说服力和客户满意度
      return {
        needsUnderstanding: 25,
        professionalism: 20,
        problemSolving: 15,
        persuasiveness: 20,
        culturalSensitivity: 10,
        termControl: 5,
        customerSatisfaction: 5
      }
    }
  }

  // 获取当前动态权重
  const currentWeights = calculateDynamicWeights(userMessages.length)

  // 更新评分维度配置，使用动态权重
  const dynamicEvaluationDimensions = evaluationDimensions.map(dimension => ({
    ...dimension,
    weight: currentWeights[dimension.id as keyof typeof currentWeights] || dimension.weight
  }))

  // 验证总分计算
  const verifyOverallScore = () => {
    if (!currentEvaluation) return null

    const detailedScores = currentEvaluation.detailedScores
    const calculatedScore = Math.round(
      (detailedScores.needsUnderstanding * currentWeights.needsUnderstanding +
        detailedScores.professionalism * currentWeights.professionalism +
        detailedScores.problemSolving * currentWeights.problemSolving +
        detailedScores.persuasiveness * currentWeights.persuasiveness +
        detailedScores.culturalSensitivity * currentWeights.culturalSensitivity +
        detailedScores.termControl * currentWeights.termControl +
        detailedScores.customerSatisfaction * currentWeights.customerSatisfaction) / 100
    )

    return {
      original: currentEvaluation.overallScore,
      calculated: calculatedScore,
      isCorrect: Math.abs(currentEvaluation.overallScore - calculatedScore) < 1
    }
  }

  const scoreVerification = useMemo(() => verifyOverallScore(), [currentEvaluation, userMessages.length])

  // 获取分数等级和颜色
  const getScoreLevel = (score: number) => {
    if (score >= 90) return { level: '卓越', color: 'text-emerald-600', bg: 'bg-emerald-100' }
    if (score >= 80) return { level: '优秀', color: 'text-green-600', bg: 'bg-green-100' }
    if (score >= 70) return { level: '良好', color: 'text-blue-600', bg: 'bg-blue-100' }
    if (score >= 60) return { level: '及格', color: 'text-yellow-600', bg: 'bg-yellow-100' }
    return { level: '需改进', color: 'text-red-600', bg: 'bg-red-100' }
  }

  // 获取变化图标
  const getChangeIcon = (change: number) => {
    if (change > 0.5) return <ArrowUp className="w-3 h-3 text-green-500" />
    if (change < -0.5) return <ArrowDown className="w-3 h-3 text-red-500" />
    return <Minus className="w-3 h-3 text-gray-400" />
  }

  // 检查是否可以进行评分
  // 严格要求：只有在训练停止/暂停状态下才能进行评分
  const canEvaluate = userMessages.length >= 1 && !isSessionActive && !isEvaluating

  console.log('评分条件检查', {
    userMessagesLength: userMessages.length,
    isSessionActive,
    isEvaluating,
    hasCurrentEvaluation: !!currentEvaluation,
    canEvaluate
  })

  const overallScore = currentEvaluation?.overallScore || 0
  const overallLevel = getScoreLevel(overallScore)

  return (
    <div className="space-y-4 h-full flex flex-col px-3 pt-3">
      {/* 整体评分卡片 */}
      <Card className="bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-800 dark:via-gray-800 dark:to-gray-700 border-blue-200/50 dark:border-gray-600 w-full shadow-sm rounded-lg">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg">
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">AI智能评分</h3>
                {/* <p className="text-xs text-gray-500 dark:text-gray-400">基于Morphik Core AI分析</p> */}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {currentEvaluation && (
                <Badge className={`${overallLevel.bg} ${overallLevel.color} border-0`}>
                  {overallLevel.level}
                </Badge>
              )}
            </div>
          </div>

          {currentEvaluation ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-gray-900 dark:text-white">
                  {overallScore.toFixed(1)}
                </span>
                <div className="flex items-center space-x-1 text-sm">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-400">
                    {currentEvaluation.evaluatedMessageCount} 轮对话
                  </span>
                </div>
              </div>

              <Progress
                value={overallScore}
                className="h-2"
              />

              <p className="text-xs text-gray-600 dark:text-gray-400">
                评分时间: {new Date(currentEvaluation.evaluatedAt).toLocaleString()}
              </p>

              {/* 计算验证信息 */}
              {scoreVerification && !scoreVerification.isCorrect && (
                <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-md">
                  <p className="text-xs text-yellow-800 dark:text-yellow-200">
                    ⚠️ 总分计算异常: 显示{scoreVerification.original}分，实际应为{scoreVerification.calculated}分
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-center py-6">
                {/* 美化的图标和加载状态 */}
                <div className="relative mb-4">
                  {isEvaluating ? (
                    <div className="relative">
                      <div className="w-12 h-12 mx-auto mb-3 relative">
                        <div className="absolute inset-0 rounded-full border-4 border-blue-100 dark:border-blue-900/30"></div>
                        <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-500 animate-spin"></div>
                        <Brain className="w-6 h-6 text-blue-500 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                      </div>
                    </div>
                  ) : (
                    <div className="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-full flex items-center justify-center">
                      <Brain className="w-6 h-6 text-blue-500 dark:text-blue-400" />
                    </div>
                  )}
                </div>

                {/* 状态文本 */}
                <div className="space-y-2 mb-4">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {isEvaluating ? 'AI智能分析中' : 'AI智能评分'}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {userMessages.length < 1
                      ? '至少需要1轮对话才能进行评分'
                      : isSessionActive
                        ? '训练进行中，请暂停后进行评分'
                        : isEvaluating
                          ? '正在分析您的销售表现，请稍候...'
                          : '点击开始获取专业的销售能力评估'
                    }
                  </p>
                </div>

                {/* 按钮 */}
                {canEvaluate && onStartEvaluation && (
                  <Button
                    onClick={onStartEvaluation}
                    disabled={isEvaluating}
                    className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-md hover:shadow-lg transition-all duration-200 px-6 py-2.5"
                  >
                    {isEvaluating ? (
                      <>
                        <div className="w-4 h-4 mr-2 relative">
                          <div className="absolute inset-0 rounded-full border-2 border-white/30"></div>
                          <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-white animate-spin"></div>
                        </div>
                        AI分析中...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        开始AI评分
                      </>
                    )}
                  </Button>
                )}

                {/* 错误提示 */}
                {evaluationError && (
                  <Alert className="mt-4 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20 rounded-lg">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-sm text-red-800 dark:text-red-200">
                      <div className="space-y-3 text-center">
                        <div>
                          <p className="font-medium">评分失败</p>
                          <p className="text-xs mt-1 text-red-600 dark:text-red-300">{evaluationError}</p>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={onStartEvaluation}
                          disabled={isEvaluating}
                          className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-300 dark:hover:bg-red-900/30"
                        >
                          <RefreshCw className="w-3 h-3 mr-1" />
                          重试
                        </Button>
                      </div>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 评分详情 */}
      {currentEvaluation && (
        <Card className="flex-1 flex flex-col overflow-hidden shadow-sm rounded-lg">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex flex-col h-full">
            <div className="flex-shrink-0 p-3 border-b border-gray-200 dark:border-gray-700">
              <TabsList className="grid w-full grid-cols-3 bg-gray-100 dark:bg-gray-800">
                <TabsTrigger value="overview" className="text-xs">
                  <Activity className="w-3 h-3 mr-1" />
                  概览
                </TabsTrigger>
                <TabsTrigger value="details" className="text-xs">
                  <BarChart3 className="w-3 h-3 mr-1" />
                  详情
                </TabsTrigger>
                <TabsTrigger value="feedback" className="text-xs">
                  <Lightbulb className="w-3 h-3 mr-1" />
                  反馈
                </TabsTrigger>
              </TabsList>
            </div>

            <div className="flex-1 overflow-y-auto">
              {/* 概览标签页 */}
              <TabsContent value="overview" className="mt-0 p-3 space-y-3">
                <div className="grid grid-cols-1 gap-3">
                  {dynamicEvaluationDimensions.map((dimension) => {
                    const score = currentEvaluation.detailedScores[dimension.id] || 0
                    const Icon = dimension.icon

                    return (
                      <Card key={dimension.id} className={`${dimension.lightBg} dark:${dimension.darkBg} border ${dimension.borderColor} dark:border-gray-600`}>
                        <CardContent className="p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <div className={`p-1.5 ${dimension.bgColor} rounded-md`}>
                                <Icon className="w-3 h-3 text-white" />
                              </div>
                              <span className="text-sm font-medium text-gray-900 dark:text-white">
                                {dimension.shortName}
                              </span>
                              <Badge variant="outline" className="text-xs px-1">
                                {dimension.weight}%
                              </Badge>
                            </div>
                            <span className={`text-sm font-bold ${dimension.color} dark:text-white`}>
                              {score.toFixed(0)}
                            </span>
                          </div>
                          <Progress
                            value={score}
                            className="h-1.5"
                          />
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </TabsContent>

              {/* 详情标签页 */}
              <TabsContent value="details" className="mt-0 p-3">
                <ScrollArea className="h-full">
                  <div className="space-y-3">
                    {currentEvaluation.scoreReasons?.map((reason: any, index: number) => {
                      const dimension = dynamicEvaluationDimensions.find(d => d.name === reason.dimension)
                      const Icon = dimension?.icon || Star

                      return (
                        <Card key={index} className="border border-gray-200 dark:border-gray-700">
                          <CardContent className="p-4">
                            <div className="flex items-start space-x-3">
                              <div className={`p-2 ${dimension?.bgColor || 'bg-gray-500'} rounded-lg flex-shrink-0`}>
                                <Icon className="w-4 h-4 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-2">
                                  <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                                    {reason.dimension}
                                  </h4>
                                  <Badge className="text-xs">
                                    {reason.score}分
                                  </Badge>
                                </div>

                                <div className="space-y-2 text-xs">
                                  <div>
                                    <span className="font-medium text-gray-700 dark:text-gray-300">评分理由:</span>
                                    <p className="text-gray-600 dark:text-gray-400 mt-1">{reason.reason}</p>
                                  </div>

                                  {reason.keyStrengths?.length > 0 && (
                                    <div>
                                      <span className="font-medium text-green-700 dark:text-green-300">优势表现:</span>
                                      <ul className="list-disc list-inside text-green-600 dark:text-green-400 mt-1">
                                        {reason.keyStrengths.map((strength: string, idx: number) => (
                                          <li key={idx}>{strength}</li>
                                        ))}
                                      </ul>
                                    </div>
                                  )}

                                  {reason.improvementAreas?.length > 0 && (
                                    <div>
                                      <span className="font-medium text-orange-700 dark:text-orange-300">改进空间:</span>
                                      <ul className="list-disc list-inside text-orange-600 dark:text-orange-400 mt-1">
                                        {reason.improvementAreas.map((area: string, idx: number) => (
                                          <li key={idx}>{area}</li>
                                        ))}
                                      </ul>
                                    </div>
                                  )}

                                  <div>
                                    <span className="font-medium text-blue-700 dark:text-blue-300">改进建议:</span>
                                    <p className="text-blue-600 dark:text-blue-400 mt-1">{reason.suggestion}</p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </ScrollArea>
              </TabsContent>

              {/* 反馈标签页 */}
              <TabsContent value="feedback" className="mt-0 p-3">
                <div className="space-y-4">
                  {/* 整体反馈 */}
                  <Card className="border border-gray-200 dark:border-gray-700">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <MessageCircle className="w-4 h-4 mr-2 text-blue-500" />
                        整体反馈
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {currentEvaluation.overallFeedback}
                      </p>
                    </CardContent>
                  </Card>

                  {/* 核心优势 */}
                  {currentEvaluation.keyStrengths?.length > 0 && (
                    <Card className="border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/10">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center text-green-700 dark:text-green-300">
                          <ThumbsUp className="w-4 h-4 mr-2" />
                          核心优势
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-1">
                          {currentEvaluation.keyStrengths.map((strength: string, index: number) => (
                            <li key={index} className="text-sm text-green-700 dark:text-green-300 flex items-start">
                              <CheckCircle className="w-3 h-3 mr-2 mt-0.5 flex-shrink-0" />
                              {strength}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}

                  {/* 改进建议 */}
                  {currentEvaluation.improvementSuggestions?.length > 0 && (
                    <Card className="border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-900/10">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center text-orange-700 dark:text-orange-300">
                          <TrendingUp className="w-4 h-4 mr-2" />
                          改进建议
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-1">
                          {currentEvaluation.improvementSuggestions.map((suggestion: string, index: number) => (
                            <li key={index} className="text-sm text-orange-700 dark:text-orange-300 flex items-start">
                              <Target className="w-3 h-3 mr-2 mt-0.5 flex-shrink-0" />
                              {suggestion}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}

                  {/* 下步建议 */}
                  {currentEvaluation.nextStepRecommendations?.length > 0 && (
                    <Card className="border border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/10">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium flex items-center text-blue-700 dark:text-blue-300">
                          <Lightbulb className="w-4 h-4 mr-2" />
                          下步建议
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-1">
                          {currentEvaluation.nextStepRecommendations.map((recommendation: string, index: number) => (
                            <li key={index} className="text-sm text-blue-700 dark:text-blue-300 flex items-start">
                              <ArrowUp className="w-3 h-3 mr-2 mt-0.5 flex-shrink-0" />
                              {recommendation}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </Card>
      )}

      {/* 当会话进行中且已有评分结果时的提示 */}
      {isSessionActive && currentEvaluation && (
        <Alert className="bg-yellow-50 border-yellow-200 text-yellow-800">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            训练进行中，请暂停训练后进行新的AI智能评分
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
