/**
 * 文档管理专用 Hooks
 * 提供文档列表、详情、状态管理等功能
 */
import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { toast } from 'sonner';
import morphikApi from '@/services/morphikApi';
import { morphikQueryKeys } from './useMorphik';
import type {
  Document,
  DocumentMetadata,
  PaginationParams,
  PaginatedResponse,
  DocumentStatus,
  MorphikApiError
} from '@/types/morphik';

// ==================== 文档列表 Hooks ====================

/**
 * 获取文档列表 (分页)
 */
export function useDocuments(params?: PaginationParams & {
  status?: DocumentStatus;
  category?: string;
  search?: string;
}) {
  return useQuery({
    queryKey: morphikQueryKeys.documentsList(params),
    queryFn: () => morphikApi.getDocuments(params),
    staleTime: 2 * 60 * 1000, // 2分钟
    gcTime: 10 * 60 * 1000, // 10分钟
    placeholderData: (previousData) => previousData, // 保持之前的数据显示
    refetchInterval: (query) => {
      // 如果有处理中的文档，每5秒自动刷新
      const hasProcessingDocs = query.state.data?.items?.some(
        doc => doc.system_metadata?.status === 'processing'
      );
      return hasProcessingDocs ? 5000 : false;
    },
  });
}

/**
 * 无限滚动文档列表
 */
export function useInfiniteDocuments(filters?: {
  status?: DocumentStatus;
  category?: string;
  search?: string;
}) {
  return useInfiniteQuery({
    queryKey: [...morphikQueryKeys.documents(), 'infinite', filters],
    queryFn: ({ pageParam = 0 }) =>
      morphikApi.getDocuments({
        skip: pageParam,
        limit: 20,
        ...filters
      }),
    initialPageParam: 0,
    getNextPageParam: (lastPage, allPages) => {
      const totalLoaded = allPages.reduce((sum, page) => sum + page.items.length, 0);
      return totalLoaded < lastPage.total ? totalLoaded : undefined;
    },
    staleTime: 2 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}

// ==================== 单文档 Hooks ====================

/**
 * 获取单个文档详情
 */
export function useDocument(documentId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: morphikQueryKeys.document(documentId),
    queryFn: () => morphikApi.getDocument(documentId),
    enabled: enabled && !!documentId,
    staleTime: 5 * 60 * 1000, // 5分钟
    gcTime: 15 * 60 * 1000, // 15分钟
    retry: (failureCount, error: any) => {
      // 如果是404错误，不重试
      if (error?.status === 404) return false;
      return failureCount < 2;
    },
  });
}

/**
 * 获取文档内容预览
 */
export function useDocumentContent(documentId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: [...morphikQueryKeys.document(documentId), 'content'],
    queryFn: () => morphikApi.getDocumentContent(documentId),
    enabled: enabled && !!documentId,
    staleTime: 10 * 60 * 1000, // 10分钟
    gcTime: 30 * 60 * 1000, // 30分钟
    retry: (failureCount, error: any) => {
      // 如果是404错误，不重试
      if (error?.status === 404) return false;
      return failureCount < 2;
    },
  });
}

/**
 * 更新文档元数据
 */
export function useUpdateDocumentMetadata() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ documentId, metadata }: { documentId: string; metadata: DocumentMetadata }) =>
      morphikApi.updateDocumentMetadata(documentId, metadata),
    onSuccess: (updatedDocument, { documentId }) => {
      // 更新特定文档的缓存
      queryClient.setQueryData(
        morphikQueryKeys.document(documentId),
        updatedDocument
      );

      // 刷新文档列表
      queryClient.invalidateQueries({ queryKey: morphikQueryKeys.documents() });

      toast.success('📝 文档元数据更新成功');
    },
    onError: (error: MorphikApiError) => {
      toast.error(`❌ 更新失败: ${error.message}`);
    },
  });
}

// ==================== 文档状态管理 Hooks ====================

/**
 * 文档状态轮询 Hook
 */
export function useDocumentStatusPolling(documentId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: [...morphikQueryKeys.document(documentId), 'status'],
    queryFn: async () => {
      const doc = await morphikApi.getDocument(documentId);
      return (doc as any).status || 'unknown';
    },
    enabled: enabled && !!documentId,
    refetchInterval: (query) => {
      // 如果文档还在处理中，每5秒轮询一次
      const status = query.state.data;
      if (status === 'processing' || status === 'pending') {
        return 5000;
      }
      // 否则停止轮询
      return false;
    },
    staleTime: 0, // 总是获取最新状态
  });
}

/**
 * 批量文档状态检查
 */
export function useBatchDocumentStatus(documentIds: string[]) {
  return useQuery({
    queryKey: [...morphikQueryKeys.documents(), 'batch-status', documentIds],
    queryFn: async () => {
      const statusPromises = documentIds.map(async (id) => {
        try {
          const doc = await morphikApi.getDocument(id);
          return { id, status: (doc as any).status || 'unknown', error: null };
        } catch (error) {
          return { id, status: null, error: error as MorphikApiError };
        }
      });

      return Promise.all(statusPromises);
    },
    enabled: documentIds.length > 0,
    staleTime: 30 * 1000, // 30秒
    refetchInterval: (query) => {
      // 如果有文档还在处理中，继续轮询
      const hasProcessing = query.state.data?.some(item =>
        item.status === 'processing' ||
        item.status === 'pending'
      );
      return hasProcessing ? 10000 : false; // 10秒间隔
    },
  });
}

// ==================== 文档筛选和搜索 Hooks ====================

/**
 * 文档筛选 Hook
 */
export function useDocumentFilters() {
  const queryClient = useQueryClient();

  const applyFilters = (filters: {
    status?: DocumentStatus[];
    categories?: string[];
    dateRange?: { start: string; end: string };
    fileTypes?: string[];
    search?: string;
  }) => {
    // 使文档列表查询失效，触发重新获取
    queryClient.invalidateQueries({ queryKey: morphikQueryKeys.documents() });

    return filters;
  };

  const clearFilters = () => {
    queryClient.invalidateQueries({ queryKey: morphikQueryKeys.documents() });
  };

  return {
    applyFilters,
    clearFilters,
  };
}

/**
 * 文档搜索建议 Hook
 */
export function useDocumentSearchSuggestions(query: string, enabled: boolean = true) {
  return useQuery({
    queryKey: [...morphikQueryKeys.documents(), 'search-suggestions', query],
    queryFn: async () => {
      if (!query || query.length < 2) return [];

      try {
        // 这里可以调用专门的搜索建议API，或者使用现有的搜索API
        const searchResult = await morphikApi.search({
          query,
          limit: 5,
          similarity_threshold: 0.5,
        });

        // 提取建议关键词
        return searchResult.results.map(result => ({
          text: result.content.substring(0, 100) + '...',
          documentId: result.document_id,
          score: result.similarity_score,
        }));
      } catch (error) {
        console.error('Search suggestions failed:', error);
        return [];
      }
    },
    enabled: enabled && query.length >= 2,
    staleTime: 5 * 60 * 1000, // 5分钟
    gcTime: 10 * 60 * 1000, // 10分钟
  });
}

// ==================== 文档统计和分析 Hooks ====================

/**
 * 文档类型分布统计
 */
export function useDocumentTypeStats() {
  return useQuery({
    queryKey: [...morphikQueryKeys.stats(), 'types'],
    queryFn: async () => {
      const stats = await morphikApi.getDocumentStats();
      return stats.by_type;
    },
    staleTime: 5 * 60 * 1000, // 5分钟
  });
}

/**
 * 文档状态分布统计
 */
export function useDocumentStatusStats() {
  return useQuery({
    queryKey: [...morphikQueryKeys.stats(), 'status'],
    queryFn: async () => {
      const stats = await morphikApi.getDocumentStats();
      return stats.by_status;
    },
    staleTime: 2 * 60 * 1000, // 2分钟
  });
}

// ==================== 实用 Hooks ====================

/**
 * 文档缓存管理
 */
export function useDocumentCache() {
  const queryClient = useQueryClient();

  const prefetchDocument = (documentId: string) => {
    queryClient.prefetchQuery({
      queryKey: morphikQueryKeys.document(documentId),
      queryFn: () => morphikApi.getDocument(documentId),
      staleTime: 5 * 60 * 1000,
    });
  };

  const invalidateDocument = (documentId: string) => {
    queryClient.invalidateQueries({ queryKey: morphikQueryKeys.document(documentId) });
  };

  const removeDocumentFromCache = (documentId: string) => {
    queryClient.removeQueries({ queryKey: morphikQueryKeys.document(documentId) });
  };

  return {
    prefetchDocument,
    invalidateDocument,
    removeDocumentFromCache,
  };
}

/**
 * 文档操作历史
 */
export function useDocumentHistory(documentId: string) {
  // 这里可以实现文档操作历史的跟踪
  // 目前返回基础结构，后续可以扩展
  return {
    history: [],
    addHistoryEntry: (action: string, details?: any) => {
      console.log(`Document ${documentId} action: ${action}`, details);
    },
  };
}

// 所有 hooks 已在定义时导出
