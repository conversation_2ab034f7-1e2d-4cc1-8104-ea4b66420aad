/**
 * Morphik Core API 客户端
 * 提供与 Morphik Core 服务的完整 API 交互功能
 */
import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import type {
  MorphikApiConfig,
  MorphikResponse,
  Document,
  DocumentUploadRequest,
  DocumentUploadResponse,
  SearchRequest,
  SearchResponse,
  RAGQueryRequest,
  RAGQueryResponse,
  DocumentChunk,
  ChunkRetrievalRequest,
  KnowledgeGraph,
  DocumentStats,
  SystemHealth,
  PaginationParams,
  PaginatedResponse,
  BatchUploadRequest,
  BatchUploadResponse,
  ChatMessage,
  ChatSessionMeta
} from '@/types/morphik';
import { MorphikApiError } from '@/types/morphik';
import { templateIntegration } from './templateIntegration';

// 默认配置
const DEFAULT_CONFIG: MorphikApiConfig = {
  baseUrl: import.meta.env.VITE_MORPHIK_API_URL || (import.meta.env.DEV ? '/morphik-api' : 'http://localhost:8000'),
  timeout: 90000, // 增加默认超时时间到90秒，适应大模型响应时间
  retries: 3,
  headers: {
    'Content-Type': 'application/json',
  },
};

/**
 * Morphik API 客户端类
 */
export class MorphikApiClient {
  private client: AxiosInstance;
  private config: MorphikApiConfig;

  constructor(config: Partial<MorphikApiConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.client = this.createAxiosInstance();
  }

  /**
   * 创建 Axios 实例
   */
  private createAxiosInstance(): AxiosInstance {
    const client = axios.create({
      baseURL: this.config.baseUrl,
      timeout: this.config.timeout,
      headers: this.config.headers,
    });

    // 请求拦截器
    client.interceptors.request.use(
      (config) => {
        // 添加 API Key 如果存在
        if (this.config.apiKey) {
          config.headers.Authorization = `Bearer ${this.config.apiKey}`;
        }

        return config;
      },
      (error) => {

        return Promise.reject(error);
      }
    );

    // 响应拦截器
    client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`✅ Morphik API Response: ${response.status} ${response.config.url}`, response.data);
        return response;
      },
      async (error: AxiosError) => {
        console.error('❌ Morphik API Response Error:', error.response?.status, error.message, error.response?.data);

        // 重试逻辑
        if (this.shouldRetry(error) && this.config.retries && this.config.retries > 0) {
          this.config.retries--;
          // console.log(`🔄 Retrying request... (${this.config.retries} attempts left)`);
          return this.client.request(error.config!);
        }

        return Promise.reject(this.handleApiError(error));
      }
    );

    return client;
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: AxiosError): boolean {
    if (!error.response) return true; // 网络错误
    const status = error.response.status;
    return status >= 500 || status === 429; // 服务器错误或限流
  }

  /**
   * 处理 API 错误
   */
  private handleApiError(error: AxiosError): MorphikApiError {
    const response = error.response;
    const message = (response?.data as any)?.message || error.message || 'Unknown error';
    const code = (response?.data as any)?.code || `HTTP_${response?.status}` || 'NETWORK_ERROR';

    return new MorphikApiError(code, message, {
      status: response?.status,
      data: response?.data,
      url: error.config?.url,
    });
  }

  /**
   * 通用 API 调用方法
   */
  private async apiCall<T>(
    method: 'get' | 'post' | 'put' | 'delete',
    endpoint: string,
    data?: any,
    config?: any
  ): Promise<T> {
    try {
      let response;
      if (method === 'get' || method === 'delete') {
        // GET和DELETE请求：axios(url, config)
        response = await this.client[method](endpoint, config || data);
      } else {
        // POST和PUT请求：axios(url, data, config)
        response = await this.client[method](endpoint, data, config);
      }
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  // ==================== 文档管理 API ====================

  /**
   * 上传文档 (文件)
   */
  async uploadDocument(request: DocumentUploadRequest): Promise<DocumentUploadResponse> {
    if (request.file) {
      const formData = new FormData();
      formData.append('file', request.file);

      if (request.metadata) {
        formData.append('metadata', JSON.stringify(request.metadata));
      }

      const response = await this.apiCall<any>('post', '/ingest/file', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return {
        document_id: response.external_id,
        status: response.system_metadata?.status || 'processing',
        message: 'File uploaded successfully'
      };
    } else if (request.content) {
      // 文本内容上传
      const textRequest = {
        content: request.content,
        filename: request.title,
        metadata: request.metadata || {},
      };

      const response = await this.apiCall<any>('post', '/ingest/text', textRequest);

      return {
        document_id: response.external_id,
        status: response.system_metadata?.status || 'completed',
        message: 'Text uploaded successfully'
      };
    } else {
      throw new Error('Either file or content must be provided');
    }
  }

  /**
   * 批量上传文档
   */
  async batchUploadDocuments(request: BatchUploadRequest): Promise<BatchUploadResponse> {
    const formData = new FormData();

    request.files.forEach((file) => {
      formData.append('files', file);
    });

    if (request.metadata) {
      formData.append('metadata', JSON.stringify(request.metadata));
    }

    const response = await this.apiCall<any>('post', '/ingest/files', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    // 转换响应格式
    const results = response.documents?.map((doc: any) => ({
      document_id: doc.external_id,
      status: doc.system_metadata?.status || 'processing',
      message: 'File uploaded successfully'
    })) || [];

    return {
      results,
      summary: {
        total: request.files.length,
        successful: results.filter((r: any) => r.status !== 'failed').length,
        failed: results.filter((r: any) => r.status === 'failed').length,
        processing: results.filter((r: any) => r.status === 'processing').length,
      }
    };
  }

  /**
   * 获取文档列表
   */
  async getDocuments(params?: PaginationParams): Promise<PaginatedResponse<Document>> {
    // 构建查询参数
    const queryParams = new URLSearchParams();
    if (params?.skip) queryParams.append('skip', params.skip.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    // 构建URL
    const url = `/documents${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

    const documents = await this.apiCall<Document[]>('post', url, {});

    return {
      items: documents,
      total: documents.length,
      skip: params?.skip || 0,
      limit: params?.limit || 10000,
    };
  }

  /**
   * 获取单个文档
   */
  async getDocument(documentId: string): Promise<Document> {
    return this.apiCall<Document>('get', `/documents/${documentId}`);
  }

  /**
   * 获取文档内容预览
   */
  async getDocumentContent(documentId: string): Promise<string> {
    try {
      // 首先尝试从文档元数据中获取内容
      const document = await this.getDocument(documentId);

      // 检查system_metadata中是否有content字段
      if (document.system_metadata?.content) {
        return document.system_metadata.content;
      }

      // 如果没有直接内容，尝试通过chunks获取
      // 使用文档ID作为查询来获取该文档的所有chunks
      const chunks = await this.retrieveChunks({
        query: documentId, // 使用文档ID作为查询
        limit: 50, // 获取前50个chunks
        similarity_threshold: 0.0
      });

      if (chunks.length > 0) {
        // 过滤出属于当前文档的chunks
        const documentChunks = chunks.filter(chunk => chunk.document_id === documentId);
        if (documentChunks.length > 0) {
          // 按chunk_index排序并合并内容
          const sortedChunks = documentChunks.sort((a, b) => (a.chunk_index || 0) - (b.chunk_index || 0));
          return sortedChunks.map(chunk => chunk.content).join('\n\n');
        }
      }

      return '暂无内容预览';
    } catch (error) {
      // console.error('获取文档内容失败:', error);
      return '获取文档内容失败';
    }
  }

  /**
   * 删除文档
   */
  async deleteDocument(documentId: string): Promise<MorphikResponse> {
    const response = await this.apiCall<any>('delete', `/documents/${documentId}`);
    return {
      success: true,
      message: 'Document deleted successfully',
      data: response
    };
  }

  /**
   * 更新文档元数据
   */
  async updateDocumentMetadata(documentId: string, metadata: any): Promise<Document> {
    return this.apiCall<Document>('post', `/documents/${documentId}/update_metadata`, metadata);
  }

  /**
   * 下载文档
   */
  async downloadDocument(documentId: string): Promise<Blob> {
    try {
      // 首先获取文档信息
      const document = await this.getDocument(documentId);

      // 检查文档是否有存储信息
      if (!document.storage_info) {
        throw new Error('文档没有可下载的文件');
      }

      // 通过存储信息构建下载请求
      const { bucket, key } = document.storage_info;

      // 调用存储下载API（需要实现）
      const response = await this.apiCall<ArrayBuffer>('get', `/storage/${bucket}/${key}`, undefined, {
        responseType: 'arraybuffer'
      });

      // 根据文档类型确定MIME类型
      const mimeType = document.content_type || 'application/octet-stream';

      return new Blob([response], { type: mimeType });
    } catch (error) {
      console.error('下载文档失败:', error);
      throw new Error('无法下载文档文件');
    }
  }

  // ==================== 模型管理 API ====================



  // ==================== 搜索和检索 API ====================

  /**
   * 语义搜索 (通过检索文档块实现)
   */
  async search(request: SearchRequest): Promise<SearchResponse> {
    const retrieveRequest = {
      query: request.query,
      filters: request.filters,
      k: request.limit || 10,
      min_score: request.similarity_threshold || 0.0,
    };

    const chunks = await this.apiCall<any[]>('post', '/retrieve/chunks', retrieveRequest);

    return {
      results: chunks.map(chunk => ({
        id: `${chunk.document_id}_${chunk.chunk_number}`,
        content: chunk.content,
        metadata: chunk.metadata,
        similarity_score: chunk.score,
        document_id: chunk.document_id,
        chunk_index: chunk.chunk_number,
      })),
      total: chunks.length,
      query: request.query,
      execution_time: 0, // API doesn't return this
    };
  }

  /**
   * 检索文档块
   */
  async retrieveChunks(request: ChunkRetrievalRequest): Promise<DocumentChunk[]> {
    const retrieveRequest = {
      query: request.query,
      filters: request.filters,
      k: request.limit || 10,
      min_score: request.similarity_threshold || 0.0,
    };

    const chunks = await this.apiCall<any[]>('post', '/retrieve/chunks', retrieveRequest);

    return chunks.map(chunk => ({
      id: `${chunk.document_id}_${chunk.chunk_number}`,
      document_id: chunk.document_id,
      content: chunk.content,
      metadata: chunk.metadata,
      chunk_index: chunk.chunk_number,
      created_at: new Date().toISOString(), // API doesn't return this
    }));
  }

  // ==================== RAG 问答 API ====================

  /**
   * RAG 查询 - 支持话术模板集成
   */
  async ragQuery(request: RAGQueryRequest, context: 'knowledge-base' | 'chat' | 'general' = 'general'): Promise<RAGQueryResponse> {
    const startTime = Date.now();

    // 应用激活的话术模板
    const enhancedRequest = templateIntegration.applyTemplateForContext(request, context);

    // 验证模板适用性
    const validation = templateIntegration.validateTemplateForQuery(request.query);
    if (validation.warnings.length > 0) {
      console.warn('模板验证警告:', validation.warnings);
    }

    const queryRequest: any = {
      query: enhancedRequest.query,
      filters: enhancedRequest.filters,
      k: enhancedRequest.max_chunks || 3, // 减少检索块数量提升速度
      temperature: enhancedRequest.temperature || 0.1, // 降低温度提升一致性和速度
      max_tokens: enhancedRequest.max_tokens || 800, // 减少最大token数量
      include_sources: enhancedRequest.include_sources !== false,
      prompt_overrides: enhancedRequest.prompt_overrides, // 添加模板支持
    };

    // 添加聊天会话ID支持多轮对话
    if (enhancedRequest.chat_id) {
      queryRequest.chat_id = enhancedRequest.chat_id;
    }

    // 只在有知识图谱时添加graph_name参数
    if (enhancedRequest.graph_name) {
      queryRequest.graph_name = enhancedRequest.graph_name;
      // 知识图谱查询不支持流式响应，确保不传递stream_response参数
    } else {
      // 只有在非知识图谱查询时才添加stream_response参数
      queryRequest.stream_response = false;
    }



    try {
      // 优化超时时间配置 - 根据实际模型响应时间调整
      const timeout = enhancedRequest.graph_name ? 180000 : 60000; // 知识图谱查询3分钟，普通查询1分钟

      const response = await this.apiCall<any>('post', '/query', queryRequest, {
        timeout
      });

      const responseTime = Date.now() - startTime;
      console.log('RAG Query Success:', response);

      // 记录模板使用统计
      await templateIntegration.recordTemplateUsage(true, responseTime, request.query);

      return this.processRAGResponse(response);
    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.error('RAG Query Error:', error);

      // 记录模板使用失败统计
      await templateIntegration.recordTemplateUsage(false, responseTime, request.query);

      throw error;
    }
  }

  private processRAGResponse(response: any): RAGQueryResponse {
    console.log('RAG Query Raw Response:', {
      completion: response.completion,
      completionType: typeof response.completion,
      completionLength: response.completion?.length || 0,
      sources: response.sources?.length || 0,
      usage: response.usage
    });

    // 检查响应是否为空（只检查 null 或 undefined，允许空字符串）
    if (response.completion === null || response.completion === undefined) {
      // console.warn('RAG Query returned null/undefined completion:', response);
      throw new Error('模型返回了空的回答内容，请尝试重新提问或切换模型');
    }

    // 过滤 DeepSeek 思考过程（如果使用 DeepSeek 模型）
    const filteredCompletion = this.filterDeepSeekThinking(response.completion);

    // 检查过滤后的内容（只有当原始内容不为空但过滤后为空时才使用原始内容）
    if (!filteredCompletion && response.completion && response.completion.trim()) {
      // console.warn('Filtered completion is empty but original has content, using original:', response.completion);
      return {
        completion: response.completion, // 使用原始内容
        sources: response.sources?.map((source: any) => ({
          id: `${source.document_id}_${source.chunk_number}`,
          content: source.content || '',
          metadata: source.metadata || {},
          similarity_score: source.score || 0,
          document_id: source.document_id,
          chunk_index: source.chunk_number,
        })) || [],
        execution_time: response.execution_time || 0,
        token_usage: response.usage || {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0,
        },
      };
    }

    return {
      completion: filteredCompletion,
      sources: response.sources?.map((source: any) => ({
        id: `${source.document_id}_${source.chunk_number}`,
        content: source.content || '', // 使用实际内容或空字符串
        metadata: source.metadata || {},
        similarity_score: source.score || 0,
        document_id: source.document_id,
        chunk_index: source.chunk_number,
      })) || [],
      execution_time: response.execution_time || 0,
      token_usage: response.usage || {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
      },
    };
  }

  // ==================== 聊天会话 API ====================

  /**
   * 获取聊天历史
   */
  async getChatHistory(chatId: string): Promise<ChatMessage[]> {
    try {
      const response = await this.apiCall<ChatMessage[]>('get', `/chat/${chatId}`);
      return response;
    } catch (error) {
      console.error('Get chat history error:', error);
      return [];
    }
  }

  /**
   * 获取聊天会话列表
   */
  async getChatSessions(limit: number = 100): Promise<ChatSessionMeta[]> {
    try {
      const response = await this.apiCall<ChatSessionMeta[]>('get', `/chats?limit=${limit}`);
      return response;
    } catch (error) {
      console.error('Get chat sessions error:', error);
      return [];
    }
  }

  // ==================== 知识图谱 API ====================

  /**
   * 创建知识图谱
   */
  async createGraph(request: {
    name: string;
    filters?: Record<string, any>;
    documents?: string[];
  }): Promise<{ id: string; name: string; status: string }> {
    const response = await this.apiCall<any>('post', '/graph/create', request);

    // 转换数据格式，从system_metadata中提取status
    return {
      id: response.id,
      name: response.name,
      status: response.system_metadata?.status || 'processing',
    };
  }

  /**
   * 获取图谱列表
   */
  async getGraphs(): Promise<Array<{
    id: string;
    name: string;
    status: string;
    created_at: string;
    node_count: number;
    edge_count: number;
    document_count: number;
    system_metadata?: any; // 保留原始系统元数据
  }>> {
    const response = await this.apiCall<Array<any>>('get', '/graphs');

    // 转换数据格式，从system_metadata中提取status和统计信息
    return response.map(graph => ({
      id: graph.id,
      name: graph.name,
      status: graph.system_metadata?.status || 'unknown',
      created_at: graph.created_at || graph.system_metadata?.created_at,
      node_count: graph.entities?.length || 0,
      edge_count: graph.relationships?.length || 0,
      document_count: graph.document_ids?.length || 0,
      system_metadata: graph.system_metadata, // 保留完整的系统元数据
    }));
  }

  /**
   * 获取指定图谱
   */
  async getGraph(name: string): Promise<{
    id: string;
    name: string;
    status: string;
    entities: Array<{ id: string; label: string; type: string; properties: Record<string, any> }>;
    relationships: Array<{ id: string; source: string; target: string; type: string; properties: Record<string, any> }>;
    metadata: Record<string, any>;
  }> {
    const response = await this.apiCall<any>('get', `/graph/${encodeURIComponent(name)}`);

    // 转换数据格式
    return {
      id: response.id,
      name: response.name,
      status: response.system_metadata?.status || 'unknown',
      entities: response.entities || [],
      relationships: response.relationships || [],
      metadata: response.metadata || {},
    };
  }

  /**
   * 获取图谱可视化数据
   */
  async getGraphVisualization(name: string): Promise<{
    nodes: Array<{ id: string; label: string; type: string; properties: Record<string, any>; color?: string }>;
    links: Array<{ source: string; target: string; type: string; label?: string }>;
  }> {
    const response = await this.apiCall<{
      nodes: Array<{ id: string; label: string; type: string; properties: Record<string, any>; color?: string }>;
      links: Array<{ source: string; target: string; type: string; label?: string }>;
    }>('get', `/graph/${encodeURIComponent(name)}/visualization`);
    return response;
  }

  /**
   * 更新图谱
   */
  async updateGraph(name: string, request: {
    additional_filters?: Record<string, any>;
    additional_documents?: string[];
  }): Promise<{ id: string; name: string; status: string }> {
    const response = await this.apiCall<{ id: string; name: string; status: string }>('post', `/graph/${encodeURIComponent(name)}/update`, request);
    return response;
  }

  /**
   * 删除图谱
   */
  async deleteGraph(name: string): Promise<{ success: boolean; message: string }> {
    const response = await this.apiCall<{ success: boolean; message: string }>('delete', `/graph/${encodeURIComponent(name)}`);
    return response;
  }

  /**
   * 检查图谱构建状态
   */
  async getGraphWorkflowStatus(workflowId: string, runId?: string): Promise<{
    status: string;
    progress?: number;
    message?: string;
    completed_at?: string;
  }> {
    const params = runId ? { run_id: runId } : {};
    const response = await this.apiCall<{
      status: string;
      progress?: number;
      message?: string;
      completed_at?: string;
    }>('get', `/graph/workflow/${workflowId}/status`, { params });
    return response;
  }

  /**
   * 获取知识图谱 (兼容旧接口)
   */
  async getKnowledgeGraph(filters?: any): Promise<KnowledgeGraph> {
    try {
      // 尝试获取图谱列表
      const graphs = await this.getGraphs();
      if (graphs.length === 0) {
        return {
          nodes: [],
          edges: [],
          metadata: {
            total_nodes: 0,
            total_edges: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }
        };
      }

      // 使用第一个图谱获取可视化数据
      const firstGraph = graphs[0];
      const visualization = await this.getGraphVisualization(firstGraph.name);

      return {
        nodes: visualization.nodes.map(node => ({
          id: node.id,
          label: node.label,
          type: node.type,
          properties: node.properties,
          document_ids: node.properties.document_ids || [],
        })),
        edges: visualization.links.map((link, index) => ({
          id: `edge-${index}`,
          source: typeof link.source === 'string' ? link.source : (link.source as any)?.id || link.source,
          target: typeof link.target === 'string' ? link.target : (link.target as any)?.id || link.target,
          relationship: link.type,
          weight: 1,
          properties: { label: link.label || link.type },
        })),
        metadata: {
          total_nodes: visualization.nodes.length,
          total_edges: visualization.links.length,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      };
    } catch (error) {
      console.warn('Failed to fetch real knowledge graph, returning empty data:', error);
      return {
        nodes: [],
        edges: [],
        metadata: {
          total_nodes: 0,
          total_edges: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      };
    }
  }

  // ==================== 统计和监控 API ====================

  /**
   * 获取文档统计
   */
  async getDocumentStats(): Promise<DocumentStats> {
    try {
      // 获取文档列表来计算统计
      const documents = await this.getDocuments({ limit: 10000 });

      const stats: DocumentStats = {
        total_documents: documents.items.length,
        total_chunks: 0, // 需要额外计算
        processing_queue: 0,
        failed_documents: 0,
        storage_used: 0,
        by_status: {
          pending: 0,
          processing: 0,
          completed: 0,
          failed: 0,
          deleted: 0,
        },
        by_type: {},
      };

      // 计算状态分布
      documents.items.forEach(doc => {
        const status = doc.system_metadata?.status || 'completed';
        stats.by_status[status] = (stats.by_status[status] || 0) + 1;

        // 计算文件类型分布
        const fileType = doc.content_type || 'unknown';
        stats.by_type[fileType] = (stats.by_type[fileType] || 0) + 1;
      });

      return stats;
    } catch (error) {
      // 返回默认统计
      return {
        total_documents: 0,
        total_chunks: 0,
        processing_queue: 0,
        failed_documents: 0,
        storage_used: 0,
        by_status: {
          pending: 0,
          processing: 0,
          completed: 0,
          failed: 0,
          deleted: 0,
        },
        by_type: {},
      };
    }
  }

  /**
   * 获取系统健康状态
   */
  async getSystemHealth(): Promise<SystemHealth> {
    try {
      // 使用ping端点检查健康状态
      await this.apiCall<any>('get', '/ping');

      return {
        status: 'healthy',
        services: {
          api: true,
          database: true,
          vector_store: true,
          embedding_service: true,
          completion_service: true,
        },
        metrics: {
          response_time: 100,
          memory_usage: 65,
          cpu_usage: 45,
          disk_usage: 30,
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        services: {
          api: false,
          database: false,
          vector_store: false,
          embedding_service: false,
          completion_service: false,
        },
        metrics: {
          response_time: 0,
          memory_usage: 0,
          cpu_usage: 0,
          disk_usage: 0,
        }
      };
    }
  }

  // ==================== 实用方法 ====================

  /**
   * 过滤 DeepSeek 思考过程
   * DeepSeek-R1 模型会输出 <think>...</think> 标签包围的思考过程，需要过滤掉
   */
  private filterDeepSeekThinking(content: string): string {
    if (!content || typeof content !== 'string') {
      return content || '';
    }

    const originalContent = content;
    let filtered = content;

    // 记录过滤前的内容长度
    const originalLength = content.length;

    // 检查是否包含DeepSeek思考过程标记
    const hasThinkingProcess =
      content.includes('Thinking...') && content.includes('...done thinking.') ||
      content.includes('<think>') && content.includes('</think>') ||
      content.includes('思考中...') && content.includes('思考完成.');

    // 只有检测到思考过程时才进行过滤
    if (!hasThinkingProcess) {
      // console.log('No thinking process detected, returning original content');
      return originalContent;
    }

    // console.log('DeepSeek thinking process detected, applying filters...');

    // 过滤 <think>...</think> 标签及其内容（支持多行和嵌套）
    filtered = filtered.replace(/<think>[\s\S]*?<\/think>/gi, '');

    // 过滤 "Thinking..." 开头到 "...done thinking." 结尾的内容
    filtered = filtered.replace(/Thinking\.\.\.[\s\S]*?\.\.\.done thinking\./gi, '');

    // 过滤其他可能的思考标记
    filtered = filtered.replace(/^思考中\.\.\.[\s\S]*?思考完成\./gm, '');

    // 过滤可能的思考过程标记（更保守的匹配）
    filtered = filtered.replace(/\*\*思考过程\*\*[\s\S]*?(?=\*\*|$)/gi, '');
    filtered = filtered.replace(/【思考】[\s\S]*?(?=【|$)/gi, '');

    // 清理多余的空白字符和换行
    filtered = filtered.replace(/\n\s*\n\s*\n/g, '\n\n'); // 合并多个空行
    filtered = filtered.trim();

    // 记录过滤后的内容长度
    const filteredLength = filtered.length;

    // 如果过滤后内容太短（少于原内容的10%），可能过滤过度，返回原内容
    if (filtered && filteredLength < originalLength * 0.1) {
      // console.warn('DeepSeek thinking filter may have removed too much content, returning original');
      // console.log('Original length:', originalLength, 'Filtered length:', filteredLength);
      return originalContent;
    }

    // 如果过滤后为空但原内容不为空，返回原内容
    if (!filtered && originalContent) {
      // console.warn('DeepSeek thinking filter removed all content, returning original');
      return originalContent;
    }

    // 如果有过滤操作，记录日志
    if (filteredLength !== originalLength) {
      // console.log('DeepSeek thinking content filtered:', {
      //   originalLength,
      //   filteredLength,
      //   removed: originalLength - filteredLength
      // });
    }

    return filtered || originalContent;
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.getSystemHealth();
      return true;
    } catch (error) {
      // console.error('Morphik connection test failed:', error);
      return false;
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<MorphikApiConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.client = this.createAxiosInstance();
  }
}

// 创建默认实例
export const morphikApi = new MorphikApiClient({
  baseUrl: import.meta.env.VITE_MORPHIK_API_URL || 'http://localhost:8000',
});

// 默认导出以保持向后兼容
export default morphikApi;
