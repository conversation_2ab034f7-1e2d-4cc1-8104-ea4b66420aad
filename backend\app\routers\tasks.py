"""
任务管理API路由
提供完整的CRUD操作端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List
from math import ceil

from app.core.database import get_db
from app.crud.task import task_crud
from app.schemas.task import Task, TaskCreate, TaskUpdate, TaskList, TaskStats
from app.models.task import TaskStatus, TaskPriority

router = APIRouter()


@router.post("/", response_model=Task, summary="创建任务")
async def create_task(
    *,
    db: AsyncSession = Depends(get_db),
    task_in: TaskCreate
) -> Task:
    """
    创建新任务
    
    - **title**: 任务标题（必填）
    - **description**: 任务描述（可选）
    - **status**: 任务状态（默认：pending）
    - **priority**: 任务优先级（默认：medium）
    - **assignee**: 负责人（可选）
    - **is_active**: 是否激活（默认：true）
    """
    return await task_crud.create(db=db, obj_in=task_in)


@router.get("/", response_model=TaskList, summary="获取任务列表")
async def read_tasks(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="每页记录数"),
    status: Optional[TaskStatus] = Query(None, description="按状态过滤"),
    priority: Optional[TaskPriority] = Query(None, description="按优先级过滤"),
    assignee: Optional[str] = Query(None, description="按负责人过滤"),
    is_active: Optional[bool] = Query(None, description="按激活状态过滤")
) -> TaskList:
    """
    获取任务列表，支持分页和过滤
    
    - **skip**: 跳过的记录数（用于分页）
    - **limit**: 每页记录数（1-100）
    - **status**: 按状态过滤（pending/in_progress/completed/cancelled）
    - **priority**: 按优先级过滤（low/medium/high/urgent）
    - **assignee**: 按负责人过滤
    - **is_active**: 按激活状态过滤
    """
    # 获取任务列表
    tasks = await task_crud.get_multi(
        db=db,
        skip=skip,
        limit=limit,
        status=status,
        priority=priority,
        assignee=assignee,
        is_active=is_active
    )
    
    # 获取总数
    total = await task_crud.count(
        db=db,
        status=status,
        priority=priority,
        assignee=assignee,
        is_active=is_active
    )
    
    # 计算页数
    pages = ceil(total / limit) if total > 0 else 0
    page = (skip // limit) + 1
    
    return TaskList(
        items=tasks,
        total=total,
        page=page,
        size=limit,
        pages=pages
    )


@router.get("/stats", response_model=TaskStats, summary="获取任务统计")
async def read_task_stats(
    db: AsyncSession = Depends(get_db)
) -> TaskStats:
    """
    获取任务统计信息
    
    返回任务总数、各状态数量、各优先级数量等统计数据
    """
    stats = await task_crud.get_stats(db=db)
    return TaskStats(**stats)


@router.get("/{task_id}", response_model=Task, summary="获取单个任务")
async def read_task(
    task_id: int,
    db: AsyncSession = Depends(get_db)
) -> Task:
    """
    根据ID获取单个任务详情
    
    - **task_id**: 任务ID
    """
    task = await task_crud.get(db=db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    return task


@router.put("/{task_id}", response_model=Task, summary="更新任务")
async def update_task(
    *,
    db: AsyncSession = Depends(get_db),
    task_id: int,
    task_in: TaskUpdate
) -> Task:
    """
    更新任务信息
    
    - **task_id**: 任务ID
    - 其他字段：需要更新的字段（只传入需要更新的字段）
    """
    task = await task_crud.get(db=db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return await task_crud.update(db=db, db_obj=task, obj_in=task_in)


@router.delete("/{task_id}", response_model=Task, summary="删除任务")
async def delete_task(
    task_id: int,
    db: AsyncSession = Depends(get_db)
) -> Task:
    """
    删除任务
    
    - **task_id**: 任务ID
    """
    task = await task_crud.remove(db=db, id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    return task
