/**
 * AI聊天培训窗体 - 全面优化版本
 * 集成Morphik Core AI服务，支持流式输出、多轮对话和完整功能体验
 * 新增功能：语音输入、智能建议、会话保存、导出功能、快捷键支持
 */

import { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import {
  X,
  Send,
  Bot,
  User,
  Maximize2,
  Minimize2,
  PanelLeftOpen,
  PanelLeftClose,
  History,
  RotateCcw,
  Pause,
  Play,
  Clock,
  Sparkles,
  BookOpen,
  TrendingUp,
  Save,
  Download,
  MessageCircle,
  Mic,
  MicOff,

  Volume2,
  VolumeX,
  Loader2,
  CheckCircle,
  AlertCircle,
  Target,
  Award,
  Zap,
  Brain,
  FileText,
  MessageSquare,
  Lightbulb,
  HelpCircle,
  Star,

  RefreshCw,
  FastForward,
  Rewind,
  Camera,
  FileImage,
  Paperclip,
  Keyboard,
  Smile,
  Code,
  Database,
  Globe,
  Wifi,
  WifiOff,
  Shield,
  Lock,
  UnlockKeyhole,
  UserCheck,
  Timer,
  PlayCircle,
  StopCircle,
  SkipForward,
  Video,
  Phone,
  Package
} from 'lucide-react'
import { Button } from '@/components/ui/button'

import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Progress } from '@/components/ui/progress'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { RealTimeEvaluationPanel } from './RealTimeEvaluationPanel'
import { AIGuidancePanel } from './AIGuidancePanel'
import { useAITrainingChat } from '@/hooks/useAITrainingChat'
import { useAIGuidance } from '@/hooks/useAIGuidance'
import type { TrainingCustomer } from '@/types/salesTraining'
import { toast } from 'sonner'


interface AITrainingDialogProps {
  customer: TrainingCustomer
  open: boolean
  onClose: () => void
}

// 快捷键配置
const SHORTCUTS = {
  SEND_MESSAGE: 'Enter',
  NEW_LINE: 'Shift+Enter',
  RESET_SESSION: 'Ctrl+R',
  SAVE_SESSION: 'Ctrl+S',
  TOGGLE_SIDEBAR: 'Ctrl+B',
  TOGGLE_FULLSCREEN: 'F11',
  FOCUS_INPUT: '/',
  TOGGLE_VOICE: 'Ctrl+M'
}

export function AITrainingDialog({ customer, open, onClose }: AITrainingDialogProps) {
  // 基础UI状态
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showSidebar, setShowSidebar] = useState(true)
  const [sidebarTab, setSidebarTab] = useState<'evaluation' | 'guidance' | 'history'>('evaluation')
  const [inputValue, setInputValue] = useState('')
  const [isVoiceEnabled, setIsVoiceEnabled] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [typingIndicator, setTypingIndicator] = useState(false)
  const [isConnected, setIsConnected] = useState(true)

  // 新增功能状态
  const [showShortcuts, setShowShortcuts] = useState(false)
  const [enableSounds] = useState(true)
  const [autoSave] = useState(true)
  const [autoScroll] = useState(true)
  const [showTimestamps] = useState(false)
  const [compactMode] = useState(false)
  const [fontSize] = useState(14)
  const [messagePreview] = useState('')
  const [dragOver, setDragOver] = useState(false)

  // AI建议区域显示控制 - 每次打开窗体都默认关闭
  const [showAIAssist, setShowAIAssist] = useState(false)
  const [userManuallyDisabled, setUserManuallyDisabled] = useState(false) // 用户是否手动关闭了AI建议
  const [aiAssistLoading, setAIAssistLoading] = useState<number | null>(null)



  // 计时器状态
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [responseTimer, setResponseTimer] = useState<number | null>(null)

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null)

  // 使用AI训练聊天Hook
  const {
    session,
    messages,
    isLoading,
    isEvaluating,
    trainingConfig,
    currentGuidance,
    isGeneratingGuidance,
    currentEvaluation,
    evaluationError,
    initializeSession,
    sendMessage,
    resetSession,
    toggleSession,
    saveSession,
    getSessionDuration,
    performIntelligentEvaluation,
    updateConfig,
    clearAIGuidance
  } = useAITrainingChat({
    customer,
    config: {
      enableRealTimeEvaluation: true,
      enableSuggestions: true,
      enableAIGuidance: true,
      enableIntelligentScoring: true,
      difficulty: 'intermediate'
    }
  })

  // AI指导分析hook
  const {
    overallAnalysis,
    replyAnalyses,
    isAnalyzing: isGuidanceAnalyzing,
    error: guidanceError,
    performGuidanceAnalysis,
    resetGuidance
  } = useAIGuidance(
    messages.map(msg => ({
      ...msg,
      role: msg.role === 'assistant' ? 'customer' : msg.role
    }) as any),
    customer
  )

  // const ragQuery = useRAGQuery() // 已切换到SmoLAgents，不再使用

  // 切换AI建议显示状态
  const toggleAIAssist = useCallback(() => {
    const newState = !showAIAssist
    setShowAIAssist(newState)
    updateConfig({ showAISuggestions: newState })

    // 如果用户手动关闭，记录这个状态
    if (!newState) {
      setUserManuallyDisabled(true)
    } else {
      setUserManuallyDisabled(false)
    }
  }, [showAIAssist, updateConfig])

  // 初始化时同步AI建议状态到训练配置
  useEffect(() => {
    updateConfig({ showAISuggestions: showAIAssist })
  }, [showAIAssist, updateConfig])

  // 计算会话统计
  const sessionStats = useMemo(() => {
    const userMessages = messages.filter(m => m.role === 'user')
    const assistantMessages = messages.filter(m => m.role === 'assistant')
    const avgResponseTime = userMessages.length > 0 ?
      userMessages.reduce((sum, msg, index) => {
        if (index > 0) {
          const timeDiff = msg.timestamp.getTime() - messages[messages.indexOf(msg) - 1].timestamp.getTime()
          return sum + timeDiff / 1000
        }
        return sum
      }, 0) / Math.max(userMessages.length - 1, 1) : 0

    return {
      totalMessages: messages.length,
      userMessages: userMessages.length,
      assistantMessages: assistantMessages.length,
      avgResponseTime: Math.round(avgResponseTime),
      longestMessage: Math.max(...messages.map(m => m.content.length), 0),
      shortestMessage: Math.min(...messages.map(m => m.content.length), 0)
    }
  }, [messages])

  // 自动滚动到底部
  const scrollToBottom = useCallback(() => {
    if (autoScroll) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
    }
  }, [autoScroll])

  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  // 时间更新
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // 会话自动保存
  useEffect(() => {
    if (autoSave && session && messages.length > 0) {
      const saveTimer = setTimeout(() => {
        saveSession()
      }, 30000) // 30秒自动保存
      return () => clearTimeout(saveTimer)
    }
  }, [autoSave, session, messages.length, saveSession])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (isFullscreen) setIsFullscreen(false)
        else if (showShortcuts) setShowShortcuts(false)
        else onClose()
        return
      }

      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'r':
            e.preventDefault()
            resetSession()
            resetGuidance() // 同时重置AI指导状态
            setUserManuallyDisabled(false) // 重置用户手动关闭状态
            break
          case 's':
            e.preventDefault()
            saveSession()
            break
          case 'b':
            e.preventDefault()
            setShowSidebar(!showSidebar)
            break
          case 'm':
            e.preventDefault()
            setIsVoiceEnabled(!isVoiceEnabled)
            break
        }
      }

      if (e.key === 'F11') {
        e.preventDefault()
        setIsFullscreen(!isFullscreen)
      }

      if (e.key === '/' && e.target !== inputRef.current) {
        e.preventDefault()
        inputRef.current?.focus()
      }
    }

    if (open) {
      window.addEventListener('keydown', handleKeyDown)
      return () => window.removeEventListener('keydown', handleKeyDown)
    }
  }, [open, isFullscreen, showShortcuts, showSidebar, isVoiceEnabled, onClose, resetSession, resetGuidance, saveSession])

  // 处理输入
  const handleSendMessage = useCallback(async () => {
    if (inputValue.trim() && !isLoading && !isEvaluating) {
      const message = inputValue.trim()
      setInputValue('')
      setTypingIndicator(true)

      // 开始响应计时
      const startTime = Date.now()
      setResponseTimer(startTime)

      // 延迟显示打字指示器，模拟真实体验
      setTimeout(() => setTypingIndicator(false), 2000)

      try {
        await sendMessage(message)

        // 计算响应时间
        const endTime = Date.now()
        const responseTime = endTime - startTime

        if (enableSounds) {
          // 播放发送音效（模拟）
        }

        toast.success("消息已发送", {
          description: `响应时间: ${responseTime}ms`,
          duration: 2000
        })
      } catch (error) {
        toast.error("发送失败", {
          description: "请检查网络连接后重试",
        })
      } finally {
        setResponseTimer(null)
      }
    }
  }, [inputValue, isLoading, isEvaluating, sendMessage, enableSounds])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }, [handleSendMessage])

  // 语音输入处理
  const handleVoiceToggle = useCallback(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      setIsRecording(!isRecording)
      if (!isRecording) {
        toast.info("开始语音输入", {
          description: "请开始说话...",
        })
      } else {
        toast.info("语音输入结束")
      }
    } else {
      toast.error("不支持语音输入", {
        description: "您的浏览器不支持语音识别功能",
      })
    }
  }, [isRecording])

  // 文件拖拽处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      toast.info("文件上传功能开发中...")
    }
  }, [])

  // 初始化
  useEffect(() => {
    if (open && !session) {
      initializeSession()
      setSessionStartTime(new Date())
    }
  }, [open, session, initializeSession])



  // 获取消息时间
  const formatMessageTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: showTimestamps ? '2-digit' : undefined
    })
  }

  // 获取会话状态颜色
  const getSessionStatusColor = () => {
    if (!session) return 'text-gray-400'
    switch (session.status) {
      case 'active': return 'text-green-500'
      case 'paused': return 'text-yellow-500'
      case 'completed': return 'text-blue-500'
      default: return 'text-gray-400'
    }
  }

  // 导出会话记录
  const handleExportSession = useCallback(() => {
    if (messages.length === 0) {
      toast.warning("暂无会话记录可导出")
      return
    }

    const exportData = {
      customer: customer.name,
      country: customer.country.name,
      product: customer.product.name,
      sessionStart: sessionStartTime,
      sessionEnd: new Date(),
      duration: getSessionDuration(),
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp
      })),
      score: null,
      evaluation: currentEvaluation,
      stats: sessionStats
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `training-session-${customer.name}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success("会话记录已导出")
  }, [messages, customer, sessionStartTime, getSessionDuration, currentEvaluation, sessionStats])

  // 处理AI智能评分
  const handleStartEvaluation = useCallback(async () => {
    console.log('点击开始评分按钮', {
      sessionStatus: session?.status,
      messagesLength: messages.length,
      isEvaluating
    })

    try {
      const result = await performIntelligentEvaluation()
      console.log('评分完成', result)
      toast.success("AI评分完成", {
        description: "评分结果已生成，请查看右侧面板",
      })
    } catch (error) {
      console.error('评分失败', error)
      toast.error("评分失败", {
        description: error.message || "AI服务暂时不可用，请稍后重试",
      })
    }
  }, [performIntelligentEvaluation, session?.status, messages.length, isEvaluating])

  // 处理AI指导分析（参考智能评分逻辑）
  const handleStartGuidanceAnalysis = useCallback(async () => {
    console.log('点击开始指导分析按钮', {
      sessionStatus: session?.status,
      messagesLength: messages.length,
      isGuidanceAnalyzing
    })

    try {
      const result = await performGuidanceAnalysis()
      console.log('指导分析完成', result)
      toast.success("AI指导分析完成", {
        description: "分析结果已生成，请查看指导面板",
      })
    } catch (error) {
      console.error('指导分析失败', error)
      toast.error("指导分析失败", {
        description: error.message || "AI服务暂时不可用，请稍后重试",
      })
    }
  }, [performGuidanceAnalysis, session?.status, messages.length, isGuidanceAnalyzing])

  // 快速回复建议
  const quickReplies = useMemo(() => {
    if (!currentGuidance?.suggestions || !Array.isArray(currentGuidance.suggestions)) {
      return []
    }

    // 过滤有效建议
    const validSuggestions = currentGuidance.suggestions.filter(suggestion =>
      suggestion && typeof suggestion === 'string' && suggestion.trim().length > 0
    )

    if (validSuggestions.length === 0) {
      return []
    }

    const replies = validSuggestions.slice(0, 3).map((suggestion, index) => ({
      id: `${currentGuidance.id}_${index}_${Date.now()}`,
      text: suggestion.length > 40 ? suggestion.substring(0, 40) + '...' : suggestion,
      fullText: suggestion
    }))

    return replies
  }, [currentGuidance, isGeneratingGuidance])

  // 当有AI建议时自动激活灯泡状态（但不覆盖用户的手动关闭操作）
  useEffect(() => {
    if ((isGeneratingGuidance || quickReplies.length > 0) && !showAIAssist && !userManuallyDisabled) {
      setShowAIAssist(true)
      updateConfig({ showAISuggestions: true })
    }
  }, [isGeneratingGuidance, quickReplies.length, showAIAssist, userManuallyDisabled, updateConfig])



  // AI答复方向生成销售回复 - 使用SmoLAgents服务
  const handleAIDirectionClick = async (direction: string, idx: number) => {
    setAIAssistLoading(idx)
    try {
      const prompt = `请用中文作答。你是一名专业的销售人员，请根据以下答复方向，生成一条高质量、专业且有说服力的销售回复，内容要具体、有逻辑、有亮点，字数80-200字：\n\n答复方向：${direction}`

      const { smolagentsService } = await import('@/services/smolagentsService')

      const result = await smolagentsService.generateAIReply({
        query: prompt,
        direction,
        maxSteps: 2,
        temperature: 0.6
      })

      if (!result.success) {
        throw new Error(result.error || 'AI答复生成服务调用失败')
      }

      const completionText = result.result || result.completion || ''
      setInputValue(completionText)
    } catch (error) {
      console.error('AI建议生成失败:', error)
      toast.error("AI建议生成失败", {
        description: error.message || "请稍后重试",
      })
    } finally {
      setAIAssistLoading(null)
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onClose} modal={true}>
        <DialogContent
          className={`
          ${isFullscreen
              ? 'fixed inset-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 left-0 top-0'
              : 'w-[95vw] max-w-7xl h-[90vh] max-h-[900px]'
            }
          p-0 gap-0 overflow-hidden
          flex flex-col
          border-none shadow-2xl rounded-2xl
          bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/20
          dark:from-gray-900 dark:via-gray-800 dark:to-gray-900
          [&>button]:hidden
          ${compactMode ? 'text-sm' : ''}
        `}
          style={{ fontSize: `${fontSize}px` }}
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          {/* 增强的头部工具栏 */}
          <div className="flex-shrink-0 flex items-center justify-between px-6 py-4 border-b border-gray-200/50 dark:border-gray-700/50 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800 ${getSessionStatusColor()} bg-current`}></div>
                  {!isConnected && (
                    <div className="absolute -top-1 -left-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                      <WifiOff className="w-2 h-2 text-white" />
                    </div>
                  )}
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">AI销售培训</h2>
                  <p className="text-xs text-gray-500 dark:text-gray-400">智能对话训练系统 v2.0</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {session && (
                <>
                  {/* 增强的会话统计 */}
                  <div className="flex items-center gap-4 px-4 py-2 bg-gray-50/80 dark:bg-gray-800/80 rounded-lg border border-gray-200/50 dark:border-gray-700/50">
                    <div className="flex items-center gap-1 text-sm">
                      <Clock className="w-4 h-4 text-blue-500" />
                      <span className="font-medium text-gray-700 dark:text-gray-300">{getSessionDuration()}</span>
                    </div>
                    <Separator orientation="vertical" className="h-4" />
                    <div className="flex items-center gap-1 text-sm">
                      <MessageCircle className="w-4 h-4 text-green-500" />
                      <span className="font-medium text-gray-700 dark:text-gray-300">{sessionStats.totalMessages}</span>
                    </div>

                    {sessionStats.avgResponseTime > 0 && (
                      <>
                        <Separator orientation="vertical" className="h-4" />
                        <div className="flex items-center gap-1 text-sm">
                          <Timer className="w-4 h-4 text-orange-500" />
                          <span className="font-medium text-gray-700 dark:text-gray-300">{sessionStats.avgResponseTime}s</span>
                        </div>
                      </>
                    )}
                  </div>

                  {/* 会话控制 */}
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={toggleSession}
                      disabled={isLoading}
                      className="h-9 w-9"
                      title={session?.status === 'active' ? '暂停训练 (Ctrl+P)' : '继续训练 (Ctrl+P)'}
                    >
                      {session?.status === 'active' ? (
                        <Pause className="w-4 h-4" />
                      ) : (
                        <Play className="w-4 h-4" />
                      )}
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        resetSession()
                        resetGuidance() // 同时重置AI指导状态
                        setUserManuallyDisabled(false) // 重置用户手动关闭状态
                      }}
                      disabled={isLoading}
                      className="h-9 w-9"
                      title="重新开始 (Ctrl+R)"
                    >
                      <RotateCcw className="w-4 h-4" />
                    </Button>




                  </div>
                </>
              )}

              {/* 增强的设置和控制 */}
              <div className="flex items-center gap-1">


                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSidebar(!showSidebar)}
                  className="h-9 w-9"
                  title={showSidebar ? '隐藏侧栏 (Ctrl+B)' : '显示侧栏 (Ctrl+B)'}
                >
                  {showSidebar ? (
                    <PanelLeftClose className="w-5 h-5 text-blue-500" />
                  ) : (
                    <PanelLeftOpen className="w-5 h-5 text-gray-600" />
                  )}
                </Button>



                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  className="h-9 w-9"
                  title={isFullscreen ? '退出全屏 (F11)' : '全屏模式 (F11)'}
                >
                  {isFullscreen ? (
                    <Minimize2 className="w-4 h-4" />
                  ) : (
                    <Maximize2 className="w-4 h-4" />
                  )}
                </Button>

                <Button
                  variant="destructive"
                  size="sm"
                  onClick={onClose}
                  className="h-9 w-9 bg-red-500 hover:bg-red-600 text-white rounded-md shadow-sm"
                  title="关闭 (Esc)"
                >
                  <X className="w-4 h-4 stroke-2" />
                </Button>
              </div>
            </div>
          </div>

          {/* 连接状态提示 */}
          {!isConnected && (
            <Alert className="mx-6 mt-4 border-red-200 bg-red-50 text-red-800">
              <WifiOff className="h-4 w-4" />
              <AlertDescription>
                网络连接中断，正在尝试重新连接...
              </AlertDescription>
            </Alert>
          )}

          {/* 主要内容区域 */}
          <div className="flex-1 flex overflow-hidden">
            {/* 左侧客户信息卡片 */}
            <div className="w-72 flex-shrink-0 border-r border-gray-100 dark:border-gray-800 bg-gray-50 dark:bg-gray-900">
              <div className="p-4 space-y-4">
                {/* 客户基本信息 */}
                <div className="bg-white dark:bg-gray-900 rounded-xl border border-gray-100 dark:border-gray-800 p-4 mb-2">
                  <div className="flex items-center mb-3">
                    <User className="w-4 h-4 text-blue-500 mr-2" />
                    <span className="font-semibold text-gray-800 dark:text-gray-100 text-sm">客户信息</span>
                  </div>
                  <div className="flex items-center gap-3 mb-3">
                    <Avatar className="w-12 h-12 ring-2 ring-blue-100 dark:ring-blue-900">
                      <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 text-base font-bold">
                        {customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="font-semibold text-gray-900 dark:text-white text-base">{customer.name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{customer.background?.position || '职位未知'}</div>
                      {session?.status === 'active' && (
                        <div className="flex items-center gap-1 mt-1">
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                          <span className="text-xs text-green-600 dark:text-green-400">在线</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 客户详细标签 */}
                  <div className="flex flex-wrap gap-2">
                    {/* 公司名称 */}
                    {customer.background?.company && (
                      <span className="px-2 py-0.5 bg-green-100 dark:bg-green-800 rounded text-xs text-green-700 dark:text-green-200">
                        🏢 {customer.background.company}
                      </span>
                    )}

                    {/* 国家/地区 */}
                    {customer.country && (
                      <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-800 rounded text-xs text-blue-700 dark:text-blue-200">
                        {customer.country.flag || '🌍'} {customer.country.name || '未知国家'}
                      </span>
                    )}

                    {/* 相关产品 */}
                    {customer.product && (
                      <span className="px-2 py-0.5 bg-purple-100 dark:bg-purple-800 rounded text-xs text-purple-700 dark:text-purple-200">
                        📦 {customer.product.name}
                      </span>
                    )}
                  </div>
                </div>

                {/* 训练场景分组 */}
                <div className="bg-white dark:bg-gray-900 rounded-xl border border-gray-100 dark:border-gray-800 p-4 mb-2">
                  <div className="flex items-center mb-3">
                    <Target className="w-4 h-4 text-green-500 mr-2" />
                    <span className="font-semibold text-gray-800 dark:text-gray-100 text-sm">训练场景</span>
                  </div>

                  {/* 动态显示训练场景信息 */}
                  {customer.background?.experience ? (
                    <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      {customer.background.experience}
                    </div>
                  ) : (
                    <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      <div className="font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {customer.product?.category || '通用'}销售场景训练
                      </div>
                      <div className="text-xs leading-relaxed">
                        模拟真实销售环境，与{customer.name}进行专业的{customer.product?.name || '产品'}销售对话。
                        通过AI智能分析，提升您的销售技巧和沟通能力。
                      </div>
                    </div>
                  )}

                  {/* 训练目标 */}
                  <div className="mb-3">
                    <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">训练目标</div>
                    <div className="flex flex-wrap gap-1">
                      <span className="px-2 py-0.5 bg-blue-100 dark:bg-blue-800 rounded text-xs text-blue-700 dark:text-blue-200">
                        🎯 需求挖掘
                      </span>
                      <span className="px-2 py-0.5 bg-green-100 dark:bg-green-800 rounded text-xs text-green-700 dark:text-green-200">
                        💬 沟通技巧
                      </span>
                      <span className="px-2 py-0.5 bg-purple-100 dark:bg-purple-800 rounded text-xs text-purple-700 dark:text-purple-200">
                        🤝 成交技巧
                      </span>
                    </div>
                  </div>

                  {/* 客户偏好标签 */}
                  <div className="flex flex-wrap gap-2">
                    {customer.background?.preferences && customer.background.preferences.length > 0 ? (
                      customer.background.preferences.map((preference, index) => (
                        <span key={index} className="px-2 py-0.5 bg-orange-100 dark:bg-orange-800 rounded text-xs text-orange-700 dark:text-orange-200">
                          ⭐ {preference}
                        </span>
                      ))
                    ) : (
                      // 默认客户关注点
                      <>
                        <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-xs text-gray-700 dark:text-gray-200">
                          💰 性价比
                        </span>
                        <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-xs text-gray-700 dark:text-gray-200">
                          🛡️ 质量保证
                        </span>
                        <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-xs text-gray-700 dark:text-gray-200">
                          🚀 快速交付
                        </span>
                      </>
                    )}
                  </div>
                </div>

                {/* 训练提示卡片 */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200/50 dark:border-blue-800/50 p-4">
                  <div className="flex items-center mb-2">
                    <Lightbulb className="w-4 h-4 text-blue-500 mr-2" />
                    <span className="font-semibold text-blue-700 dark:text-blue-300 text-sm">训练提示</span>
                  </div>
                  <div className="text-xs text-blue-600 dark:text-blue-400 leading-relaxed">
                    <div className="mb-2">
                      <strong>场景背景：</strong>您是一名专业的销售代表，正在与来自{customer.country.name}的{customer.name}进行{customer.product?.name || '产品'}销售洽谈。
                    </div>
                    <div className="mb-2">
                      <strong>角色设定：</strong>{customer.name}担任{customer.background?.position || '决策者'}，对{customer.product?.name || '相关产品'}有采购需求。
                    </div>
                    <div>
                      <strong>训练重点：</strong>通过专业的销售对话，了解客户需求，建立信任关系，并推进销售进程。
                    </div>
                  </div>
                </div>

              </div>
            </div>

            {/* 对话区域 */}
            <div
              className="flex-1 flex flex-col min-w-0 relative"
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              {/* 拖拽覆盖层 */}
              {dragOver && (
                <div className="absolute inset-0 bg-blue-500/10 border-2 border-dashed border-blue-500 rounded-lg flex items-center justify-center z-50">
                  <div className="text-center">
                    <FileImage className="w-12 h-12 text-blue-500 mx-auto mb-2" />
                    <p className="text-blue-700 font-medium">释放文件到这里</p>
                    <p className="text-blue-600 text-sm">支持图片、文档等格式</p>
                  </div>
                </div>
              )}

              {/* 消息列表 */}
              <ScrollArea className="flex-1 p-6">
                <div className="space-y-6">
                  {messages.map((message, index) => (
                    <div
                      key={message.id || index}
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} group`}
                    >
                      <div className={`flex items-start space-x-3 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                        {/* 头像 */}
                        <Avatar className="w-10 h-10 flex-shrink-0">
                          <AvatarFallback className={message.role === 'user'
                            ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white'
                            : 'bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 text-gray-600 dark:text-gray-300'
                          }>
                            {message.role === 'user' ? (
                              <User className="w-5 h-5" />
                            ) : (
                              <Bot className="w-5 h-5" />
                            )}
                          </AvatarFallback>
                        </Avatar>

                        {/* 消息内容 */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            {/* <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              {message.role === 'user' ? '我' : customer.name}
                            </span> */}
                            {/* <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatMessageTime(message.timestamp)}
                            </span> */}
                            {/* {message.role === 'assistant' && (
                              <Badge variant="outline" className="text-xs">
                                AI回复
                              </Badge>
                            )} */}
                          </div>

                          <div className={`relative p-4 rounded-2xl shadow-sm border transition-all duration-200 hover:shadow-md ${message.role === 'user'
                              ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-300'
                              : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white border-gray-200 dark:border-gray-700'
                            }`}>
                            <p className="text-sm leading-relaxed whitespace-pre-wrap">
                              {message.content}
                            </p>




                          </div>

                          {/* 消息预览（实时打字效果） */}
                          {message.role === 'assistant' && messagePreview && index === messages.length - 1 && (
                            <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                              <p className="text-xs text-gray-600 dark:text-gray-400 animate-pulse">
                                正在思考回复...
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* 增强的打字指示器 */}
                  {(isLoading || typingIndicator) && (
                    <div className="flex justify-start group">
                      <div className="flex items-start space-x-3 max-w-[80%]">
                        <Avatar className="w-10 h-10 flex-shrink-0">
                          <AvatarFallback className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 text-gray-600 dark:text-gray-300">
                            <Bot className="w-5 h-5" />
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              {customer.name}
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              正在输入...
                            </span>
                            {/* <div className="flex items-center space-x-1">
                              <Brain className="w-3 h-3 text-purple-500 animate-pulse" />
                              <span className="text-xs text-purple-600">AI思考中</span>
                            </div> */}
                          </div>
                          <div className="p-4 rounded-2xl shadow-sm border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
                            <div className="flex items-center space-x-1">
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                              <span className="ml-2 text-xs text-gray-500">分析中...</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* 增强的输入区域 */}
              <div className="flex-shrink-0 p-6 border-t border-gray-200/50 dark:border-gray-700/50 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                <div className="space-y-3">
                  {/* AI建议区域 */}
                  {showAIAssist && (isGeneratingGuidance || quickReplies.length > 0) && (
                    <div className="mb-3 bg-blue-50 dark:bg-blue-900/10 rounded-lg p-3 border border-blue-200/50 dark:border-blue-800/50">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs text-gray-500">
                          {isGeneratingGuidance ? 'AI正在生成建议...' : `AI建议：以下为自动生成的答复方向，点击可快速生成高质量回复。`}
                        </span>
                      </div>

                      {/* 生成动效 */}
                      {isGeneratingGuidance && (
                        <div className="flex items-center justify-center py-4">
                          <div className="flex items-center space-x-2 text-blue-600">
                            <div className="w-4 h-4 relative">
                              <div className="absolute inset-0 rounded-full border-2 border-blue-200"></div>
                              <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-blue-600 animate-spin"></div>
                            </div>
                            <span className="text-sm">正在分析对话内容，生成智能建议...</span>
                          </div>
                        </div>
                      )}

                      {/* AI建议按钮 */}
                      {!isGeneratingGuidance && quickReplies.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {quickReplies.map((reply, idx) => (
                          <Button
                            key={reply.id}
                            variant="outline"
                            size="sm"
                            onClick={() => handleAIDirectionClick(reply.fullText, idx)}
                            className="h-8 text-xs bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/40 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300"
                            disabled={aiAssistLoading === idx}
                          >
                            {aiAssistLoading === idx ? <Loader2 className="w-3 h-3 animate-spin mr-1" /> : <Sparkles className="w-3 h-3 mr-1" />}
                            {reply.text}
                          </Button>
                        ))}
                        </div>
                      )}
                    </div>
                  )}

                  {/* 响应时间提示 */}
                  {responseTimer && (
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <Timer className="w-3 h-3 animate-spin" />
                      <span>响应时间: {((Date.now() - responseTimer) / 1000).toFixed(1)}s</span>
                    </div>
                  )}

                  {/* 输入框 */}
                  <div className="flex items-center gap-2">
                    <div className="flex-1 relative">
                      <Textarea
                        ref={inputRef}
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyDown={handleKeyDown}
                        placeholder={
                          isEvaluating
                            ? "AI评分进行中，请稍候..."
                            : session?.status === 'active'
                              ? "输入您的回复... (按 / 快速聚焦)"
                              : "点击开始训练"
                        }
                        disabled={!session || session.status !== 'active' || isLoading || isEvaluating}
                        className="w-full !h-12 !min-h-[48px] max-h-[120px] bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none pr-10 py-3"
                        maxLength={500}
                      />
                      {/* AI建议切换按钮 */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={toggleAIAssist}
                        className={`absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 transition-all duration-200 ${showAIAssist
                            ? 'text-yellow-500 hover:text-yellow-600 bg-yellow-50 hover:bg-yellow-100 dark:bg-yellow-900/20 dark:hover:bg-yellow-900/40'
                            : 'text-gray-400 hover:text-gray-600 bg-gray-50 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700'
                          }`}
                        title={showAIAssist ? "关闭AI建议" : "开启AI建议"}
                      >
                        <Lightbulb className={`w-3 h-3 transition-all duration-200 ${showAIAssist ? 'drop-shadow-sm' : ''}`} />
                      </Button>
                    </div>
                    <Button
                      onClick={handleSendMessage}
                      disabled={!inputValue.trim() || !session || session.status !== 'active' || isLoading || isEvaluating}
                      className="!h-12 !min-h-[48px] px-6 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg"
                    >
                      {isLoading || isEvaluating ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Send className="w-4 h-4" />
                      )}
                    </Button>
                  </div>

                  {/* 增强的状态栏 */}
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-4">
                      <span>按 Enter 发送，Shift+Enter 换行</span>
                      {session && (
                        <>
                          <span>•</span>
                          <span className="flex items-center space-x-1">
                            <div className={`w-2 h-2 rounded-full ${getSessionStatusColor()} bg-current`}></div>
                            <span>
                              {session.status === 'active' ? '训练中' :
                                session.status === 'paused' ? '已暂停' : '已完成'}
                            </span>
                          </span>
                        </>
                      )}
                    </div>
                    <div className="flex items-center space-x-4">
                      <span>{inputValue.length}/500</span>
                      {sessionStats.avgResponseTime > 0 && (
                        <>
                          <span>•</span>
                          <span>平均响应: {sessionStats.avgResponseTime}s</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 增强的右侧面板 */}
            {showSidebar && (
              <div className="w-80 border-l border-gray-200/50 dark:border-gray-700/50 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm flex flex-col">
                <Tabs value={sidebarTab} onValueChange={(value) => setSidebarTab(value as any)} className="flex flex-col h-full">
                  <div className="flex-shrink-0 p-4 border-b border-gray-200/50 dark:border-gray-700/50">
                    <TabsList className="grid w-full grid-cols-2 bg-gray-100 dark:bg-gray-800 h-10">
                      <TabsTrigger value="evaluation" className="text-xs px-2">
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="w-3 h-3" />
                          <span>评分</span>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger value="guidance" className="text-xs px-2">
                        <div className="flex items-center space-x-1">
                          <Brain className="w-3 h-3" />
                          <span>指导</span>
                        </div>
                      </TabsTrigger>

                    </TabsList>
                  </div>

                  <div className="flex-1 overflow-hidden">
                    <TabsContent value="evaluation" className="mt-0 h-full">
                      <RealTimeEvaluationPanel
                        messages={messages.map(msg => ({
                          ...msg,
                          role: msg.role === 'assistant' ? 'customer' : msg.role
                        }) as any)}
                        customer={customer}
                        currentEvaluation={currentEvaluation}
                        isEvaluating={isEvaluating}
                        isSessionActive={session?.status === 'active'}
                        evaluationError={evaluationError}
                        onStartEvaluation={handleStartEvaluation}
                      />
                    </TabsContent>

                    <TabsContent value="guidance" className="mt-0 h-full">
                      <AIGuidancePanel
                        messages={messages.map(msg => ({
                          ...msg,
                          role: msg.role === 'assistant' ? 'customer' : msg.role
                        }) as any)}
                        customer={customer}
                        isSessionActive={session?.status === 'active'}
                        overallAnalysis={overallAnalysis}
                        replyAnalyses={replyAnalyses}
                        isAnalyzing={isGuidanceAnalyzing}
                        error={guidanceError}
                        onStartAnalysis={handleStartGuidanceAnalysis}
                      />
                    </TabsContent>


                  </div>
                </Tabs>
              </div>
            )}
          </div>

          {/* 隐藏的文件输入 */}
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            accept="image/*,.pdf,.doc,.docx,.txt"
            multiple
            onChange={(e) => {
              const files = Array.from(e.target.files || [])
              if (files.length > 0) {
                toast.info("文件上传功能开发中...")
              }
            }}
          />
        </DialogContent>
      </Dialog>

      {/* 快捷键帮助对话框 */}
      <Dialog open={showShortcuts} onOpenChange={setShowShortcuts}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Keyboard className="w-5 h-5" />
              <span>键盘快捷键</span>
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 pt-4">
            {Object.entries(SHORTCUTS).map(([action, key]) => (
              <div key={action} className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {action.replace(/_/g, ' ').toLowerCase().replace(/^\w/, c => c.toUpperCase())}
                </span>
                <Badge variant="outline" className="font-mono text-xs">
                  {key}
                </Badge>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
