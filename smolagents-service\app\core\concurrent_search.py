"""
并发搜索服务
实现市场分析的并发搜索优化，提升性能
"""

import asyncio
import hashlib
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from app.models.schemas import AgentRequest, SearchStrategy
from app.core.redis_client import RedisClient
from app.core.prompt_optimizer import prompt_optimizer, performance_tracker, group_analysis_optimizer
from app.core.config_validator import get_agent_request_config, TaskType
from app.config import Settings


class ConcurrentSearchService:
    """并发搜索服务"""

    def __init__(self, agent_service, redis_client: RedisClient, settings: Settings):
        self.agent_service = agent_service
        self.redis_client = redis_client
        self.settings = settings
        self.logger = logging.getLogger(__name__)

    async def execute_parallel_search(self, strategies: List[SearchStrategy], product: str) -> Dict[str, Any]:
        """并发执行多个搜索策略"""
        self.logger.info(f"🚀 开始并发搜索，策略数量: {len(strategies)}")

        # 创建搜索任务
        search_tasks = []
        for i, strategy in enumerate(strategies):
            task = asyncio.create_task(
                self._execute_single_strategy(strategy, product, f"strategy_{i}")
            )
            search_tasks.append(task)

        # 并发执行，设置超时
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*search_tasks, return_exceptions=True),
                timeout=self.settings.strategy_execution_timeout * len(strategies)
            )

            # 合并搜索结果
            merged_results = self._merge_search_results(results, strategies)

            self.logger.info(f"✅ 并发搜索完成，成功: {merged_results['successful_searches']}/{len(strategies)}")
            return merged_results

        except asyncio.TimeoutError:
            self.logger.warning("⚠️ 并发搜索超时，返回部分结果")
            return {
                "success": False,
                "error": "搜索超时，请稍后重试",
                "successful_searches": 0,
                "total_strategies": len(strategies)
            }

    async def _execute_single_strategy(self, strategy: SearchStrategy, product: str, task_id: str) -> Dict[str, Any]:
        """执行单个搜索策略"""
        try:
            self.logger.info(f"🔍 执行策略: {strategy.category} - {task_id}")

            # 检查缓存
            if self.settings.enable_search_cache:
                cached_result = await self._get_search_cache(product, strategy.query)
                if cached_result:
                    self.logger.info(f"📦 使用缓存结果: {task_id}")
                    return {
                        "success": True,
                        "result": cached_result,
                        "strategy": strategy.category,
                        "cached": True,
                        "task_id": task_id
                    }

            # 根据策略类别选择合适的提示词优化器
            group_analysis_categories = [
                "functional_needs", "usage_scenarios", "pain_points",
                "preferences", "future_needs", "behavioral_patterns",
                "demographic_insights", "功能需求", "使用场景", "痛点分析",
                "偏好研究", "未来需求", "行为模式", "人群洞察"
            ]

            strategy_key = strategy.category.lower().replace(" ", "_").replace("-", "_")
            is_group_analysis = any(category in strategy_key.lower() or category in strategy.category.lower()
                                  for category in group_analysis_categories)

            if is_group_analysis:
                # 使用群体分析专用优化器
                optimized_query = group_analysis_optimizer.generate_optimized_search_prompt(product, strategy)
            else:
                # 使用标准市场分析优化器
                optimized_query = prompt_optimizer.generate_optimized_search_prompt(product, strategy)

            # 使用统一配置管理器获取单次搜索配置
            config_params = get_agent_request_config(
                task_type=TaskType.SINGLE_SEARCH
            )

            # 创建代理请求
            agent_request = AgentRequest(
                query=optimized_query,
                task_id=task_id,
                **config_params
            )

            # 执行搜索
            start_time = datetime.now()
            agent_response = await self.agent_service.process_query(agent_request)
            execution_time = (datetime.now() - start_time).total_seconds()

            if agent_response.success:
                # 缓存结果
                if self.settings.enable_search_cache:
                    await self._cache_search_result(product, strategy.query, agent_response.result)

                # 跟踪性能
                performance_tracker.track_search_performance(strategy.category, execution_time, True)

                self.logger.info(f"✅ 策略完成: {task_id}, 耗时: {execution_time:.2f}s")
                return {
                    "success": True,
                    "result": agent_response.result,
                    "strategy": strategy.category,
                    "execution_time": execution_time,
                    "cached": False,
                    "task_id": task_id
                }
            else:
                # 跟踪失败的性能
                performance_tracker.track_search_performance(strategy.category, execution_time, False)

                self.logger.warning(f"⚠️ 策略失败: {task_id}, 错误: {agent_response.error}")
                return {
                    "success": False,
                    "error": agent_response.error,
                    "strategy": strategy.category,
                    "task_id": task_id
                }

        except Exception as e:
            self.logger.error(f"❌ 策略执行异常: {task_id}, 错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "strategy": strategy.category,
                "task_id": task_id
            }



    def _merge_search_results(self, results: List[Any], strategies: List[SearchStrategy]) -> Dict[str, Any]:
        """合并搜索结果"""
        successful_results = []
        failed_results = []
        total_execution_time = 0.0
        cached_count = 0

        for result in results:
            if isinstance(result, Exception):
                failed_results.append({"error": str(result)})
            elif isinstance(result, dict):
                if result.get("success"):
                    successful_results.append(result)
                    total_execution_time += result.get("execution_time", 0)
                    if result.get("cached"):
                        cached_count += 1
                else:
                    failed_results.append(result)

        # 合并所有成功的搜索内容，使用中文标题
        combined_content = []

        # 扩展的策略标题映射，支持更多可能的策略分类
        strategy_titles = {
            # 英文标准分类 - 市场分析
            "market_trends": "市场趋势分析",
            "competitive_analysis": "竞争格局分析",
            "consumer_demand": "消费者需求分析",
            "technology_innovation": "技术创新分析",
            "regional_analysis": "区域市场分析",
            "price_analysis": "价格趋势分析",
            "growth_forecast": "增长预测分析",

            # 中文分类支持 - 市场分析
            "市场趋势": "市场趋势分析",
            "竞争格局": "竞争格局分析",
            "消费者需求": "消费者需求分析",
            "技术创新": "技术创新分析",
            "区域市场": "区域市场分析",
            "价格趋势": "价格趋势分析",
            "增长预测": "增长预测分析",

            # 常见的其他分类 - 市场分析
            "market_overview": "市场概况分析",
            "brand_analysis": "品牌分析",
            "supply_chain": "供应链分析",
            "investment_analysis": "投资分析",
            "risk_assessment": "风险评估",
            "market_size": "市场规模分析",
            "user_behavior": "用户行为分析",
            "industry_chain": "产业链分析",

            # 群体分析专用分类 - 英文
            "functional_needs": "功能需求分析",
            "usage_scenarios": "使用场景分析",
            "pain_points": "痛点分析",
            "preferences": "偏好研究",
            "future_needs": "未来需求分析",
            "behavioral_patterns": "行为模式分析",
            "demographic_insights": "人群洞察分析",

            # 群体分析专用分类 - 中文
            "功能需求": "功能需求分析",
            "使用场景": "使用场景分析",
            "痛点分析": "痛点分析",
            "偏好研究": "偏好研究",
            "未来需求": "未来需求分析",
            "行为模式": "行为模式分析",
            "人群洞察": "人群洞察分析",

            # 群体分析扩展分类
            "使用场景与痛点分析": "使用场景与痛点分析",
            "功能需求与偏好研究": "功能需求与偏好研究",
            "未来需求与趋势预测": "未来需求与趋势预测"
        }

        for i, result in enumerate(successful_results):
            if result.get("result"):
                strategy_name = result.get('strategy', f'strategy_{i+1}')

                # 智能匹配策略标题
                chinese_title = None

                # 1. 直接匹配
                if strategy_name in strategy_titles:
                    chinese_title = strategy_titles[strategy_name]
                else:
                    # 2. 模糊匹配（包含关键词）
                    strategy_lower = strategy_name.lower()
                    for key, title in strategy_titles.items():
                        if key.lower() in strategy_lower or strategy_lower in key.lower():
                            chinese_title = title
                            break

                    # 3. 如果还是没有匹配，使用原始名称
                    if not chinese_title:
                        if strategy_name.endswith('分析'):
                            chinese_title = strategy_name
                        else:
                            chinese_title = f"{strategy_name}分析"

                combined_content.append(f"## {chinese_title}\n{result['result']}")

        return {
            "success": len(successful_results) > 0,
            "combined_analysis": "\n\n".join(combined_content),
            "successful_searches": len(successful_results),
            "failed_searches": len(failed_results),
            "total_strategies": len(strategies),
            "total_execution_time": total_execution_time,
            "cached_results": cached_count,
            "individual_results": successful_results
        }

    async def _get_search_cache(self, product: str, query: str) -> Optional[str]:
        """获取搜索结果缓存"""
        try:
            cache_key = self._generate_cache_key(product, query)
            cached_data = await self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            self.logger.warning(f"缓存读取失败: {e}")
            return None

    async def _cache_search_result(self, product: str, query: str, result: str):
        """缓存搜索结果"""
        try:
            cache_key = self._generate_cache_key(product, query)
            await self.redis_client.setex(
                cache_key,
                self.settings.search_cache_ttl,
                json.dumps(result)
            )
        except Exception as e:
            self.logger.warning(f"缓存写入失败: {e}")

    def _generate_cache_key(self, product: str, query: str) -> str:
        """生成缓存键"""
        content = f"{product}:{query}".lower()
        return f"search_cache:{hashlib.md5(content.encode()).hexdigest()}"
