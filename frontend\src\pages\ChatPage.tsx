/**
 * 智能对话页面
 * 集成纯AI聊天界面，支持直接调用Ollama API
 */

import React from 'react';
import { PureChatInterface } from '@/components/chat/PureChatInterface';
import TemplateStatusIndicator from '@/components/template/TemplateStatusIndicator';

export default function ChatPage() {
  return (
    <div className="flex-1 flex flex-col h-full">
      {/* 页面头部 */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-14 items-center px-4">
          <div className="flex items-center space-x-2">
            <h1 className="text-lg font-semibold">智能对话</h1>
            <div className="h-4 w-px bg-border" />
            <p className="text-sm text-muted-foreground">
              与AI助手进行智能对话，支持多轮对话和会话管理
            </p>
          </div>
        </div>
      </div>

      {/* 话术模板状态指示器 */}
      <div className="px-4 py-2">
        <TemplateStatusIndicator
          onManageClick={() => window.location.href = '/market-analysis'}
          showDetails={false}
        />
      </div>

      {/* 聊天界面 */}
      <div className="flex-1 overflow-hidden">
        <PureChatInterface className="h-full" />
      </div>
    </div>
  );
}
