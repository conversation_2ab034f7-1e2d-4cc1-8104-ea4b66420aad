<#
.SYNOPSIS
    ZHT System 0624 统一管理脚本 - Windows 平台
.DESCRIPTION
    用于启动、停止、重启和监控 ZHT System 完整服务栈的 PowerShell 脚本
    包含前端、后端、Morphik Core 和数据库服务
.NOTES
    作者: ZHT 系统管理员
    版本: 2.0
    支持服务: 前端(React) + 后端(FastAPI) + Morphik Core + PostgreSQL + Redis
#>

# 脚本版本和名称
$SCRIPT_VERSION = "2.0"
$SCRIPT_NAME = "ZHT System 0624 统一管理脚本"

# 服务分组定义
$DATABASE_SERVICES = @("zht_db_0624", "zht_morphik_postgres_0624", "zht_morphik_redis_0624")
$CORE_SERVICES = @("zht_backend_0624", "zht_morphik_0624", "zht_morphik_worker_0624")
$AI_SERVICES = @("zht_smolagents_0624")
$FRONTEND_SERVICES = @("zht_frontend_0624")
$ALL_SERVICES = $DATABASE_SERVICES + $CORE_SERVICES + $AI_SERVICES + $FRONTEND_SERVICES

# 日志颜色函数
function Write-LogInfo {
    param([string]$Message)
    Write-Host "ℹ️ $Message" -ForegroundColor Cyan
}

function Write-LogSuccess {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-LogWarning {
    param([string]$Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

function Write-LogError {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-LogStep {
    param([string]$Message)
    Write-Host "🔄 $Message" -ForegroundColor Magenta
}

# 显示帮助信息
function Show-Help {
    Write-Host "$SCRIPT_NAME v$SCRIPT_VERSION" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "用法: .\manage-zht-system.ps1 [选项]" -ForegroundColor White
    Write-Host ""
    Write-Host "选项:" -ForegroundColor Yellow
    Write-Host "  -h, --help        显示帮助信息" -ForegroundColor White
    Write-Host "  -s, --start       启动所有服务" -ForegroundColor White
    Write-Host "  -r, --restart     重启所有服务" -ForegroundColor White
    Write-Host "  -t, --stop        停止所有服务" -ForegroundColor White
    Write-Host "  -l, --logs        查看服务日志" -ForegroundColor White
    Write-Host "  --status          查看服务状态" -ForegroundColor White
    Write-Host "  --build           强制重新构建服务" -ForegroundColor White
    Write-Host "  --no-cache        清理缓存并重新构建" -ForegroundColor White
    Write-Host "  --frontend-only   仅启动前端服务" -ForegroundColor White
    Write-Host "  --backend-only    仅启动后端服务" -ForegroundColor White
    Write-Host "  --morphik-only    仅启动Morphik服务" -ForegroundColor White
    Write-Host "  --ai-only         仅启动AI服务(SmoLAgents)" -ForegroundColor White
    Write-Host "  --dev-mode        切换开发模式 (on/off)" -ForegroundColor White
    Write-Host ""
    Write-Host "示例:" -ForegroundColor Yellow
    Write-Host "  .\manage-zht-system.ps1 --start           # 启动完整系统" -ForegroundColor White
    Write-Host "  .\manage-zht-system.ps1 --frontend-only   # 仅启动前端" -ForegroundColor White
    Write-Host "  .\manage-zht-system.ps1 --ai-only         # 仅启动AI服务" -ForegroundColor White
    Write-Host "  .\manage-zht-system.ps1 --dev-mode on     # 启用开发模式" -ForegroundColor White
    Write-Host "  .\manage-zht-system.ps1 --dev-mode off    # 禁用开发模式" -ForegroundColor White
    Write-Host "  .\manage-zht-system.ps1 --logs            # 查看日志" -ForegroundColor White
    Write-Host "  .\manage-zht-system.ps1 --restart         # 重启所有服务" -ForegroundColor White
    Write-Host ""
    Write-Host "服务访问:" -ForegroundColor Yellow
    Write-Host "  🌐 前端应用:     http://localhost:5173" -ForegroundColor White
    Write-Host "  🔧 后端API:      http://localhost:8001/docs" -ForegroundColor White
    Write-Host "  🧠 Morphik Core: http://localhost:8000/docs" -ForegroundColor White
    Write-Host "  🤖 SmoLAgents:   http://localhost:8002/docs" -ForegroundColor White
    Write-Host "  🗄️ PostgreSQL:   localhost:5433" -ForegroundColor White
    Write-Host ""
}

# 检查 Docker 状态
function Test-Docker {
    try {
        $dockerVersion = docker version --format "{{.Server.Version}}" 2>&1
        if ($LASTEXITCODE -ne 0) {
            throw "Docker 命令执行失败，退出代码: $LASTEXITCODE"
        }
        Write-LogInfo "Docker 运行中 (服务器版本: $dockerVersion)"
        return $true
    }
    catch {
        Write-LogError "Docker 未运行或无法访问。请启动 Docker Desktop。"
        return $false
    }
}

# 读取 .env 文件配置
function Read-EnvFile {
    param([string]$EnvFilePath = ".env")

    $envConfig = @{}

    if (Test-Path $EnvFilePath) {
        $content = Get-Content $EnvFilePath
        foreach ($line in $content) {
            $line = $line.Trim()
            if ($line -and !$line.StartsWith("#") -and $line.Contains("=")) {
                $parts = $line.Split("=", 2)
                if ($parts.Length -eq 2) {
                    $key = $parts[0].Trim()
                    $value = $parts[1].Trim()
                    $envConfig[$key] = $value
                }
            }
        }
    }

    return $envConfig
}

# 设置 Docker 环境
function Set-DockerEnvironment {
    param([string]$DevMode = $null)

    Write-LogStep "配置 Docker 环境..."

    # 读取 .env 文件配置
    $envConfig = Read-EnvFile

    # 如果没有指定 DevMode，从 .env 文件读取
    if (-not $DevMode) {
        $DevMode = $envConfig["DEV_MODE"]
        if (-not $DevMode) {
            $DevMode = "false"  # 默认为生产模式
        }
    }

    # 设置 Docker 构建环境变量
    $env:COMPOSE_DOCKER_CLI_BUILD = 1
    $env:DOCKER_BUILDKIT = 1

    # 清除可能存在的环境变量，让 Docker Compose 读取 .env 文件
    $env:NODE_ENV = $null
    $env:DEV_MODE = $null
    $env:VITE_DEV_MODE = $null
    $env:MORPHIK_DEV_MODE = $null
    $env:MORPHIK_RELOAD = $null
    $env:MORPHIK_WORKER_RELOAD = $null
    $env:MORPHIK_VOLUME_MODE = $null
    $env:FRONTEND_VOLUME_MODE = $null
    $env:BACKEND_VOLUME_MODE = $null
    $env:SMOLAGENTS_VOLUME_MODE = $null

    # 从 .env 文件读取实际的开发模式状态
    $actualDevMode = $envConfig["DEV_MODE"]
    $actualViteDevMode = $envConfig["VITE_DEV_MODE"]

    Write-LogInfo "从 .env 文件读取配置:"
    Write-LogInfo "  DEV_MODE=$actualDevMode"
    Write-LogInfo "  VITE_DEV_MODE=$actualViteDevMode"

    if ($actualDevMode -eq "true" -or $actualViteDevMode -eq "true") {
        Write-LogSuccess "Docker 环境配置完成 (开发模式: 启用)"
    } else {
        Write-LogSuccess "Docker 环境配置完成 (开发模式: 禁用)"
    }
}



# 检查前端依赖
function Test-FrontendDependencies {
    Write-LogStep "检查前端依赖..."

    if (!(Test-Path "frontend/node_modules")) {
        Write-LogInfo "安装前端依赖..."
        Push-Location frontend
        try {
            npm install --registry=https://registry.npmmirror.com
            if ($LASTEXITCODE -ne 0) {
                throw "npm install 失败"
            }
            Write-LogSuccess "前端依赖安装完成"
        }
        catch {
            Write-LogError "前端依赖安装失败: $_"
            return $false
        }
        finally {
            Pop-Location
        }
    } else {
        Write-LogSuccess "前端依赖已存在"
    }
    return $true
}

# 获取脚本目录
function Get-ScriptDirectory {
    $scriptDir = $PSScriptRoot
    if (-not $scriptDir) {
        $scriptDir = Get-Location
    }
    return $scriptDir
}

# 启动所有服务
function Start-AllServices {
    Write-LogInfo "启动完整 ZHT System 服务栈..."

    $scriptDir = Get-ScriptDirectory

    # 配置环境
    Set-DockerEnvironment

    # 检查前端依赖
    if (-not (Test-FrontendDependencies)) {
        return $false
    }

    # 停止现有容器
    Write-LogStep "清理现有容器..."
    docker-compose -f "$scriptDir\docker-compose.yml" down --remove-orphans 2>$null

    # 分阶段启动服务
    Write-LogStep "启动数据库服务..."
    try {
        docker-compose -f "$scriptDir\docker-compose.yml" up -d $DATABASE_SERVICES
        if ($LASTEXITCODE -ne 0) {
            throw "启动数据库服务失败"
        }
    }
    catch {
        Write-LogError "启动数据库服务失败: $_"
        return $false
    }

    # 启动核心服务
    Write-LogStep "启动核心服务..."
    try {
        docker-compose -f "$scriptDir\docker-compose.yml" up -d $CORE_SERVICES
        if ($LASTEXITCODE -ne 0) {
            throw "启动核心服务失败"
        }
    }
    catch {
        Write-LogError "启动核心服务失败: $_"
        return $false
    }

    # 启动AI服务
    Write-LogStep "启动AI服务(SmoLAgents)..."
    try {
        docker-compose -f "$scriptDir\docker-compose.yml" up -d $AI_SERVICES
        if ($LASTEXITCODE -ne 0) {
            throw "启动AI服务失败"
        }
    }
    catch {
        Write-LogError "启动AI服务失败: $_"
        return $false
    }

    # 等待核心服务和AI服务启动
    Write-LogInfo "等待核心服务和AI服务启动..."
    Start-Sleep 15

    # 启动前端服务
    Write-LogStep "启动前端服务..."
    try {
        docker-compose -f "$scriptDir\docker-compose.yml" up -d $FRONTEND_SERVICES
        if ($LASTEXITCODE -ne 0) {
            throw "启动前端服务失败"
        }
    }
    catch {
        Write-LogError "启动前端服务失败: $_"
        return $false
    }

    # 等待所有服务启动
    Write-LogInfo "等待所有服务启动..."
    Start-Sleep 10

    # 显示服务状态
    Show-ServiceStatus

    # 显示访问信息
    Show-AccessInfo

    Write-LogSuccess "ZHT System 启动完成！"
    return $true
}

# 启动前端服务
function Start-FrontendOnly {
    Write-LogInfo "启动前端服务..."

    $scriptDir = Get-ScriptDirectory
    Set-DockerEnvironment

    if (-not (Test-FrontendDependencies)) {
        return $false
    }

    try {
        docker-compose -f "$scriptDir\docker-compose.yml" up -d $FRONTEND_SERVICES
        Write-LogSuccess "前端服务启动完成"
        Write-Host "🌐 前端应用: http://localhost:5173" -ForegroundColor Yellow
        return $true
    }
    catch {
        Write-LogError "启动前端服务失败: $_"
        return $false
    }
}

# 启动后端服务
function Start-BackendOnly {
    Write-LogInfo "启动后端服务..."

    $scriptDir = Get-ScriptDirectory
    Set-DockerEnvironment

    $backendServices = $DATABASE_SERVICES + @("zht_backend_0624")

    try {
        docker-compose -f "$scriptDir\docker-compose.yml" up -d $backendServices

        Write-LogSuccess "后端服务启动完成"
        Write-Host "🔧 后端API: http://localhost:8001/docs" -ForegroundColor Yellow
        return $true
    }
    catch {
        Write-LogError "启动后端服务失败: $_"
        return $false
    }
}

# 启动Morphik服务
function Start-MorphikOnly {
    Write-LogInfo "启动Morphik服务..."

    $scriptDir = Get-ScriptDirectory
    Set-DockerEnvironment

    $morphikServices = @("zht_morphik_postgres_0624", "zht_morphik_redis_0624", "zht_morphik_0624", "zht_morphik_worker_0624")

    try {
        docker-compose -f "$scriptDir\docker-compose.yml" up -d $morphikServices
        Write-LogSuccess "Morphik服务启动完成"
        Write-Host "🧠 Morphik Core: http://localhost:8000/docs" -ForegroundColor Yellow
        return $true
    }
    catch {
        Write-LogError "启动Morphik服务失败: $_"
        return $false
    }
}

# 启动AI服务
function Start-AIOnly {
    Write-LogInfo "启动AI服务(SmoLAgents)..."

    $scriptDir = Get-ScriptDirectory
    Set-DockerEnvironment

    try {
        docker-compose -f "$scriptDir\docker-compose.yml" up -d $AI_SERVICES
        Write-LogSuccess "AI服务启动完成"
        Write-Host "🤖 SmoLAgents: http://localhost:8002/docs" -ForegroundColor Yellow
        return $true
    }
    catch {
        Write-LogError "启动AI服务失败: $_"
        return $false
    }
}

# 切换开发模式
function Set-DevMode {
    param(
        [Parameter(Mandatory=$true)]
        [ValidateSet("on", "off")]
        [string]$Mode
    )

    Write-Host "🔧 ZHT系统模式切换工具" -ForegroundColor Cyan
    Write-Host "================================" -ForegroundColor Cyan

    $scriptDir = Get-ScriptDirectory
    $envFile = Join-Path $scriptDir ".env"
    $devModeValue = if ($Mode -eq "on") { "true" } else { "false" }

    # 检查 .env 文件是否存在
    if (-not (Test-Path $envFile)) {
        Write-LogError "找不到 .env 文件: $envFile"
        return $false
    }

    # 更新 .env 文件配置
    Write-LogStep "更新 .env 文件配置..."
    try {
        # 读取当前配置
        $content = Get-Content $envFile

        if ($Mode -eq "on") {
            Write-Host "🚀 切换到开发模式..." -ForegroundColor Yellow

            # 替换开发模式配置
            $content = $content -replace "^SYSTEM_DEV_MODE=.*", "SYSTEM_DEV_MODE=true"
            $content = $content -replace "^DEV_MODE=.*", "DEV_MODE=true"
            $content = $content -replace "^VITE_DEV_MODE=.*", "VITE_DEV_MODE=true"
            $content = $content -replace "^MORPHIK_DEV_MODE=.*", "MORPHIK_DEV_MODE=true"
            $content = $content -replace "^DEBUG=.*", "DEBUG=true"
            $content = $content -replace "^NODE_ENV=.*", "NODE_ENV=development"
            $content = $content -replace "^MORPHIK_VOLUME_MODE=.*", "MORPHIK_VOLUME_MODE=rw"
            $content = $content -replace "^FRONTEND_VOLUME_MODE=.*", "FRONTEND_VOLUME_MODE=rw"
            $content = $content -replace "^BACKEND_VOLUME_MODE=.*", "BACKEND_VOLUME_MODE=rw"
            $content = $content -replace "^LOG_LEVEL=.*", "LOG_LEVEL=DEBUG"

            Write-Host "✅ 开发模式配置:" -ForegroundColor Green
            Write-Host "   - 跳过用户认证" -ForegroundColor Gray
            Write-Host "   - 启用代码热重载" -ForegroundColor Gray
            Write-Host "   - 启用调试日志" -ForegroundColor Gray
            Write-Host "   - 代码目录可写挂载" -ForegroundColor Gray
            Write-Host "   - 数据库配置统一管理" -ForegroundColor Gray

        } else {
            Write-Host "🔒 切换到生产模式..." -ForegroundColor Yellow

            # 替换生产模式配置
            $content = $content -replace "^SYSTEM_DEV_MODE=.*", "SYSTEM_DEV_MODE=false"
            $content = $content -replace "^DEV_MODE=.*", "DEV_MODE=false"
            $content = $content -replace "^VITE_DEV_MODE=.*", "VITE_DEV_MODE=false"
            $content = $content -replace "^MORPHIK_DEV_MODE=.*", "MORPHIK_DEV_MODE=false"
            $content = $content -replace "^DEBUG=.*", "DEBUG=false"
            $content = $content -replace "^NODE_ENV=.*", "NODE_ENV=production"
            $content = $content -replace "^MORPHIK_VOLUME_MODE=.*", "MORPHIK_VOLUME_MODE=ro"
            $content = $content -replace "^FRONTEND_VOLUME_MODE=.*", "FRONTEND_VOLUME_MODE=ro"
            $content = $content -replace "^BACKEND_VOLUME_MODE=.*", "BACKEND_VOLUME_MODE=ro"
            $content = $content -replace "^LOG_LEVEL=.*", "LOG_LEVEL=INFO"

            Write-Host "✅ 生产模式配置:" -ForegroundColor Green
            Write-Host "   - 需要用户认证" -ForegroundColor Gray
            Write-Host "   - 禁用代码热重载" -ForegroundColor Gray
            Write-Host "   - 标准日志级别" -ForegroundColor Gray
            Write-Host "   - 代码目录只读挂载" -ForegroundColor Gray
            Write-Host "   - 数据库配置统一管理" -ForegroundColor Gray
        }

        # 写入配置文件
        $content | Set-Content $envFile -Encoding UTF8
        Write-LogSuccess ".env 文件更新完成"

    } catch {
        Write-LogError "更新 .env 文件失败: $_"
        return $false
    }

    # 配置环境（不设置环境变量，只清除）
    Set-DockerEnvironment

    Write-LogStep "重新构建前端服务以应用新配置..."

    try {
        # 停止前端服务
        Write-LogInfo "停止前端服务..."
        docker-compose -f "$scriptDir\docker-compose.yml" stop zht_frontend_0624

        # 重新构建前端
        Write-LogInfo "重新构建前端服务..."
        docker-compose -f "$scriptDir\docker-compose.yml" build --no-cache zht_frontend_0624

        # 启动前端服务
        Write-LogInfo "启动前端服务..."
        docker-compose -f "$scriptDir\docker-compose.yml" up -d zht_frontend_0624

        Write-LogSuccess "前端服务重启完成"

        # 验证环境变量
        Write-LogStep "验证容器环境变量..."
        Start-Sleep 3

        try {
            $envCheck = docker exec zht_frontend_0624 printenv VITE_DEV_MODE 2>$null
            if ($envCheck -eq $devModeValue) {
                Write-LogSuccess "环境变量验证成功: VITE_DEV_MODE=$envCheck"
            } else {
                Write-LogWarning "环境变量验证失败: 期望 $devModeValue，实际 $envCheck"
            }
        } catch {
            Write-LogWarning "无法验证环境变量，请检查容器状态"
        }

        Write-Host ""
        Write-LogSuccess "开发模式切换完成！"
        Write-Host "🌐 前端应用: http://localhost:5173" -ForegroundColor Cyan
        Write-Host ""

        if ($Mode -eq "on") {
            Write-Host "✅ 开发模式已启用 - 无需登录即可访问应用" -ForegroundColor Green
        } else {
            Write-Host "🔒 开发模式已禁用 - 需要登录才能访问应用" -ForegroundColor Yellow
        }

        Write-Host ""
        Write-Host "🔄 如需重启所有服务以应用新配置:" -ForegroundColor Cyan
        Write-Host "   .\manage-zht-system.ps1 --restart" -ForegroundColor White
        Write-Host ""
        Write-Host "🔍 检查当前状态:" -ForegroundColor Cyan
        Write-Host "   .\check-dev-mode.ps1" -ForegroundColor White

        return $true

    } catch {
        Write-LogError "切换开发模式失败: $_"
        Write-LogInfo "请手动执行以下命令："
        Write-Host "  docker-compose stop zht_frontend_0624" -ForegroundColor Yellow
        Write-Host "  docker-compose build --no-cache zht_frontend_0624" -ForegroundColor Yellow
        Write-Host "  docker-compose up -d zht_frontend_0624" -ForegroundColor Yellow
        return $false
    }
}

# 停止所有服务
function Stop-AllServices {
    Write-LogInfo "停止所有服务..."

    $scriptDir = Get-ScriptDirectory

    try {
        docker-compose -f "$scriptDir\docker-compose.yml" down --remove-orphans
        Write-LogSuccess "所有服务已停止"
        return $true
    }
    catch {
        Write-LogError "停止服务失败: $_"
        return $false
    }
}

# 重启所有服务
function Restart-AllServices {
    Write-LogInfo "重启所有服务..."

    if (Stop-AllServices) {
        Start-Sleep 3
        return Start-AllServices
    }
    return $false
}

# 查看服务日志
function Show-ServiceLogs {
    Write-LogInfo "显示服务日志 (按 Ctrl+C 退出)..."

    $scriptDir = Get-ScriptDirectory
    docker-compose -f "$scriptDir\docker-compose.yml" logs -f
}

# 测试端口连接
function Test-ServicePort {
    param(
        [string]$HostName = "localhost",
        [int]$Port,
        [int]$TimeoutMs = 3000
    )

    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $asyncResult = $tcpClient.BeginConnect($HostName, $Port, $null, $null)
        $wait = $asyncResult.AsyncWaitHandle.WaitOne($TimeoutMs, $false)

        if ($wait) {
            $tcpClient.EndConnect($asyncResult)
            $tcpClient.Close()
            return $true
        } else {
            $tcpClient.Close()
            return $false
        }
    }
    catch {
        return $false
    }
}

# 查看服务状态
function Show-ServiceStatus {
    Write-LogInfo "检查服务状态..."

    $scriptDir = Get-ScriptDirectory

    # 显示容器状态表格
    docker-compose -f "$scriptDir\docker-compose.yml" ps

    Write-Host ""
    Write-LogInfo "容器运行状态:"

    # 获取容器状态信息
    $psOutput = docker-compose -f "$scriptDir\docker-compose.yml" ps --format "table {{.Name}}\t{{.State}}\t{{.Health}}"
    $lines = $psOutput -split "`n"

    # 跳过标题行，处理每个容器
    for ($i = 1; $i -lt $lines.Length; $i++) {
        $line = $lines[$i].Trim()
        if ($line -ne "") {
            $parts = $line -split "\s+"
            if ($parts.Length -ge 2) {
                $name = $parts[0]
                $status = $parts[1]
                $health = if ($parts.Length -gt 2) { $parts[2] } else { "" }

                if ($status -eq "running") {
                    if ($health -eq "healthy" -or $health -eq "" -or $health -eq "Up") {
                        Write-LogSuccess "$name - 运行正常"
                    } else {
                        Write-LogWarning "$name - 运行中但健康检查异常: $health"
                    }
                } else {
                    Write-LogError "$name - 状态异常: $status"
                }
            }
        }
    }

    Write-Host ""
    Write-LogInfo "服务访问地址:"
    Write-Host "  🌐 前端应用:     http://localhost:5173" -ForegroundColor Cyan
    Write-Host "  🔧 后端API:      http://localhost:8001/docs" -ForegroundColor Cyan
    Write-Host "  🧠 Morphik Core: http://localhost:8000/docs" -ForegroundColor Cyan
    Write-Host "  🤖 SmoLAgents:   http://localhost:8002/docs" -ForegroundColor Cyan
    Write-Host "  🗄️ PostgreSQL:   localhost:5433" -ForegroundColor Cyan

    Write-Host ""
    Write-LogInfo "快速测试命令:"
    Write-Host "  curl http://localhost:5173" -ForegroundColor Yellow
    Write-Host "  curl http://localhost:8001/api/v1/health" -ForegroundColor Yellow
    Write-Host "  curl http://localhost:8000/ping" -ForegroundColor Yellow
    Write-Host "  curl http://localhost:8002/health" -ForegroundColor Yellow

    # 简单的端口检查（不依赖HTTP）
    Write-Host ""
    Write-LogInfo "端口连通性检查:"

    $ports = @(
        @{Port=5173; Name="前端服务"},
        @{Port=8001; Name="后端API"},
        @{Port=8000; Name="Morphik Core"},
        @{Port=8002; Name="SmoLAgents"},
        @{Port=5433; Name="主数据库"}
    )

    foreach ($portInfo in $ports) {
        try {
            # 使用简化的 TcpClient 同步连接方法
            $tcpClient = New-Object System.Net.Sockets.TcpClient
            $tcpClient.ReceiveTimeout = 1000
            $tcpClient.SendTimeout = 1000
            $tcpClient.Connect("127.0.0.1", $portInfo.Port)
            $tcpClient.Close()
            Write-LogSuccess "$($portInfo.Name) 端口 $($portInfo.Port) 可访问"
        }
        catch {
            Write-LogWarning "$($portInfo.Name) 端口 $($portInfo.Port) 连接失败"
        }
    }
}

# 显示访问信息
function Show-AccessInfo {
    Write-Host ""
    Write-Host "🎉 ZHT System 0624 启动完成！" -ForegroundColor Green
    Write-Host "======================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📱 服务访问地址:" -ForegroundColor Yellow
    Write-Host "  🌐 前端应用:     http://localhost:5173" -ForegroundColor White
    Write-Host "  🔧 后端API:      http://localhost:8001/docs" -ForegroundColor White
    Write-Host "  🧠 Morphik Core: http://localhost:8000/docs" -ForegroundColor White
    Write-Host "  🤖 SmoLAgents:   http://localhost:8002/docs" -ForegroundColor White
    Write-Host ""
    Write-Host "🗄️ 数据库信息:" -ForegroundColor Yellow
    Write-Host "  📊 PostgreSQL:   localhost:5433" -ForegroundColor White
    Write-Host "  👤 用户名:       zht_user" -ForegroundColor White
    Write-Host "  🔑 密码:         zht_password" -ForegroundColor White
    Write-Host "  🗃️ 数据库:       zht_db" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 管理命令:" -ForegroundColor Yellow
    Write-Host "  查看日志:        .\manage-zht-system.ps1 --logs" -ForegroundColor White
    Write-Host "  停止服务:        .\manage-zht-system.ps1 --stop" -ForegroundColor White
    Write-Host "  重启服务:        .\manage-zht-system.ps1 --restart" -ForegroundColor White
    Write-Host "  查看状态:        .\manage-zht-system.ps1 --status" -ForegroundColor White
    Write-Host ""
    Write-Host "📋 功能特性:" -ForegroundColor Yellow
    Write-Host "  ✅ 任务管理CRUD操作" -ForegroundColor White
    Write-Host "  ✅ 异步SQLAlchemy 2.0" -ForegroundColor White
    Write-Host "  ✅ PostgreSQL数据库" -ForegroundColor White
    Write-Host "  ✅ React + TypeScript前端" -ForegroundColor White
    Write-Host "  ✅ TanStack Query状态管理" -ForegroundColor White
    Write-Host "  ✅ shadcn/ui组件库" -ForegroundColor White
    Write-Host "  ✅ Morphik AI服务集成" -ForegroundColor White
    Write-Host "  ✅ SmoLAgents智能代理" -ForegroundColor White
    Write-Host "======================================" -ForegroundColor Cyan
}

# 强制重建服务
function Build-AllServices {
    Write-LogInfo "强制重建并启动所有服务..."

    $scriptDir = Get-ScriptDirectory
    Set-DockerEnvironment

    # 检查前端依赖
    if (-not (Test-FrontendDependencies)) {
        return $false
    }

    # 停止现有容器
    Write-LogStep "停止现有容器..."
    docker-compose -f "$scriptDir\docker-compose.yml" down --remove-orphans

    # 构建镜像
    Write-LogStep "构建镜像（这可能需要几分钟时间）..."
    try {
        docker-compose -f "$scriptDir\docker-compose.yml" build
        if ($LASTEXITCODE -ne 0) {
            throw "构建失败"
        }
        Write-LogSuccess "构建成功完成"
        return Start-AllServices
    }
    catch {
        Write-LogError "构建失败: $_"
        Write-LogInfo "故障排除提示:"
        Write-LogInfo "  • 检查 Docker Desktop 是否运行"
        Write-LogInfo "  • 确保有足够的磁盘空间"
        Write-LogInfo "  • 尝试: docker system prune -f"
        Write-LogInfo "  • 检查网络连接"
        return $false
    }
}

# 清理缓存并重建
function Build-NoCache {
    Write-LogInfo "清理缓存并重建..."

    $scriptDir = Get-ScriptDirectory
    Set-DockerEnvironment

    # 检查前端依赖
    if (-not (Test-FrontendDependencies)) {
        return $false
    }

    # 清理 Docker 系统
    Write-LogStep "清理 Docker 系统缓存..."
    docker system prune -f

    # 移除未使用的镜像
    Write-LogStep "移除未使用的镜像..."
    docker image prune -f

    # 构建并启动
    Write-LogStep "开始无缓存构建..."
    try {
        docker-compose -f "$scriptDir\docker-compose.yml" build --no-cache
        if ($LASTEXITCODE -ne 0) {
            throw "构建失败"
        }
        Write-LogSuccess "构建成功完成"
        return Start-AllServices
    }
    catch {
        Write-LogError "构建失败: $_"
        return $false
    }
}

# 主函数
function Main {
    param(
        [switch]$h,
        [switch]$Help,
        [switch]$s,
        [switch]$Start,
        [switch]$r,
        [switch]$Restart,
        [switch]$l,
        [switch]$Logs,
        [switch]$t,
        [switch]$Stop,
        [switch]$Status,
        [switch]$Build,
        [switch]$NoCache,
        [switch]$FrontendOnly,
        [switch]$BackendOnly,
        [switch]$MorphikOnly,
        [switch]$AIOnly,
        [string]$DevMode
    )

    # 显示脚本标题
    Write-Host "$SCRIPT_NAME v$SCRIPT_VERSION" -ForegroundColor Cyan
    Write-Host ""

    # 检查 Docker 状态（跳过帮助命令）
    if (-not ($h -or $Help)) {
        $dockerRunning = Test-Docker
        if (-not $dockerRunning) {
            exit 1
        }
    }

    # 处理参数
    if ($h -or $Help) {
        Show-Help
    }
    elseif ($s -or $Start) {
        Start-AllServices
    }
    elseif ($FrontendOnly) {
        Start-FrontendOnly
    }
    elseif ($BackendOnly) {
        Start-BackendOnly
    }
    elseif ($MorphikOnly) {
        Start-MorphikOnly
    }
    elseif ($AIOnly) {
        Start-AIOnly
    }
    elseif ($DevMode) {
        if ($DevMode -in @("on", "off")) {
            Set-DevMode -Mode $DevMode
        } else {
            Write-LogError "无效的开发模式参数: $DevMode (应为 'on' 或 'off')"
            exit 1
        }
    }
    elseif ($r -or $Restart) {
        Restart-AllServices
    }
    elseif ($l -or $Logs) {
        Show-ServiceLogs
    }
    elseif ($t -or $Stop) {
        Stop-AllServices
    }
    elseif ($Status) {
        Show-ServiceStatus
    }
    elseif ($Build) {
        Build-AllServices
    }
    elseif ($NoCache) {
        Build-NoCache
    }
    else {
        Write-LogInfo "使用 --help 查看帮助信息"
        Write-Host ""
        Show-ServiceStatus
    }
}

# 解析命令行参数并调用主函数
$params = @{}
$skipNext = $false

for ($i = 0; $i -lt $args.Length; $i++) {
    if ($skipNext) {
        $skipNext = $false
        continue
    }

    $arg = $args[$i]
    switch ($arg) {
        { $_ -in @("-h", "--help") } { $params['Help'] = $true }
        { $_ -in @("-s", "--start") } { $params['Start'] = $true }
        { $_ -in @("-r", "--restart") } { $params['Restart'] = $true }
        { $_ -in @("-l", "--logs") } { $params['Logs'] = $true }
        { $_ -in @("-t", "--stop") } { $params['Stop'] = $true }
        "--status" { $params['Status'] = $true }
        "--build" { $params['Build'] = $true }
        "--no-cache" { $params['NoCache'] = $true }
        "--frontend-only" { $params['FrontendOnly'] = $true }
        "--backend-only" { $params['BackendOnly'] = $true }
        "--morphik-only" { $params['MorphikOnly'] = $true }
        "--ai-only" { $params['AIOnly'] = $true }
        { $_ -match "^--dev-mode=(.+)$" } { $params['DevMode'] = $matches[1] }
        "--dev-mode" {
            # 处理 --dev-mode on/off 格式
            if (($i + 1) -lt $args.Length -and $args[$i + 1] -in @("on", "off")) {
                $params['DevMode'] = $args[$i + 1]
                $skipNext = $true
            } else {
                Write-LogError "--dev-mode 需要参数 'on' 或 'off'"
                Write-Host "使用方法: .\manage-zht-system.ps1 --dev-mode on|off"
                exit 1
            }
        }
        default {
            # 跳过已处理的 on/off 参数
            if ($arg -in @("on", "off") -and $i -gt 0 -and $args[$i-1] -eq "--dev-mode") {
                continue
            }
            Write-LogError "未知参数: $arg"
            Write-Host "使用 --help 查看帮助信息"
            exit 1
        }
    }
}

# 调用主函数
try {
    Main @params
}
catch {
    Write-LogError "脚本执行失败: $_"
    Write-LogInfo "请检查错误信息并重试"
    exit 1
}
