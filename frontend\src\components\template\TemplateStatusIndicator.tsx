/**
 * 话术模板状态指示器组件
 * 显示当前激活的模板信息和快速操作
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ScrollText,
  Play,
  Pause,
  Settings,
  Info,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { templateService } from '@/services/templateService';
import { templateIntegration } from '@/services/templateIntegration';
import { Template } from '@/types/template';
import { toast } from 'sonner';

interface TemplateStatusIndicatorProps {
  className?: string;
  showDetails?: boolean;
  onManageClick?: () => void;
}

const TemplateStatusIndicator: React.FC<TemplateStatusIndicatorProps> = ({
  className = '',
  showDetails = true,
  onManageClick
}) => {
  const [activeTemplate, setActiveTemplate] = useState<Template | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  // 加载激活模板
  useEffect(() => {
    loadActiveTemplate();

    // 监听存储变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'zht_active_template') {
        loadActiveTemplate();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const loadActiveTemplate = () => {
    const template = templateService.getActiveTemplate();
    setActiveTemplate(template);
  };

  const handleDeactivate = () => {
    try {
      templateService.deactivateTemplate();
      setActiveTemplate(null);
      toast.success('已取消激活模板');
    } catch (error) {
      console.error('取消激活失败:', error);
      toast.error('取消激活失败');
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
  };

  // 如果没有激活模板或已隐藏，不显示组件
  if (!activeTemplate || !isVisible) {
    return null;
  }

  const templateInfo = templateIntegration.getActiveTemplateInfo();
  const usageHint = templateIntegration.getTemplateUsageHint();

  return (
    <Card className={`border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950 ${className}`}>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <div className="p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 flex-1">
              <Play className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-700 dark:text-green-300">
                {usageHint}
              </span>
              <Badge variant="outline" className="text-xs border-green-300 text-green-700">
                激活中
              </Badge>
            </div>

            <div className="flex items-center gap-1">
              {showDetails && (
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    {isExpanded ? (
                      <ChevronUp className="h-3 w-3" />
                    ) : (
                      <ChevronDown className="h-3 w-3" />
                    )}
                  </Button>
                </CollapsibleTrigger>
              )}

              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={handleDeactivate}
                title="取消激活"
              >
                <Pause className="h-3 w-3" />
              </Button>

              {onManageClick && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={onManageClick}
                  title="管理模板"
                >
                  <Settings className="h-3 w-3" />
                </Button>
              )}

              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={handleDismiss}
                title="隐藏提示"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {showDetails && (
          <CollapsibleContent>
            <CardContent className="pt-0 pb-3">
              <div className="space-y-3 text-sm">
                {/* 模板基本信息 */}
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <span className="font-medium text-green-700 dark:text-green-300">模板名称:</span>
                    <div className="text-green-600 dark:text-green-400">{activeTemplate.name}</div>
                  </div>
                  <div>
                    <span className="font-medium text-green-700 dark:text-green-300">类别:</span>
                    <div className="text-green-600 dark:text-green-400">{activeTemplate.category}</div>
                  </div>
                  <div>
                    <span className="font-medium text-green-700 dark:text-green-300">角色:</span>
                    <div className="text-green-600 dark:text-green-400">
                      {activeTemplate.role.icon} {activeTemplate.role.name}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium text-green-700 dark:text-green-300">风格:</span>
                    <div className="text-green-600 dark:text-green-400">{activeTemplate.style.name}</div>
                  </div>
                </div>

                {/* 模板描述 */}
                <div>
                  <span className="font-medium text-green-700 dark:text-green-300">描述:</span>
                  <div className="text-green-600 dark:text-green-400 mt-1">
                    {activeTemplate.description}
                  </div>
                </div>

                {/* 使用统计 */}
                <div className="flex items-center gap-4 text-xs text-green-600 dark:text-green-400">
                  <span>使用次数: {activeTemplate.usage_count}</span>
                  <span>评分: {activeTemplate.rating.toFixed(1)}</span>
                  <span>版本: v{activeTemplate.version}</span>
                </div>

                {/* 标签 */}
                {activeTemplate.tags.length > 0 && (
                  <div>
                    <span className="font-medium text-green-700 dark:text-green-300">标签:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {activeTemplate.tags.map(tag => (
                        <Badge
                          key={tag}
                          variant="outline"
                          className="text-xs border-green-300 text-green-700"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDeactivate}
                    className="border-green-300 text-green-700 hover:bg-green-100"
                  >
                    <Pause className="h-3 w-3 mr-1" />
                    取消激活
                  </Button>

                  {onManageClick && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onManageClick}
                      className="border-green-300 text-green-700 hover:bg-green-100"
                    >
                      <ScrollText className="h-3 w-3 mr-1" />
                      管理模板
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        )}
      </Collapsible>
    </Card>
  );
};

export default TemplateStatusIndicator;
