import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { GenericSelector } from '@/components/ProductSelector'
import { Flag } from 'lucide-react'
import { useCountryOptions } from '@/hooks/useCountryOptions'

// 创建静态测试数据
const testOptions = [
  {
    id: 'test_1',
    value: 'usa',
    label: '美国',
    icon: Flag,
    iconName: 'Flag',
    createdAt: new Date().toISOString()
  },
  {
    id: 'test_2',
    value: 'china',
    label: '中国',
    icon: Flag,
    iconName: 'Flag',
    createdAt: new Date().toISOString()
  }
]

export default function TestPage() {
  const [count, setCount] = useState(0)
  const [selectedCountry, setSelectedCountry] = useState<string>('')
  const [selectedCountry2, setSelectedCountry2] = useState<string>('')

  // 测试useCountryOptions hook
  const { countryOptions, isLoading: countryLoading } = useCountryOptions()

  const handleDeleteOption = (optionId: string, optionLabel: string) => {
    console.log('删除选项:', optionId, optionLabel)
  }

  const handleAddOption = () => {
    console.log('添加选项')
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Hook vs 静态数据对比测试</h1>

      <div className="space-y-4">
        <p>计数器: {count}</p>
        <Button onClick={() => setCount(count + 1)}>
          点击增加
        </Button>

        <div className="mt-4 p-4 border rounded">
          <h3 className="font-bold">静态测试数据:</h3>
          <p>选项数量: {testOptions.length}</p>
          <p>当前选择: {selectedCountry}</p>
        </div>

        <div className="mt-4 p-4 border rounded">
          <h3 className="font-bold">Hook数据:</h3>
          <p>加载状态: {countryLoading ? '加载中' : '已加载'}</p>
          <p>选项数量: {countryOptions.length}</p>
          <p>当前选择: {selectedCountry2}</p>
        </div>

        <div className="mt-4 p-4 border rounded">
          <h3 className="font-bold">静态数据GenericSelector (应该正常):</h3>
          <div className="mt-2">
            <label className="text-sm font-medium">静态数据</label>
            <GenericSelector
              options={testOptions}
              selectedValue={selectedCountry}
              onValueChange={setSelectedCountry}
              onDeleteOption={handleDeleteOption}
              onAddOption={handleAddOption}
              isLoading={false}
              placeholder="选择国家..."
            />
          </div>
        </div>

        <div className="mt-4 p-4 border rounded">
          <h3 className="font-bold">Hook数据GenericSelector (可能白屏):</h3>
          <div className="mt-2">
            <label className="text-sm font-medium">Hook数据</label>
            <GenericSelector
              options={countryOptions}
              selectedValue={selectedCountry2}
              onValueChange={setSelectedCountry2}
              onDeleteOption={handleDeleteOption}
              onAddOption={handleAddOption}
              isLoading={countryLoading}
              placeholder="选择国家..."
            />
          </div>
        </div>
      </div>
    </div>
  )
}
