"""add sales training tables

Revision ID: 006_add_sales_training_tables
Revises: 005_add_new_permissions
Create Date: 2025-01-30 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '006_add_sales_training_tables'
down_revision = '005'
branch_labels = None
depends_on = None


def upgrade():
    # 创建销售训练国家表
    op.create_table('sales_training_countries',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False, comment='国家名称'),
        sa.Column('code', sa.String(length=10), nullable=False, comment='国家代码'),
        sa.Column('flag', sa.String(length=20), nullable=True, comment='国旗emoji'),
        sa.Column('is_active', sa.<PERSON>(), nullable=False, comment='是否启用'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('code'),
        sa.UniqueConstraint('name')
    )
    op.create_index('idx_country_active', 'sales_training_countries', ['is_active'], unique=False)
    op.create_index('idx_country_code', 'sales_training_countries', ['code'], unique=False)
    op.create_index('idx_country_name', 'sales_training_countries', ['name'], unique=False)

    # 创建销售训练产品表
    op.create_table('sales_training_products',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False, comment='产品名称'),
        sa.Column('category', sa.String(length=100), nullable=False, comment='产品分类'),
        sa.Column('description', sa.Text(), nullable=True, comment='产品描述'),
        sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否启用'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_product_active', 'sales_training_products', ['is_active'], unique=False)
    op.create_index('idx_product_category', 'sales_training_products', ['category'], unique=False)
    op.create_index('idx_product_name', 'sales_training_products', ['name'], unique=False)
    op.create_index('uk_product_name_category', 'sales_training_products', ['name', 'category'], unique=True)

    # 创建销售训练职位表
    op.create_table('sales_training_positions',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False, comment='职位名称'),
        sa.Column('category', sa.String(length=100), nullable=False, comment='职位部门'),
        sa.Column('description', sa.Text(), nullable=True, comment='职位描述'),
        sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否启用'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_position_active', 'sales_training_positions', ['is_active'], unique=False)
    op.create_index('idx_position_category', 'sales_training_positions', ['category'], unique=False)
    op.create_index('idx_position_name', 'sales_training_positions', ['name'], unique=False)
    op.create_index('uk_position_name_category', 'sales_training_positions', ['name', 'category'], unique=True)

    # 插入初始数据
    # 国家数据
    countries_table = sa.table('sales_training_countries',
        sa.column('name', sa.String),
        sa.column('code', sa.String),
        sa.column('flag', sa.String),
        sa.column('is_active', sa.Boolean)
    )

    op.bulk_insert(countries_table, [
        {'name': '美国', 'code': 'US', 'flag': '🇺🇸', 'is_active': True},
        {'name': '德国', 'code': 'DE', 'flag': '🇩🇪', 'is_active': True},
        {'name': '日本', 'code': 'JP', 'flag': '🇯🇵', 'is_active': True},
        {'name': '英国', 'code': 'GB', 'flag': '🇬🇧', 'is_active': True},
        {'name': '法国', 'code': 'FR', 'flag': '🇫🇷', 'is_active': True},
        {'name': '加拿大', 'code': 'CA', 'flag': '🇨🇦', 'is_active': True},
        {'name': '澳大利亚', 'code': 'AU', 'flag': '🇦🇺', 'is_active': True},
        {'name': '新加坡', 'code': 'SG', 'flag': '🇸🇬', 'is_active': True}
    ])

    # 产品数据
    products_table = sa.table('sales_training_products',
        sa.column('name', sa.String),
        sa.column('category', sa.String),
        sa.column('description', sa.String),
        sa.column('is_active', sa.Boolean)
    )

    op.bulk_insert(products_table, [
        {'name': '智能制造系统', 'category': '工业自动化', 'description': '工业4.0智能制造解决方案', 'is_active': True},
        {'name': '数据分析平台', 'category': '大数据', 'description': '企业级数据分析和可视化平台', 'is_active': True},
        {'name': '云服务解决方案', 'category': '云计算', 'description': '企业云服务和基础设施解决方案', 'is_active': True},
        {'name': 'AI客服系统', 'category': '人工智能', 'description': '智能客服和自动化服务系统', 'is_active': True},
        {'name': '物联网平台', 'category': '物联网', 'description': '工业物联网和设备管理平台', 'is_active': True},
        {'name': '区块链解决方案', 'category': '区块链', 'description': '企业级区块链技术应用', 'is_active': True},
        {'name': '网络安全系统', 'category': '网络安全', 'description': '全方位网络安全防护解决方案', 'is_active': True},
        {'name': '移动应用平台', 'category': '移动开发', 'description': '跨平台移动应用开发解决方案', 'is_active': True}
    ])

    # 职位数据
    positions_table = sa.table('sales_training_positions',
        sa.column('name', sa.String),
        sa.column('category', sa.String),
        sa.column('description', sa.String),
        sa.column('is_active', sa.Boolean)
    )

    op.bulk_insert(positions_table, [
        {'name': '采购经理', 'category': '采购', 'description': '负责企业采购决策', 'is_active': True},
        {'name': '技术总监', 'category': '技术', 'description': '负责技术决策和规划', 'is_active': True},
        {'name': '项目经理', 'category': '项目', 'description': '负责项目实施和管理', 'is_active': True},
        {'name': 'CEO', 'category': '高管', 'description': '企业最高决策者', 'is_active': True},
        {'name': '运营总监', 'category': '运营', 'description': '负责企业运营管理', 'is_active': True},
        {'name': 'CTO', 'category': '高管', 'description': '首席技术官', 'is_active': True},
        {'name': '财务总监', 'category': '财务', 'description': '负责财务管理和预算控制', 'is_active': True},
        {'name': '销售总监', 'category': '销售', 'description': '负责销售策略和团队管理', 'is_active': True}
    ])


def downgrade():
    # 删除表
    op.drop_table('sales_training_positions')
    op.drop_table('sales_training_products')
    op.drop_table('sales_training_countries')
