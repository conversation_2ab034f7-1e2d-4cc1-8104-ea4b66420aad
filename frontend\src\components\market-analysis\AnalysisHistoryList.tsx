/**
 * 市场分析历史记录列表组件
 */

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Calendar,
  Clock,
  Package,
  CheckCircle,
  XCircle,
  AlertCircle,
  Search,
  Eye,
  Trash2,
  Filter,
  RefreshCw
} from 'lucide-react';
import { MarketAnalysisRecord, HistoryFilterOptions } from '@/types/marketAnalysis';
import { toast } from 'sonner';

// 简单的日期格式化函数
const formatTimeAgo = (timestamp: string): string => {
  const now = new Date();
  const date = new Date(timestamp);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return '刚刚';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} 分钟前`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} 小时前`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} 天前`;

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};
import AnalysisHistoryDetailDialog from './AnalysisHistoryDetailDialog';

interface AnalysisHistoryListProps {
  records: MarketAnalysisRecord[];
  isLoading: boolean;
  onDeleteRecord: (id: string) => Promise<boolean>;
  onDeleteMultipleRecords: (ids: string[]) => Promise<number>;
  onClearAllRecords: () => Promise<void>;
  onApplyFilters: (filters: HistoryFilterOptions) => void;
  onResetFilters: () => void;
  onRefreshData: () => void;
  currentFilters: HistoryFilterOptions;
  compact?: boolean; // 紧凑模式
}

export function AnalysisHistoryList({
  records,
  isLoading,
  onDeleteRecord,
  onDeleteMultipleRecords,
  onClearAllRecords,
  onApplyFilters,
  onResetFilters,
  onRefreshData,
  currentFilters,
  compact = false
}: AnalysisHistoryListProps) {
  const [selectedRecords, setSelectedRecords] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<MarketAnalysisRecord | null>(null);
  const [showDetailDialog, setShowDetailDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showClearAllDialog, setShowClearAllDialog] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<string | null>(null);

  // 过滤记录
  const filteredRecords = records.filter(record => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      record.productLabel.toLowerCase().includes(searchLower) ||
      record.productType.toLowerCase().includes(searchLower) ||
      record.analysisResult.toLowerCase().includes(searchLower)
    );
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'cancelled':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success':
        return '成功';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'cancelled':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  // 处理记录选择
  const handleRecordSelect = (recordId: string, checked: boolean) => {
    if (checked) {
      setSelectedRecords(prev => [...prev, recordId]);
    } else {
      setSelectedRecords(prev => prev.filter(id => id !== recordId));
    }
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRecords(filteredRecords.map(record => record.id));
    } else {
      setSelectedRecords([]);
    }
  };

  // 查看详情
  const handleViewDetail = (record: MarketAnalysisRecord) => {
    setSelectedRecord(record);
    setShowDetailDialog(true);
  };

  // 删除单条记录
  const handleDeleteSingle = async (recordId: string) => {
    setRecordToDelete(recordId);
    setShowDeleteDialog(true);
  };

  const confirmDeleteSingle = async () => {
    if (!recordToDelete) return;

    const success = await onDeleteRecord(recordToDelete);
    if (success) {
      toast.success('记录删除成功');
      setSelectedRecords(prev => prev.filter(id => id !== recordToDelete));
    } else {
      toast.error('删除失败');
    }

    setShowDeleteDialog(false);
    setRecordToDelete(null);
  };

  // 批量删除
  const handleDeleteMultiple = async () => {
    if (selectedRecords.length === 0) return;

    const deletedCount = await onDeleteMultipleRecords(selectedRecords);
    if (deletedCount > 0) {
      toast.success(`成功删除 ${deletedCount} 条记录`);
      setSelectedRecords([]);
    } else {
      toast.error('删除失败');
    }
  };

  // 清空所有记录
  const handleClearAll = async () => {
    setShowClearAllDialog(true);
  };

  const confirmClearAll = async () => {
    await onClearAllRecords();
    toast.success('所有记录已清空');
    setSelectedRecords([]);
    setShowClearAllDialog(false);
  };



  // 应用过滤器
  const handleApplyFilters = (newFilters: Partial<HistoryFilterOptions>) => {
    onApplyFilters({ ...currentFilters, ...newFilters });
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-16 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-700">
        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg mb-4">
          <RefreshCw className="w-6 h-6 animate-spin text-white" />
        </div>
        <span className="text-gray-700 dark:text-gray-300 font-medium">加载历史记录...</span>
        <span className="text-sm text-gray-500 dark:text-gray-400 mt-1">请稍候</span>
      </div>
    );
  }

  return (
    <div className={compact ? "space-y-3" : "space-y-6"}>
      {/* 工具栏 - 美化版 */}
      {!compact && (
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded-xl p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex-1 max-w-lg">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400 w-4 h-4" />
                <Input
                  placeholder="搜索产品、结果内容..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-10 bg-white dark:bg-gray-800 border-blue-200 dark:border-blue-700 focus:border-blue-500 dark:focus:border-blue-400 shadow-sm transition-all duration-200"
                />
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="h-10 bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-300 shadow-sm transition-all duration-200"
              >
                <Filter className="w-4 h-4 mr-2" />
                过滤器
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={onRefreshData}
                className="h-10 bg-white dark:bg-gray-800 hover:bg-green-50 dark:hover:bg-green-900/20 border-green-200 dark:border-green-700 text-green-700 dark:text-green-300 shadow-sm transition-all duration-200"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                刷新
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 过滤器面板 - 美化版 */}
      {!compact && showFilters && (
        <div className="bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-700 shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center">
              <Filter className="w-5 h-5 mr-2 text-blue-500" />
              筛选条件
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={onResetFilters}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              重置
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <CheckCircle className="w-4 h-4 mr-1 text-green-500" />
                状态筛选
              </label>
              <Select
                value={currentFilters.status || 'all'}
                onValueChange={(value) => handleApplyFilters({ status: value as any })}
              >
                <SelectTrigger className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="success">成功</SelectItem>
                  <SelectItem value="failed">失败</SelectItem>
                  <SelectItem value="cancelled">已取消</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                排序方式
              </label>
              <Select
                value={currentFilters.sortBy || 'timestamp'}
                onValueChange={(value) => handleApplyFilters({ sortBy: value as any })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="timestamp">按时间</SelectItem>
                  <SelectItem value="productType">按产品</SelectItem>
                  <SelectItem value="status">按状态</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                排序顺序
              </label>
              <Select
                value={currentFilters.sortOrder || 'desc'}
                onValueChange={(value) => handleApplyFilters({ sortOrder: value as any })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">降序</SelectItem>
                  <SelectItem value="asc">升序</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end mt-4">
            <Button variant="outline" size="sm" onClick={onResetFilters}>
              重置过滤器
            </Button>
          </div>
        </div>
      )}

      {/* 批量操作栏 - 紧凑模式下隐藏 */}
      {!compact && selectedRecords.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-700 shadow-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                <CheckCircle className="w-4 h-4 text-white" />
              </div>
              <span className="text-sm font-semibold text-blue-700 dark:text-blue-300">
                已选择 {selectedRecords.length} 条记录
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDeleteMultiple}
                className="bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20 border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 shadow-sm transition-all duration-200"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                批量删除
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedRecords([])}
                className="bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 shadow-sm transition-all duration-200"
              >
                取消选择
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 记录列表 */}
      {filteredRecords.length === 0 ? (
        <div className="text-center py-16 bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <Package className="w-8 h-8 text-white" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
            暂无历史记录
          </h3>
          <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto leading-relaxed">
            完成市场分析后，记录将显示在这里。开始您的第一次分析吧！
          </p>
          <div className="mt-6">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-sm text-blue-700 dark:text-blue-300">
              <Search className="w-4 h-4 mr-2" />
              尝试进行市场分析
            </div>
          </div>
        </div>
      ) : (
        <div className={compact ? "space-y-2" : "space-y-4"}>
          {/* 表头 - 美化版 */}
          {!compact && (
            <div className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded-xl p-4 border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="flex items-center space-x-4">
                <Checkbox
                  checked={selectedRecords.length === filteredRecords.length}
                  onCheckedChange={handleSelectAll}
                  className="border-blue-300 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                />
                <span className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center">
                  <Package className="w-4 h-4 mr-2 text-blue-500" />
                  全选 ({filteredRecords.length} 条记录)
                </span>
                {filteredRecords.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearAll}
                    className="ml-auto bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20 border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 shadow-sm transition-all duration-200"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    清空所有
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* 记录卡片 - 美化版 */}
          {filteredRecords.map((record) => (
            <div
              key={record.id}
              className={`group bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-750 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-blue-200 dark:hover:border-blue-700 hover:shadow-lg transition-all duration-200 ${
                compact ? 'p-3' : 'p-6'
              }`}
            >
              <div className={`flex items-start ${compact ? 'space-x-2' : 'space-x-4'}`}>
                {!compact && (
                  <Checkbox
                    checked={selectedRecords.includes(record.id)}
                    onCheckedChange={(checked) => handleRecordSelect(record.id, checked as boolean)}
                    className="mt-1 border-blue-300 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                  />
                )}

                <div className="flex-1 min-w-0">
                  <div className={`flex items-center justify-between ${compact ? 'mb-2' : 'mb-4'}`}>
                    <div className={`flex items-center ${compact ? 'space-x-2' : 'space-x-3'}`}>
                      <div className={`${compact ? 'w-6 h-6' : 'w-10 h-10'} bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 rounded-lg flex items-center justify-center shadow-md group-hover:shadow-lg transition-shadow duration-300`}>
                        <Package className={`${compact ? 'w-3 h-3' : 'w-5 h-5'} text-white`} />
                      </div>
                      <div>
                        <h3 className={`font-bold text-gray-900 dark:text-white group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors duration-300 ${compact ? 'text-sm' : 'text-lg'}`}>
                          {record.productLabel}
                        </h3>
                        {!compact && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors duration-300">
                            {record.strategyCount} 个策略 • {record.analysisType}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Badge className={`${getStatusColor(record.status)} shadow-sm ${compact ? 'text-xs px-2 py-1' : 'px-3 py-1'}`}>
                        {getStatusIcon(record.status)}
                        {!compact && <span className="ml-1 font-medium">{getStatusText(record.status)}</span>}
                      </Badge>
                    </div>
                  </div>

                  {!compact && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 rounded-lg px-3 py-2 group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20 transition-colors duration-300">
                        <Calendar className="w-4 h-4 mr-2 text-blue-500" />
                        <span className="font-medium">{formatTimeAgo(record.timestamp)}</span>
                      </div>

                      {record.duration && (
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 rounded-lg px-3 py-2 group-hover:bg-green-50 dark:group-hover:bg-green-900/20 transition-colors duration-300">
                          <Clock className="w-4 h-4 mr-2 text-green-500" />
                          <span className="font-medium">
                            {record.duration < 60 ? `${record.duration}秒` : `${Math.floor(record.duration / 60)}分${record.duration % 60}秒`}
                          </span>
                        </div>
                      )}

                      <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 rounded-lg px-3 py-2 group-hover:bg-purple-50 dark:group-hover:bg-purple-900/20 transition-colors duration-300">
                        <Search className="w-4 h-4 mr-2 text-purple-500" />
                        <span className="font-medium">{record.searchStrategies.length} 个搜索策略</span>
                      </div>
                    </div>
                  )}

                  {compact && (
                    <div className="flex items-center text-xs text-gray-600 dark:text-gray-400 mb-2">
                      <Calendar className="w-3 h-3 mr-1" />
                      {formatTimeAgo(record.timestamp)}
                      {record.duration && (
                        <>
                          <span className="mx-2">•</span>
                          <Clock className="w-3 h-3 mr-1" />
                          {record.duration < 60 ? `${record.duration}秒` : `${Math.floor(record.duration / 60)}分`}
                        </>
                      )}
                    </div>
                  )}

                  {!compact && record.status === 'success' && record.analysisResult && (
                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 mb-4 border border-green-200 dark:border-green-700">
                      <div className="flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-3 leading-relaxed">
                          {record.analysisResult.substring(0, 200)}...
                        </p>
                      </div>
                    </div>
                  )}

                  {!compact && record.status === 'failed' && record.errorMessage && (
                    <div className="bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg p-4 mb-4 border border-red-200 dark:border-red-700">
                      <div className="flex items-start space-x-2">
                        <XCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                        <p className="text-sm text-red-700 dark:text-red-300">
                          错误: {record.errorMessage}
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700">
                    {!compact && (
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1">
                        <Calendar className="w-3 h-3 mr-1" />
                        {new Date(record.timestamp).toLocaleString('zh-CN')}
                      </div>
                    )}

                    <div className={`flex items-center ${compact ? 'space-x-1 ml-auto' : 'space-x-3'}`}>
                      <Button
                        variant="outline"
                        size={compact ? "sm" : "sm"}
                        onClick={() => handleViewDetail(record)}
                        className={`group bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-300 shadow-sm transition-all duration-200 ${compact ? "h-7 px-2" : ""}`}
                      >
                        <Eye className={`${compact ? 'w-3 h-3' : 'w-4 h-4'} ${compact ? '' : 'mr-2'} group-hover:scale-110 transition-transform duration-200`} />
                        {!compact && "查看详情"}
                      </Button>

                      {!compact && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteSingle(record.id)}
                          className="group bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20 border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 shadow-sm transition-all duration-200"
                        >
                          <Trash2 className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" />
                          删除
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 详情对话框 */}
      <AnalysisHistoryDetailDialog
        open={showDetailDialog}
        onOpenChange={setShowDetailDialog}
        record={selectedRecord}
      />

      {/* 删除确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={(open) => open ? null : (() => setShowDeleteDialog(false))()} modal={true}>
        <DialogContent
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除这条分析记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDeleteSingle}>
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 清空所有确认对话框 */}
      <Dialog open={showClearAllDialog} onOpenChange={(open) => open ? null : (() => setShowClearAllDialog(false))()} modal={true}>
        <DialogContent
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle>确认清空所有记录</DialogTitle>
            <DialogDescription>
              您确定要清空所有历史记录吗？此操作无法撤销，将删除所有保存的分析记录。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowClearAllDialog(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmClearAll}>
              清空所有
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default AnalysisHistoryList;
