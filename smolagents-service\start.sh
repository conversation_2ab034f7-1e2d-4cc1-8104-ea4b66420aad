#!/bin/bash

# SmoLAgents 微服务启动脚本

set -e

echo "🚀 启动 SmoLAgents 微服务..."

# 检查环境变量
if [ -z "$SERVICE_PORT" ]; then
    export SERVICE_PORT=8002
fi

if [ -z "$SERVICE_HOST" ]; then
    export SERVICE_HOST=0.0.0.0
fi

if [ -z "$LOG_LEVEL" ]; then
    export LOG_LEVEL=INFO
fi

# 创建日志目录
mkdir -p /app/logs

# 等待依赖服务
echo "⏳ 等待依赖服务启动..."

# 等待 Redis
echo "🔍 检查 Redis 连接..."
timeout=30
while ! nc -z $REDIS_HOST $REDIS_PORT; do
    echo "等待 Redis ($REDIS_HOST:$REDIS_PORT) 启动..."
    sleep 2
    timeout=$((timeout - 2))
    if [ $timeout -le 0 ]; then
        echo "❌ Redis 连接超时"
        exit 1
    fi
done
echo "✅ Redis 连接成功"

# 等待 Ollama
echo "🔍 检查 Ollama 连接..."
timeout=30
while ! curl -f http://host.docker.internal:11434/api/tags >/dev/null 2>&1; do
    echo "等待 Ollama (http://host.docker.internal:11434) 启动..."
    sleep 2
    timeout=$((timeout - 2))
    if [ $timeout -le 0 ]; then
        echo "⚠️ Ollama 连接超时，服务将继续启动但功能可能受限"
        break
    fi
done

if curl -f http://host.docker.internal:11434/api/tags >/dev/null 2>&1; then
    echo "✅ Ollama 连接成功"
else
    echo "⚠️ Ollama 连接失败，请确保 Ollama 服务正在运行"
fi

# 启动服务
echo "🎯 启动 SmoLAgents 微服务..."
echo "📍 服务地址: http://$SERVICE_HOST:$SERVICE_PORT"
echo "📚 API 文档: http://$SERVICE_HOST:$SERVICE_PORT/docs"

if [ "$DEV_MODE" = "true" ]; then
    echo "🔧 开发模式启动 (热重载已启用)"
    exec uvicorn app.main:app \
        --host $SERVICE_HOST \
        --port $SERVICE_PORT \
        --reload \
        --log-level $LOG_LEVEL
else
    echo "🏭 生产模式启动"
    exec uvicorn app.main:app \
        --host $SERVICE_HOST \
        --port $SERVICE_PORT \
        --workers $MAX_WORKERS \
        --log-level $LOG_LEVEL
fi
