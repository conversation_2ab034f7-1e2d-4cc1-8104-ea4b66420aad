/**
 * AI指导服务 - 专门处理销售培训的AI指导分析
 * 修复角色混淆问题，确保只分析销售员的回复
 */

import type { ChatMessage, TrainingCustomer } from '@/types/salesTraining'

// 回复分析类型
export interface ReplyAnalysis {
    messageIndex: number
    userMessage: string
    strengths: string[]
    weaknesses: string[]
    score: number
    suggestedReply: string
    reasoning: string
}

// 整体分析类型
export interface OverallAnalysis {
    conversationFlow: string
    communicationStyle: string
    needsIdentification: string
    relationshipBuilding: string
    culturalAdaptation: string
    improvementAreas: string[]
    nextSteps: string[]
}

// AI指导分析结果
export interface AIGuidanceResult {
    overallAnalysis: OverallAnalysis
    replyAnalyses: ReplyAnalysis[]
}

/**
 * 验证消息角色，确保正确区分销售员和AI客户
 */
function validateMessageRoles(messages: ChatMessage[]): {
    userMessages: ChatMessage[]
    conversationPairs: Array<{ user: Cha<PERSON><PERSON><PERSON><PERSON>, assistant: Chat<PERSON><PERSON><PERSON>, userIndex: number }>
} {
    const userMessages = messages.filter(msg => msg.role === 'user')
    const conversationPairs: Array<{ user: Chat<PERSON><PERSON><PERSON>, assistant: ChatMess<PERSON>, userIndex: number }> = []

    let userIndex = 0
    for (let i = 0; i < messages.length - 1; i++) {
        const current = messages[i]
        const next = messages[i + 1]

        if (current.role === 'user' && next.role === 'customer') {
            conversationPairs.push({
                user: current,
                assistant: next,
                userIndex: userIndex++
            })
        }
    }

    return { userMessages, conversationPairs }
}

/**
 * 构建强化的AI指导分析提示词
 */
function buildEnhancedGuidancePrompt(
    messages: ChatMessage[],
    customer: TrainingCustomer
): string {
    const { userMessages, conversationPairs } = validateMessageRoles(messages)

    // 构建完整对话历史
    const conversationHistory = messages
        .filter(msg => msg.role !== 'system')
        .map((msg, index) => {
            const role = msg.role === 'user' ? '销售员' : 'AI客户'
            return `${index + 1}. ${role}：${msg.content}`
        })
        .join('\n')

    // 构建销售员回复列表（用于精确分析）
    const salesRepReplies = userMessages.map((msg, index) => {
        return `销售员回复${index + 1}：${msg.content}`
    }).join('\n')

    // 构建对话对列表（显示上下文关系）
    const conversationPairsText = conversationPairs.map((pair, index) => {
        return `对话轮次${index + 1}：
AI客户：${pair.assistant.content}
销售员回复：${pair.user.content}`
    }).join('\n\n')

    return `作为资深销售培训专家，请对以下销售培训对话进行专业分析。

**重要说明：**
- 这是销售培训场景，"销售员"是真人学员，"AI客户"是训练用的模拟客户
- 请ONLY分析"销售员"的回复表现，完全忽略"AI客户"的内容
- 不要把AI客户的问题当作销售员的回复来分析
- messageIndex必须对应销售员回复的序号（从0开始）

**客户背景：**
- 姓名：${customer.name}
- 国家：${customer.country.name}
- 产品：${customer.product.name}
- 背景：${customer.background?.experience || '专业客户'}

**完整对话记录：**
${conversationHistory}

**销售员回复汇总（共${userMessages.length}轮，仅分析这些）：**
${salesRepReplies}

**对话上下文关系：**
${conversationPairsText}

**分析要求：**

1. **整体分析**：
   - 对话流程：评估销售员的对话推进逻辑
   - 沟通风格：分析销售员的表达方式
   - 需求识别：评估挖掘客户需求的能力
   - 关系建立：评价建立信任的表现
   - 跨文化适应：针对${customer.country.name}客户的适应性
   - 改进建议：3-5个具体改进方向
   - 下步行动：3-4个后续行动建议

2. **逐条回复分析**：
   - 仅分析销售员的${userMessages.length}轮回复
   - messageIndex从0开始，对应销售员回复序号
   - 评估每轮回复的优缺点和得分
   - 提供改进建议和示范回复

**评分标准（严格执行）：**

**内容质量评估：**
- 简短无意义回复（如"1"、"2"、"好"、"哈哈"等）：0-20分
- 过于简单的回复（少于10个字且无实质内容）：20-40分
- 基本回复但缺乏专业性：40-60分
- 有一定专业性但不够深入：60-75分
- 专业且有针对性的回复：75-85分
- 卓越的专业回复，展现深度理解：85-100分

**具体评分维度：**
- 内容丰富度（30%）：回复是否有实质内容，不是简单的数字或单词
- 专业性（25%）：是否展现产品知识和销售技巧
- 针对性（20%）：是否针对客户具体需求和问题
- 沟通技巧（15%）：语言表达和沟通方式
- 推进效果（10%）：是否有效推进销售进程

**严格要求：**
- 单个字符、数字、简单词汇（如"1"、"2"、"好"）必须给0-15分
- 少于5个字的回复最高不超过25分
- 没有实质销售内容的回复最高不超过40分
- 必须基于回复的实际质量严格评分，不能给虚高分数

**评分示例：**
- "1" → 5分（极差，无任何销售价值）
- "2" → 8分（极差，无任何销售价值）
- "好的" → 15分（过于简单，无专业性）
- "我理解您的需求" → 35分（基本回应但缺乏具体内容）
- "根据您提到的协作问题，我们的平台..." → 75分（专业且有针对性）

**关键要求：**
1. 严格只分析销售员回复，忽略AI客户内容
2. messageIndex必须准确对应销售员回复序号（从0开始）
3. **userMessage必须是销售员的原始回复内容，完全不要修改、总结或改写**
4. 不要分析AI客户的问题或反馈
5. 基于对话上下文评估销售员的应对能力
6. **确保userMessage字段包含销售员的完整原始回复文本**
7. **严格按照评分标准执行，简短无意义回复必须给低分（0-20分）**
8. **不要因为是训练场景就给虚高分数，必须客观评价回复质量**

**输出格式（仅返回JSON）：**

{
  "overallAnalysis": {
    "conversationFlow": "销售员对话推进分析",
    "communicationStyle": "销售员沟通风格评价",
    "needsIdentification": "销售员需求识别能力",
    "relationshipBuilding": "销售员关系建立表现",
    "culturalAdaptation": "销售员跨文化适应性",
    "improvementAreas": ["改进建议1", "改进建议2", "改进建议3"],
    "nextSteps": ["行动建议1", "行动建议2", "行动建议3"]
  },
  "replyAnalyses": [
    {
      "messageIndex": 0,
      "userMessage": "必须是销售员的原始回复内容，不要总结或改写",
      "strengths": ["优点1", "优点2"],
      "weaknesses": ["缺点1", "缺点2"],
      "score": 75,
      "suggestedReply": "改进后的回复示例",
      "reasoning": "详细的评分理由"
    },
    {
      "messageIndex": 1,
      "userMessage": "必须是销售员的原始回复内容，不要总结或改写",
      "strengths": ["优点1", "优点2"],
      "weaknesses": ["缺点1", "缺点2"],
      "score": 82,
      "suggestedReply": "改进后的回复示例",
      "reasoning": "详细的评分理由"
    }
  ]
}`
}

/**
 * 调用SmoLAgents API进行AI指导分析
 */
export async function performAIGuidanceAnalysis(
    messages: ChatMessage[],
    customer: TrainingCustomer,
    signal?: AbortSignal
): Promise<AIGuidanceResult> {
    console.log('开始AI指导分析', {
        messagesCount: messages.length,
        userMessagesCount: messages.filter(m => m.role === 'user').length,
        customerName: customer.name
    })

    // 验证输入
    const userMessages = messages.filter(m => m.role === 'user')
    if (userMessages.length === 0) {
        throw new Error('需要至少一轮销售员回复才能进行分析')
    }

    if (messages.length < 2) {
        throw new Error('需要至少2条消息（一轮完整对话）才能进行分析')
    }

    // 构建分析提示词
    const analysisPrompt = buildEnhancedGuidancePrompt(messages, customer)
    console.log('分析提示词构建完成，长度:', analysisPrompt.length)

    try {
        // 调用SmoLAgents API
        console.log('调用SmoLAgents服务...')
        const { smolagentsService } = await import('@/services/smolagentsService')

        const apiResponse = await smolagentsService.performAIGuidanceAnalysis({
            query: analysisPrompt,
            messages,
            customer,
            maxSteps: 3,
            temperature: 0.3,
            signal
        })

        if (!apiResponse.success) {
            throw new Error(apiResponse.error || 'AI指导分析服务调用失败')
        }

        console.log('SmoLAgents API响应成功')

        const completionText = apiResponse.result || apiResponse.completion || ''

        if (!completionText) {
            throw new Error('AI服务返回空响应')
        }

        // 解析AI响应
        const result = parseAIGuidanceResponse(completionText, userMessages)
        console.log('AI指导分析解析完成', {
            hasOverallAnalysis: !!result.overallAnalysis,
            replyAnalysesCount: result.replyAnalyses.length
        })

        return result

    } catch (error: any) {
        console.error('AI指导分析失败:', error)

        if (error.name === 'AbortError') {
            throw error
        }

        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            throw new Error('网络连接失败，请检查SmoLAgents服务状态')
        }

        if (error.message.includes('timeout')) {
            throw new Error('AI分析超时，请稍后重试')
        }

        throw new Error(`AI指导分析失败: ${error.message}`)
    }
}

/**
 * 解析AI响应，确保只包含销售员回复的分析
 */
function parseAIGuidanceResponse(aiResponse: string, userMessages: ChatMessage[]): AIGuidanceResult {
    try {
        console.log('开始解析AI指导响应...')
        let jsonString = aiResponse

        // 清理markdown标记
        if (jsonString.includes('```json')) {
            const jsonMatch = jsonString.match(/```json\s*([\s\S]*?)\s*```/)
            if (jsonMatch) {
                jsonString = jsonMatch[1]
            }
        } else if (jsonString.includes('```')) {
            const jsonMatch = jsonString.match(/```\s*([\s\S]*?)\s*```/)
            if (jsonMatch) {
                jsonString = jsonMatch[1]
            }
        }

        // 提取JSON部分
        const jsonMatch = jsonString.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
            jsonString = jsonMatch[0]
        }

        const result = JSON.parse(jsonString)

        console.log('解析后的AI响应结构:', {
            hasOverallAnalysis: !!result.overallAnalysis,
            hasReplyAnalyses: !!result.replyAnalyses,
            replyAnalysesLength: result.replyAnalyses?.length || 0,
            userMessagesLength: userMessages.length
        })

        // 打印原始的replyAnalyses用于调试
        if (result.replyAnalyses) {
            console.log('原始replyAnalyses:', result.replyAnalyses.map((analysis: any, index: number) => ({
                index,
                messageIndex: analysis.messageIndex,
                userMessage: analysis.userMessage?.substring(0, 100) + '...',
                hasScore: typeof analysis.score === 'number',
                score: analysis.score
            })))
        }

        // 验证结构
        if (!result.overallAnalysis || !result.replyAnalyses) {
            throw new Error('AI分析结果结构不完整')
        }

        // 验证回复分析（放宽验证条件）
        const validatedReplyAnalyses = result.replyAnalyses.filter((analysis: any, index: number) => {
            console.log(`验证回复分析${index}:`, {
                messageIndex: analysis.messageIndex,
                userMessage: analysis.userMessage?.substring(0, 100) + '...',
                hasScore: typeof analysis.score === 'number',
                hasStrengths: Array.isArray(analysis.strengths),
                hasWeaknesses: Array.isArray(analysis.weaknesses)
            })

            // 验证基本字段
            if (typeof analysis.messageIndex !== 'number' ||
                !analysis.userMessage ||
                typeof analysis.score !== 'number' ||
                !Array.isArray(analysis.strengths) ||
                !Array.isArray(analysis.weaknesses)) {
                console.warn(`回复分析${index}字段不完整，已过滤`, analysis)
                return false
            }

            // 验证索引范围
            if (analysis.messageIndex < 0 || analysis.messageIndex >= userMessages.length) {
                console.warn(`回复分析${index}索引超出范围，已过滤`, {
                    messageIndex: analysis.messageIndex,
                    userMessagesLength: userMessages.length
                })
                return false
            }

            // 获取对应的用户消息
            const expectedUserMessage = userMessages[analysis.messageIndex]?.content
            if (!expectedUserMessage) {
                console.warn(`回复分析${index}找不到对应的用户消息，已过滤`)
                return false
            }

            console.log(`回复分析${index}验证通过:`, {
                expectedUserMessage: expectedUserMessage.substring(0, 100) + '...',
                analysisUserMessage: analysis.userMessage.substring(0, 100) + '...'
            })

            // 放宽内容匹配验证 - 只要不是完全不相关即可
            // AI可能会总结或重新表述用户的回复，所以不需要严格匹配
            return true
        })

        console.log(`AI响应解析完成，有效回复分析: ${validatedReplyAnalyses.length}/${result.replyAnalyses.length}`)

        return {
            overallAnalysis: result.overallAnalysis,
            replyAnalyses: validatedReplyAnalyses
        }

    } catch (parseError) {
        console.error('解析AI响应失败:', parseError)
        throw new Error(`AI响应解析失败: ${parseError instanceof Error ? parseError.message : '未知错误'}`)
    }
}