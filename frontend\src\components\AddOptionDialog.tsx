import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'

interface AddOptionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onAddOption: (label: string) => Promise<{ success: boolean; error?: string; value?: string }>
  onOptionAdded?: (optionValue: string) => void
  title: string
  description: string
  placeholder: string
  optionType: string // 用于显示错误信息和成功信息
}

export const AddOptionDialog: React.FC<AddOptionDialogProps> = ({
  open,
  onOpenChange,
  onAddOption,
  onOptionAdded,
  title,
  description,
  placeholder,
  optionType
}) => {
  const [formData, setFormData] = useState({
    label: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  // 重置表单
  const resetForm = () => {
    setFormData({ label: '' })
    setError('')
    setIsSubmitting(false)
  }

  // 处理对话框关闭
  const handleOpenChange = (newOpen: boolean) => {
    if (!isSubmitting) {
      onOpenChange(newOpen)
      if (!newOpen) {
        resetForm()
      }
    }
  }

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 表单验证
    if (!formData.label.trim()) {
      setError(`请输入${optionType}名称`)
      return
    }

    setIsSubmitting(true)
    setError('')

    try {
      const result = await onAddOption(formData.label.trim())

      if (result.success) {
        // 成功后关闭对话框并重置表单
        toast.success(`${optionType}选项 "${formData.label}" 已成功添加`)

        // 如果有回调函数且返回了选项value，则自动选择该选项
        if (onOptionAdded && result.value) {
          onOptionAdded(result.value)
        }

        onOpenChange(false)
        resetForm()
      } else {
        setError(result.error || `添加${optionType}选项失败`)
        toast.error(result.error || `添加${optionType}选项失败`)
      }
    } catch (err) {
      setError('添加过程中发生错误')
      console.error(`添加${optionType}选项失败:`, err)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange} modal={true}>
      <DialogContent
        className="sm:max-w-[425px]"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 选项名称输入 */}
          <div className="space-y-2">
            <Label htmlFor="option-name">{optionType}名称</Label>
            <Input
              id="option-name"
              placeholder={placeholder}
              value={formData.label}
              onChange={(e) => setFormData(prev => ({ ...prev, label: e.target.value }))}
              disabled={isSubmitting}
              className="w-full"
            />
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={isSubmitting}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.label.trim()}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  添加中...
                </>
              ) : (
                '添加'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default AddOptionDialog
