"""
密码重置CRUD操作
实现密码重置令牌的增删改查功能
"""
import secrets
from datetime import datetime, timedelta, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from typing import Optional
from app.models.password_reset import PasswordResetToken
from app.models.user import User


class PasswordResetCRUD:
    """密码重置CRUD操作类"""
    
    async def create_reset_token(
        self, db: AsyncSession, *, user_id: int, expires_hours: int = 24
    ) -> PasswordResetToken:
        """创建密码重置令牌"""
        import secrets
        from datetime import datetime, timedelta, timezone

        # 生成安全的随机令牌
        token = secrets.token_urlsafe(32)

        # 设置过期时间
        expires_at = datetime.now(timezone.utc) + timedelta(hours=expires_hours)

        # 创建令牌对象
        db_obj = PasswordResetToken(
            user_id=user_id,
            token=token,
            expires_at=expires_at
        )

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get_by_token(self, db: AsyncSession, token: str) -> Optional[PasswordResetToken]:
        """根据令牌获取重置记录"""
        result = await db.execute(
            select(PasswordResetToken).where(PasswordResetToken.token == token)
        )
        return result.scalar_one_or_none()
    
    async def get_valid_token(self, db: AsyncSession, token: str) -> Optional[PasswordResetToken]:
        """获取有效的重置令牌（未使用且未过期）"""
        now = datetime.now(timezone.utc)
        result = await db.execute(
            select(PasswordResetToken).where(
                and_(
                    PasswordResetToken.token == token,
                    PasswordResetToken.used == False,
                    PasswordResetToken.expires_at > now
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def mark_as_used(self, db: AsyncSession, *, token_obj: PasswordResetToken) -> PasswordResetToken:
        """标记令牌为已使用"""
        token_obj.used = True
        db.add(token_obj)
        await db.commit()
        await db.refresh(token_obj)
        return token_obj
    
    async def get_user_active_tokens(self, db: AsyncSession, user_id: int) -> list[PasswordResetToken]:
        """获取用户的所有有效令牌"""
        now = datetime.now(timezone.utc)
        result = await db.execute(
            select(PasswordResetToken).where(
                and_(
                    PasswordResetToken.user_id == user_id,
                    PasswordResetToken.used == False,
                    PasswordResetToken.expires_at > now
                )
            )
        )
        return result.scalars().all()
    
    async def invalidate_user_tokens(self, db: AsyncSession, user_id: int) -> int:
        """使用户的所有未使用令牌失效"""
        from sqlalchemy import update as sql_update

        now = datetime.now(timezone.utc)

        # 使用SQL UPDATE语句而不是ORM更新
        result = await db.execute(
            sql_update(PasswordResetToken)
            .where(
                and_(
                    PasswordResetToken.user_id == user_id,
                    PasswordResetToken.used == False,
                    PasswordResetToken.expires_at > now
                )
            )
            .values(used=True)
        )

        count = result.rowcount
        return count
    
    async def cleanup_expired_tokens(self, db: AsyncSession) -> int:
        """清理过期的令牌"""
        from sqlalchemy import delete as sql_delete

        now = datetime.now(timezone.utc)

        # 使用SQL DELETE语句而不是ORM删除
        result = await db.execute(
            sql_delete(PasswordResetToken).where(
                PasswordResetToken.expires_at <= now
            )
        )

        count = result.rowcount

        if count > 0:
            await db.commit()

        return count
    
    async def get_token_with_user(self, db: AsyncSession, token: str) -> Optional[tuple[PasswordResetToken, User]]:
        """获取令牌及其关联的用户信息"""
        result = await db.execute(
            select(PasswordResetToken, User).join(User).where(
                PasswordResetToken.token == token
            )
        )
        row = result.first()
        return (row[0], row[1]) if row else None


# 创建全局实例
password_reset_crud = PasswordResetCRUD()
