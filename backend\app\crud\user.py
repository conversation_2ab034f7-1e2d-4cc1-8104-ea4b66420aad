"""
用户管理CRUD操作
实现用户的增删改查功能
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_
from typing import Optional
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.core.auth import get_password_hash, verify_password


class UserCRUD:
    """用户CRUD操作类"""
    
    async def create(self, db: AsyncSession, *, obj_in: UserCreate) -> User:
        """创建新用户"""
        # 检查用户名和邮箱是否已存在
        existing_user = await self.get_by_username_or_email(
            db, username=obj_in.username, email=obj_in.email
        )
        if existing_user:
            if existing_user.username == obj_in.username:
                raise ValueError("用户名已存在")
            if existing_user.email == obj_in.email:
                raise ValueError("邮箱已存在")
        
        # 创建用户对象
        db_obj = User(
            username=obj_in.username,
            email=obj_in.email,
            hashed_password=get_password_hash(obj_in.password),
            full_name=obj_in.full_name,
            phone=obj_in.phone,
            is_active=obj_in.is_active
        )
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get(self, db: AsyncSession, id: int) -> Optional[User]:
        """根据ID获取用户"""
        result = await db.execute(select(User).where(User.id == id))
        return result.scalar_one_or_none()
    
    async def get_by_username(self, db: AsyncSession, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        result = await db.execute(select(User).where(User.username == username))
        return result.scalar_one_or_none()
    
    async def get_by_email(self, db: AsyncSession, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        result = await db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none()
    
    async def get_by_username_or_email(
        self, db: AsyncSession, username: str, email: str
    ) -> Optional[User]:
        """根据用户名或邮箱获取用户"""
        result = await db.execute(
            select(User).where(or_(User.username == username, User.email == email))
        )
        return result.scalar_one_or_none()
    
    async def authenticate(
        self, db: AsyncSession, username: str, password: str
    ) -> Optional[User]:
        """用户认证"""
        # 支持用户名或邮箱登录
        user = await db.execute(
            select(User).where(or_(User.username == username, User.email == username))
        )
        user = user.scalar_one_or_none()
        
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user
    
    async def update(self, db: AsyncSession, *, db_obj: User, obj_in: UserUpdate) -> User:
        """更新用户信息"""
        update_data = obj_in.model_dump(exclude_unset=True)
        
        # 检查用户名和邮箱唯一性
        if "username" in update_data:
            existing_user = await self.get_by_username(db, username=update_data["username"])
            if existing_user and existing_user.id != db_obj.id:
                raise ValueError("用户名已存在")
        
        if "email" in update_data:
            existing_user = await self.get_by_email(db, email=update_data["email"])
            if existing_user and existing_user.id != db_obj.id:
                raise ValueError("邮箱已存在")
        
        # 更新字段
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def update_password(
        self, db: AsyncSession, *, db_obj: User, new_password: str
    ) -> User:
        """更新用户密码"""
        db_obj.hashed_password = get_password_hash(new_password)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def deactivate(self, db: AsyncSession, *, id: int) -> Optional[User]:
        """停用用户（软删除）"""
        db_obj = await self.get(db, id=id)
        if db_obj:
            db_obj.is_active = False
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def remove(self, db: AsyncSession, *, id: int) -> bool:
        """删除用户（硬删除）"""
        from sqlalchemy import delete
        from app.models.user_role import UserRole

        # 首先删除用户的角色关联
        await db.execute(delete(UserRole).where(UserRole.user_id == id))

        # 然后删除用户
        result = await db.execute(delete(User).where(User.id == id))
        await db.commit()

        return result.rowcount > 0

    async def check_user_dependencies(self, db: AsyncSession, *, id: int) -> dict:
        """检查用户的依赖关系"""
        from sqlalchemy import select, func
        from app.models.user_role import UserRole

        # 检查用户角色关联数量
        role_count_result = await db.execute(
            select(func.count(UserRole.id)).where(UserRole.user_id == id)
        )
        role_count = role_count_result.scalar() or 0

        # 检查用户是否为超级用户
        user = await self.get(db, id=id)
        is_superuser = user.is_superuser if user else False

        return {
            "role_count": role_count,
            "is_superuser": is_superuser,
            "can_delete": not is_superuser  # 超级用户不能删除
        }


# 创建全局实例
user_crud = UserCRUD()
