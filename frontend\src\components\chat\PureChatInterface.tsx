/**
 * 纯AI聊天界面组件
 * 直接调用Ollama API，支持多轮对话和会话管理
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  Send,
  Plus,
  MoreVertical,
  Copy,
  RotateCcw,
  Trash2,
  Download,
  Upload,
  Settings,
  Bot,
  User,
  AlertCircle,
  Loader2,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useOllamaChat } from '@/hooks/useOllamaChat';
import { ChatMessage } from '@/types/ollama';

interface PureChatInterfaceProps {
  className?: string;
}

export function PureChatInterface({ className }: PureChatInterfaceProps) {
  const [inputValue, setInputValue] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [ollamaHealth, setOllamaHealth] = useState<boolean | null>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    sessions,
    currentSession,
    availableModels,
    selectedModel,
    isLoading,
    isStreaming,
    error,
    createNewSession,
    loadSession,
    deleteSession,
    sendMessage,
    regenerateLastMessage,
    deleteMessage,
    setSelectedModel,
    refreshModels,
    clearCurrentSession,
    exportSession,
    importSession,
  } = useOllamaChat();

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentSession?.messages]);

  // 检查Ollama服务健康状态
  useEffect(() => {
    const checkOllamaHealth = async () => {
      try {
        const response = await fetch('http://localhost:11434/api/tags', {
          method: 'GET',
          mode: 'cors',
          signal: AbortSignal.timeout(5000),
        });
        setOllamaHealth(response.ok);
      } catch (error) {
        setOllamaHealth(false);
      }
    };

    checkOllamaHealth();
    // 每30秒检查一次
    const interval = setInterval(checkOllamaHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  // 处理发送消息
  const handleSendMessage = async () => {
    if (!inputValue.trim() || isStreaming) return;

    const message = inputValue.trim();
    setInputValue('');

    try {
      await sendMessage(message);
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 复制消息内容
  const handleCopyMessage = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  // 导出会话
  const handleExportSession = () => {
    if (!currentSession) return;

    try {
      const data = exportSession(currentSession.id);
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `chat-${currentSession.title}-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('导出失败:', error);
    }
  };

  // 导入会话
  const handleImportSession = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = e.target?.result as string;
        importSession(data);
      } catch (error) {
        console.error('导入失败:', error);
      }
    };
    reader.readAsText(file);
  };

  // 渲染消息
  const renderMessage = (message: ChatMessage) => {
    const isUser = message.role === 'user';
    const isStreaming = message.isStreaming;
    const hasError = !!message.error;

    return (
      <div key={message.id} className={`flex gap-3 p-4 ${isUser ? 'bg-muted/30' : ''}`}>
        <div className="flex-shrink-0">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
            isUser ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
          }`}>
            {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
          </div>
        </div>

        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">
              {isUser ? '您' : '助手'}
            </span>
            <span className="text-xs text-muted-foreground">
              {message.timestamp.toLocaleTimeString()}
            </span>
            {isStreaming && (
              <Loader2 className="w-3 h-3 animate-spin text-muted-foreground" />
            )}
          </div>

          <div className="prose prose-sm max-w-none">
            {hasError ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{message.error}</AlertDescription>
              </Alert>
            ) : (
              <div className="whitespace-pre-wrap break-words">
                {message.content}
                {isStreaming && <span className="animate-pulse">▋</span>}
              </div>
            )}
          </div>

          {!isUser && !isStreaming && !hasError && (
            <div className="flex items-center gap-1 pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleCopyMessage(message.content)}
                className="h-6 px-2 text-xs"
              >
                <Copy className="w-3 h-3 mr-1" />
                复制
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={regenerateLastMessage}
                className="h-6 px-2 text-xs"
                disabled={isStreaming}
              >
                <RotateCcw className="w-3 h-3 mr-1" />
                重新生成
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => deleteMessage(message.id)}
                className="h-6 px-2 text-xs text-destructive hover:text-destructive"
              >
                <Trash2 className="w-3 h-3 mr-1" />
                删除
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={`flex h-full ${className}`}>
      {/* 侧边栏 - 会话列表 */}
      <div className="w-80 border-r bg-muted/30 flex flex-col">
        {/* 头部 */}
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-3">
            <h2 className="font-semibold">对话历史</h2>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={createNewSession}
                className="h-8 w-8 p-0"
              >
                <Plus className="w-4 h-4" />
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleExportSession} disabled={!currentSession}>
                    <Download className="w-4 h-4 mr-2" />
                    导出当前对话
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <label className="flex items-center cursor-pointer">
                      <Upload className="w-4 h-4 mr-2" />
                      导入对话
                      <input
                        type="file"
                        accept=".json"
                        onChange={handleImportSession}
                        className="hidden"
                      />
                    </label>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setShowSettings(true)}>
                    <Settings className="w-4 h-4 mr-2" />
                    设置
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* 模型选择 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">模型选择</span>
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${
                  ollamaHealth === true ? 'bg-green-500' :
                  ollamaHealth === false ? 'bg-red-500' : 'bg-yellow-500'
                }`} />
                <span className="text-xs text-muted-foreground">
                  {ollamaHealth === true ? '已连接' :
                   ollamaHealth === false ? '未连接' : '检查中...'}
                </span>
              </div>
            </div>
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="选择模型" />
              </SelectTrigger>
              <SelectContent>
                {availableModels.length > 0 ? (
                  availableModels.map((model) => (
                    <SelectItem key={model.name} value={model.name}>
                      <div className="flex items-center justify-between w-full">
                        <span>{model.name}</span>
                        <Badge variant="secondary" className="ml-2 text-xs">
                          {(model.size / 1024 / 1024 / 1024).toFixed(1)}GB
                        </Badge>
                      </div>
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-models" disabled>
                    {ollamaHealth === false ? '无法连接到Ollama' : '正在加载模型...'}
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 会话列表 */}
        <ScrollArea className="flex-1">
          <div className="p-2 space-y-1">
            {sessions.map((session) => (
              <div
                key={session.id}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  currentSession?.id === session.id
                    ? 'bg-primary/10 border border-primary/20'
                    : 'hover:bg-muted/50'
                }`}
                onClick={() => loadSession(session.id)}
              >
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-sm truncate flex-1">
                    {session.title}
                  </h3>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreVertical className="w-3 h-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => deleteSession(session.id)}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-xs text-muted-foreground">
                    {session.messages.length} 条消息
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {session.updatedAt.toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}

            {sessions.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Bot className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">暂无对话历史</p>
                <p className="text-xs mt-1">点击"+"开始新对话</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </div>

      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col">
        {/* 错误提示 */}
        {error && (
          <Alert variant="destructive" className="m-4 mb-0">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Ollama服务状态提示 */}
        {ollamaHealth === false && (
          <Alert variant="destructive" className="m-4 mb-0">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              无法连接到Ollama服务 (http://localhost:11434)。请确保：
              <br />• Ollama已安装并正在运行
              <br />• 运行命令: ollama serve
              <br />• 端口11434未被占用
            </AlertDescription>
          </Alert>
        )}

        {/* 消息区域 */}
        <ScrollArea className="flex-1">
          {currentSession ? (
            <div className="space-y-0">
              {currentSession.messages.map(renderMessage)}
              <div ref={messagesEndRef} />
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center space-y-4">
                <Bot className="w-16 h-16 mx-auto text-muted-foreground/50" />
                <div>
                  <h3 className="text-lg font-medium">欢迎使用智能对话</h3>
                  <p className="text-muted-foreground mt-1">
                    选择一个对话或创建新对话开始聊天
                  </p>
                </div>
                <Button onClick={createNewSession}>
                  <Plus className="w-4 h-4 mr-2" />
                  开始新对话
                </Button>
              </div>
            </div>
          )}
        </ScrollArea>

        {/* 输入区域 */}
        {currentSession && (
          <div className="border-t p-4">
            <div className="flex gap-2">
              <div className="flex-1 relative">
                <Textarea
                  ref={inputRef}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="输入消息... (Shift+Enter 换行)"
                  className="min-h-[60px] max-h-[200px] resize-none pr-12"
                  disabled={isStreaming}
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isStreaming}
                  className="absolute right-2 bottom-2 h-8 w-8 p-0"
                >
                  {isStreaming ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
              <span>当前模型: {selectedModel}</span>
              <span>
                {currentSession.messages.length} 条消息
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
