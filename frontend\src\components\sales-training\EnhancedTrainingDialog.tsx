/**
 * 增强版训练对话窗口
 * 集成所有新功能的主要对话训练界面
 */

import { useState, useRef, useEffect } from 'react'
import {
  X,
  User,
  Settings,
  Maximize2,
  Minimize2,
  Clock,
  Star,
  BarChart3,
  Trophy,
  Lightbulb,
  Target,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { EnhancedTrainingScenarioSelector } from './EnhancedTrainingScenarioSelector'
import { SmartChatInterface } from './SmartChatInterface'
import { RealTimeEvaluationPanel } from './RealTimeEvaluationPanel'
import { LearningTipsPanel } from './LearningTipsPanel'

import type { TrainingCustomer, ChatMessage, ChatSession, TrainingScenario } from '@/types/salesTraining'

interface EnhancedTrainingDialogProps {
  customer: TrainingCustomer
  open: boolean
  onClose: () => void
}

export function EnhancedTrainingDialog({ customer, open, onClose }: EnhancedTrainingDialogProps) {
  const [currentView, setCurrentView] = useState<'scenario' | 'training'>('scenario')
  const [selectedScenario, setSelectedScenario] = useState<TrainingScenario | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showRightPanel, setShowRightPanel] = useState(true)
  const [rightPanelTab, setRightPanelTab] = useState<'evaluation' | 'learning'>('evaluation')
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null)
  const [sessionPaused, setSessionPaused] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [sessionStats, setSessionStats] = useState({
    sessionCount: 5,
    averageScore: 78,
    totalDuration: 3600 // 秒
  })

  // 重置对话状态
  const resetSession = () => {
    setMessages([])
    setSessionStartTime(null)
    setSessionPaused(false)
    setIsTyping(false)
  }

  // 处理场景选择
  const handleScenarioSelect = (scenario: TrainingScenario) => {
    setSelectedScenario(scenario)
    setCurrentView('training')
    setSessionStartTime(new Date())
    
    // 初始化对话
    const welcomeMessage: ChatMessage = {
      id: '1',
      role: 'customer',
      content: `你好！我是来自${customer.country.name}的${customer.name}。${scenario.description}。我想了解一下你们的${customer.product.name}。`,
      timestamp: new Date(),
      metadata: {
        type: 'text',
        confidence: 0.9
      }
    }
    setMessages([welcomeMessage])
  }

  // 处理发送消息
  const handleSendMessage = (content: string) => {
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content,
      timestamp: new Date(),
      metadata: {
        type: 'text'
      }
    }

    setMessages(prev => [...prev, userMessage])
    setIsTyping(true)

    // 模拟客户回复
    setTimeout(() => {
      const customerReplies = [
        '这个价格有点高，能不能再优惠一些？',
        '我们需要考虑一下质量保证的问题。',
        '交货期能保证吗？我们的项目时间很紧。',
        '你们的售后服务怎么样？',
        '能提供一些成功案例吗？',
        '我需要和我的团队讨论一下，稍后给你回复。',
        '这个解决方案听起来不错，但我们还有一些技术问题。',
        '价格方面我们还需要进一步谈判。'
      ]
      
      const reply = customerReplies[Math.floor(Math.random() * customerReplies.length)]
      const customerMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'customer',
        content: reply,
        timestamp: new Date(),
        metadata: {
          type: 'text',
          confidence: 0.8
        }
      }
      
      setMessages(prev => [...prev, customerMessage])
      setIsTyping(false)
    }, 1500 + Math.random() * 2000)
  }

  // 计算会话时长
  const getSessionDuration = () => {
    if (!sessionStartTime) return '00:00'
    const duration = Math.floor((Date.now() - sessionStartTime.getTime()) / 1000)
    const minutes = Math.floor(duration / 60)
    const seconds = duration % 60
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }

  // 处理暂停/继续
  const togglePause = () => {
    setSessionPaused(!sessionPaused)
  }

  // 返回场景选择
  const backToScenarioSelection = () => {
    setCurrentView('scenario')
    setSelectedScenario(null)
    resetSession()
  }

  return (
    <Dialog open={open} onOpenChange={onClose} modal={true}>
      <DialogContent
        className={`
          ${isFullscreen
            ? 'fixed inset-0 w-screen h-screen max-w-none translate-x-0 translate-y-0 left-0 top-0'
            : 'w-[95vw] max-w-7xl h-[95vh] max-h-[900px]'
          }
          p-0 gap-0 overflow-hidden
          flex flex-col
          border-none shadow-2xl
          [&>button]:hidden
        `}
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        {currentView === 'scenario' ? (
          // 场景选择界面
                      <div className="h-full flex flex-col overflow-hidden">
              <div className="flex-1 overflow-y-auto scrollbar-thin">
                <div className="p-6">
                <EnhancedTrainingScenarioSelector
                  customer={customer}
                  onScenarioSelect={handleScenarioSelect}
                  onClose={onClose}
                />
              </div>
            </div>
          </div>
        ) : (
          // 训练界面
          <div className="h-full flex flex-col bg-white dark:bg-gray-900">
            {/* 头部工具栏 - 固定在顶部 */}
            <div className="flex-shrink-0 flex items-center justify-between px-4 py-3 border-b bg-white dark:bg-gray-800 shadow-sm z-30 relative">
              <div className="flex items-center space-x-3 flex-1 min-w-0 pr-4">
                <div className="flex items-center space-x-2 min-w-0">
                  <div className="relative flex-shrink-0">
                    <Avatar className="w-8 h-8 ring-1 ring-blue-200 dark:ring-blue-700">
                      <AvatarFallback className="bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 text-blue-600 dark:text-blue-400 font-medium text-sm">
                        {customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-green-400 rounded-full border border-white dark:border-gray-800"></div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-gray-900 dark:text-white truncate text-base">
                      {selectedScenario?.name || '训练对话'}
                    </h3>
                    <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                      <span className="text-sm">{customer.country?.flag}</span>
                      <span className="truncate">{customer.name}</span>
                      <span>·</span>
                      <span className="truncate">{selectedScenario?.industry}</span>
                    </div>
                  </div>
                </div>
                
                {sessionStartTime && (
                  <div className="flex items-center space-x-2 flex-shrink-0">
                    <Badge variant="secondary" className="bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-400 text-xs px-2 py-1">
                      <Clock className="w-3 h-3 mr-1" />
                      {getSessionDuration()}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={togglePause}
                      className="h-6 w-6 p-0"
                    >
                      {sessionPaused ? (
                        <Play className="w-3 h-3" />
                      ) : (
                        <Pause className="w-3 h-3" />
                      )}
                    </Button>
                  </div>
                )}
              </div>

              {/* 右侧控制按钮 */}
              <div className="flex items-center space-x-2 flex-shrink-0">
                <div className="flex items-center space-x-2 text-xs">
                  <Switch
                    id="right-panel"
                    checked={showRightPanel}
                    onCheckedChange={setShowRightPanel}
                    className="scale-75"
                  />
                  <Label htmlFor="right-panel" className="text-xs font-medium text-gray-600 dark:text-gray-400 whitespace-nowrap">
                    侧栏
                  </Label>
                </div>
                <Separator orientation="vertical" className="h-4" />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={backToScenarioSelection}
                  className="h-8 px-2 text-xs whitespace-nowrap"
                >
                  <RotateCcw className="w-3 h-3 mr-1" />
                  重选场景
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  className="h-8 w-8 p-0"
                >
                  {isFullscreen ? (
                    <Minimize2 className="w-4 h-4" />
                  ) : (
                    <Maximize2 className="w-4 h-4" />
                  )}
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={onClose}
                  className="h-8 w-8 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* 主要内容区域 - 响应式布局 */}
            <div className="flex flex-1 overflow-hidden min-h-0">
              {/* 左侧：客户信息面板 */}
              <div className={`
                ${isFullscreen ? 'w-80' : 'w-72 lg:w-80'} 
                flex-shrink-0 border-r bg-gradient-to-b from-gray-50/80 to-white dark:from-gray-800/80 dark:to-gray-800 
                overflow-hidden flex flex-col
                ${!isFullscreen ? 'hidden md:flex' : ''}
              `}>
                <div className="flex-1 overflow-y-auto scrollbar-thin p-3">
                  <Card className="shadow-sm border-gray-200 dark:border-gray-700">
                    <CardHeader className="pb-2 bg-gradient-to-r from-blue-50/50 to-white dark:from-blue-900/10 dark:to-gray-800">
                      <CardTitle className="text-base flex items-center space-x-2">
                        <div className="p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-md">
                          <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </div>
                        <span>客户信息</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3 p-3">
                      <div className="flex items-center space-x-2 p-2 bg-gray-50/50 dark:bg-gray-800/50 rounded-md border border-gray-100 dark:border-gray-700">
                        <Avatar className="w-10 h-10 ring-1 ring-blue-200 dark:ring-blue-800 shadow-sm">
                          <AvatarFallback className="bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 text-blue-600 dark:text-blue-400 font-semibold text-sm">
                            {customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="min-w-0 flex-1">
                          <h4 className="font-semibold text-gray-900 dark:text-white text-sm truncate">{customer.name}</h4>
                          <p className="text-xs text-gray-600 dark:text-gray-400 font-medium truncate">
                            {customer.background?.position}
                          </p>
                        </div>
                      </div>

                      <Separator />

                      {/* 场景信息 */}
                      {selectedScenario && (
                        <div className="space-y-2">
                          <h5 className="font-medium text-sm">训练场景</h5>
                          <div className="p-2 bg-purple-50/50 dark:bg-purple-900/10 rounded-md border border-purple-100 dark:border-purple-800">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="text-lg">{selectedScenario.icon}</span>
                              <span className="font-medium text-sm truncate">{selectedScenario.name}</span>
                            </div>
                            <p className="text-xs text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                              {selectedScenario.description}
                            </p>
                            <div className="flex flex-wrap gap-1">
                              {selectedScenario.tags.slice(0, 3).map((tag, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}

                      <Separator />

                      {/* 客户偏好 */}
                      {customer.background?.preferences && (
                        <div>
                          <h5 className="font-medium mb-2 text-sm">客户偏好</h5>
                          <div className="flex flex-wrap gap-1">
                            {customer.background.preferences.map((pref, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {pref}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* 中间：对话窗口 */}
              <div className="flex-1 bg-gradient-to-b from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 min-w-0">
                <SmartChatInterface
                  messages={messages}
                  customer={customer}
                  onSendMessage={handleSendMessage}
                  isTyping={isTyping}
                  className="h-full"
                />
              </div>

              {/* 右侧：功能面板 */}
              {showRightPanel && (
                <div className={`
                  ${isFullscreen ? 'w-96' : 'w-80 lg:w-96'} 
                  flex-shrink-0 border-l bg-gray-50/50 dark:bg-gray-800/50 
                  overflow-hidden flex flex-col
                  ${!isFullscreen ? 'hidden lg:flex' : ''}
                `}>
                  <Tabs value={rightPanelTab} onValueChange={(value) => setRightPanelTab(value as any)} className="h-full flex flex-col">
                    <div className="flex-shrink-0 p-2">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="evaluation" className="text-xs">
                          <BarChart3 className="w-3 h-3 mr-1" />
                          评分
                        </TabsTrigger>
                        <TabsTrigger value="learning" className="text-xs">
                          <Lightbulb className="w-3 h-3 mr-1" />
                          学习
                        </TabsTrigger>
                      </TabsList>
                    </div>

                    <div className="flex-1 overflow-y-auto scrollbar-thin">
                      <div className="p-3">
                        <TabsContent value="evaluation" className="mt-0">
                          <RealTimeEvaluationPanel messages={messages} customer={customer} />
                        </TabsContent>

                        <TabsContent value="learning" className="mt-0">
                          <LearningTipsPanel messages={messages} customer={customer} />
                        </TabsContent>
                      </div>
                    </div>
                  </Tabs>
                </div>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
