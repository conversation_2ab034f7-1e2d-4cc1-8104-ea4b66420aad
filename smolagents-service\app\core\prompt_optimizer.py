"""
提示词优化模块
优化AI推理效率，减少推理时间
"""

import logging
from typing import Dict, List, Any
from app.models.schemas import SearchStrategy


class PromptOptimizer:
    """提示词优化器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def generate_optimized_search_prompt(self, product: str, strategy: SearchStrategy) -> str:
        """生成优化的搜索提示词"""

        # 根据策略类型选择优化的模板
        template_map = {
            "market_size": self._get_market_size_template(),
            "growth_trends": self._get_growth_trends_template(),
            "competition": self._get_competition_template(),
            "consumer_demand": self._get_consumer_demand_template(),
            "pricing": self._get_pricing_template(),
            "regional_analysis": self._get_regional_template()
        }

        # 获取策略类型的简化名称
        strategy_key = strategy.category.lower().replace(" ", "_").replace("-", "_")

        # 选择合适的模板
        if strategy_key in template_map:
            template = template_map[strategy_key]
        else:
            template = self._get_generic_template()

        # 填充模板
        optimized_prompt = template.format(
            product=product,
            query=strategy.query,
            category=strategy.category
        )

        self.logger.debug(f"生成优化提示词: {strategy.category} - 长度: {len(optimized_prompt)}")
        return optimized_prompt

    def _get_market_size_template(self) -> str:
        """市场规模分析模板"""
        return """搜索{product}市场数据，重点查找：
- 全球市场规模（收入/价值）
- 市场增长率
- 主要市场细分
- 地理分布情况

重点关注最新数据（2023-2024年）和权威来源。
请提供具体数字和数据来源，并用中文回答。"""

    def _get_growth_trends_template(self) -> str:
        """增长趋势分析模板"""
        return """搜索{product}增长趋势，重点查找：
- 历史增长率（3-5年）
- 未来增长预测（2025-2027年）
- 增长驱动因素
- 市场扩张模式

重点关注定量数据和预测，并用中文回答。"""

    def _get_competition_template(self) -> str:
        """竞争分析模板"""
        return """搜索{product}竞争格局，重点查找：
- 前5-10名市场领导者
- 市场份额百分比
- 竞争定位
- 关键差异化因素

重点关注当前市场领导者及其地位，并用中文回答。"""

    def _get_consumer_demand_template(self) -> str:
        """消费者需求分析模板"""
        return """搜索{product}消费者需求，重点查找：
- 目标客户群体
- 需求模式和季节性
- 消费者偏好和趋势
- 购买行为洞察

重点关注消费者研究和需求指标，并用中文回答。"""

    def _get_pricing_template(self) -> str:
        """价格分析模板"""
        return """搜索{product}价格信息，重点查找：
- 平均价格区间
- 价格趋势变化
- 不同地区价格对比
- 价值主张分析

重点关注当前价格数据和趋势，并用中文回答。"""

    def _get_regional_template(self) -> str:
        """区域分析模板"""
        return """搜索{product}区域市场数据，重点查找：
- 各地区市场规模
- 地理增长模式
- 区域偏好和差异
- 各地区市场渗透率

重点关注地理市场分布情况，并用中文回答。"""

    def _get_generic_template(self) -> str:
        """通用模板"""
        return """搜索{product}相关的{category}市场信息。
查询内容：{query}

请提供相关的市场数据、统计信息和洞察。
重点关注权威来源和最新信息。
请用中文回答所有内容。"""

    def generate_analysis_prompt(self, product: str, search_results: List[Dict[str, Any]]) -> str:
        """生成分析整合提示词"""

        # 提取搜索结果摘要
        results_summary = self._summarize_search_results(search_results)

        # 生成中文分析提示词
        analysis_prompt = f"""基于以下{product}的市场研究数据：

{results_summary}

请生成一份全面的市场分析报告，包含以下部分：

1. **市场概览**
   - 市场规模和价值
   - 关键增长指标
   - 市场发展现状

2. **竞争格局**
   - 主要参与者和市场份额
   - 竞争动态分析
   - 竞争优势对比

3. **市场趋势**
   - 增长驱动因素
   - 未来发展前景
   - 技术发展趋势

4. **关键洞察**
   - 重要发现
   - 市场机遇
   - 风险分析

要求：
- 分析必须基于数据驱动，尽可能引用具体来源
- 使用结构化格式，标题清晰
- 所有内容必须使用中文
- 提供具体的数据和统计信息
- 分析要客观、准确、有深度"""

        return analysis_prompt

    def _summarize_search_results(self, search_results: List[Dict[str, Any]]) -> str:
        """总结搜索结果"""
        summaries = []

        for i, result in enumerate(search_results[:4], 1):  # 限制为前4个结果
            if result.get("success") and result.get("result"):
                strategy = result.get("strategy", f"Strategy {i}")
                content = result.get("result", "")

                # 截取内容摘要（前200字符）
                summary = content[:200] + "..." if len(content) > 200 else content
                summaries.append(f"**{strategy.title()}:**\n{summary}")

        return "\n\n".join(summaries) if summaries else "No detailed search results available."

    def optimize_query_length(self, query: str, max_length: int = 1000) -> str:
        """优化查询长度"""
        if len(query) <= max_length:
            return query

        # 简单的截断策略，保留重要部分
        lines = query.split('\n')
        optimized_lines = []
        current_length = 0

        for line in lines:
            if current_length + len(line) <= max_length:
                optimized_lines.append(line)
                current_length += len(line)
            else:
                break

        optimized_query = '\n'.join(optimized_lines)

        # 如果还是太长，进行更激进的截断
        if len(optimized_query) > max_length:
            optimized_query = optimized_query[:max_length-3] + "..."

        self.logger.info(f"查询长度优化: {len(query)} -> {len(optimized_query)}")
        return optimized_query


class PerformanceTracker:
    """性能跟踪器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics = {}

    def track_search_performance(self, strategy: str, execution_time: float, success: bool):
        """跟踪搜索性能"""
        if strategy not in self.metrics:
            self.metrics[strategy] = {
                "total_executions": 0,
                "successful_executions": 0,
                "total_time": 0.0,
                "average_time": 0.0,
                "success_rate": 0.0
            }

        metrics = self.metrics[strategy]
        metrics["total_executions"] += 1
        metrics["total_time"] += execution_time

        if success:
            metrics["successful_executions"] += 1

        # 更新平均值
        metrics["average_time"] = metrics["total_time"] / metrics["total_executions"]
        metrics["success_rate"] = metrics["successful_executions"] / metrics["total_executions"]

        self.logger.debug(f"性能跟踪 - {strategy}: {execution_time:.2f}s, 成功率: {metrics['success_rate']:.2%}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics:
            return {"message": "暂无性能数据"}

        summary = {
            "total_strategies": len(self.metrics),
            "overall_success_rate": 0.0,
            "average_execution_time": 0.0,
            "strategy_details": self.metrics
        }

        # 计算总体指标
        total_executions = sum(m["total_executions"] for m in self.metrics.values())
        total_successful = sum(m["successful_executions"] for m in self.metrics.values())
        total_time = sum(m["total_time"] for m in self.metrics.values())

        if total_executions > 0:
            summary["overall_success_rate"] = total_successful / total_executions
            summary["average_execution_time"] = total_time / total_executions

        return summary

    def reset_metrics(self):
        """重置性能指标"""
        self.metrics.clear()
        self.logger.info("性能指标已重置")


# 全局实例
prompt_optimizer = PromptOptimizer()
performance_tracker = PerformanceTracker()


class GroupAnalysisPromptOptimizer:
    """特定群体需求分析专用提示词优化器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def generate_optimized_search_prompt(self, product: str, strategy: SearchStrategy, target_group: str = "", scenario: str = "") -> str:
        """生成针对特定群体需求分析的优化搜索提示词"""

        # 根据策略类型选择群体分析专用模板
        template_map = {
            "functional_needs": self._get_functional_needs_template(),
            "usage_scenarios": self._get_usage_scenarios_template(),
            "pain_points": self._get_pain_points_template(),
            "preferences": self._get_preferences_template(),
            "future_needs": self._get_future_needs_template(),
            "behavioral_patterns": self._get_behavioral_patterns_template(),
            "demographic_insights": self._get_demographic_insights_template()
        }

        # 获取策略类型的简化名称
        strategy_key = strategy.category.lower().replace(" ", "_").replace("-", "_")

        # 选择合适的模板
        if strategy_key in template_map:
            template = template_map[strategy_key]
        else:
            template = self._get_group_generic_template()

        # 填充模板
        optimized_prompt = template.format(
            product=product,
            query=strategy.query,
            category=strategy.category,
            target_group=target_group,
            scenario=scenario
        )

        self.logger.debug(f"生成群体分析优化提示词: {strategy.category} - 长度: {len(optimized_prompt)}")
        return optimized_prompt

    def _get_functional_needs_template(self) -> str:
        """功能需求分析模板"""
        return """搜索{target_group}对{product}的具体功能需求，重点查找：
- 该群体最看重的核心功能特性
- 对产品性能的具体要求和期望
- 功能使用频率和重要性排序
- 与其他群体功能需求的差异化特点

重点关注该特定群体的真实使用需求和功能偏好，并用中文回答。"""

    def _get_usage_scenarios_template(self) -> str:
        """使用场景分析模板"""
        return """搜索{target_group}使用{product}的典型场景，重点查找：
- 主要使用场景和环境
- 使用频率和时间模式
- 场景下的具体需求和挑战
- 不同场景下的产品期望差异

重点关注该群体的真实使用情境和场景化需求，并用中文回答。"""

    def _get_pain_points_template(self) -> str:
        """痛点分析模板"""
        return """搜索{target_group}在使用{product}时遇到的问题和痛点，重点查找：
- 当前产品无法满足的需求
- 使用过程中的困难和障碍
- 对现有解决方案的不满意之处
- 迫切需要改进的功能或体验

重点关注该群体的真实痛点和未满足需求，并用中文回答。"""

    def _get_preferences_template(self) -> str:
        """偏好分析模板"""
        return """搜索{target_group}对{product}的偏好和期望，重点查找：
- 产品设计和外观偏好
- 品牌和价格敏感度
- 购买决策影响因素
- 对产品体验的期望标准

重点关注该群体的消费偏好和决策模式，并用中文回答。"""

    def _get_future_needs_template(self) -> str:
        """未来需求分析模板"""
        return """搜索{target_group}对{product}的未来需求趋势，重点查找：
- 新兴需求和潜在需求
- 技术发展带来的新期望
- 生活方式变化产生的新需求
- 对产品创新的期待方向

重点关注该群体的潜在需求和未来趋势，并用中文回答。"""

    def _get_behavioral_patterns_template(self) -> str:
        """行为模式分析模板"""
        return """搜索{target_group}的{product}相关行为模式，重点查找：
- 购买行为和决策过程
- 使用习惯和行为特征
- 信息获取和评价方式
- 社交分享和推荐行为

重点关注该群体的行为特征和模式，并用中文回答。"""

    def _get_demographic_insights_template(self) -> str:
        """人群洞察分析模板"""
        return """搜索{target_group}的人群特征和{product}相关洞察，重点查找：
- 人群的基本特征和属性
- 消费能力和消费观念
- 生活方式和价值观
- 对{product}类别的整体态度

重点关注该群体的深层特征和洞察，并用中文回答。"""

    def _get_group_generic_template(self) -> str:
        """群体分析通用模板"""
        return """搜索{target_group}对{product}的{category}相关信息。
查询内容：{query}

请重点关注该特定群体的真实需求、使用场景、痛点和偏好。
避免泛泛而谈的市场信息，专注于该群体的独特需求特征。
请用中文回答所有内容。"""


    def generate_group_analysis_prompt(self, product: str, target_group: str, scenario: str, search_results: List[Dict[str, Any]]) -> str:
        """生成特定群体需求分析整合提示词"""

        # 提取搜索结果摘要
        results_summary = self._summarize_search_results(search_results)

        # 生成专门针对群体需求分析的提示词
        analysis_prompt = f"""基于以下关于{target_group}对{product}在{scenario}场景下的研究数据：

{results_summary}

请生成一份专门针对该特定群体的需求分析报告。

**重要：报告标题必须是：{target_group}对{product}在{scenario}场景下的需求分析报告**

报告应包含以下部分：

1. **群体特征概述**
   - 该群体的基本特征和属性
   - 与{product}相关的独特背景
   - 群体规模和重要性

2. **功能需求分析**
   - 该群体对{product}的核心功能需求
   - 功能重要性排序和使用频率
   - 与其他群体的功能需求差异

3. **使用场景和痛点**
   - 主要使用场景和环境
   - 当前遇到的问题和痛点
   - 未被满足的需求缺口

4. **偏好和期望**
   - 产品设计和体验偏好
   - 价格敏感度和购买决策因素
   - 对产品改进的期望方向

5. **未来需求趋势**
   - 新兴需求和潜在需求
   - 技术发展带来的新机会
   - 该群体需求的发展趋势

6. **针对性建议**
   - 产品功能优化建议
   - 用户体验改进方向
   - 市场策略和定位建议

请确保分析专注于该特定群体的真实需求，避免泛泛而谈的市场分析。
基于真实数据进行分析，避免使用模拟或假设数据。
所有内容请用中文回答，并提供具体的数据支撑。
"""

        return analysis_prompt

    def _summarize_search_results(self, search_results: List[Dict[str, Any]]) -> str:
        """提取搜索结果摘要"""
        if not search_results:
            return "暂无搜索结果数据"

        summaries = []
        for i, result in enumerate(search_results[:10], 1):  # 限制最多10个结果
            title = result.get('title', '未知标题')
            content = result.get('content', '无内容')
            url = result.get('url', '无链接')

            # 截取内容前200字符作为摘要
            content_summary = content[:200] + "..." if len(content) > 200 else content

            summaries.append(f"""
**数据源 {i}**: {title}
**链接**: {url}
**内容摘要**: {content_summary}
""")

        return "\n".join(summaries)


# 群体分析专用实例
group_analysis_optimizer = GroupAnalysisPromptOptimizer()
