"""
用户认证API路由
提供用户注册、登录、登出等认证功能
"""
from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.core.config import settings
from app.core.auth import (
    create_access_token,
    get_current_active_user,
    get_current_user,
    verify_password
)
from app.crud.user import user_crud
from app.crud.password_reset import password_reset_crud
from app.services.email import email_service
from app.schemas.user import (
    UserCreate,
    UserLogin,
    UserLoginResponse,
    User,
    Token,
    UserPasswordUpdate
)
from app.schemas.password_reset import (
    PasswordResetRequest,
    PasswordResetResponse,
    PasswordResetVerify,
    PasswordResetVerifyResponse,
    PasswordResetConfirm,
    PasswordResetConfirmResponse
)
from app.schemas.rbac import UserPermissionResponse
from app.crud.rbac import user_role_crud

router = APIRouter()


@router.post("/register", response_model=User, summary="用户注册")
async def register(
    *,
    db: AsyncSession = Depends(get_db),
    user_in: UserCreate
) -> User:
    """
    用户注册

    - **username**: 用户名（3-50字符，唯一）
    - **email**: 邮箱地址（唯一）
    - **password**: 密码（6-50字符）
    - **full_name**: 全名（可选）
    - **phone**: 电话号码（可选）
    """
    try:
        user = await user_crud.create(db=db, obj_in=user_in)
        return user
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/login", response_model=UserLoginResponse, summary="用户登录")
async def login(
    *,
    db: AsyncSession = Depends(get_db),
    user_in: UserLogin
) -> UserLoginResponse:
    """
    用户登录

    - **username**: 用户名或邮箱
    - **password**: 密码

    返回用户信息和JWT访问令牌
    """
    user = await user_crud.authenticate(
        db=db, username=user_in.username, password=user_in.password
    )

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )

    # 创建访问令牌
    access_token_expires = timedelta(minutes=settings.JWT_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id},
        expires_delta=access_token_expires
    )

    token = Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.JWT_EXPIRE_MINUTES * 60  # 转换为秒
    )

    return UserLoginResponse(user=user, token=token)


@router.post("/logout", summary="用户登出")
async def logout(current_user: User = Depends(get_current_active_user)):
    """
    用户登出

    注意：JWT是无状态的，实际的登出需要在客户端删除token
    这个端点主要用于记录登出事件或执行清理操作
    """
    return {"message": "成功登出"}


@router.get("/me", response_model=User, summary="获取当前用户信息")
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """
    获取当前登录用户的信息

    需要在请求头中提供有效的JWT token：
    Authorization: Bearer <token>
    """
    return current_user


@router.get("/me/permissions", response_model=UserPermissionResponse, summary="获取当前用户权限")
async def get_current_user_permissions(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> UserPermissionResponse:
    """
    获取当前用户的所有权限和角色

    需要在请求头中提供有效的JWT token：
    Authorization: Bearer <token>
    """
    # 获取用户角色
    user_roles = await user_role_crud.get_user_roles(db, current_user.id)
    roles = [ur.role for ur in user_roles if ur.role.is_active]

    # 获取用户所有权限
    permissions = await user_role_crud.get_user_permissions(db, current_user.id)

    return UserPermissionResponse(
        user_id=current_user.id,
        roles=roles,
        permissions=permissions
    )


@router.post("/refresh", response_model=Token, summary="刷新访问令牌")
async def refresh_token(
    current_user: User = Depends(get_current_user)
) -> Token:
    """
    刷新访问令牌

    使用当前有效的token获取新的token
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )

    # 创建新的访问令牌
    access_token_expires = timedelta(minutes=settings.JWT_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": current_user.username, "user_id": current_user.id},
        expires_delta=access_token_expires
    )

    return Token(
        access_token=access_token,
        token_type="bearer",
        expires_in=settings.JWT_EXPIRE_MINUTES * 60
    )


@router.post("/change-password", summary="修改密码")
async def change_password(
    *,
    db: AsyncSession = Depends(get_db),
    password_update: UserPasswordUpdate,
    current_user: User = Depends(get_current_active_user)
):
    """
    修改当前用户密码

    - **current_password**: 当前密码
    - **new_password**: 新密码（6-50字符）
    """
    # 验证当前密码
    if not verify_password(password_update.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )

    # 更新密码
    await user_crud.update_password(
        db=db, db_obj=current_user, new_password=password_update.new_password
    )

    return {"message": "密码修改成功"}


@router.post("/forgot-password", response_model=PasswordResetResponse, summary="申请密码重置")
async def forgot_password(
    *,
    db: AsyncSession = Depends(get_db),
    request: PasswordResetRequest
) -> PasswordResetResponse:
    """
    申请密码重置

    - **email**: 注册时使用的邮箱地址

    系统会向该邮箱发送密码重置链接
    """
    # 查找用户
    user = await user_crud.get_by_email(db, email=request.email)

    if not user:
        # 为了安全，即使用户不存在也返回成功消息
        return PasswordResetResponse(
            message="如果该邮箱地址存在于我们的系统中，您将收到密码重置邮件",
            email=request.email
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )

    try:
        # 创建新的重置令牌
        import secrets
        from datetime import datetime, timedelta, timezone
        from sqlalchemy import text

        # 生成安全的随机令牌
        token = secrets.token_urlsafe(32)

        # 设置过期时间
        expires_at = datetime.now(timezone.utc) + timedelta(hours=24)

        # 使用原始SQL插入令牌
        await db.execute(
            text("""
                INSERT INTO password_reset_tokens (user_id, token, expires_at, used, created_at)
                VALUES (:user_id, :token, :expires_at, :used, :created_at)
            """),
            {
                "user_id": user.id,
                "token": token,
                "expires_at": expires_at,
                "used": False,
                "created_at": datetime.now(timezone.utc)
            }
        )
        await db.commit()

        # 发送重置邮件
        email_sent = email_service.send_password_reset_email(
            email=user.email,
            username=user.username,
            reset_token=token,
            full_name=user.full_name
        )

        if not email_sent:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="发送邮件失败，请稍后重试"
            )

        return PasswordResetResponse(
            message="密码重置邮件已发送，请检查您的邮箱",
            email=request.email
        )

    except Exception as e:
        import traceback
        traceback.print_exc()  # 打印详细错误信息到日志
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理密码重置请求时发生错误: {str(e)}"
        )


@router.post("/verify-reset-token", response_model=PasswordResetVerifyResponse, summary="验证重置令牌")
async def verify_reset_token(
    *,
    db: AsyncSession = Depends(get_db),
    request: PasswordResetVerify
) -> PasswordResetVerifyResponse:
    """
    验证密码重置令牌的有效性

    - **token**: 密码重置令牌

    返回令牌是否有效以及关联的用户信息
    """
    # 获取有效的重置令牌
    reset_token = await password_reset_crud.get_valid_token(db, token=request.token)

    if not reset_token:
        return PasswordResetVerifyResponse(
            valid=False,
            message="重置令牌无效或已过期"
        )

    # 获取关联的用户
    user = await user_crud.get(db, id=reset_token.user_id)
    if not user or not user.is_active:
        return PasswordResetVerifyResponse(
            valid=False,
            message="关联的用户账户不存在或已被禁用"
        )

    return PasswordResetVerifyResponse(
        valid=True,
        message="令牌有效",
        email=user.email
    )


@router.post("/reset-password", response_model=PasswordResetConfirmResponse, summary="重置密码")
async def reset_password(
    *,
    db: AsyncSession = Depends(get_db),
    request: PasswordResetConfirm
) -> PasswordResetConfirmResponse:
    """
    使用重置令牌重置密码

    - **token**: 密码重置令牌
    - **new_password**: 新密码（6-50字符）

    重置成功后令牌将被标记为已使用
    """
    # 获取有效的重置令牌
    reset_token = await password_reset_crud.get_valid_token(db, token=request.token)

    if not reset_token:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="重置令牌无效或已过期"
        )

    # 获取关联的用户
    user = await user_crud.get(db, id=reset_token.user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="关联的用户不存在"
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )

    try:
        from sqlalchemy import text
        from app.core.auth import get_password_hash

        # 使用原始SQL更新密码
        hashed_password = get_password_hash(request.new_password)

        await db.execute(
            text("UPDATE users SET hashed_password = :hashed_password WHERE id = :user_id"),
            {"hashed_password": hashed_password, "user_id": user.id}
        )

        # 标记令牌为已使用
        await db.execute(
            text("UPDATE password_reset_tokens SET used = true WHERE token = :token"),
            {"token": request.token}
        )

        await db.commit()

        return PasswordResetConfirmResponse(
            message="密码重置成功"
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重置密码时发生错误"
        )
