/**
 * 知识库全局状态管理
 * 使用 Zustand 管理知识库相关的全局状态
 */
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { Document, SearchResult, RAGQueryResponse } from '@/types/morphik';

// 知识库状态接口
interface KnowledgeBaseState {
  // 当前选中的文档
  selectedDocument: Document | null;
  setSelectedDocument: (document: Document | null) => void;

  // 搜索相关状态
  searchQuery: string;
  searchResults: SearchResult[];
  setSearchQuery: (query: string) => void;
  setSearchResults: (results: SearchResult[]) => void;
  clearSearch: () => void;

  // 聊天相关状态
  chatHistory: ChatMessage[];
  addChatMessage: (message: ChatMessage) => void;
  clearChatHistory: () => void;
  updateChatMessage: (id: string, updates: Partial<ChatMessage>) => void;

  // 上传相关状态
  uploadProgress: UploadProgress[];
  addUploadProgress: (progress: UploadProgress) => void;
  updateUploadProgress: (id: string, updates: Partial<UploadProgress>) => void;
  removeUploadProgress: (id: string) => void;
  clearUploadProgress: () => void;

  // 视图状态
  activeTab: string;
  setActiveTab: (tab: string) => void;

  // 筛选和排序
  documentFilters: DocumentFilters;
  setDocumentFilters: (filters: DocumentFilters) => void;
  resetDocumentFilters: () => void;

  // 系统设置
  settings: KnowledgeBaseSettings;
  updateSettings: (settings: Partial<KnowledgeBaseSettings>) => void;

  // 统计信息
  stats: KnowledgeBaseStats | null;
  setStats: (stats: KnowledgeBaseStats) => void;

  // 错误状态
  errors: ErrorState[];
  addError: (error: ErrorState) => void;
  removeError: (id: string) => void;
  clearErrors: () => void;
}

// 聊天消息接口
interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  sources?: SearchResult[];
  isLoading?: boolean;
  error?: string;
  feedback?: 'positive' | 'negative';
}

// 上传进度接口
interface UploadProgress {
  id: string;
  fileName: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
  documentId?: string;
}

// 文档筛选接口
interface DocumentFilters {
  status?: string;
  category?: string;
  tags?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  fileType?: string;
  sortBy?: 'name' | 'date' | 'size' | 'category';
  sortOrder?: 'asc' | 'desc';
}

// 系统设置接口
interface KnowledgeBaseSettings {
  theme: 'light' | 'dark' | 'system';
  language: 'zh' | 'en';
  autoSave: boolean;
  showSources: boolean;
  maxChatMessages: number;
  defaultSearchLimit: number;
  enableNotifications: boolean;
  compactMode: boolean;
}

// 统计信息接口
interface KnowledgeBaseStats {
  totalDocuments: number;
  totalSearches: number;
  totalChats: number;
  storageUsed: number;
  lastUpdated: Date;
  topCategories: Array<{ name: string; count: number }>;
  topTags: Array<{ name: string; count: number }>;
  recentActivity: Array<{
    type: 'upload' | 'search' | 'chat';
    timestamp: Date;
    description: string;
  }>;
}

// 错误状态接口
interface ErrorState {
  id: string;
  type: 'upload' | 'search' | 'chat' | 'system';
  message: string;
  timestamp: Date;
  details?: any;
}

// 默认设置
const defaultSettings: KnowledgeBaseSettings = {
  theme: 'system',
  language: 'zh',
  autoSave: true,
  showSources: true,
  maxChatMessages: 50,
  defaultSearchLimit: 10,
  enableNotifications: true,
  compactMode: false,
};

// 默认筛选器
const defaultFilters: DocumentFilters = {
  sortBy: 'date',
  sortOrder: 'desc',
};

// 创建知识库状态存储
export const useKnowledgeBaseStore = create<KnowledgeBaseState>()(
  devtools(
    persist(
      (set, get) => ({
        // 文档状态
        selectedDocument: null,
        setSelectedDocument: (document) => set({ selectedDocument: document }),

        // 搜索状态
        searchQuery: '',
        searchResults: [],
        setSearchQuery: (query) => set({ searchQuery: query }),
        setSearchResults: (results) => set({ searchResults: results }),
        clearSearch: () => set({ searchQuery: '', searchResults: [] }),

        // 聊天状态
        chatHistory: [
          {
            id: 'welcome',
            type: 'assistant',
            content: '您好！我是您的智能助手，可以帮您在知识库中查找信息并回答问题。请随时向我提问！',
            timestamp: new Date(),
          }
        ],
        addChatMessage: (message) => {
          const { chatHistory, settings } = get();
          const newHistory = [...chatHistory, message];

          // 限制消息数量
          if (newHistory.length > settings.maxChatMessages) {
            newHistory.splice(1, newHistory.length - settings.maxChatMessages); // 保留欢迎消息
          }

          set({ chatHistory: newHistory });
        },
        clearChatHistory: () => set({
          chatHistory: [
            {
              id: 'welcome',
              type: 'assistant',
              content: '对话已清空。请随时向我提问！',
              timestamp: new Date(),
            }
          ]
        }),
        updateChatMessage: (id, updates) => {
          const { chatHistory } = get();
          const updatedHistory = chatHistory.map(msg =>
            msg.id === id ? { ...msg, ...updates } : msg
          );
          set({ chatHistory: updatedHistory });
        },

        // 上传状态
        uploadProgress: [],
        addUploadProgress: (progress) => {
          const { uploadProgress } = get();
          set({ uploadProgress: [...uploadProgress, progress] });
        },
        updateUploadProgress: (id, updates) => {
          const { uploadProgress } = get();
          const updatedProgress = uploadProgress.map(p =>
            p.id === id ? { ...p, ...updates } : p
          );
          set({ uploadProgress: updatedProgress });
        },
        removeUploadProgress: (id) => {
          const { uploadProgress } = get();
          set({ uploadProgress: uploadProgress.filter(p => p.id !== id) });
        },
        clearUploadProgress: () => set({ uploadProgress: [] }),

        // 视图状态
        activeTab: 'documents',
        setActiveTab: (tab) => set({ activeTab: tab }),

        // 筛选状态
        documentFilters: defaultFilters,
        setDocumentFilters: (filters) => set({ documentFilters: filters }),
        resetDocumentFilters: () => set({ documentFilters: defaultFilters }),

        // 设置
        settings: defaultSettings,
        updateSettings: (newSettings) => {
          const { settings } = get();
          set({ settings: { ...settings, ...newSettings } });
        },

        // 统计信息
        stats: null,
        setStats: (stats) => set({ stats }),

        // 错误状态
        errors: [],
        addError: (error) => {
          const { errors } = get();
          set({ errors: [...errors, error] });

          // 自动清除错误（5秒后）
          setTimeout(() => {
            const currentErrors = get().errors;
            set({ errors: currentErrors.filter(e => e.id !== error.id) });
          }, 5000);
        },
        removeError: (id) => {
          const { errors } = get();
          set({ errors: errors.filter(e => e.id !== id) });
        },
        clearErrors: () => set({ errors: [] }),
      }),
      {
        name: 'knowledge-base-storage',
        partialize: (state) => ({
          // 只持久化设置和筛选器
          settings: state.settings,
          documentFilters: state.documentFilters,
          activeTab: state.activeTab,
        }),
      }
    ),
    {
      name: 'knowledge-base-store',
    }
  )
);

// 选择器函数
export const useSelectedDocument = () => useKnowledgeBaseStore(state => state.selectedDocument);
export const useSearchState = () => useKnowledgeBaseStore(state => ({
  query: state.searchQuery,
  results: state.searchResults,
  setQuery: state.setSearchQuery,
  setResults: state.setSearchResults,
  clear: state.clearSearch,
}));
export const useChatState = () => useKnowledgeBaseStore(state => ({
  history: state.chatHistory,
  add: state.addChatMessage,
  clear: state.clearChatHistory,
  update: state.updateChatMessage,
}));
export const useUploadState = () => useKnowledgeBaseStore(state => ({
  progress: state.uploadProgress,
  add: state.addUploadProgress,
  update: state.updateUploadProgress,
  remove: state.removeUploadProgress,
  clear: state.clearUploadProgress,
}));
export const useKnowledgeBaseSettings = () => useKnowledgeBaseStore(state => ({
  settings: state.settings,
  update: state.updateSettings,
}));
export const useKnowledgeBaseErrors = () => useKnowledgeBaseStore(state => ({
  errors: state.errors,
  add: state.addError,
  remove: state.removeError,
  clear: state.clearErrors,
}));

// 导出类型
export type {
  ChatMessage,
  UploadProgress,
  DocumentFilters,
  KnowledgeBaseSettings,
  KnowledgeBaseStats,
  ErrorState,
};
