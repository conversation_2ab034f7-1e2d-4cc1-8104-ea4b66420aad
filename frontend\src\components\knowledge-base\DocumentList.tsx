/**
 * 文档列表组件
 * 显示和管理已上传的文档，支持分页、筛选、预览和删除
 */
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  FileText,
  Search,
  Filter,
  Trash2,
  Download,
  Calendar,
  User,
  Tag,
  Loader2,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { useDocuments, useDocument, useDocumentContent, useDocumentCache } from '@/hooks/useDocuments';
import { useDeleteDocument } from '@/hooks/useMorphik';
import type { Document, DocumentStatus } from '@/types/morphik';

interface DocumentListProps {
  onDocumentSelect?: (document: Document) => void;
  showActions?: boolean;
  maxHeight?: string;
}

function DocumentList({
  onDocumentSelect,
  showActions = true,
  maxHeight = "600px"
}: DocumentListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<DocumentStatus | 'all'>('all');
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  const {
    data: documentsData,
    isLoading,
    error,
    refetch
  } = useDocuments({
    limit: 100,
    search: searchQuery || undefined,
    status: statusFilter !== 'all' ? statusFilter : undefined,
  });



  const deleteDocument = useDeleteDocument();
  const { prefetchDocument } = useDocumentCache();

  // 轮询处理中的文档状态
  useEffect(() => {
    const processingDocuments = documentsData?.items.filter(
      doc => doc.system_metadata?.status === 'processing'
    ) || [];

    if (processingDocuments.length === 0) return;

    const pollInterval = setInterval(async () => {
      try {
        // 检查每个处理中文档的状态
        const statusChecks = processingDocuments.map(async (doc) => {
          try {
            const response = await fetch(`http://localhost:8000/documents/${doc.external_id}`);
            if (!response.ok) throw new Error('Failed to fetch document status');

            const updatedDoc = await response.json();
            const status = updatedDoc.system_metadata?.status || 'completed';

            return {
              documentId: doc.external_id,
              status: status
            };
          } catch (error) {
            console.error(`Failed to check status for document ${doc.external_id}:`, error);
            return null;
          }
        });

        const results = await Promise.all(statusChecks);

        // 如果有状态变化，刷新文档列表
        const hasStatusChange = results.some(result =>
          result && result.status !== 'processing'
        );

        if (hasStatusChange) {
          refetch();
        }

      } catch (error) {
        console.error('Error polling document status:', error);
      }
    }, 5000); // 每5秒检查一次

    return () => clearInterval(pollInterval);
  }, [documentsData?.items, refetch]);



  // 过滤文档
  const filteredDocuments = documentsData?.items.filter(doc => {
    const matchesSearch = !searchQuery ||
      doc.filename?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.metadata?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.metadata?.category?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' ||
      (doc.system_metadata?.status || 'completed') === statusFilter;

    return matchesSearch && matchesStatus;
  }) || [];

  // 格式化文件大小
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'N/A';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 获取状态颜色
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-orange-100 text-orange-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取状态标签
  const getStatusLabel = (status?: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'processing':
        return '处理中';
      case 'pending':
        return '待处理';
      case 'failed':
        return '失败';
      default:
        return '未知';
    }
  };

  // 处理文档删除
  const handleDelete = async (document: Document) => {
    if (!confirm(`确定要删除文档 "${document.filename || document.metadata?.title || '未命名文档'}" 吗？`)) {
      return;
    }

    try {
      await deleteDocument.mutateAsync(document.external_id);
    } catch (error) {
      console.error('Delete failed:', error);
    }
  };

  // 处理文档预览
  const handlePreview = (document: Document) => {
    setSelectedDocument(document);
    setShowPreview(true);
    prefetchDocument(document.external_id);
  };

  // 处理文档下载
  const handleDownload = async (doc: Document) => {
    try {
      // 检查文档是否有存储信息
      if (!doc.storage_info) {
        alert('该文档没有可下载的文件');
        return;
      }

      const filename = doc.filename || doc.metadata?.title || `document-${doc.external_id}`;

      // 构建正确的下载URL
      const { key } = doc.storage_info;

      // 对于本地存储，直接使用 key 作为路径
      // 因为 LocalStorage 返回的 bucket 是存储根路径，key 是相对路径
      const downloadUrl = `http://localhost:8000/storage/${key}`;



      // 创建一个临时的 a 标签来触发下载
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      link.target = '_blank';

      // 添加到 DOM，点击，然后移除
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);


    } catch (error) {

      alert('文档下载失败，请稍后重试');
    }
  };

  // 处理文档选择
  const handleSelect = (document: Document) => {
    onDocumentSelect?.(document);
  };

  if (error) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">加载失败</h3>
          <p className="text-muted-foreground mb-4">
            无法加载文档列表: {error.message}
          </p>
          <Button onClick={() => refetch()} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            重试
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            文档管理
          </CardTitle>
          <CardDescription>
            查看和管理您的文档库
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索文档名称、标题或分类..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as DocumentStatus | 'all')}
              className="px-3 py-2 border border-input rounded-md bg-background"
            >
              <option value="all">所有状态</option>
              <option value="completed">已完成</option>
              <option value="processing">处理中</option>
              <option value="pending">待处理</option>
              <option value="failed">失败</option>
            </select>
            <Button onClick={() => refetch()} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4" />
            </Button>

          </div>

          {/* 统计信息 */}
          <div className="flex gap-4 text-sm text-muted-foreground">
            <span>总计: {documentsData?.total || 0} 个文档</span>
            <span>显示: {filteredDocuments.length} 个</span>
          </div>
        </CardContent>
      </Card>

      {/* 文档列表 */}
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="text-center py-12">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">加载文档列表...</p>
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {searchQuery ? '未找到匹配的文档' : '暂无文档'}
              </h3>
              <p className="text-muted-foreground">
                {searchQuery ? '尝试使用不同的搜索关键词' : '开始上传您的第一个文档'}
              </p>
            </div>
          ) : (
            <div style={{ maxHeight, overflowY: 'auto' }}>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>文档名称</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>分类</TableHead>
                    <TableHead>大小</TableHead>
                    <TableHead>上传时间</TableHead>
                    {showActions && <TableHead className="text-right">操作</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredDocuments.map((document) => (
                    <TableRow
                      key={document.external_id}
                      className="cursor-pointer hover:bg-accent/50"
                      onClick={() => handlePreview(document)}
                    >
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">
                              {document.filename || document.metadata?.title || '未命名文档'}
                            </p>
                            {document.metadata?.title && document.filename && (
                              <p className="text-xs text-muted-foreground">
                                {document.filename}
                              </p>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={`${getStatusColor(document.system_metadata?.status)} ${
                          document.system_metadata?.status === 'processing' ? 'animate-pulse' : ''
                        }`}>
                          {document.system_metadata?.status === 'processing' && (
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          )}
                          {getStatusLabel(document.system_metadata?.status)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {document.metadata?.category ? (
                          <Badge variant="outline">
                            {document.metadata.category}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {formatFileSize(document.metadata?.fileSize)}
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {formatDate(document.system_metadata?.created_at)}
                      </TableCell>
                      {showActions && (
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownload(document);
                              }}
                              title="下载文档"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDelete(document);
                              }}
                              disabled={deleteDocument.isPending}
                              title="删除文档"
                            >
                              {deleteDocument.isPending ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Trash2 className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 文档预览对话框 */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedDocument?.filename || selectedDocument?.metadata?.title || '文档预览'}
            </DialogTitle>
            <DialogDescription>
              文档ID: {selectedDocument?.external_id}
            </DialogDescription>
          </DialogHeader>

          {selectedDocument && (
            <DocumentPreview document={selectedDocument} />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

// 渲染文档内容的辅助函数
function renderDocumentContent(content: string, contentType?: string) {
  // 限制显示长度，避免过长的内容
  const maxLength = 5000;
  const displayContent = content.length > maxLength
    ? content.substring(0, maxLength) + '\n\n... (内容已截断，显示前5000字符)'
    : content;

  // 根据内容类型选择不同的渲染方式
  if (contentType?.includes('json')) {
    try {
      const parsed = JSON.parse(displayContent);
      return (
        <pre className="text-sm whitespace-pre-wrap font-mono leading-relaxed bg-gray-50 p-3 rounded">
          {JSON.stringify(parsed, null, 2)}
        </pre>
      );
    } catch {
      // 如果JSON解析失败，按普通文本处理
    }
  }

  if (contentType?.includes('code') || contentType?.includes('javascript') || contentType?.includes('python')) {
    return (
      <pre className="text-sm whitespace-pre-wrap font-mono leading-relaxed bg-gray-50 p-3 rounded border">
        <code>{displayContent}</code>
      </pre>
    );
  }

  // 默认文本显示
  return (
    <div className="text-sm leading-relaxed">
      {displayContent.split('\n').map((line, index) => (
        <p key={index} className={line.trim() === '' ? 'h-4' : ''}>
          {line || '\u00A0'}
        </p>
      ))}
    </div>
  );
}

// 文档预览组件
function DocumentPreview({ document }: { document: Document }) {
  const { data: fullDocument, isLoading: documentLoading, error: documentError } = useDocument(document.external_id);
  const { data: documentContent, isLoading: contentLoading, error: contentError } = useDocumentContent(document.external_id);

  const isLoading = documentLoading || contentLoading;
  const error = documentError || contentError;

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <p>加载文档内容...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
        <p className="text-red-600">加载失败: {error.message}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 文档信息 */}
      <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
        <div>
          <h4 className="font-medium mb-2">基本信息</h4>
          <div className="space-y-1 text-sm">
            <p><span className="text-muted-foreground">文件名:</span> {document.filename || 'N/A'}</p>
            <p><span className="text-muted-foreground">类型:</span> {document.content_type}</p>
            <p><span className="text-muted-foreground">状态:</span> {document.system_metadata?.status || 'completed'}</p>
          </div>
        </div>
        <div>
          <h4 className="font-medium mb-2">元数据</h4>
          <div className="space-y-1 text-sm">
            {document.metadata?.title && (
              <p><span className="text-muted-foreground">标题:</span> {document.metadata.title}</p>
            )}
            {document.metadata?.category && (
              <p><span className="text-muted-foreground">分类:</span> {document.metadata.category}</p>
            )}
            {document.metadata?.tags && document.metadata.tags.length > 0 && (
              <p><span className="text-muted-foreground">标签:</span> {document.metadata.tags.join(', ')}</p>
            )}
          </div>
        </div>
      </div>

      {/* 文档内容预览 */}
      <div>
        <h4 className="font-medium mb-2">内容预览</h4>
        <div className="p-4 border rounded-lg bg-background max-h-96 overflow-y-auto">
          {contentLoading ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span className="text-sm text-muted-foreground">加载内容中...</span>
            </div>
          ) : contentError ? (
            <div className="text-center py-4">
              <AlertTriangle className="h-4 w-4 text-red-500 mx-auto mb-2" />
              <p className="text-sm text-red-600">内容加载失败</p>
            </div>
          ) : documentContent ? (
            <div className="space-y-2">
              {renderDocumentContent(documentContent, document.content_type)}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">
              暂无内容预览
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

export default DocumentList;
