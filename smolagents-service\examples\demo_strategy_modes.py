#!/usr/bin/env python3
"""
演示策略模式选择功能
展示4个策略 vs 7个策略的区别
"""

import asyncio
from market_analysis_demo import MarketAnalysisDemo, AnalysisContext

async def demo_strategy_modes():
    """演示不同策略模式"""
    print("🌟 智能市场分析 - 策略模式演示")
    print("=" * 60)
    print()
    
    # 创建测试上下文
    search_term = "沙滩包2024亚洲市场销售情况分析及2025市场增长预期"
    
    async with MarketAnalysisDemo() as analyzer:
        # 解析搜索词
        context = analyzer.parse_search_term(search_term)
        
        print("📊 策略模式对比:")
        print()
        
        # 展示优化模式（4个策略）
        print("🚀 优化模式 (推荐):")
        print("   - 搜索策略数量: 4个")
        print("   - 预计执行时间: 8-12分钟")
        print("   - 特点: 快速稳定，合并相关搜索")
        queries_optimized = analyzer.generate_search_queries(context, "optimized")
        print()
        
        # 展示全面模式（7个策略）
        print("🔍 全面模式 (详细):")
        print("   - 搜索策略数量: 7个")
        print("   - 预计执行时间: 15-20分钟")
        print("   - 特点: 详细全面，独立搜索各维度")
        queries_comprehensive = analyzer.generate_search_queries(context, "comprehensive")
        print()
        
        print("💡 建议:")
        print("   - 首次使用或快速分析: 选择优化模式")
        print("   - 深度研究或详细报告: 选择全面模式")
        print("   - 网络不稳定时: 建议使用优化模式")
        print()
        
        print("🎯 策略对比总结:")
        print(f"   优化模式: {len(queries_optimized)} 个策略")
        print(f"   全面模式: {len(queries_comprehensive)} 个策略")
        print(f"   策略减少: {((len(queries_comprehensive) - len(queries_optimized)) / len(queries_comprehensive) * 100):.0f}%")

if __name__ == "__main__":
    asyncio.run(demo_strategy_modes())
