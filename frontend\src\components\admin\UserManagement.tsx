/**
 * 用户管理组件
 */
import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Loader2, Users, Shield, Mail, Calendar, Settings, Trash2 } from 'lucide-react'
import { useAuthStore } from '@/store/auth'
import { CreateUserDialog } from './CreateUserDialog'
import { EditUserDialog } from './EditUserDialog'
import { UserPermissionsDialog } from './UserPermissionsDialog'
import { UserRoleAssignDialog } from './UserRoleAssignDialog'
import { DeleteUserConfirmDialog } from './DeleteUserConfirmDialog'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface AdminUser {
  id: number
  username: string
  email: string
  full_name?: string
  is_active: boolean
  is_superuser: boolean
  created_at: string
  roles: string[]
}

export const UserManagement: React.FC = () => {
  const { token } = useAuthStore()
  const [users, setUsers] = useState<AdminUser[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showPermissionsDialog, setShowPermissionsDialog] = useState(false)
  const [showRoleAssignDialog, setShowRoleAssignDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null)

  // 获取用户列表
  const fetchUsers = async () => {
    if (!token) return

    try {
      const response = await fetch(`${API_BASE_URL}/admin/users`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data: AdminUser[] = await response.json()
        setUsers(data)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '获取用户列表失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [token])

  // 处理创建用户成功
  const handleCreateSuccess = () => {
    setSuccess('用户创建成功')
    fetchUsers() // 刷新用户列表
    // 3秒后清除成功消息
    setTimeout(() => setSuccess(''), 3000)
  }

  // 处理编辑用户成功
  const handleEditSuccess = () => {
    setSuccess('用户信息更新成功')
    fetchUsers() // 刷新用户列表
    // 3秒后清除成功消息
    setTimeout(() => setSuccess(''), 3000)
  }

  // 打开编辑对话框
  const handleEditUser = (user: AdminUser) => {
    setSelectedUser(user)
    setShowEditDialog(true)
  }

  // 打开权限查看对话框
  const handleViewPermissions = (user: AdminUser) => {
    setSelectedUser(user)
    setShowPermissionsDialog(true)
  }

  // 打开角色分配对话框
  const handleManageRoles = (user: AdminUser) => {
    setSelectedUser(user)
    setShowRoleAssignDialog(true)
  }

  // 处理角色分配成功
  const handleRoleAssignSuccess = () => {
    setSuccess('用户角色分配成功')
    fetchUsers() // 刷新用户列表
    // 3秒后清除成功消息
    setTimeout(() => setSuccess(''), 3000)
  }

  // 打开删除确认对话框
  const handleDeleteUser = (user: AdminUser) => {
    setSelectedUser(user)
    setShowDeleteDialog(true)
  }

  // 处理删除用户成功
  const handleDeleteSuccess = () => {
    setSuccess('用户删除成功')
    fetchUsers() // 刷新用户列表
    // 3秒后清除成功消息
    setTimeout(() => setSuccess(''), 3000)
  }

  // 获取用户显示名称的首字母
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // 获取角色颜色
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'superadmin':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'admin':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'moderator':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'user':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 获取角色中文名
  const getRoleName = (role: string) => {
    switch (role) {
      case 'superadmin':
        return '超级管理员'
      case 'admin':
        return '管理员'
      case 'moderator':
        return '版主'
      case 'user':
        return '普通用户'
      case 'guest':
        return '访客'
      default:
        return role
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>加载中...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="mr-2 h-5 w-5" />
          用户管理
        </CardTitle>
        <CardDescription>
          管理系统用户和角色分配
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        {success && (
          <Alert className="mb-4">
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-4">
          {users.map((user) => {
            const displayName = user.full_name || user.username
            const initials = getInitials(displayName)

            return (
              <Card key={user.id} className="border">
                <CardContent className="pt-6">
                  <div className="flex items-start space-x-4">
                    <Avatar className="h-12 w-12">
                      <AvatarFallback className="text-sm">{initials}</AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="text-lg font-semibold truncate">{displayName}</h3>
                        <Badge variant={user.is_active ? "default" : "secondary"}>
                          {user.is_active ? "正常" : "禁用"}
                        </Badge>
                        {user.is_superuser && (
                          <Badge variant="destructive">
                            <Shield className="h-3 w-3 mr-1" />
                            超级用户
                          </Badge>
                        )}
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Mail className="mr-2 h-4 w-4" />
                          {user.email}
                        </div>
                        
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Calendar className="mr-2 h-4 w-4" />
                          注册时间: {formatDate(user.created_at)}
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-muted-foreground">角色:</span>
                          {user.roles.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {user.roles.map((role) => (
                                <Badge 
                                  key={role} 
                                  variant="outline" 
                                  className={getRoleColor(role)}
                                >
                                  {getRoleName(role)}
                                </Badge>
                              ))}
                            </div>
                          ) : (
                            <Badge variant="outline" className="text-gray-500">
                              无角色
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewPermissions(user)}
                      >
                        <Shield className="h-4 w-4 mr-1" />
                        权限
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditUser(user)}
                      >
                        <Settings className="h-4 w-4 mr-1" />
                        编辑
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteUser(user)}
                        className={user.is_superuser ? 'opacity-50 cursor-not-allowed' : ''}
                        disabled={user.is_superuser}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        删除
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {users.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            暂无用户数据
          </div>
        )}

        <div className="mt-6 pt-6 border-t">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              共 {users.length} 个用户
            </div>
            <Button
              onClick={() => setShowCreateDialog(true)}
            >
              <Users className="h-4 w-4 mr-2" />
              创建用户
            </Button>
          </div>
        </div>
      </CardContent>

      {/* 创建用户对话框 */}
      <CreateUserDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={handleCreateSuccess}
      />

      {/* 编辑用户对话框 */}
      <EditUserDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        onSuccess={handleEditSuccess}
        user={selectedUser}
      />

      {/* 用户权限查看对话框 */}
      <UserPermissionsDialog
        open={showPermissionsDialog}
        onOpenChange={setShowPermissionsDialog}
        user={selectedUser}
        onManageRoles={handleManageRoles}
      />

      {/* 用户角色分配对话框 */}
      <UserRoleAssignDialog
        open={showRoleAssignDialog}
        onOpenChange={setShowRoleAssignDialog}
        user={selectedUser}
        onSuccess={handleRoleAssignSuccess}
      />

      {/* 删除用户确认对话框 */}
      <DeleteUserConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onSuccess={handleDeleteSuccess}
        user={selectedUser}
      />
    </Card>
  )
}
