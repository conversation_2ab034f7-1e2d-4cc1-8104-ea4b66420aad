import { useEffect, useState } from "react"
import { Brain, Search, FileText, MessageSquare, Activity } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useKnowledgeBaseStore } from "@/store/knowledgeBase"
import { KnowledgeBaseErrorBoundary } from "@/components/knowledge-base/ErrorBoundary"
import { KnowledgeBaseThemeProvider } from "@/components/knowledge-base/ThemeProvider"
import SearchResultModal from "@/components/knowledge-base/SearchResultModal"
import TemplateStatusIndicator from "@/components/template/TemplateStatusIndicator"
import type { SearchResult } from "@/types/morphik"
import {
  LazyDocumentUploadWrapper,
  LazySmartSearchWrapper,
  LazyDocumentListWrapper,
  LazyChatInterfaceWrapper,
  LazyMorphikConnectionTestWrapper,
  LazyGraphManagerWrapper,
  preloadComponentsByTab
} from "@/components/knowledge-base/LazyComponents"

// 图谱页面内容组件
function GraphPageContent() {
  return (
    <div className="w-full">
      {/* 知识图谱网格布局 */}
      <LazyGraphManagerWrapper
        mode="grid" // 新增网格模式
        onGraphSelect={(graphName: string) => {
          console.log('Graph selected:', graphName);
        }}
      />
    </div>
  );
}

function KnowledgeBasePageContent() {
  const { activeTab, setActiveTab } = useKnowledgeBaseStore();
  const [selectedSearchResult, setSelectedSearchResult] = useState<SearchResult | null>(null);
  const [showSearchResultModal, setShowSearchResultModal] = useState(false);

  // 预加载组件
  useEffect(() => {
    preloadComponentsByTab(activeTab);
  }, [activeTab]);

  // 处理标签页切换
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    preloadComponentsByTab(tab);
  };

  // 处理搜索结果选择
  const handleSearchResultSelect = (result: SearchResult) => {
    setSelectedSearchResult(result);
    setShowSearchResultModal(true);
  };

  // 关闭搜索结果模态框
  const handleCloseSearchResultModal = () => {
    setShowSearchResultModal(false);
    setSelectedSearchResult(null);
  };

  return (
    <div className="flex-1 space-y-4 p-8 pt-6 knowledge-base-container">
      {/* 页面头部 */}
      <div className="flex items-center justify-between space-y-2">
        <div className="flex items-center space-x-2">
          <Brain className="h-8 w-8 text-primary" />
          <h2 className="text-3xl font-bold tracking-tight">智能知识库</h2>
        </div>
      </div>

      {/* 标签页导航 */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            文档管理
          </TabsTrigger>
          <TabsTrigger value="search" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            智能搜索
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            AI问答
          </TabsTrigger>
          <TabsTrigger value="graph" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            知识图谱
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            系统状态
          </TabsTrigger>
        </TabsList>

        {/* 话术模板状态指示器 */}
        <TemplateStatusIndicator
          onManageClick={() => window.location.href = '/market-analysis'}
          className="mb-4"
        />

        <TabsContent value="documents" className="space-y-4 kb-fade-in">
          <KnowledgeBaseErrorBoundary>
            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <LazyDocumentUploadWrapper
                  onUploadComplete={(documentIds: string[]) => {
                    console.log('Documents uploaded:', documentIds);
                    // 刷新统计信息
                    // TODO: 更新stats
                  }}
                  maxFiles={5}
                  allowMultiple={true}
                />
              </div>
              <div>
                <LazyDocumentListWrapper
                  onDocumentSelect={(document: any) => {
                    console.log('Document selected:', document);
                    // 可以在这里添加文档选择逻辑
                  }}
                  showActions={true}
                  maxHeight="600px"
                />
              </div>
            </div>
          </KnowledgeBaseErrorBoundary>
        </TabsContent>

        <TabsContent value="search" className="space-y-4 kb-fade-in">
          <KnowledgeBaseErrorBoundary>
            <LazySmartSearchWrapper
              onResultSelect={handleSearchResultSelect}
              showFilters={true}
            />
          </KnowledgeBaseErrorBoundary>
        </TabsContent>

        <TabsContent value="chat" className="space-y-4 kb-fade-in">
          <KnowledgeBaseErrorBoundary>
            <LazyChatInterfaceWrapper
              onSourceSelect={(source: any) => {
                console.log('Source selected from chat:', source);
                // 可以在这里添加来源选择逻辑，比如跳转到文档详情
              }}
              maxMessages={50}
              showSources={true}
            />
          </KnowledgeBaseErrorBoundary>
        </TabsContent>

        <TabsContent value="graph" className="space-y-4 kb-fade-in">
          <KnowledgeBaseErrorBoundary>
            <GraphPageContent />
          </KnowledgeBaseErrorBoundary>
        </TabsContent>

        <TabsContent value="system" className="space-y-4 kb-fade-in">
          <KnowledgeBaseErrorBoundary>
            <div className="space-y-6">
              <LazyMorphikConnectionTestWrapper />
            </div>
          </KnowledgeBaseErrorBoundary>
        </TabsContent>
      </Tabs>

      {/* 搜索结果详情模态框 */}
      <SearchResultModal
        result={selectedSearchResult}
        isOpen={showSearchResultModal}
        onClose={handleCloseSearchResultModal}
      />
    </div>
  );
}

// 主要导出组件，包装了主题提供者和错误边界
export default function KnowledgeBasePage() {
  return (
    <KnowledgeBaseThemeProvider>
      <KnowledgeBaseErrorBoundary
        onError={(error, errorInfo) => {
          console.error('Knowledge Base Page Error:', error, errorInfo);
          // 这里可以添加错误上报逻辑
        }}
      >
        <KnowledgeBasePageContent />
      </KnowledgeBaseErrorBoundary>
    </KnowledgeBaseThemeProvider>
  );
}
