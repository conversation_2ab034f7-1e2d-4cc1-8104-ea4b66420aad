/**
 * 编辑用户对话框组件
 */
import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-react'
import { useAuthStore } from '@/store/auth'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface EditUserData {
  username: string
  email: string
  full_name: string
  phone: string
  is_active: boolean
}

interface AdminUser {
  id: number
  username: string
  email: string
  full_name?: string
  phone?: string
  is_active: boolean
  is_superuser: boolean
  created_at: string
  roles: string[]
}

interface EditUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
  user: AdminUser | null
}

export const EditUserDialog: React.FC<EditUserDialogProps> = ({
  open,
  onOpenChange,
  onSuccess,
  user,
}) => {
  const { token } = useAuthStore()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const [formData, setFormData] = useState<EditUserData>({
    username: '',
    email: '',
    full_name: '',
    phone: '',
    is_active: true,
  })

  // 当用户数据变化时，更新表单数据
  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username,
        email: user.email,
        full_name: user.full_name || '',
        phone: user.phone || '',
        is_active: user.is_active,
      })
    }
  }, [user])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
    // 清除错误信息
    if (error) setError('')
  }

  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      is_active: checked,
    }))
  }

  const validateForm = (): boolean => {
    if (!formData.username.trim()) {
      setError('请输入用户名')
      return false
    }
    if (formData.username.length < 3 || formData.username.length > 50) {
      setError('用户名长度必须在3-50字符之间')
      return false
    }
    if (!formData.email.trim()) {
      setError('请输入邮箱地址')
      return false
    }
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!validateForm()) {
      return
    }

    if (!token || !user) {
      setError('未授权，请重新登录')
      return
    }

    setIsLoading(true)

    try {
      // 只发送有变化的字段
      const updateData: Partial<EditUserData> = {}

      if (formData.username !== user.username) {
        updateData.username = formData.username
      }
      if (formData.email !== user.email) {
        updateData.email = formData.email
      }
      if (formData.full_name !== (user.full_name || '')) {
        updateData.full_name = formData.full_name || undefined
      }
      if (formData.phone !== (user.phone || '')) {
        updateData.phone = formData.phone || undefined
      }
      if (formData.is_active !== user.is_active) {
        updateData.is_active = formData.is_active
      }

      // 如果没有变化，直接关闭对话框
      if (Object.keys(updateData).length === 0) {
        onOpenChange(false)
        return
      }

      const response = await fetch(`${API_BASE_URL}/admin/users/${user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(updateData),
      })

      if (response.ok) {
        onSuccess()
        onOpenChange(false)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '更新用户失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    if (!isLoading) {
      setError('')
      onOpenChange(false)
    }
  }

  if (!user) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={handleClose} modal={true}>
      <DialogContent
        className="sm:max-w-[500px]"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>编辑用户</DialogTitle>
          <DialogDescription>
            修改用户 "{user.username}" 的信息
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4 py-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-username">用户名 *</Label>
                <Input
                  id="edit-username"
                  name="username"
                  type="text"
                  placeholder="3-50字符"
                  value={formData.username}
                  onChange={handleInputChange}
                  disabled={isLoading}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-email">邮箱地址 *</Label>
                <Input
                  id="edit-email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleInputChange}
                  disabled={isLoading}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-full_name">全名</Label>
                <Input
                  id="edit-full_name"
                  name="full_name"
                  type="text"
                  placeholder="用户的全名"
                  value={formData.full_name}
                  onChange={handleInputChange}
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-phone">电话号码</Label>
                <Input
                  id="edit-phone"
                  name="phone"
                  type="tel"
                  placeholder="联系电话"
                  value={formData.phone}
                  onChange={handleInputChange}
                  disabled={isLoading}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="edit-is_active"
                checked={formData.is_active}
                onCheckedChange={handleSwitchChange}
                disabled={isLoading}
              />
              <Label htmlFor="edit-is_active">激活用户</Label>
            </div>

            {user.is_superuser && (
              <Alert>
                <AlertDescription>
                  注意：此用户是超级用户，请谨慎修改其信息。
                </AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              保存更改
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
