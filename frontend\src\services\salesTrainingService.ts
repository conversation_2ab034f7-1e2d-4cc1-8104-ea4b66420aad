/**
 * 销冠实战训练数据服务
 * 提供国家、产品、职位的CRUD操作
 */

import type { Customer, CustomerCreate, CustomerUpdate, Country, Product, TrainingScenarioData, TrainingScenarioCreate, TrainingScenarioUpdate } from '@/types/salesTraining'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001'
const SALES_TRAINING_API = `${API_BASE_URL}/api/sales-training`

// 通用请求配置
const defaultHeaders = {
  'Content-Type': 'application/json',
}

// 通用错误处理
class SalesTrainingError extends Error {
  constructor(message: string, public status?: number) {
    super(message)
    this.name = 'SalesTrainingError'
  }
}

// 通用请求方法
async function request<T>(url: string, options: RequestInit = {}): Promise<T> {
  try {
    const response = await fetch(url, {
      headers: defaultHeaders,
      ...options,
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new SalesTrainingError(
        errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
        response.status
      )
    }

    return await response.json()
  } catch (error) {
    if (error instanceof SalesTrainingError) {
      throw error
    }
    throw new SalesTrainingError(`网络请求失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// 客户相关API
export const customerAPI = {
  // 获取所有客户
  async getAll(includeInactive = false): Promise<Customer[]> {
    const url = `${SALES_TRAINING_API}/customers?include_inactive=${includeInactive}`
    return request<Customer[]>(url)
  },

  // 搜索客户
  async search(params: {
    name?: string
    company?: string
    industry?: string
    level?: string
    region?: string
    includeInactive?: boolean
  }): Promise<Customer[]> {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        searchParams.append(key, String(value))
      }
    })
    const url = `${SALES_TRAINING_API}/customers?${searchParams.toString()}`
    return request<Customer[]>(url)
  },

  // 根据ID获取客户
  async getById(id: number): Promise<Customer> {
    return request<Customer>(`${SALES_TRAINING_API}/customers/${id}`)
  },

  // 创建客户
  async create(data: CustomerCreate): Promise<Customer> {
    return request<Customer>(`${SALES_TRAINING_API}/customers`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },

  // 更新客户
  async update(id: number, data: CustomerUpdate): Promise<Customer> {
    return request<Customer>(`${SALES_TRAINING_API}/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  },

  // 删除客户（软删除）
  async delete(id: number): Promise<{ message: string }> {
    return request<{ message: string }>(`${SALES_TRAINING_API}/customers/${id}`, {
      method: 'DELETE',
    })
  },
}

// 国家相关API
export const countryAPI = {
  // 获取所有国家
  async getAll(includeInactive = false): Promise<Country[]> {
    const url = `${SALES_TRAINING_API}/countries?include_inactive=${includeInactive}`
    return request<Country[]>(url)
  },

  // 创建国家
  async create(data: Omit<Country, 'id'>): Promise<Country> {
    return request<Country>(`${SALES_TRAINING_API}/countries`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },

  // 更新国家
  async update(id: number, data: Partial<Omit<Country, 'id'>>): Promise<Country> {
    return request<Country>(`${SALES_TRAINING_API}/countries/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  },

  // 删除国家
  async delete(id: number, hardDelete = true): Promise<{ message: string }> {
    const url = `${SALES_TRAINING_API}/countries/${id}?hard_delete=${hardDelete}`
    return request<{ message: string }>(url, {
      method: 'DELETE',
    })
  },
}

// 产品相关API
export const productAPI = {
  // 获取所有产品
  async getAll(includeInactive = false): Promise<Product[]> {
    const url = `${SALES_TRAINING_API}/products?include_inactive=${includeInactive}`
    return request<Product[]>(url)
  },

  // 创建产品
  async create(data: Omit<Product, 'id'>): Promise<Product> {
    return request<Product>(`${SALES_TRAINING_API}/products`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },

  // 更新产品
  async update(id: number, data: Partial<Omit<Product, 'id'>>): Promise<Product> {
    return request<Product>(`${SALES_TRAINING_API}/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  },

  // 删除产品
  async delete(id: number, hardDelete = true): Promise<{ success: boolean; message: string }> {
    const url = `${SALES_TRAINING_API}/products/${id}?hard_delete=${hardDelete}`
    return request<{ success: boolean; message: string }>(url, {
      method: 'DELETE',
    })
  },
}



// 批量操作API
export const batchAPI = {
  // 批量获取所有数据
  async getAllData(): Promise<{
    customers: Customer[]
    countries: Country[]
    products: Product[]
  }> {
    try {
      const [customers, countries, products] = await Promise.all([
        customerAPI.getAll(true), // 包含非活跃的数据
        countryAPI.getAll(true), // 包含非活跃的数据
        productAPI.getAll(true), // 包含非活跃的数据
      ])

      return { customers, countries, products }
    } catch (error) {
      throw new SalesTrainingError(`批量获取数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  },

  // 检查数据完整性
  async validateData(): Promise<{
    isValid: boolean
    issues: string[]
  }> {
    try {
      const { countries, products, positions } = await this.getAllData()
      const issues: string[] = []

      if (countries.length === 0) {
        issues.push('缺少国家选项')
      }
      if (products.length === 0) {
        issues.push('缺少产品选项')
      }
      if (positions.length === 0) {
        issues.push('缺少职位选项')
      }

      return {
        isValid: issues.length === 0,
        issues,
      }
    } catch (error) {
      return {
        isValid: false,
        issues: [`数据验证失败: ${error instanceof Error ? error.message : '未知错误'}`],
      }
    }
  },
}

// 导出错误类型
// 训练场景相关API
export const scenarioAPI = {
  // 获取所有训练场景
  async getAll(params?: { category?: string; difficulty?: string }): Promise<TrainingScenarioData[]> {
    const searchParams = new URLSearchParams()
    if (params?.category) searchParams.append('category', params.category)
    if (params?.difficulty) searchParams.append('difficulty', params.difficulty)

    const url = `${SALES_TRAINING_API}/scenarios${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
    return request<TrainingScenarioData[]>(url)
  },

  // 根据ID获取训练场景
  async getById(id: number): Promise<TrainingScenarioData> {
    return request<TrainingScenarioData>(`${SALES_TRAINING_API}/scenarios/${id}`)
  },

  // 创建训练场景
  async create(data: TrainingScenarioCreate): Promise<TrainingScenarioData> {
    return request<TrainingScenarioData>(`${SALES_TRAINING_API}/scenarios`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  },

  // 更新训练场景
  async update(id: number, data: TrainingScenarioUpdate): Promise<TrainingScenarioData> {
    return request<TrainingScenarioData>(`${SALES_TRAINING_API}/scenarios/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  },

  // 删除训练场景
  async delete(id: number, hardDelete = false): Promise<{ message: string }> {
    const url = `${SALES_TRAINING_API}/scenarios/${id}${hardDelete ? '?hard_delete=true' : ''}`
    return request<{ message: string }>(url, {
      method: 'DELETE',
    })
  },
}

export { SalesTrainingError }

// 默认导出所有API
export default {
  customer: customerAPI,
  country: countryAPI,
  product: productAPI,
  scenario: scenarioAPI,
  batch: batchAPI,
}
