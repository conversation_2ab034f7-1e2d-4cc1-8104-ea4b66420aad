"""
任务管理Pydantic模式
定义API请求和响应的数据结构
"""
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from datetime import datetime
from app.models.task import TaskStatus, TaskPriority


class TaskBase(BaseModel):
    """任务基础模式"""
    title: str = Field(..., min_length=1, max_length=200, description="任务标题")
    description: Optional[str] = Field(None, description="任务描述")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM, description="任务优先级")
    assignee: Optional[str] = Field(None, max_length=100, description="负责人")
    is_active: bool = Field(default=True, description="是否激活")


class TaskCreate(TaskBase):
    """创建任务的请求模式"""
    pass


class TaskUpdate(BaseModel):
    """更新任务的请求模式"""
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="任务标题")
    description: Optional[str] = Field(None, description="任务描述")
    status: Optional[TaskStatus] = Field(None, description="任务状态")
    priority: Optional[TaskPriority] = Field(None, description="任务优先级")
    assignee: Optional[str] = Field(None, max_length=100, description="负责人")
    is_active: Optional[bool] = Field(None, description="是否激活")


class TaskInDB(TaskBase):
    """数据库中的任务模式"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


class Task(TaskInDB):
    """任务响应模式"""
    pass


class TaskList(BaseModel):
    """任务列表响应模式"""
    items: list[Task]
    total: int
    page: int
    size: int
    pages: int


class TaskStats(BaseModel):
    """任务统计模式"""
    total: int
    pending: int
    in_progress: int
    completed: int
    cancelled: int
    by_priority: dict[str, int]
