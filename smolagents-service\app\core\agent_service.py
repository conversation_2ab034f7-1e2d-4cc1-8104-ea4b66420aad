"""
SmoLAgents 核心服务
基于官方 SmoLAgents 库实现的智能代理服务
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List, AsyncGenerator
from datetime import datetime

from smolagents import CodeAgent, DuckDuckGoSearchTool, LiteLLMModel
from smolagents.agents import ActionStep

from app.config import Settings
from app.core.redis_client import RedisClient
from app.core.config_validator import get_task_config, TaskType
from app.models.schemas import (
    AgentRequest, AgentResponse, AgentStreamResponse,
    TaskRequest, TaskResponse, TaskStatus
)
from app.utils.exceptions import AgentServiceError, ModelNotAvailableError


class AgentService:
    """SmoLAgents 智能代理服务"""
    
    def __init__(self, settings: Settings, redis_client: RedisClient):
        self.settings = settings
        self.redis_client = redis_client
        self.logger = logging.getLogger(__name__)
        
        # 代理实例
        self.code_agent: Optional[CodeAgent] = None
        self.model: Optional[LiteLLMModel] = None
        
        # 工具实例
        self.search_tool: Optional[DuckDuckGoSearchTool] = None
        
        # 任务管理
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        
    async def initialize(self):
        """初始化 Agent 服务"""
        try:
            self.logger.info("🔧 初始化 SmoLAgents 服务...")
            
            # 初始化模型
            await self._initialize_model()
            
            # 初始化工具
            await self._initialize_tools()
            
            # 初始化代理
            await self._initialize_agents()
            
            # 验证服务可用性
            await self._verify_service()
            
            self.logger.info("✅ SmoLAgents 服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ SmoLAgents 服务初始化失败: {e}")
            raise AgentServiceError(f"服务初始化失败: {e}")
    
    async def _initialize_model(self):
        """初始化模型 - 支持Ollama和Qwen双模型切换"""
        try:
            self.logger.info("🤖 初始化模型...")
            self.logger.info(f"🔧 当前模型提供商配置: {self.settings.model_provider}")

            # 方式1: 通过配置文件切换（推荐）
            if self.settings.model_provider == "qwen":
                # 使用Qwen在线模型
                from smolagents import LiteLLMModel
                self.model = LiteLLMModel(
                    model_id=f"openai/{self.settings.qwen_model_id}",
                    api_key=self.settings.qwen_api_key,
                    api_base=self.settings.qwen_api_base,
                    temperature=self.settings.qwen_temperature,
                    max_tokens=self.settings.qwen_max_tokens
                )
                self.logger.info(f"✅ Qwen在线模型初始化成功: {self.settings.qwen_model_id}")
                
            else:
                # 使用Ollama本地模型（默认）
                from smolagents import LiteLLMModel
                self.model = LiteLLMModel(
                    model_id=f"ollama/{self.settings.ollama_model}",
                    api_base=self.settings.ollama_base_url,
                    timeout=self.settings.ollama_timeout
                )
                self.logger.info(f"✅ Ollama本地模型初始化成功: {self.settings.ollama_model}")
                           
        except Exception as e:
            self.logger.error(f"❌ 模型初始化失败: {e}")
            raise AgentServiceError(f"模型初始化失败: {e}")
    
    async def _initialize_tools(self):
        """初始化工具"""
        try:
            self.logger.info("🔧 初始化工具...")
            
            # 初始化网络搜索工具
            if self.settings.enable_web_search:
                self.search_tool = DuckDuckGoSearchTool()
                self.logger.info("✅ 网络搜索工具初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ 工具初始化失败: {e}")
            raise AgentServiceError(f"工具初始化失败: {e}")
    
    async def _initialize_agents(self):
        """初始化智能代理"""
        try:
            self.logger.info("🤖 初始化智能代理...")
            
            # 准备工具列表
            tools = []
            if self.search_tool:
                tools.append(self.search_tool)
            
            # 使用统一配置管理器获取代理默认配置
            agent_config = get_task_config(TaskType.AGENT_DEFAULT)

            # 创建 CodeAgent，添加中文输出指令和代码执行规范
            chinese_instructions = """你是一个专业的市场分析助手。请始终使用中文回答所有问题和提供所有分析内容。
无论输入是什么语言，你的回答都必须是中文。
在进行市场分析时，请提供准确、详细、有价值的中文分析报告。

重要的代码执行规范：
1. 在执行任何代码之前，请仔细检查语法的正确性
2. 避免使用复杂的代码结构，优先使用简单直接的方法
3. 如果遇到代码执行错误，请提供文字分析而不是继续尝试代码
4. 专注于使用搜索工具获取信息，而不是依赖代码执行
5. 当无法确定代码正确性时，请直接提供基于搜索结果的分析"""

            self.code_agent = CodeAgent(
                tools=tools,
                model=self.model,
                max_steps=agent_config.max_steps,
                verbosity_level=1,  # 设置详细程度
                stream_outputs=False,  # 暂时不启用流式输出
                instructions=chinese_instructions  # 添加中文输出指令
            )
            
            self.logger.info("✅ 智能代理初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ 智能代理初始化失败: {e}")
            raise AgentServiceError(f"智能代理初始化失败: {e}")
    
    async def _check_model_availability(self):
        """检查模型可用性"""
        try:
            # 这里我们通过直接访问 Ollama API 来检查模型
            import httpx
            
            async with httpx.AsyncClient(timeout=self.settings.ollama_timeout) as client:
                # 检查 Ollama 服务是否可用
                response = await client.get(f"{self.settings.ollama_base_url}/api/tags")
                if response.status_code != 200:
                    raise Exception(f"Ollama 服务不可用: {response.status_code}")
                
                # 检查指定模型是否存在
                models_data = response.json()
                available_models = [model["name"] for model in models_data.get("models", [])]
                
                model_found = any(
                    self.settings.ollama_model in model_name or 
                    self.settings.ollama_model.split(":")[0] in model_name
                    for model_name in available_models
                )
                
                if not model_found:
                    raise Exception(f"模型 {self.settings.ollama_model} 未找到。可用模型: {available_models}")
                
                self.logger.info(f"✅ 模型 {self.settings.ollama_model} 可用")
                
        except Exception as e:
            self.logger.error(f"❌ 模型可用性检查失败: {e}")
            raise ModelNotAvailableError(f"模型不可用: {e}")
    
    async def _verify_service(self):
        """验证服务可用性"""
        try:
            self.logger.info("🔍 验证服务可用性...")
            
            # 执行简单的测试查询
            test_query = "计算 2 + 2 的结果"
            result = await self._run_agent_task(test_query)
            
            if result and "4" in str(result):
                self.logger.info("✅ 服务验证成功")
            else:
                self.logger.warning(f"⚠️ 服务验证结果异常: {result}")
                
        except Exception as e:
            self.logger.error(f"❌ 服务验证失败: {e}")
            # 不抛出异常，允许服务继续运行
    
    async def _run_agent_task(self, query: str, task_id: str = None, **kwargs) -> str:
        """运行代理任务"""
        try:
            # 使用统一配置管理器获取默认步骤数
            if 'max_steps' not in kwargs or kwargs['max_steps'] is None:
                agent_config = get_task_config(TaskType.AGENT_DEFAULT)
                kwargs['max_steps'] = agent_config.max_steps

            max_steps = kwargs['max_steps']

            self.logger.info(f"🚀 开始执行代理任务，任务ID: {task_id}, 最大步骤数: {max_steps}")

            # 检查任务是否已被取消
            if task_id and task_id in self.active_tasks:
                if self.active_tasks[task_id]["status"] == TaskStatus.CANCELLED:
                    self.logger.info(f"🚫 任务已被取消，停止执行: {task_id}")
                    return "任务已被用户取消"

            # 在线程池中运行同步的 agent.run 方法
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: self._run_agent_with_cancellation_check(query, task_id, **kwargs)
            )

            # 检查结果是否为空或异常
            if not result:
                self.logger.warning("⚠️ 代理返回空结果，使用默认响应")
                return "抱歉，AI分析过程中遇到了问题，请稍后重试。"

            # 确保result是字符串类型
            result_str = str(result)
            if result_str.strip() == "":
                self.logger.warning("⚠️ 代理返回空字符串，使用默认响应")
                return "抱歉，AI分析过程中遇到了问题，请稍后重试。"

            # 过滤AI输出中的<think></think>标签内容
            filtered_result = self._filter_think_tags(result_str)

            # 检查是否包含错误信息
            if "cannot access local variable" in filtered_result.lower():
                self.logger.warning("⚠️ 检测到变量访问错误，返回简化响应")
                return "AI分析过程中遇到技术问题，建议简化查询内容后重试。"

            # 检查代码解析错误
            if "code parsing failed" in filtered_result.lower() or "syntaxerror" in filtered_result.lower():
                self.logger.warning("⚠️ 检测到代码解析错误，返回简化响应")
                return "AI在处理过程中遇到了代码执行问题，建议重新尝试或简化查询内容。"

            return filtered_result

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"❌ 代理任务执行失败: {error_msg}")

            # 针对特定错误提供友好的错误信息
            if "final_answer" in error_msg.lower():
                return "AI分析过程中遇到了执行错误，建议重新尝试或简化查询内容。"
            elif "max steps" in error_msg.lower():
                return "分析任务过于复杂，已达到最大处理步骤限制，建议简化查询内容。"
            elif "code parsing failed" in error_msg.lower() or "syntaxerror" in error_msg.lower():
                return "AI在处理过程中遇到了代码执行问题，建议重新尝试或简化查询内容。"
            else:
                raise AgentServiceError(f"代理任务执行失败: {error_msg}")

    def _filter_think_tags(self, text) -> str:
        """过滤AI输出中的<think></think>标签内容"""
        if not text:
            return str(text) if text is not None else ""

        # 确保输入是字符串类型
        text_str = str(text)

        import re
        # 使用正则表达式移除<think>...</think>标签及其内容
        # 支持多行和嵌套情况
        pattern = r'<think>.*?</think>'
        filtered_text = re.sub(pattern, '', text_str, flags=re.DOTALL | re.IGNORECASE)

        # 清理多余的空行
        filtered_text = re.sub(r'\n\s*\n\s*\n', '\n\n', filtered_text)

        return filtered_text.strip()




    async def process_query(self, request: AgentRequest) -> AgentResponse:
        """处理查询请求"""
        try:
            self.logger.info(f"📝 处理查询: {request.query[:100]}...")
            
            # 记录任务开始
            task_id = request.task_id or f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.active_tasks[task_id] = {
                "status": TaskStatus.RUNNING,
                "start_time": datetime.now(),
                "query": request.query
            }
            
            # 缓存检查
            if request.use_cache:
                cached_result = await self._get_cached_result(request.query)
                if cached_result:
                    self.logger.info("📦 返回缓存结果")
                    self.active_tasks[task_id]["status"] = TaskStatus.COMPLETED
                    return AgentResponse(
                        success=True,
                        result=cached_result,
                        task_id=task_id,
                        cached=True
                    )
            
            # 执行代理任务
            result = await self._run_agent_task(
                request.query,
                task_id=task_id,
                max_steps=request.max_steps or 10
            )
            
            # 缓存结果
            if request.use_cache and result:
                await self._cache_result(request.query, result)
            
            # 更新任务状态
            self.active_tasks[task_id]["status"] = TaskStatus.COMPLETED
            self.active_tasks[task_id]["result"] = result
            
            self.logger.info(f"✅ 查询处理完成: {task_id}")
            
            return AgentResponse(
                success=True,
                result=result,
                task_id=task_id,
                cached=False
            )
            
        except Exception as e:
            error_msg = str(e)



            # 其他异常
            self.logger.error(f"❌ 查询处理失败: {error_msg}")
            if task_id in self.active_tasks:
                self.active_tasks[task_id]["status"] = TaskStatus.FAILED
                self.active_tasks[task_id]["error"] = error_msg

            return AgentResponse(
                success=False,
                error=error_msg,
                task_id=task_id
            )
    
    async def _get_cached_result(self, query: str) -> Optional[str]:
        """获取缓存结果"""
        try:
            cache_key = f"agent_query:{hash(query)}"
            cached_data = await self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            self.logger.warning(f"缓存读取失败: {e}")
        return None
    
    async def _cache_result(self, query: str, result: str):
        """缓存结果"""
        try:
            cache_key = f"agent_query:{hash(query)}"
            await self.redis_client.set(
                cache_key, 
                json.dumps(result), 
                expire=self.settings.cache_ttl
            )
        except Exception as e:
            self.logger.warning(f"缓存写入失败: {e}")
    
    async def get_task_status(self, task_id: str) -> TaskResponse:
        """获取任务状态"""
        if task_id not in self.active_tasks:
            return TaskResponse(
                task_id=task_id,
                status=TaskStatus.NOT_FOUND,
                error="任务不存在"
            )

        task_info = self.active_tasks[task_id]
        return TaskResponse(
            task_id=task_id,
            status=task_info["status"],
            result=task_info.get("result"),
            error=task_info.get("error"),
            start_time=task_info.get("start_time")
        )

    async def cancel_task(self, task_id: str) -> TaskResponse:
        """取消任务"""
        if task_id not in self.active_tasks:
            return TaskResponse(
                task_id=task_id,
                status=TaskStatus.NOT_FOUND,
                error="任务不存在"
            )

        task_info = self.active_tasks[task_id]

        # 如果任务已经完成或失败，不能取消
        if task_info["status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            return TaskResponse(
                task_id=task_id,
                status=task_info["status"],
                error=f"任务已处于 {task_info['status']} 状态，无法取消"
            )

        # 标记任务为已取消
        self.active_tasks[task_id]["status"] = TaskStatus.CANCELLED
        self.active_tasks[task_id]["cancelled_at"] = datetime.now()

        self.logger.info(f"🚫 任务已取消: {task_id}")

        return TaskResponse(
            task_id=task_id,
            status=TaskStatus.CANCELLED,
            result="任务已被用户取消"
        )

    def _run_agent_with_cancellation_check(self, query: str, task_id: str = None, **kwargs) -> str:
        """运行代理任务并定期检查取消状态"""
        try:
            # 检查任务是否已被取消
            if task_id and task_id in self.active_tasks:
                if self.active_tasks[task_id]["status"] == TaskStatus.CANCELLED:
                    return "任务已被用户取消"

            # 运行代理任务
            result = self.code_agent.run(query, **kwargs)
            return result

        except Exception as e:
            # 检查是否是取消导致的异常
            if task_id and task_id in self.active_tasks:
                if self.active_tasks[task_id]["status"] == TaskStatus.CANCELLED:
                    return "任务已被用户取消"
            raise e
    
    async def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("🧹 清理 Agent 服务资源...")
            
            # 清理活跃任务
            self.active_tasks.clear()
            
            # 清理代理实例
            self.code_agent = None
            self.model = None
            self.search_tool = None
            
            self.logger.info("✅ Agent 服务资源清理完成")
            
        except Exception as e:
            self.logger.error(f"❌ 资源清理失败: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            health_status = {
                "service": "healthy",
                "model": "unknown",
                "tools": "unknown",
                "redis": "unknown",
                "timestamp": datetime.now().isoformat()
            }
            
            # 检查模型
            try:
                await self._check_model_availability()
                health_status["model"] = "healthy"
            except Exception:
                health_status["model"] = "unhealthy"
            
            # 检查工具
            if self.search_tool and self.code_agent:
                health_status["tools"] = "healthy"
            else:
                health_status["tools"] = "unhealthy"
            
            # 检查 Redis
            try:
                await self.redis_client.ping()
                health_status["redis"] = "healthy"
            except Exception:
                health_status["redis"] = "unhealthy"
            
            # 总体状态
            if all(status == "healthy" for key, status in health_status.items() 
                   if key not in ["timestamp"]):
                health_status["service"] = "healthy"
            else:
                health_status["service"] = "degraded"
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"❌ 健康检查失败: {e}")
            return {
                "service": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

