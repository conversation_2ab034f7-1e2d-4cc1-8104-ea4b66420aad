/**
 * 文档上传组件
 * 支持拖拽上传、文件类型验证、上传进度显示
 */
import React, { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Upload,
  FileText,
  Image,
  File,
  X,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { useUploadDocument, useBatchUploadDocuments } from '@/hooks/useMorphik';
import { useDocumentStatusPolling } from '@/hooks/useDocuments';
import type { DocumentMetadata } from '@/types/morphik';

// 支持的文件类型
const SUPPORTED_FILE_TYPES = {
  'application/pdf': { icon: FileText, label: 'PDF', color: 'bg-red-100 text-red-800' },
  'application/msword': { icon: FileText, label: 'DOC', color: 'bg-blue-100 text-blue-800' },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': {
    icon: FileText, label: 'DOCX', color: 'bg-blue-100 text-blue-800'
  },
  'text/plain': { icon: FileText, label: 'TXT', color: 'bg-gray-100 text-gray-800' },
  'text/markdown': { icon: FileText, label: 'MD', color: 'bg-green-100 text-green-800' },
  'application/vnd.ms-excel': { icon: FileText, label: 'XLS', color: 'bg-green-100 text-green-800' },
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
    icon: FileText, label: 'XLSX', color: 'bg-green-100 text-green-800'
  },
  'image/jpeg': { icon: Image, label: 'JPG', color: 'bg-purple-100 text-purple-800' },
  'image/png': { icon: Image, label: 'PNG', color: 'bg-purple-100 text-purple-800' },
  'image/gif': { icon: Image, label: 'GIF', color: 'bg-purple-100 text-purple-800' },
};

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

interface UploadFile {
  file: File;
  id: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  documentId?: string;
}

interface DocumentUploadProps {
  onUploadComplete?: (documentIds: string[]) => void;
  maxFiles?: number;
  allowMultiple?: boolean;
}

function DocumentUpload({
  onUploadComplete,
  maxFiles = 10,
  allowMultiple = true
}: DocumentUploadProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [metadata, setMetadata] = useState<DocumentMetadata>({});
  const [textContent, setTextContent] = useState('');
  const [uploadMode, setUploadMode] = useState<'file' | 'text'>('file');

  const uploadDocument = useUploadDocument();
  const batchUploadDocuments = useBatchUploadDocuments();

  // 轮询处理中的文档状态
  useEffect(() => {
    const processingFiles = uploadFiles.filter(f => f.status === 'processing' && f.documentId);

    if (processingFiles.length === 0) return;

    const pollInterval = setInterval(async () => {
      try {
        // 检查每个处理中文档的状态
        const statusChecks = processingFiles.map(async (file) => {
          if (!file.documentId) return null;

          try {
            const response = await fetch(`http://localhost:8000/documents/${file.documentId}`);
            if (!response.ok) throw new Error('Failed to fetch document status');

            const doc = await response.json();
            const status = doc.system_metadata?.status || 'completed';

            return {
              fileId: file.id,
              documentId: file.documentId,
              status: status
            };
          } catch (error) {
            console.error(`Failed to check status for document ${file.documentId}:`, error);
            return null;
          }
        });

        const results = await Promise.all(statusChecks);

        // 更新文件状态
        setUploadFiles(prev => prev.map(f => {
          const result = results.find(r => r && r.fileId === f.id);
          if (result && result.status !== 'processing') {
            return {
              ...f,
              status: result.status === 'completed' ? 'completed' : 'error',
              progress: result.status === 'completed' ? 100 : f.progress,
              error: result.status === 'failed' ? '处理失败' : undefined
            };
          }
          return f;
        }));

      } catch (error) {
        console.error('Error polling document status:', error);
      }
    }, 3000); // 每3秒检查一次

    return () => clearInterval(pollInterval);
  }, [uploadFiles]);

  // 文件拖拽处理
  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // 处理被拒绝的文件
    rejectedFiles.forEach(({ file, errors }) => {
      console.warn(`File ${file.name} rejected:`, errors);
    });

    // 添加接受的文件到上传列表
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      file,
      id: `${file.name}-${Date.now()}-${Math.random()}`,
      status: 'pending',
      progress: 0,
    }));

    setUploadFiles(prev => {
      const combined = [...prev, ...newFiles];
      return combined.slice(0, maxFiles); // 限制文件数量
    });
  }, [maxFiles]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: Object.keys(SUPPORTED_FILE_TYPES).reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize: MAX_FILE_SIZE,
    multiple: allowMultiple,
    disabled: uploadFiles.some(f => f.status === 'uploading'),
  });

  // 移除文件
  const removeFile = (fileId: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // 获取文件图标和样式
  const getFileInfo = (file: File) => {
    const fileType = SUPPORTED_FILE_TYPES[file.type as keyof typeof SUPPORTED_FILE_TYPES];
    return fileType || { icon: File, label: 'FILE', color: 'bg-gray-100 text-gray-800' };
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 上传单个文件
  const uploadSingleFile = async (uploadFile: UploadFile) => {
    setUploadFiles(prev => prev.map(f =>
      f.id === uploadFile.id ? { ...f, status: 'uploading', progress: 0 } : f
    ));

    try {
      const response = await uploadDocument.mutateAsync({
        file: uploadFile.file,
        metadata: {
          ...metadata,
          filename: uploadFile.file.name,
          fileSize: uploadFile.file.size,
          fileType: uploadFile.file.type,
        },
      });

      // 根据后端返回的实际状态设置文件状态
      const actualStatus = response.status || 'processing';
      setUploadFiles(prev => prev.map(f =>
        f.id === uploadFile.id ? {
          ...f,
          status: actualStatus === 'processing' ? 'processing' : 'completed',
          progress: actualStatus === 'processing' ? 50 : 100, // 处理中显示50%进度
          documentId: response.document_id
        } : f
      ));

      return response.document_id;
    } catch (error: any) {
      setUploadFiles(prev => prev.map(f =>
        f.id === uploadFile.id ? {
          ...f,
          status: 'error',
          error: error.message || '上传失败'
        } : f
      ));
      throw error;
    }
  };

  // 上传文本内容
  const uploadText = async () => {
    if (!textContent.trim()) return;

    try {
      const response = await uploadDocument.mutateAsync({
        content: textContent,
        title: metadata.title || '文本文档',
        metadata: {
          ...metadata,
          contentType: 'text/plain',
        },
      });

      setTextContent('');
      setMetadata({});

      if (onUploadComplete) {
        onUploadComplete([response.document_id]);
      }
    } catch (error) {
      console.error('Text upload failed:', error);
    }
  };

  // 批量上传文件
  const handleBatchUpload = async () => {
    const pendingFiles = uploadFiles.filter(f => f.status === 'pending');
    if (pendingFiles.length === 0) return;

    if (pendingFiles.length === 1) {
      // 单文件上传
      try {
        const documentId = await uploadSingleFile(pendingFiles[0]);
        if (onUploadComplete) {
          onUploadComplete([documentId]);
        }
      } catch (error) {
        console.error('Single file upload failed:', error);
      }
    } else {
      // 批量上传
      try {
        const response = await batchUploadDocuments.mutateAsync({
          files: pendingFiles.map(f => f.file),
          metadata,
        });

        // 更新文件状态
        setUploadFiles(prev => prev.map(f => {
          const result = response.results.find(r => r.document_id);
          if (f.status === 'pending' && result) {
            return { ...f, status: 'completed', progress: 100, documentId: result.document_id };
          }
          return f;
        }));

        if (onUploadComplete) {
          const documentIds = response.results.map(r => r.document_id);
          onUploadComplete(documentIds);
        }
      } catch (error) {
        console.error('Batch upload failed:', error);
      }
    }
  };

  // 清空所有文件
  const clearAllFiles = () => {
    setUploadFiles([]);
  };

  const isUploading = uploadFiles.some(f => f.status === 'uploading') ||
                     uploadDocument.isPending ||
                     batchUploadDocuments.isPending;

  return (
    <div className="space-y-6">
      {/* 上传模式切换 */}
      <div className="flex space-x-2">
        <Button
          variant={uploadMode === 'file' ? 'default' : 'outline'}
          onClick={() => setUploadMode('file')}
          size="sm"
        >
          文件上传
        </Button>
        <Button
          variant={uploadMode === 'text' ? 'default' : 'outline'}
          onClick={() => setUploadMode('text')}
          size="sm"
        >
          文本输入
        </Button>
      </div>

      {uploadMode === 'file' ? (
        <>
          {/* 文件拖拽区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                文档上传
              </CardTitle>
              <CardDescription>
                支持 PDF、DOC、DOCX、TXT、MD、XLS、XLSX、JPG、PNG 等格式，单文件最大 50MB
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div
                {...getRootProps()}
                className={`
                  border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
                  ${isDragActive
                    ? 'border-primary bg-primary/5'
                    : 'border-muted-foreground/25 hover:border-primary/50'
                  }
                  ${isUploading ? 'pointer-events-none opacity-50' : ''}
                `}
              >
                <input {...getInputProps()} />
                <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                {isDragActive ? (
                  <p className="text-primary font-medium">释放文件到此处...</p>
                ) : (
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">
                      拖拽文件到此处或点击选择文件
                    </p>
                    <Button variant="outline" disabled={isUploading}>
                      选择文件
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      ) : (
        /* 文本输入区域 */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              文本输入
            </CardTitle>
            <CardDescription>
              直接输入文本内容创建文档
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="text-content">文本内容</Label>
              <Textarea
                id="text-content"
                placeholder="输入您的文本内容..."
                value={textContent}
                onChange={(e) => setTextContent(e.target.value)}
                rows={8}
                className="mt-1"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* 元数据输入 */}
      <Card>
        <CardHeader>
          <CardTitle>文档信息</CardTitle>
          <CardDescription>
            为文档添加标题、分类和标签等信息
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">标题</Label>
              <Input
                id="title"
                placeholder="文档标题"
                value={metadata.title || ''}
                onChange={(e) => setMetadata(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="category">分类</Label>
              <Input
                id="category"
                placeholder="文档分类"
                value={metadata.category || ''}
                onChange={(e) => setMetadata(prev => ({ ...prev, category: e.target.value }))}
              />
            </div>
          </div>
          <div>
            <Label htmlFor="tags">标签</Label>
            <Input
              id="tags"
              placeholder="标签1, 标签2, 标签3"
              value={metadata.tags?.join(', ') || ''}
              onChange={(e) => setMetadata(prev => ({
                ...prev,
                tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
              }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* 文件列表 */}
      {uploadFiles.length > 0 && uploadMode === 'file' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>待上传文件 ({uploadFiles.length})</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFiles}
                disabled={isUploading}
              >
                清空列表
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {uploadFiles.map((uploadFile) => {
                const fileInfo = getFileInfo(uploadFile.file);
                const IconComponent = fileInfo.icon;

                return (
                  <div
                    key={uploadFile.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3 flex-1">
                      <IconComponent className="h-8 w-8 text-muted-foreground" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {uploadFile.file.name}
                        </p>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          <Badge variant="outline" className={fileInfo.color}>
                            {fileInfo.label}
                          </Badge>
                          <span>{formatFileSize(uploadFile.file.size)}</span>
                        </div>

                        {/* 进度条 */}
                        {(uploadFile.status === 'uploading' || uploadFile.status === 'processing') && (
                          <div className="mt-2">
                            <Progress value={uploadFile.progress} className="h-1" />
                            {uploadFile.status === 'processing' && (
                              <p className="text-xs text-muted-foreground mt-1">
                                正在处理文档内容...
                              </p>
                            )}
                          </div>
                        )}

                        {/* 错误信息 */}
                        {uploadFile.status === 'error' && uploadFile.error && (
                          <p className="text-xs text-red-600 mt-1">{uploadFile.error}</p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {/* 状态图标 */}
                      {uploadFile.status === 'pending' && (
                        <div className="h-5 w-5 rounded-full bg-gray-200" />
                      )}
                      {uploadFile.status === 'uploading' && (
                        <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                      )}
                      {uploadFile.status === 'processing' && (
                        <Loader2 className="h-5 w-5 animate-spin text-orange-600" />
                      )}
                      {uploadFile.status === 'completed' && (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      )}
                      {uploadFile.status === 'error' && (
                        <AlertCircle className="h-5 w-5 text-red-600" />
                      )}

                      {/* 删除按钮 */}
                      {uploadFile.status !== 'uploading' && uploadFile.status !== 'processing' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(uploadFile.id)}
                          className="h-8 w-8 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 上传控制按钮 */}
      <div className="flex justify-end space-x-2">
        {uploadMode === 'file' ? (
          <>
            <Button
              variant="outline"
              onClick={clearAllFiles}
              disabled={isUploading || uploadFiles.length === 0}
            >
              清空
            </Button>
            <Button
              onClick={handleBatchUpload}
              disabled={isUploading || uploadFiles.filter(f => f.status === 'pending').length === 0}
              className="min-w-[120px]"
            >
              {isUploading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isUploading ? '上传中...' : `上传文件 (${uploadFiles.filter(f => f.status === 'pending').length})`}
            </Button>
          </>
        ) : (
          <Button
            onClick={uploadText}
            disabled={!textContent.trim() || uploadDocument.isPending}
            className="min-w-[120px]"
          >
            {uploadDocument.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {uploadDocument.isPending ? '上传中...' : '创建文档'}
          </Button>
        )}
      </div>
    </div>
  );
}

export default DocumentUpload;
