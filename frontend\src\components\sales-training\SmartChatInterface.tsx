/**
 * 智能对话交互系统
 * 提供增强的对话交互体验
 */

import { useState, useRef, useEffect } from 'react'
import {
  Send,
  Mic,
  MicO<PERSON>,
  User,
  <PERSON>t,
  Clock,
  Check,
  CheckCheck,
  MoreHorizontal,
  Lightbulb,
  Zap,
  Heart,
  ThumbsUp,
  MessageSquare,
  Smile
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import type { ChatMessage, TrainingCustomer, QuickReply, MessageStatus } from '@/types/salesTraining'

interface SmartChatInterfaceProps {
  messages: ChatMessage[]
  customer: TrainingCustomer
  onSendMessage: (content: string) => void
  isTyping?: boolean
  className?: string
}

// 模拟快捷回复数据
const mockQuickReplies: QuickReply[] = [
  {
    id: '1',
    text: '感谢您的询问，我很乐意为您详细介绍我们的产品。',
    category: 'greeting',
    context: ['opening', 'introduction']
  },
  {
    id: '2',
    text: '我理解您的担忧，让我为您详细解释一下这个问题。',
    category: 'objection',
    context: ['concern', 'doubt']
  },
  {
    id: '3',
    text: '根据您的需求，我推荐以下几个解决方案...',
    category: 'question',
    context: ['recommendation', 'solution']
  },
  {
    id: '4',
    text: '我们可以为您提供定制化的解决方案，具体包括...',
    category: 'closing',
    context: ['customization', 'proposal']
  }
]

export function SmartChatInterface({ 
  messages, 
  customer, 
  onSendMessage, 
  isTyping = false,
  className = '' 
}: SmartChatInterfaceProps) {
  const [inputValue, setInputValue] = useState('')
  const [isRecording, setIsRecording] = useState(false)
  const [showQuickReplies, setShowQuickReplies] = useState(false)
  const [messageStatuses, setMessageStatuses] = useState<Record<string, MessageStatus>>({})
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages, isTyping])

  // 模拟消息状态更新
  useEffect(() => {
    const userMessages = messages.filter(m => m.role === 'user')
    const latestUserMessage = userMessages[userMessages.length - 1]
    
    if (latestUserMessage && !messageStatuses[latestUserMessage.id]) {
      // 模拟消息状态变化
      setTimeout(() => {
        setMessageStatuses(prev => ({
          ...prev,
          [latestUserMessage.id]: {
            id: latestUserMessage.id,
            status: 'sent',
            timestamp: new Date()
          }
        }))
      }, 500)

      setTimeout(() => {
        setMessageStatuses(prev => ({
          ...prev,
          [latestUserMessage.id]: {
            id: latestUserMessage.id,
            status: 'delivered',
            timestamp: new Date()
          }
        }))
      }, 1000)

      setTimeout(() => {
        setMessageStatuses(prev => ({
          ...prev,
          [latestUserMessage.id]: {
            id: latestUserMessage.id,
            status: 'read',
            timestamp: new Date()
          }
        }))
      }, 2000)
    }
  }, [messages, messageStatuses])

  const handleSendMessage = () => {
    if (!inputValue.trim()) return

    onSendMessage(inputValue)
    setInputValue('')
    setShowQuickReplies(false)
    
    // 设置初始消息状态
    const messageId = Date.now().toString()
    setMessageStatuses(prev => ({
      ...prev,
      [messageId]: {
        id: messageId,
        status: 'sending',
        timestamp: new Date()
      }
    }))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleQuickReply = (reply: QuickReply) => {
    setInputValue(reply.text)
    setShowQuickReplies(false)
    inputRef.current?.focus()
  }

  const toggleVoiceRecording = () => {
    setIsRecording(!isRecording)
    // TODO: 实现语音录制功能
  }

  const getMessageStatusIcon = (messageId: string) => {
    const status = messageStatuses[messageId]?.status
    switch (status) {
      case 'sending':
        return <Clock className="w-3 h-3 text-gray-400 animate-spin" />
      case 'sent':
        return <Check className="w-3 h-3 text-gray-400" />
      case 'delivered':
        return <CheckCheck className="w-3 h-3 text-gray-400" />
      case 'read':
        return <CheckCheck className="w-3 h-3 text-blue-500" />
      default:
        return null
    }
  }

  const getSentimentIcon = (content: string) => {
    // 简单的情感分析模拟
    if (content.includes('谢谢') || content.includes('感谢')) {
      return <Heart className="w-3 h-3 text-red-500" />
    }
    if (content.includes('好的') || content.includes('同意')) {
      return <ThumbsUp className="w-3 h-3 text-green-500" />
    }
    if (content.includes('?') || content.includes('？')) {
      return <MessageSquare className="w-3 h-3 text-blue-500" />
    }
    return <Smile className="w-3 h-3 text-yellow-500" />
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* 消息列表 */}
      <ScrollArea className="flex-1 p-6">
        <div className="space-y-4">
          {messages.map((message, index) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} opacity-0 animate-fade-in-up`}
              style={{animationDelay: `${index * 0.1}s`, animationFillMode: 'forwards'}}
            >
              <div
                className={`max-w-[75%] rounded-xl p-4 shadow-md transition-all duration-200 hover:shadow-lg relative group ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                    : message.role === 'customer'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600'
                    : 'bg-gradient-to-r from-yellow-100 to-yellow-200 dark:from-yellow-900/30 dark:to-yellow-800/30 text-yellow-800 dark:text-yellow-200 border border-yellow-300 dark:border-yellow-700'
                }`}
              >
                <div className="flex items-start space-x-3">
                  {message.role === 'customer' && (
                    <div className="p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-full flex-shrink-0">
                      <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                  )}
                  {message.role === 'system' && (
                    <div className="p-1.5 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex-shrink-0">
                      <Bot className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm leading-relaxed break-words">{message.content}</p>
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center space-x-2 text-xs opacity-70">
                        <Clock className="w-3 h-3" />
                        <span>{message.timestamp.toLocaleTimeString()}</span>
                        {message.role === 'customer' && getSentimentIcon(message.content)}
                      </div>
                      {message.role === 'user' && (
                        <div className="flex items-center space-x-1">
                          {getMessageStatusIcon(message.id)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 消息反应按钮 */}
                {message.role === 'customer' && (
                  <div className="absolute -bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex space-x-1">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 bg-white dark:bg-gray-800 shadow-md hover:shadow-lg rounded-full"
                            >
                              <ThumbsUp className="w-3 h-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>有用的回复</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 bg-white dark:bg-gray-800 shadow-md hover:shadow-lg rounded-full"
                            >
                              <Lightbulb className="w-3 h-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>获取建议</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* 打字指示器 */}
          {isTyping && (
            <div className="flex justify-start">
              <div className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl p-4 shadow-md">
                <div className="flex items-center space-x-3">
                  <div className="p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                    <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '150ms'}}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '300ms'}}></div>
                  </div>
                  <span className="text-xs text-gray-500">{customer.name} 正在输入...</span>
                </div>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* 快捷回复面板 */}
      {showQuickReplies && (
        <div className="border-t border-gray-200 dark:border-gray-700 p-3 bg-gray-50 dark:bg-gray-800/50">
          <div className="flex items-center space-x-2 mb-2">
            <Zap className="w-4 h-4 text-blue-500" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">快捷回复</span>
          </div>
          <div className="grid grid-cols-1 gap-2">
            {mockQuickReplies.map((reply) => (
              <Button
                key={reply.id}
                variant="ghost"
                className="justify-start text-left h-auto p-2 text-sm hover:bg-blue-50 dark:hover:bg-blue-900/20"
                onClick={() => handleQuickReply(reply)}
              >
                <div className="truncate">{reply.text}</div>
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* 输入区域 */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800">
        <div className="flex space-x-3">
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              placeholder="输入您的回复..."
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              className="h-12 pr-12 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
              onClick={() => setShowQuickReplies(!showQuickReplies)}
            >
              <Lightbulb className="w-4 h-4 text-gray-400 hover:text-blue-500" />
            </Button>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            className={`h-12 w-12 p-0 border transition-all duration-200 ${
              isRecording 
                ? 'bg-red-500 hover:bg-red-600 text-white border-red-500' 
                : 'border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700'
            }`}
            onClick={toggleVoiceRecording}
          >
            {isRecording ? (
              <MicOff className="w-5 h-5" />
            ) : (
              <Mic className="w-5 h-5" />
            )}
          </Button>

          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim()}
            className="h-12 px-6 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="w-4 h-4 mr-2" />
            发送
          </Button>
        </div>
      </div>
    </div>
  )
}
