"""
权威数据源检测和管理
提升市场分析的数据质量和权威性
"""

import re
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from app.models.schemas import MarketDataSource


class AuthorityDataSources:
    """权威数据源配置"""
    
    # 权威数据源配置
    SOURCE_PATTERNS = {
        "market_research": {
            "statista": {
                "url": "statista.com",
                "weight": 0.9,
                "title": "Statista Market Research",
                "description": "全球领先的市场数据平台"
            },
            "ibisworld": {
                "url": "ibisworld.com", 
                "weight": 0.9,
                "title": "IBISWorld Industry Analysis",
                "description": "专业行业分析报告"
            },
            "grandview": {
                "url": "grandviewresearch.com",
                "weight": 0.8,
                "title": "Grand View Research",
                "description": "市场研究和咨询"
            },
            "marketresearch": {
                "url": "marketresearch.com",
                "weight": 0.8,
                "title": "Market Research",
                "description": "市场研究报告"
            },
            "tradingeconomics": {
                "url": "tradingeconomics.com",
                "weight": 0.8,
                "title": "Trading Economics",
                "description": "经济数据和指标"
            }
        },
        "financial_news": {
            "bloomberg": {
                "url": "bloomberg.com",
                "weight": 0.9,
                "title": "Bloomberg",
                "description": "金融新闻和市场数据"
            },
            "reuters": {
                "url": "reuters.com",
                "weight": 0.9,
                "title": "Reuters",
                "description": "国际新闻和金融信息"
            },
            "ft": {
                "url": "ft.com",
                "weight": 0.8,
                "title": "Financial Times",
                "description": "金融时报"
            },
            "wsj": {
                "url": "wsj.com",
                "weight": 0.8,
                "title": "Wall Street Journal",
                "description": "华尔街日报"
            },
            "forbes": {
                "url": "forbes.com",
                "weight": 0.7,
                "title": "Forbes",
                "description": "商业和财经杂志"
            }
        },
        "government": {
            "census": {
                "url": "census.gov",
                "weight": 0.9,
                "title": "US Census Bureau",
                "description": "美国人口普查局"
            },
            "bea": {
                "url": "bea.gov",
                "weight": 0.8,
                "title": "Bureau of Economic Analysis",
                "description": "美国经济分析局"
            },
            "trade": {
                "url": "trade.gov",
                "weight": 0.8,
                "title": "International Trade Administration",
                "description": "国际贸易管理局"
            },
            "europa": {
                "url": "ec.europa.eu",
                "weight": 0.8,
                "title": "European Commission",
                "description": "欧盟委员会"
            }
        },
        "industry": {
            "mckinsey": {
                "url": "mckinsey.com",
                "weight": 0.9,
                "title": "McKinsey & Company",
                "description": "麦肯锡咨询公司"
            },
            "deloitte": {
                "url": "deloitte.com",
                "weight": 0.8,
                "title": "Deloitte",
                "description": "德勤咨询"
            },
            "pwc": {
                "url": "pwc.com",
                "weight": 0.8,
                "title": "PwC",
                "description": "普华永道"
            },
            "bcg": {
                "url": "bcg.com",
                "weight": 0.8,
                "title": "Boston Consulting Group",
                "description": "波士顿咨询集团"
            }
        }
    }
    
    def generate_targeted_queries(self, product: str, strategy: str) -> List[str]:
        """生成定向搜索查询"""
        queries = []
        
        # 为市场研究网站生成定向查询
        for site_info in self.SOURCE_PATTERNS["market_research"].values():
            site_url = site_info["url"]
            queries.append(f"site:{site_url} {product} market analysis {strategy}")
        
        # 为新闻网站生成定向查询
        for site_info in self.SOURCE_PATTERNS["financial_news"].values():
            site_url = site_info["url"]
            queries.append(f"site:{site_url} {product} market trends {strategy}")
        
        return queries[:5]  # 限制查询数量


class IntelligentSourceDetector:
    """智能数据源检测器"""
    
    def __init__(self):
        self.authority_sources = AuthorityDataSources()
        self.logger = logging.getLogger(__name__)
    
    def extract_data_sources(self, analysis_report: str) -> List[MarketDataSource]:
        """从分析报告中提取数据源"""
        sources = []
        report_lower = analysis_report.lower()
        
        # 检测所有配置的数据源
        for category, patterns in self.authority_sources.SOURCE_PATTERNS.items():
            for name, config in patterns.items():
                if self._is_source_mentioned(report_lower, name, config["url"]):
                    source = MarketDataSource(
                        title=config["title"],
                        url=f"https://{config['url']}",
                        content=self._extract_content_snippet(analysis_report, name),
                        data_type=category,
                        relevance_score=config["weight"]
                    )
                    sources.append(source)
        
        # 按相关性分数排序
        sources.sort(key=lambda x: x.relevance_score, reverse=True)
        
        self.logger.info(f"📊 检测到 {len(sources)} 个权威数据源")
        return sources
    
    def _is_source_mentioned(self, report_lower: str, name: str, url: str) -> bool:
        """检查数据源是否在报告中被提及"""
        # 检查名称
        if name.lower() in report_lower:
            return True
        
        # 检查URL
        if url.lower() in report_lower:
            return True
        
        # 检查URL的主域名
        domain = url.split('.')[0]
        if domain.lower() in report_lower:
            return True
        
        return False
    
    def _extract_content_snippet(self, analysis_report: str, source_name: str) -> str:
        """提取与数据源相关的内容片段"""
        try:
            # 查找包含数据源名称的句子
            sentences = analysis_report.split('.')
            relevant_sentences = []
            
            for sentence in sentences:
                if source_name.lower() in sentence.lower():
                    relevant_sentences.append(sentence.strip())
            
            if relevant_sentences:
                return '. '.join(relevant_sentences[:2])  # 最多返回2个句子
            else:
                return f"来自{source_name}的市场数据"
                
        except Exception as e:
            self.logger.warning(f"内容片段提取失败: {e}")
            return f"来自{source_name}的市场数据"


class ResultQualityFilter:
    """结果质量筛选器"""
    
    def __init__(self):
        self.authority_sources = AuthorityDataSources()
        self.logger = logging.getLogger(__name__)
    
    def filter_search_results(self, results: List[Dict]) -> List[Dict]:
        """筛选高质量搜索结果"""
        filtered = []
        
        for result in results:
            try:
                score = self._calculate_quality_score(result)
                if score >= 0.4:  # 降低质量阈值，保留更多结果
                    result["quality_score"] = score
                    filtered.append(result)
            except Exception as e:
                self.logger.warning(f"质量评分计算失败: {e}")
                # 如果评分失败，仍然保留结果
                result["quality_score"] = 0.5
                filtered.append(result)
        
        # 按质量分数排序
        filtered.sort(key=lambda x: x.get("quality_score", 0), reverse=True)
        
        self.logger.info(f"🔍 质量筛选: {len(filtered)}/{len(results)} 个结果通过筛选")
        return filtered
    
    def _calculate_quality_score(self, result: Dict) -> float:
        """计算结果质量分数"""
        score = 0.0
        
        # 数据源权威性 (40%)
        authority_score = self._calculate_authority_score(result.get("url", ""))
        score += authority_score * 0.4
        
        # 内容相关性 (30%)
        relevance_score = self._calculate_relevance_score(result.get("content", ""))
        score += relevance_score * 0.3
        
        # 数据新鲜度 (20%)
        freshness_score = self._calculate_freshness_score(result.get("date", ""))
        score += freshness_score * 0.2
        
        # 内容完整性 (10%)
        completeness_score = self._calculate_completeness_score(result.get("content", ""))
        score += completeness_score * 0.1
        
        return min(score, 1.0)
    
    def _calculate_authority_score(self, url: str) -> float:
        """计算权威性分数"""
        if not url:
            return 0.3  # 默认分数
        
        url_lower = url.lower()
        
        # 检查是否为权威网站
        for category, patterns in self.authority_sources.SOURCE_PATTERNS.items():
            for name, config in patterns.items():
                if config["url"] in url_lower:
                    return config["weight"]
        
        # 检查其他指标
        if any(domain in url_lower for domain in [".gov", ".edu", ".org"]):
            return 0.7
        elif any(domain in url_lower for domain in [".com", ".net"]):
            return 0.5
        else:
            return 0.3
    
    def _calculate_relevance_score(self, content: str) -> float:
        """计算内容相关性分数"""
        if not content:
            return 0.3
        
        # 关键词匹配
        market_keywords = ["market", "analysis", "revenue", "growth", "forecast", "trends", "share", "size"]
        content_lower = content.lower()
        
        matches = sum(1 for keyword in market_keywords if keyword in content_lower)
        return min(matches / len(market_keywords), 1.0)
    
    def _calculate_freshness_score(self, date_str: str) -> float:
        """计算数据新鲜度分数"""
        if not date_str:
            return 0.5  # 默认分数
        
        try:
            # 简单的年份检查
            current_year = datetime.now().year
            if str(current_year) in date_str or str(current_year - 1) in date_str:
                return 1.0
            elif str(current_year - 2) in date_str:
                return 0.8
            else:
                return 0.6
        except:
            return 0.5
    
    def _calculate_completeness_score(self, content: str) -> float:
        """计算内容完整性分数"""
        if not content:
            return 0.0
        
        # 基于内容长度和结构
        length_score = min(len(content) / 1000, 1.0)
        
        # 检查是否包含数字（统计数据）
        has_numbers = bool(re.search(r'\d+', content))
        number_bonus = 0.2 if has_numbers else 0.0
        
        return min(length_score + number_bonus, 1.0)
