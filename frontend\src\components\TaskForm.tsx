/**
 * 任务表单组件
 * 用于创建和编辑任务
 */
import React from 'react';
import { useForm } from 'react-hook-form';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Task, CreateTaskRequest, UpdateTaskRequest, TaskStatus, TaskPriority } from '../services/taskApi';

interface TaskFormProps {
  task?: Task;
  onSubmit: (data: CreateTaskRequest | UpdateTaskRequest) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function TaskForm({ task, onSubmit, onCancel, isLoading = false }: TaskFormProps) {
  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      title: task?.title || '',
      description: task?.description || '',
      status: task?.status || TaskStatus.PENDING,
      priority: task?.priority || TaskPriority.MEDIUM,
      assignee: task?.assignee || '',
      is_active: task?.is_active ?? true,
    }
  });

  const onFormSubmit = (data: any) => {
    onSubmit(data);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{task ? '编辑任务' : '创建新任务'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
          {/* 任务标题 */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium mb-1">
              任务标题 *
            </label>
            <input
              {...register('title', { required: '任务标题不能为空' })}
              type="text"
              id="title"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入任务标题"
            />
            {errors.title && (
              <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>
            )}
          </div>

          {/* 任务描述 */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium mb-1">
              任务描述
            </label>
            <textarea
              {...register('description')}
              id="description"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入任务描述"
            />
          </div>

          {/* 状态和优先级 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="status" className="block text-sm font-medium mb-1">
                任务状态
              </label>
              <select
                {...register('status')}
                id="status"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={TaskStatus.PENDING}>待处理</option>
                <option value={TaskStatus.IN_PROGRESS}>进行中</option>
                <option value={TaskStatus.COMPLETED}>已完成</option>
                <option value={TaskStatus.CANCELLED}>已取消</option>
              </select>
            </div>

            <div>
              <label htmlFor="priority" className="block text-sm font-medium mb-1">
                优先级
              </label>
              <select
                {...register('priority')}
                id="priority"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={TaskPriority.LOW}>低</option>
                <option value={TaskPriority.MEDIUM}>中</option>
                <option value={TaskPriority.HIGH}>高</option>
                <option value={TaskPriority.URGENT}>紧急</option>
              </select>
            </div>
          </div>

          {/* 负责人 */}
          <div>
            <label htmlFor="assignee" className="block text-sm font-medium mb-1">
              负责人
            </label>
            <input
              {...register('assignee')}
              type="text"
              id="assignee"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入负责人姓名"
            />
          </div>

          {/* 是否激活 */}
          <div className="flex items-center">
            <input
              {...register('is_active')}
              type="checkbox"
              id="is_active"
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
              激活任务
            </label>
          </div>

          {/* 按钮组 */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? '保存中...' : (task ? '更新任务' : '创建任务')}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
