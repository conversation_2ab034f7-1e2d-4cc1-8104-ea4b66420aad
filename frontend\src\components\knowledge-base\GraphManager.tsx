/**
 * 知识图谱管理组件
 * 提供图谱创建、列表、状态管理等功能
 */
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Network,
  Plus,
  Loader2,
  RefreshCw,
  Eye,
  Trash,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Settings
} from 'lucide-react';
import { useGraphs, useCreateGraph, useUpdateGraph, useDeleteGraph, useGraphStatusPolling } from '@/hooks/useMorphik';
import { useDocuments } from '@/hooks/useDocuments';
import { toast } from 'sonner';
import KnowledgeGraph from './KnowledgeGraph';

interface GraphManagerProps {
  onGraphSelect?: (graphName: string) => void;
  selectedGraph?: string;
  mode?: 'sidebar' | 'grid'; // 新增模式选择
}

export function GraphManager({ onGraphSelect, selectedGraph, mode = 'sidebar' }: GraphManagerProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [createForm, setCreateForm] = useState({
    name: '',
    filters: '{}',
    documents: [] as string[],
  });

  const { data: graphs, isLoading: graphsLoading, refetch: refetchGraphs, error: graphsError } = useGraphs();
  const { data: documents } = useDocuments({ limit: 100 });
  const createGraphMutation = useCreateGraph();
  const updateGraphMutation = useUpdateGraph();
  const deleteGraphMutation = useDeleteGraph();

  // 状态轮询 - 监控处理中的图谱
  useGraphStatusPolling(graphs || [], {
    enabled: !graphsLoading && graphs && graphs.length > 0,
    pollInterval: 3000, // 每3秒检查一次
    onStatusChange: (graphName, oldStatus, newStatus) => {
      if (newStatus === 'completed') {
        toast.success(`✅ 图谱 "${graphName}" 创建完成`);
      } else if (newStatus === 'failed') {
        toast.error(`❌ 图谱 "${graphName}" 创建失败`);
      }
    }
  });





  // 定期刷新图谱列表以确保状态同步
  React.useEffect(() => {
    const interval = setInterval(() => {
      refetchGraphs();
    }, 10000); // 每10秒刷新一次

    return () => clearInterval(interval);
  }, [refetchGraphs]);

  const handleCreateGraph = async () => {
    console.log('开始创建图谱...', createForm);

    // 验证表单
    if (!createForm.name.trim()) {
      console.error('图谱名称不能为空');
      return;
    }

    try {
      let parsedFilters = {};
      if (createForm.filters.trim() && createForm.filters !== '{}') {
        try {
          parsedFilters = JSON.parse(createForm.filters);
          console.log('解析过滤条件:', parsedFilters);
        } catch (parseError) {
          console.error('过滤条件JSON格式错误:', parseError);
          alert('过滤条件格式错误，请检查JSON格式');
          return;
        }
      }

      const requestData = {
        name: createForm.name,
        filters: Object.keys(parsedFilters).length > 0 ? parsedFilters : undefined,
        documents: createForm.documents.length > 0 ? createForm.documents : undefined,
      };

      console.log('发送创建图谱请求:', requestData);

      const result = await createGraphMutation.mutateAsync(requestData);
      console.log('图谱创建成功:', result);

      setIsCreateDialogOpen(false);
      setCreateForm({ name: '', filters: '{}', documents: [] });
    } catch (error) {
      console.error('创建图谱失败:', error);
      // 显示更详细的错误信息
      if (error instanceof Error) {
        alert(`创建图谱失败: ${error.message}`);
      } else {
        alert('创建图谱失败，请检查控制台获取详细信息');
      }
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />已完成</Badge>;
      case 'processing':
        return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />处理中</Badge>;
      case 'failed':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />失败</Badge>;
      default:
        return <Badge variant="outline"><AlertCircle className="h-3 w-3 mr-1" />未知</Badge>;
    }
  };

  // 网格模式渲染
  if (mode === 'grid') {
    return (
      <div className="space-y-6">
        {/* 头部操作区 */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">知识图谱</h3>
            <p className="text-sm text-muted-foreground">查看和管理您的知识图谱</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetchGraphs()}
              disabled={graphsLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${graphsLoading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  创建图谱
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>创建知识图谱</DialogTitle>
                  <DialogDescription>
                    基于文档内容创建新的知识图谱
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="graph-name">图谱名称</Label>
                    <Input
                      id="graph-name"
                      value={createForm.name}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="输入图谱名称"
                    />
                  </div>
                  <div>
                    <Label htmlFor="graph-filters">过滤条件 (JSON)</Label>
                    <Textarea
                      id="graph-filters"
                      value={createForm.filters}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, filters: e.target.value }))}
                      placeholder='{"category": "技术文档"}'
                      rows={3}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      用于筛选文档的元数据过滤条件
                    </p>
                  </div>
                  <div>
                    <Label>选择文档 (可选)</Label>
                    <Select
                      value=""
                      onValueChange={(value) => {
                        if (value && !createForm.documents.includes(value)) {
                          setCreateForm(prev => ({
                            ...prev,
                            documents: [...prev.documents, value]
                          }));
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择要包含的文档" />
                      </SelectTrigger>
                      <SelectContent>
                        {documents?.items?.map((doc) => (
                          <SelectItem key={doc.external_id} value={doc.external_id}>
                            {doc.filename || doc.metadata?.title || '未命名文档'}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {createForm.documents.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {createForm.documents.map((docId) => {
                          const doc = documents?.items?.find(d => d.external_id === docId);
                          return (
                            <Badge
                              key={docId}
                              variant="secondary"
                              className="cursor-pointer"
                              onClick={() => {
                                setCreateForm(prev => ({
                                  ...prev,
                                  documents: prev.documents.filter(id => id !== docId)
                                }));
                              }}
                            >
                              {doc?.filename || '未知文档'} ×
                            </Badge>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateDialogOpen(false)}
                  >
                    取消
                  </Button>
                  <Button
                    onClick={handleCreateGraph}
                    disabled={!createForm.name.trim() || createGraphMutation.isPending}
                  >
                    {createGraphMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    创建
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* 图谱网格 */}
        <div>
          {graphsLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin mr-3" />
              <span className="text-lg">加载图谱列表...</span>
            </div>
          ) : graphs && graphs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {graphs.map((graph) => (
                <GraphGridCard
                  key={graph.id}
                  graph={graph}
                  onView={() => {
                    console.log('查看图谱:', graph.name);
                    onGraphSelect?.(graph.name);
                  }}
                  onUpdate={(request) => updateGraphMutation.mutate({ name: graph.name, ...request })}
                  onDelete={() => deleteGraphMutation.mutate(graph.name)}
                />
              ))}
            </div>
          ) : (
            <Card className="border-dashed border-2">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Network className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">暂无知识图谱</h3>
                <p className="text-muted-foreground text-center mb-4">
                  还没有创建任何知识图谱。<br />
                  点击上方"创建图谱"按钮开始构建您的第一个知识图谱。
                </p>
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  创建图谱
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    );
  }

  // 侧边栏模式渲染（原有逻辑）
  return (
    <div className="space-y-6">
      {/* 头部操作区 */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">知识图谱管理</h3>
          <p className="text-sm text-muted-foreground">创建和管理知识图谱</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              refetchGraphs();
            }}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              console.log('测试API连接...');
              try {
                const response = await fetch('http://localhost:8000/ping');
                const data = await response.json();
                console.log('API连接测试成功:', data);
                alert('API连接正常: ' + JSON.stringify(data));
              } catch (error) {
                console.error('API连接测试失败:', error);
                alert('API连接失败: ' + error.message);
              }
            }}
          >
            测试连接
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetchGraphs()}
            disabled={graphsLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${graphsLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                创建图谱
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>创建知识图谱</DialogTitle>
                <DialogDescription>
                  基于文档内容创建新的知识图谱
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="graph-name">图谱名称</Label>
                  <Input
                    id="graph-name"
                    value={createForm.name}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="输入图谱名称"
                  />
                </div>
                <div>
                  <Label htmlFor="graph-filters">过滤条件 (JSON)</Label>
                  <Textarea
                    id="graph-filters"
                    value={createForm.filters}
                    onChange={(e) => setCreateForm(prev => ({ ...prev, filters: e.target.value }))}
                    placeholder='{"category": "技术文档"}'
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    用于筛选文档的元数据过滤条件
                  </p>
                </div>
                <div>
                  <Label>选择文档 (可选)</Label>
                  <Select
                    value=""
                    onValueChange={(value) => {
                      if (value && !createForm.documents.includes(value)) {
                        setCreateForm(prev => ({
                          ...prev,
                          documents: [...prev.documents, value]
                        }));
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择要包含的文档" />
                    </SelectTrigger>
                    <SelectContent>
                      {documents?.items?.map((doc) => (
                        <SelectItem key={doc.external_id} value={doc.external_id}>
                          {doc.filename || doc.metadata?.title || '未命名文档'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {createForm.documents.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {createForm.documents.map((docId) => {
                        const doc = documents?.items?.find(d => d.external_id === docId);
                        return (
                          <Badge
                            key={docId}
                            variant="secondary"
                            className="cursor-pointer"
                            onClick={() => {
                              setCreateForm(prev => ({
                                ...prev,
                                documents: prev.documents.filter(id => id !== docId)
                              }));
                            }}
                          >
                            {doc?.filename || '未知文档'} ×
                          </Badge>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  取消
                </Button>
                <Button
                  onClick={handleCreateGraph}
                  disabled={!createForm.name.trim() || createGraphMutation.isPending}
                >
                  {createGraphMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  创建
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Separator />

      {/* 图谱列表 */}
      <div className="space-y-4">
        {graphsLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>加载图谱列表...</span>
          </div>
        ) : graphs && graphs.length > 0 ? (
          <div className="grid gap-4">
            {graphs.map((graph) => (
              <GraphCard
                key={graph.id}
                graph={graph}
                isSelected={selectedGraph === graph.name}
                onSelect={() => {
                  console.log('🎯 GraphCard onSelect called for:', graph.name);
                  console.log('🎯 onGraphSelect function:', onGraphSelect);
                  onGraphSelect?.(graph.name);
                }}
                onUpdate={(request) => updateGraphMutation.mutate({ name: graph.name, ...request })}
                onDelete={() => deleteGraphMutation.mutate(graph.name)}
              />
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <Network className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">暂无知识图谱</h3>
              <p className="text-muted-foreground text-center mb-4">
                创建您的第一个知识图谱来开始探索文档间的关联关系
              </p>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                创建图谱
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

// 图谱卡片组件
interface GraphCardProps {
  graph: { id: string; name: string; status: string; created_at: string };
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (request: { additional_filters?: Record<string, any>; additional_documents?: string[] }) => void;
  onDelete: () => void;
}

function GraphCard({ graph, isSelected, onSelect, onUpdate, onDelete }: GraphCardProps) {
  const [showSettings, setShowSettings] = useState(false);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />已完成</Badge>;
      case 'processing':
        return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />处理中</Badge>;
      case 'failed':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />失败</Badge>;
      default:
        return <Badge variant="outline"><AlertCircle className="h-3 w-3 mr-1" />未知</Badge>;
    }
  };

  return (
    <Card className={`cursor-pointer transition-colors ${isSelected ? 'ring-2 ring-blue-500' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">{graph.name}</CardTitle>
          {getStatusBadge(graph.status)}
        </div>
        <CardDescription>
          创建时间: {new Date(graph.created_at).toLocaleString()}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex gap-2">
          <Button
            variant={isSelected ? "default" : "outline"}
            size="sm"
            onClick={onSelect}
          >
            <Eye className="h-4 w-4 mr-2" />
            {isSelected ? '已选择' : '查看'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setShowSettings(!showSettings);
              console.log('切换设置面板:', graph.name);
            }}
          >
            <Settings className="h-4 w-4 mr-2" />
            设置
          </Button>
        </div>

        {/* 设置面板 */}
        {showSettings && (
          <div className="mt-4 p-4 bg-muted/50 rounded-lg">
            <h4 className="text-sm font-semibold mb-3">图谱设置</h4>
            <div className="space-y-3">
              <div>
                <label className="text-xs text-muted-foreground">图谱ID</label>
                <p className="text-sm font-mono">{graph.id}</p>
              </div>
              <div>
                <label className="text-xs text-muted-foreground">状态</label>
                <p className="text-sm">{graph.status}</p>
              </div>
              <div>
                <label className="text-xs text-muted-foreground">创建时间</label>
                <p className="text-sm">{new Date(graph.created_at).toLocaleString()}</p>
              </div>
              <div className="flex gap-2 pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    console.log('更新图谱:', graph.name);
                    onUpdate({ additional_filters: { updated: true } });
                  }}
                >
                  更新图谱
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled
                  title="删除功能暂未开放"
                  onClick={() => {
                    toast.info('删除功能暂未开放，请联系管理员');
                  }}
                >
                  删除图谱
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// 图谱网格卡片组件
interface GraphGridCardProps {
  graph: {
    id: string;
    name: string;
    status: string;
    created_at: string;
    node_count: number;
    edge_count: number;
    document_count: number;
  };
  onView: () => void;
  onUpdate: (request: any) => void;
  onDelete: () => void;
}

function GraphGridCard({ graph, onView, onUpdate, onDelete }: GraphGridCardProps) {
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [showVisualization, setShowVisualization] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />已完成</Badge>;
      case 'processing':
        return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />处理中</Badge>;
      case 'failed':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />失败</Badge>;
      default:
        return <Badge variant="outline"><AlertCircle className="h-3 w-3 mr-1" />未知</Badge>;
    }
  };

  return (
    <>
      <Card className="hover:shadow-lg transition-shadow duration-200 flex flex-col h-full">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <Network className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-lg">{graph.name}</CardTitle>
            </div>
            {getStatusBadge(graph.status)}
          </div>
          <CardDescription className="text-sm">
            创建时间: {new Date(graph.created_at).toLocaleString()}
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0 flex-1 flex flex-col">
          <div className="space-y-3 flex-1">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">节点数量:</span>
              <span className="font-medium">{graph.node_count}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">关系数量:</span>
              <span className="font-medium">{graph.edge_count}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">文档数量:</span>
              <span className="font-medium">{graph.document_count}</span>
            </div>
          </div>

          <div className="mt-4 pt-3 border-t">
            <div className="flex gap-2">
              <Button
                size="sm"
                className="flex-1"
                onClick={() => {
                  setShowViewDialog(true);
                  // 如果图谱已完成，直接显示可视化
                  if (graph.status === 'completed') {
                    setShowVisualization(true);
                    onView(); // 调用原有的回调
                  }
                }}
              >
                <Eye className="h-4 w-4 mr-1" />
                查看
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={() => setShowDeleteDialog(true)}
              >
                <Trash className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 图谱查看对话框 */}
      <Dialog open={showViewDialog} onOpenChange={(open) => {
        setShowViewDialog(open);
        if (!open) {
          // 关闭对话框时重置可视化状态
          setShowVisualization(false);
        }
      }}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Network className="h-5 w-5" />
              {graph.name} - 知识图谱
            </DialogTitle>
            <DialogDescription>
              图谱状态: {getStatusBadge(graph.status)} |
              节点: {graph.node_count} |
              关系: {graph.edge_count} |
              文档: {graph.document_count}
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 min-h-[500px]">
            {showVisualization || graph.status === 'completed' ? (
              /* 真正的知识图谱可视化组件 */
              <div className="w-full h-[500px] border rounded-lg overflow-hidden">
                <KnowledgeGraph
                  selectedGraph={graph.name}
                  height="500px"
                  showMiniMap={true}
                  showControls={true}
                  useRealData={true}
                  onNodeSelect={(node: any) => {
                    console.log('Graph node selected:', node);
                  }}
                />
              </div>
            ) : (
              /* 图谱处理中的占位符 */
              <div className="w-full h-[500px] border rounded-lg bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                  <Network className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">图谱处理中</h3>
                  <p className="text-muted-foreground mb-4">
                    图谱还在处理中，请稍后查看
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => window.location.reload()}
                  >
                    刷新状态
                  </Button>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowViewDialog(false);
              setShowVisualization(false);
            }}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={(open) => open ? null : (() => setShowDeleteDialog(false))()} modal={true}>
        <DialogContent
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash className="h-5 w-5 text-red-500" />
              确认删除知识图谱
            </DialogTitle>
            <DialogDescription>
              您确定要删除知识图谱 <strong>"{graph.name}"</strong> 吗？
              <br />
              <span className="text-red-600 font-medium">
                此操作不可撤销，将永久删除图谱及其所有关联数据。
              </span>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                onDelete();
                setShowDeleteDialog(false);
              }}
            >
              <Trash className="h-4 w-4 mr-2" />
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}