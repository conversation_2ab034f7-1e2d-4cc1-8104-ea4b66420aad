JWT_SECRET_KEY="..."  # Required in production, optional in dev mode (dev_mode=true in morphik.toml)
POSTGRES_URI="postgresql+asyncpg:///morphik:morphik@localhost:5432/morphik" # Required for PostgreSQL database

UNSTRUCTURED_API_KEY="..." # Optional: Needed for parsing via unstructured API
OPENAI_API_KEY="..." # Optional: Needed for OpenAI embeddings and completions
ASSEMBLYAI_API_KEY="..." # Optional: Needed for combined parser
ANTHROPIC_API_KEY="..." # Optional: Needed for contextual parser
AWS_ACCESS_KEY="..." # Optional: Needed for AWS S3 storage
AWS_SECRET_ACCESS_KEY="..." # Optional: Needed for AWS S3 storage
