import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, Loader2, FileText, Settings, Tag } from 'lucide-react';
import { useSearch } from '@/hooks/useMorphik';
import { toast } from 'sonner';
import type { SearchResult as MorphikSearchResult } from '@/types/morphik';
import SearchResultCard from './SearchResultCard';

interface SmartSearchProps {
  onResultSelect?: (result: MorphikSearchResult) => void;
  defaultQuery?: string;
  showFilters?: boolean;
}

function SmartSearch({
  onResultSelect,
  defaultQuery = '',
  showFilters = true
}: SmartSearchProps) {
  const [query, setQuery] = useState(defaultQuery);
  const [searchResults, setSearchResults] = useState<MorphikSearchResult[]>([]);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchOptions, setSearchOptions] = useState({
    limit: 10,
    similarity_threshold: 0.0,
    use_reranking: true,
  });

  const searchMutation = useSearch();

  // 搜索建议
  const searchSuggestions = [
    "如何使用AI技术",
    "机器学习算法",
    "深度学习模型",
    "数据分析方法",
    "自然语言处理",
    "计算机视觉",
    "推荐系统",
    "神经网络"
  ];

  // 执行搜索
  const handleSearch = async (searchQuery: string = query) => {
    if (!searchQuery.trim()) return;

    try {
      // 添加到搜索历史
      if (!searchHistory.includes(searchQuery)) {
        setSearchHistory(prev => [searchQuery, ...prev.slice(0, 9)]); // 保留最近10条
      }

      const result = await searchMutation.mutateAsync({
        query: searchQuery,
        filters: {},
        limit: searchOptions.limit,
        similarity_threshold: searchOptions.similarity_threshold,
      });

      setSearchResults(result.results);
      setShowSuggestions(false);
      toast.success(`找到 ${result.results.length} 个相关结果`);
    } catch (error) {
      console.error('Search failed:', error);
      toast.error('搜索失败，请重试');
      setSearchResults([]);
    }
  };

  // 处理输入变化
  const handleInputChange = (value: string) => {
    setQuery(value);
    setShowSuggestions(value.length > 0);
  };

  // 选择建议
  const handleSuggestionSelect = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
    handleSearch(suggestion);
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="space-y-4">
      {/* 搜索输入框 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            智能搜索
          </CardTitle>
          <CardDescription>
            在知识库中搜索相关文档和内容
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="输入您的问题或关键词..."
              value={query}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyDown={handleKeyPress}
              onFocus={() => setShowSuggestions(query.length > 0)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              className="pl-10 pr-20"
            />
            <Button
              onClick={() => handleSearch()}
              disabled={!query.trim() || searchMutation.isPending}
              className="absolute right-1 top-1 h-8"
              size="sm"
            >
              {searchMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* 搜索建议 */}
          {showSuggestions && (
            <div className="absolute top-full left-0 right-0 z-10 mt-1 bg-background border rounded-md shadow-lg">
              <div className="p-2">
                {/* 搜索历史 */}
                {searchHistory.length > 0 && (
                  <div className="mb-2">
                    <div className="text-xs text-muted-foreground mb-1 px-2">最近搜索</div>
                    {searchHistory.slice(0, 3).map((historyItem, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionSelect(historyItem)}
                        className="w-full text-left px-2 py-1 text-sm hover:bg-muted rounded text-muted-foreground"
                      >
                        {historyItem}
                      </button>
                    ))}
                  </div>
                )}

                {/* 搜索建议 */}
                <div>
                  <div className="text-xs text-muted-foreground mb-1 px-2">搜索建议</div>
                  {searchSuggestions
                    .filter(suggestion =>
                      suggestion.toLowerCase().includes(query.toLowerCase())
                    )
                    .slice(0, 5)
                    .map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleSuggestionSelect(suggestion)}
                        className="w-full text-left px-2 py-1 text-sm hover:bg-muted rounded"
                      >
                        {suggestion}
                      </button>
                    ))}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 快速搜索标签 */}
      {!query && searchResults.length === 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              快速搜索
            </CardTitle>
            <CardDescription>
              点击下方标签快速搜索相关内容
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {searchSuggestions.map((suggestion, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                  onClick={() => handleSuggestionSelect(suggestion)}
                >
                  {suggestion}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 搜索选项 */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              搜索选项
            </CardTitle>
            <CardDescription>
              调整搜索参数以获得更精确的结果
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">结果数量</label>
                <Input
                  type="number"
                  min="1"
                  max="50"
                  value={searchOptions.limit}
                  onChange={(e) => setSearchOptions(prev => ({
                    ...prev,
                    limit: parseInt(e.target.value) || 10
                  }))}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">相似度阈值</label>
                <Input
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  value={searchOptions.similarity_threshold}
                  onChange={(e) => setSearchOptions(prev => ({
                    ...prev,
                    similarity_threshold: parseFloat(e.target.value) || 0.0
                  }))}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">重排序</label>
                <Button
                  variant={searchOptions.use_reranking ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSearchOptions(prev => ({
                    ...prev,
                    use_reranking: !prev.use_reranking
                  }))}
                  className="w-full"
                >
                  {searchOptions.use_reranking ? "已启用" : "已禁用"}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 搜索结果 */}
      {searchResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              搜索结果 ({searchResults.length})
            </CardTitle>
            <CardDescription>
              找到 {searchResults.length} 个相关文档片段
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {searchResults.map((result, index) => (
              <SearchResultCard
                key={result.id || index}
                result={result}
                index={index}
                onSelect={onResultSelect}
                showMetadata={true}
              />
            ))}
          </CardContent>
        </Card>
      )}

      {/* 空状态 */}
      {query && searchResults.length === 0 && !searchMutation.isPending && (
        <Card>
          <CardContent className="text-center py-12">
            <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">未找到相关结果</h3>
            <p className="text-muted-foreground">
              尝试使用不同的关键词或调整搜索选项
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default SmartSearch;
