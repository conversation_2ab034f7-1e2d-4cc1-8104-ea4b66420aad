"""Add new permissions for knowledge base and AI services

Revision ID: 005
Revises: 004
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004_add_rbac_tables'
branch_labels = None
depends_on = None


def upgrade():
    """添加新的权限"""

    # 插入知识库管理权限
    op.execute("""
        INSERT INTO permissions (name, description, category) VALUES
        ('knowledge:read', '查看知识库', '知识库管理'),
        ('knowledge:create', '创建知识库', '知识库管理'),
        ('knowledge:update', '更新知识库', '知识库管理'),
        ('knowledge:delete', '删除知识库', '知识库管理'),
        ('knowledge:export', '导出知识库', '知识库管理')
        ON CONFLICT (name) DO NOTHING;
    """)

    # 插入AI服务权限
    op.execute("""
        INSERT INTO permissions (name, description, category) VALUES
        ('ai:chat', '使用AI对话', 'AI服务'),
        ('ai:embedding', '使用向量化服务', 'AI服务'),
        ('ai:rerank', '使用重排序服务', 'AI服务'),
        ('ai:config', '配置AI服务', 'AI服务')
        ON CONFLICT (name) DO NOTHING;
    """)

    # 为超级管理员分配新权限
    op.execute("""
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r, permissions p
        WHERE r.name = 'superadmin'
        AND p.name IN (
            'knowledge:read', 'knowledge:create', 'knowledge:update', 'knowledge:delete', 'knowledge:export',
            'ai:chat', 'ai:embedding', 'ai:rerank', 'ai:config'
        )
        ON CONFLICT (role_id, permission_id) DO NOTHING;
    """)

    # 为管理员分配部分新权限
    op.execute("""
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r, permissions p
        WHERE r.name = 'admin'
        AND p.name IN (
            'knowledge:read', 'knowledge:create', 'knowledge:update',
            'ai:chat', 'ai:embedding', 'ai:rerank'
        )
        ON CONFLICT (role_id, permission_id) DO NOTHING;
    """)

    # 为普通用户分配基础权限
    op.execute("""
        INSERT INTO role_permissions (role_id, permission_id)
        SELECT r.id, p.id
        FROM roles r, permissions p
        WHERE r.name = 'user'
        AND p.name IN (
            'knowledge:read',
            'ai:chat'
        )
        ON CONFLICT (role_id, permission_id) DO NOTHING;
    """)


def downgrade():
    """移除新添加的权限"""

    # 删除角色权限关联
    op.execute("""
        DELETE FROM role_permissions
        WHERE permission_id IN (
            SELECT id FROM permissions
            WHERE name IN (
                'knowledge:read', 'knowledge:create', 'knowledge:update', 'knowledge:delete', 'knowledge:export',
                'ai:chat', 'ai:embedding', 'ai:rerank', 'ai:config'
            )
        );
    """)

    # 删除权限
    op.execute("""
        DELETE FROM permissions
        WHERE name IN (
            'knowledge:read', 'knowledge:create', 'knowledge:update', 'knowledge:delete', 'knowledge:export',
            'ai:chat', 'ai:embedding', 'ai:rerank', 'ai:config'
        );
    """)
