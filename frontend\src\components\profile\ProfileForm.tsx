/**
 * 用户资料编辑表单组件
 */
import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Save, User } from 'lucide-react'
import { useAuthStore } from '@/store/auth'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface ProfileData {
  id: number
  username: string
  email: string
  full_name?: string
  phone?: string
  avatar_url?: string
  is_active: boolean
  is_superuser: boolean
  created_at: string
  updated_at: string
}

export const ProfileForm: React.FC = () => {
  const { token } = useAuthStore()
  const [profile, setProfile] = useState<ProfileData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    full_name: '',
    phone: '',
  })

  // 获取用户资料
  const fetchProfile = async () => {
    if (!token) return

    try {
      const response = await fetch(`${API_BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data: ProfileData = await response.json()
        setProfile(data)
        setFormData({
          username: data.username,
          email: data.email,
          full_name: data.full_name || '',
          phone: data.phone || '',
        })
      } else {
        setError('获取用户资料失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchProfile()
  }, [token])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
    // 清除消息
    if (error) setError('')
    if (success) setSuccess('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!token) return

    setIsSaving(true)
    setError('')
    setSuccess('')

    try {
      // 只发送有变化的字段
      const updateData: any = {}
      if (formData.username !== profile?.username) updateData.username = formData.username
      if (formData.email !== profile?.email) updateData.email = formData.email
      if (formData.full_name !== (profile?.full_name || '')) updateData.full_name = formData.full_name
      if (formData.phone !== (profile?.phone || '')) updateData.phone = formData.phone

      if (Object.keys(updateData).length === 0) {
        setSuccess('没有需要更新的内容')
        return
      }

      const response = await fetch(`${API_BASE_URL}/auth/profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      if (response.ok) {
        setSuccess('资料更新成功')
        // 重新获取最新资料
        await fetchProfile()
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '更新失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>加载中...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <User className="mr-2 h-5 w-5" />
          个人资料
        </CardTitle>
        <CardDescription>
          管理您的个人信息和账户设置
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {success && (
            <Alert>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="username">用户名</Label>
              <Input
                id="username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                disabled={isSaving}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">邮箱地址</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                disabled={isSaving}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="full_name">全名</Label>
              <Input
                id="full_name"
                name="full_name"
                value={formData.full_name}
                onChange={handleInputChange}
                disabled={isSaving}
                placeholder="请输入您的全名"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phone">电话号码</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleInputChange}
                disabled={isSaving}
                placeholder="请输入电话号码"
              />
            </div>
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button 
              type="submit" 
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  保存更改
                </>
              )}
            </Button>
          </div>
        </form>
        
        {profile && (
          <div className="mt-6 pt-6 border-t">
            <h3 className="text-sm font-medium text-muted-foreground mb-2">账户信息</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">账户状态：</span>
                <span className={profile.is_active ? "text-green-600" : "text-red-600"}>
                  {profile.is_active ? "正常" : "已禁用"}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">账户类型：</span>
                <span>{profile.is_superuser ? "管理员" : "普通用户"}</span>
              </div>
              <div>
                <span className="text-muted-foreground">注册时间：</span>
                <span>{new Date(profile.created_at).toLocaleString()}</span>
              </div>
              <div>
                <span className="text-muted-foreground">最后更新：</span>
                <span>{new Date(profile.updated_at).toLocaleString()}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
