"""
用户资料管理API路由
提供用户资料查看、编辑、头像上传等功能
"""
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.core.auth import get_current_active_user, verify_password, get_password_hash
from app.crud.user import user_crud
from app.services.file_upload import file_upload_service
from app.schemas.profile import (
    ProfileUpdate,
    ProfileResponse,
    AvatarUploadResponse,
    AvatarDeleteResponse,
    PasswordChangeRequest,
    PasswordChangeResponse
)
from app.models.user import User

router = APIRouter()


@router.get("/profile", response_model=ProfileResponse, summary="获取用户资料")
async def get_profile(
    current_user: User = Depends(get_current_active_user)
) -> ProfileResponse:
    """
    获取当前用户的详细资料
    
    返回用户的完整信息，包括头像URL
    """
    return ProfileResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        full_name=current_user.full_name,
        phone=current_user.phone,
        avatar_url=current_user.avatar_url,
        is_active=current_user.is_active,
        is_superuser=current_user.is_superuser,
        created_at=current_user.created_at.isoformat(),
        updated_at=current_user.updated_at.isoformat()
    )


@router.put("/profile", response_model=ProfileResponse, summary="更新用户资料")
async def update_profile(
    *,
    db: AsyncSession = Depends(get_db),
    profile_update: ProfileUpdate,
    current_user: User = Depends(get_current_active_user)
) -> ProfileResponse:
    """
    更新用户资料
    
    - **username**: 用户名（可选）
    - **email**: 邮箱地址（可选）
    - **full_name**: 全名（可选）
    - **phone**: 电话号码（可选）
    """
    try:
        # 获取更新数据
        update_data = profile_update.model_dump(exclude_unset=True)

        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="没有提供要更新的数据"
            )

        # 使用 SQLAlchemy ORM 更新用户信息
        updated_user = await user_crud.update(db=db, db_obj=current_user, obj_in=profile_update)

        return ProfileResponse(
            id=updated_user.id,
            username=updated_user.username,
            email=updated_user.email,
            full_name=updated_user.full_name,
            phone=updated_user.phone,
            avatar_url=updated_user.avatar_url,
            is_active=updated_user.is_active,
            is_superuser=updated_user.is_superuser,
            created_at=updated_user.created_at.isoformat(),
            updated_at=updated_user.updated_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户资料失败: {str(e)}"
        )


@router.post("/upload-avatar", response_model=AvatarUploadResponse, summary="上传用户头像")
async def upload_avatar(
    *,
    db: AsyncSession = Depends(get_db),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
) -> AvatarUploadResponse:
    """
    上传用户头像
    
    - 支持的格式：JPG, PNG, GIF, WebP
    - 文件大小限制：2MB
    - 会自动替换现有头像
    """
    try:
        # 保存头像文件
        avatar_url = await file_upload_service.save_avatar(current_user.id, file)

        # 使用 SQLAlchemy ORM 更新头像URL
        from app.schemas.profile import ProfileUpdate
        avatar_update = ProfileUpdate(avatar_url=avatar_url)
        updated_user = await user_crud.update(db=db, db_obj=current_user, obj_in=avatar_update)

        return AvatarUploadResponse(
            message="头像上传成功",
            avatar_url=updated_user.avatar_url
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传头像失败: {str(e)}"
        )


@router.delete("/avatar", response_model=AvatarDeleteResponse, summary="删除用户头像")
async def delete_avatar(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> AvatarDeleteResponse:
    """
    删除用户头像
    
    删除用户的头像文件并清空数据库中的头像URL
    """
    try:
        # 删除头像文件
        if current_user.avatar_url:
            await file_upload_service.delete_avatar(current_user.id, current_user.avatar_url)

        # 使用 SQLAlchemy ORM 清空头像URL
        from app.schemas.profile import ProfileUpdate
        avatar_clear = ProfileUpdate(avatar_url=None)
        await user_crud.update(db=db, db_obj=current_user, obj_in=avatar_clear)

        return AvatarDeleteResponse(
            message="头像删除成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除头像失败: {str(e)}"
        )


@router.post("/change-password", response_model=PasswordChangeResponse, summary="修改密码")
async def change_password(
    *,
    db: AsyncSession = Depends(get_db),
    password_change: PasswordChangeRequest,
    current_user: User = Depends(get_current_active_user)
) -> PasswordChangeResponse:
    """
    修改用户密码
    
    - **current_password**: 当前密码
    - **new_password**: 新密码（6-50字符）
    - **confirm_password**: 确认新密码
    """
    try:
        # 验证确认密码
        if password_change.new_password != password_change.confirm_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="新密码和确认密码不一致"
            )
        
        # 验证当前密码
        if not verify_password(password_change.current_password, current_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="当前密码错误"
            )
        
        # 检查新密码是否与当前密码相同
        if verify_password(password_change.new_password, current_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="新密码不能与当前密码相同"
            )
        
        # 更新密码
        hashed_password = get_password_hash(password_change.new_password)
        from app.schemas.user import UserPasswordUpdate
        password_update = UserPasswordUpdate(hashed_password=hashed_password)
        await user_crud.update(db=db, db_obj=current_user, obj_in=password_update)

        return PasswordChangeResponse(
            message="密码修改成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"修改密码失败: {str(e)}"
        )
