"""
RBAC权限控制CRUD操作
提供角色、权限的数据库操作方法
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, and_
from sqlalchemy.orm import selectinload
from app.models.role import Role, Permission
from app.models.user_role import UserRole, RolePermission
from app.models.user import User
from app.schemas.rbac import (
    RoleCreate, RoleUpdate, PermissionCreate, PermissionUpdate
)


class RoleCRUD:
    """角色CRUD操作类"""
    
    async def get(self, db: AsyncSession, id: int) -> Optional[Role]:
        """根据ID获取角色"""
        result = await db.execute(
            select(Role)
            .options(selectinload(Role.role_permissions).selectinload(RolePermission.permission))
            .where(Role.id == id)
        )
        return result.scalar_one_or_none()
    
    async def get_by_name(self, db: AsyncSession, name: str) -> Optional[Role]:
        """根据名称获取角色"""
        result = await db.execute(select(Role).where(Role.name == name))
        return result.scalar_one_or_none()
    
    async def get_multi(self, db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Role]:
        """获取角色列表"""
        result = await db.execute(
            select(Role)
            .options(selectinload(Role.role_permissions).selectinload(RolePermission.permission))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def create(self, db: AsyncSession, obj_in: RoleCreate) -> Role:
        """创建角色"""
        # 创建角色
        db_obj = Role(
            name=obj_in.name,
            description=obj_in.description,
            is_active=obj_in.is_active
        )
        db.add(db_obj)
        await db.flush()
        
        # 分配权限
        if obj_in.permission_ids:
            for permission_id in obj_in.permission_ids:
                role_permission = RolePermission(
                    role_id=db_obj.id,
                    permission_id=permission_id
                )
                db.add(role_permission)
        
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def update(self, db: AsyncSession, db_obj: Role, obj_in: RoleUpdate) -> Role:
        """更新角色"""
        # 更新基本信息
        if obj_in.name is not None:
            db_obj.name = obj_in.name
        if obj_in.description is not None:
            db_obj.description = obj_in.description
        if obj_in.is_active is not None:
            db_obj.is_active = obj_in.is_active
        
        # 更新权限
        if obj_in.permission_ids is not None:
            # 删除现有权限
            await db.execute(
                delete(RolePermission).where(RolePermission.role_id == db_obj.id)
            )
            
            # 添加新权限
            for permission_id in obj_in.permission_ids:
                role_permission = RolePermission(
                    role_id=db_obj.id,
                    permission_id=permission_id
                )
                db.add(role_permission)
        
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def remove(self, db: AsyncSession, id: int) -> bool:
        """删除角色"""
        result = await db.execute(delete(Role).where(Role.id == id))
        await db.commit()
        return result.rowcount > 0


class PermissionCRUD:
    """权限CRUD操作类"""
    
    async def get(self, db: AsyncSession, id: int) -> Optional[Permission]:
        """根据ID获取权限"""
        result = await db.execute(select(Permission).where(Permission.id == id))
        return result.scalar_one_or_none()
    
    async def get_by_name(self, db: AsyncSession, name: str) -> Optional[Permission]:
        """根据名称获取权限"""
        result = await db.execute(select(Permission).where(Permission.name == name))
        return result.scalar_one_or_none()
    
    async def get_multi(self, db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Permission]:
        """获取权限列表"""
        result = await db.execute(
            select(Permission).offset(skip).limit(limit)
        )
        return result.scalars().all()
    
    async def get_by_category(self, db: AsyncSession, category: str) -> List[Permission]:
        """根据分类获取权限"""
        result = await db.execute(
            select(Permission).where(Permission.category == category)
        )
        return result.scalars().all()
    
    async def create(self, db: AsyncSession, obj_in: PermissionCreate) -> Permission:
        """创建权限"""
        db_obj = Permission(
            name=obj_in.name,
            description=obj_in.description,
            category=obj_in.category
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def update(self, db: AsyncSession, db_obj: Permission, obj_in: PermissionUpdate) -> Permission:
        """更新权限"""
        if obj_in.name is not None:
            db_obj.name = obj_in.name
        if obj_in.description is not None:
            db_obj.description = obj_in.description
        if obj_in.category is not None:
            db_obj.category = obj_in.category
        
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def remove(self, db: AsyncSession, id: int) -> bool:
        """删除权限"""
        result = await db.execute(delete(Permission).where(Permission.id == id))
        await db.commit()
        return result.rowcount > 0


class UserRoleCRUD:
    """用户角色CRUD操作类"""
    
    async def assign_roles(self, db: AsyncSession, user_id: int, role_ids: List[int], assigned_by: int = None) -> List[UserRole]:
        """为用户分配角色"""
        # 删除现有角色
        await db.execute(delete(UserRole).where(UserRole.user_id == user_id))
        
        # 分配新角色
        user_roles = []
        for role_id in role_ids:
            user_role = UserRole(
                user_id=user_id,
                role_id=role_id,
                assigned_by=assigned_by
            )
            db.add(user_role)
            user_roles.append(user_role)
        
        await db.commit()
        return user_roles
    
    async def get_user_roles(self, db: AsyncSession, user_id: int) -> List[UserRole]:
        """获取用户的所有角色"""
        result = await db.execute(
            select(UserRole)
            .options(selectinload(UserRole.role))
            .where(UserRole.user_id == user_id)
        )
        return result.scalars().all()
    
    async def get_user_permissions(self, db: AsyncSession, user_id: int) -> List[Permission]:
        """获取用户的所有权限"""
        result = await db.execute(
            select(Permission)
            .join(RolePermission, Permission.id == RolePermission.permission_id)
            .join(Role, RolePermission.role_id == Role.id)
            .join(UserRole, Role.id == UserRole.role_id)
            .where(and_(UserRole.user_id == user_id, Role.is_active == True))
            .distinct()
        )
        return result.scalars().all()
    
    async def check_permission(self, db: AsyncSession, user_id: int, permission_name: str) -> bool:
        """检查用户是否有指定权限"""
        result = await db.execute(
            select(Permission.id)
            .join(RolePermission, Permission.id == RolePermission.permission_id)
            .join(Role, RolePermission.role_id == Role.id)
            .join(UserRole, Role.id == UserRole.role_id)
            .where(and_(
                UserRole.user_id == user_id,
                Permission.name == permission_name,
                Role.is_active == True
            ))
        )
        return result.scalar_one_or_none() is not None
    
    async def get_users_with_roles(self, db: AsyncSession, skip: int = 0, limit: int = 100) -> List[User]:
        """获取带角色信息的用户列表"""
        result = await db.execute(
            select(User)
            .options(selectinload(User.user_roles).selectinload(UserRole.role))
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()


# 创建全局实例
role_crud = RoleCRUD()
permission_crud = PermissionCRUD()
user_role_crud = UserRoleCRUD()
