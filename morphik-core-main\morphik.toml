[api]
host = "0.0.0.0"
port = 8000
reload = true

[auth]
jwt_algorithm = "HS256"
dev_mode = true  # 默认启用，便于本地开发
dev_entity_id = "dev_user"  # 默认开发用户ID
dev_entity_type = "developer"  # 默认开发用户类型
dev_permissions = ["read", "write", "admin"]  # 默认开发权限

#### 注册的模型
[registered_models]
# OpenAI 模型
openai_gpt4-1 = { model_name = "gpt-4.1" }
openai_gpt4-1-mini = { model_name = "gpt-4.1-mini" }

# Azure OpenAI 模型
azure_gpt4 = { model_name = "gpt-4", api_base = "YOUR_AZURE_URL_HERE", api_version = "2023-05-15", deployment_id = "gpt-4-deployment" }
azure_gpt35 = { model_name = "gpt-3.5-turbo", api_base = "YOUR_AZURE_URL_HERE", api_version = "2023-05-15", deployment_id = "gpt-35-turbo-deployment" }

# Anthropic 模型
claude_opus = { model_name = "claude-3-opus-20240229" }
claude_sonnet = { model_name = "claude-3-7-sonnet-latest" }

# 通义千问模型 (Qwen)
qwen-plus = { model_name = "qwen-plus", api_key = "sk-a281ffc3039c497f96ba5b62ce6baa3f", api_base = "https://dashscope.aliyuncs.com/compatible-mode/v1", description = "通义千问-Plus", cost_per_token = 0.02, streaming = true }
qwen_max = { model_name = "qwen-max-2025-01-25", api_key = "sk-a281ffc3039c497f96ba5b62ce6baa3f", api_base = "https://dashscope.aliyuncs.com/compatible-mode/v1", description = "通义千问Max版本", cost_per_token = 0.02, streaming = true }
qwen_embedding = { model_name = "text-embedding-v4", api_key = "sk-a281ffc3039c497f96ba5b62ce6baa3f", api_base = "https://dashscope.aliyuncs.com/compatible-mode/v1", description = "通义千问嵌入模型", cost_per_token = 0.0007, embedding = true, dimensions = 768 }


# Ollama 模型 (根据您的部署修改 api_base)
# - 本地 Ollama: "http://localhost:11434" (默认)
# - Morphik 在 Docker，Ollama 本地: "http://host.docker.internal:11434"
# - 都在 Docker: "http://ollama:11434"
ollama_qwen_chat = { model_name = "ollama_chat/qwen3:8b", api_base = "http://**************:11434" }
# - ollama_qwen_chat = { model_name = "ollama_chat/Qwen3-8B-M:latest", api_base = "http://host.docker.internal:11434" }
ollama_gemma3_chat = { model_name = "ollama_chat/gemma3n:e2b", api_base = "http://host.docker.internal:11434" }
ollama_deepseek_chat = { model_name = "ollama_chat/deepseek-r1:8b", api_base = "http://host.docker.internal:11434" }
ollama_qwen_vision = { model_name = "ollama_chat/qwen2.5vl:latest", api_base = "http://host.docker.internal:11434", vision = true }
ollama_llama_vision = { model_name = "ollama_chat/llama3.2-vision", api_base = "http://host.docker.internal:11434", vision = true }
ollama_embedding = { model_name = "ollama/nomic-embed-text:latest", api_base = "http://host.docker.internal:11434" }

openai_embedding = { model_name = "text-embedding-3-small" }
openai_embedding_large = { model_name = "text-embedding-3-large" }
azure_embedding = { model_name = "text-embedding-ada-002", api_base = "YOUR_AZURE_URL_HERE", api_version = "2023-05-15", deployment_id = "embedding-ada-002" }


#### 组件配置 ####

[agent]
model = "ollama_qwen_chat" # 智能体逻辑使用的模型

[completion]
model = "ollama_qwen_chat" # 默认注册使用的模型
default_max_tokens = "2000"
default_temperature = 0.1

[database]
provider = "postgres"
# 连接池设置
pool_size = 20           # 连接池中的最大连接数
max_overflow = 30        # 超出 pool_size 可以创建的最大连接数
pool_recycle = 3600      # 连接回收时间（秒），1小时后回收连接
pool_timeout = 10        # 从连接池获取连接的等待时间（秒）
pool_pre_ping = true     # 使用连接前检查连接可用性
max_retries = 3          # 数据库操作重试次数
retry_delay = 1.0        # 重试之间的初始延迟时间（秒）

[embedding]
model = "ollama_embedding"  # 使用本地 Ollama embedding 模型
dimensions = 768  # Ollama nomic-embed-text 的维度
similarity_metric = "cosine"
# CPU 优化设置
batch_size = 32  # 减小批处理大小，降低内存使用
max_tokens_per_chunk = 8000  # 限制每个块的最大 token 数

[parser]
chunk_size = 8000 # 原 6000
chunk_overlap = 300
use_unstructured_api = false
use_contextual_chunking = true
contextual_chunking_model = "ollama_qwen_chat"  # 引用 registered_models 中的键
# OCR配置 - 支持中文字符识别
ocr_languages = ["chi_sim", "eng"]  # 支持简体中文和英文
ocr_strategy = "hi_res"  # 高分辨率OCR策略

[document_analysis]
model = "ollama_qwen_chat"  # 引用 registered_models 中的键

[parser.vision]
model = "ollama_qwen_chat"  # 引用 registered_models 中的键
frame_sample_rate = -1  # 设置为 -1 禁用帧字幕

[reranker]
use_reranker = true
provider = "flag"
model_name = "BAAI/bge-reranker-large"  # GPU 可以使用大模型
query_max_length = 512  # GPU 可以处理更长序列
passage_max_length = 1024  # GPU 可以处理更长序列
use_fp16 = true  # GPU 支持 fp16 加速
device = "cuda"  # 使用 GPU
# GPU 优化设置
batch_size = 32  # GPU 可以处理更大批次
max_length = 1024  # 最大序列长度

[storage]
provider = "local"
storage_path = "/app/storage"

# [storage]
# provider = "aws-s3"
# region = "us-east-2"
# bucket_name = "morphik-s3-storage"

[vector_store]
provider = "pgvector"

[rules]
model = "ollama_qwen_chat"
batch_size = 4096

[morphik]
enable_colpali = false
mode = "self_hosted"  # "cloud" 或 "self_hosted"
api_domain = "api.morphik.ai"  # 云端 URI 的 API 域名
# 仅当 colpali_mode 为 "api" 时调用嵌入 API
morphik_embedding_api_domain = "http://localhost:6000"  # 多向量嵌入服务的端点
colpali_mode = "off" # "off"、"local" 或 "api" - 暂时禁用，内存不足

# ColPali GPU 内存优化配置
[colpali]
# 模型配置
model_name = "vidore/colpali-v1.2"  # 使用较小的模型版本
device = "cuda"
torch_dtype = "float16"  # 使用半精度减少内存使用
# 内存优化设置
max_memory_per_gpu = "6GB"  # 为 RTX 4060 预留内存
low_cpu_mem_usage = true
device_map = "auto"
load_in_8bit = false  # 8bit 量化可能影响质量
load_in_4bit = false  # 4bit 量化可能影响质量
# 批处理优化
batch_size = 1  # 减小批处理大小
max_length = 512  # 限制最大序列长度
# 缓存设置
use_cache = true
cache_dir = "/app/storage/colpali_cache"

[redis]
host = "zht_morphik_redis_0624"  # Docker 环境使用重命名后的服务名
port = 6379

[graph]
model = "ollama_qwen_chat"
enable_entity_resolution = true
# 实体提取优化配置
entity_extraction_temperature = 0.1  # 降低温度提高一致性
max_entities_per_chunk = 20  # 增加每个块的最大实体数
min_entity_confidence = 0.6  # 设置最小置信度阈值

# [graph]
# mode="api"
# base_url="https://graph-api.morphik.ai"

[telemetry]
enabled = false
honeycomb_enabled = true
honeycomb_endpoint = "https://api.honeycomb.io"
honeycomb_proxy_endpoint = "https://otel-proxy.onrender.com"
service_name = "databridge-core"
otlp_timeout = 10
otlp_max_retries = 3
otlp_retry_delay = 1
otlp_max_export_batch_size = 512
otlp_schedule_delay_millis = 5000
otlp_max_queue_size = 2048

