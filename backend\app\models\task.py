"""
任务管理数据模型
定义任务的数据库表结构
"""
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Enum
from sqlalchemy.sql import func
from app.core.database import Base
import enum


class TaskStatus(str, enum.Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 待处理
    IN_PROGRESS = "in_progress"  # 进行中
    COMPLETED = "completed"  # 已完成
    CANCELLED = "cancelled"  # 已取消


class TaskPriority(str, enum.Enum):
    """任务优先级枚举"""
    LOW = "low"         # 低优先级
    MEDIUM = "medium"   # 中优先级
    HIGH = "high"       # 高优先级
    URGENT = "urgent"   # 紧急


class Task(Base):
    """
    任务数据模型
    用于演示完整的CRUD操作
    """
    __tablename__ = "tasks"
    
    # 主键ID
    id = Column(Integer, primary_key=True, index=True, comment="任务ID")
    
    # 任务标题
    title = Column(String(200), nullable=False, index=True, comment="任务标题")
    
    # 任务描述
    description = Column(Text, nullable=True, comment="任务描述")
    
    # 任务状态
    status = Column(
        Enum(TaskStatus), 
        default=TaskStatus.PENDING, 
        nullable=False, 
        index=True,
        comment="任务状态"
    )
    
    # 任务优先级
    priority = Column(
        Enum(TaskPriority), 
        default=TaskPriority.MEDIUM, 
        nullable=False, 
        index=True,
        comment="任务优先级"
    )
    
    # 负责人
    assignee = Column(String(100), nullable=True, comment="负责人")
    
    # 是否激活
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 创建时间
    created_at = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        comment="创建时间"
    )
    
    # 更新时间
    updated_at = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        onupdate=func.now(),
        comment="更新时间"
    )
    
    def __repr__(self):
        return f"<Task(id={self.id}, title='{self.title}', status='{self.status}')>"
