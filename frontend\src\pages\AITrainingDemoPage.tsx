/**
 * AI销售培训演示页面
 * 展示全新的AI聊天培训功能
 */

import { useState } from 'react'
import {
  Sparkles,
  Users,
  Brain,
  Target,
  Play,
  Clock,
  BookOpen,
  Zap,
  Globe,
  Package,
  User
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { AITrainingDialog } from '@/components/sales-training/AITrainingDialog'
import type { TrainingCustomer } from '@/types/salesTraining'

export default function AITrainingDemoPage() {
  const [selectedCustomer, setSelectedCustomer] = useState<TrainingCustomer | null>(null)
  const [showTrainingDialog, setShowTrainingDialog] = useState(false)

  // 演示客户数据
  const demoCustomers: TrainingCustomer[] = [
    {
      id: 'demo-1',
      name: 'Hans Mueller',
      country: { id: 1, name: '德国', code: 'DE', flag: '🇩🇪' },
      product: { id: 1, name: '工业设备', category: '制造业' },
      createdAt: new Date(),
      updatedAt: new Date(),
      background: {
        company: 'German Manufacturing GmbH',
        position: '技术总监',
        experience: '10年行业经验',
        preferences: ['技术先进', '可靠性高']
      }
    },
    {
      id: 'demo-2',
      name: 'Hans Mueller',
      country: { id: 1, name: '德国', code: 'DE', flag: '🇩🇪' },
      product: { id: 2, name: '企业软件', category: '信息技术' },
      createdAt: new Date(),
      updatedAt: new Date(),
      background: {
        company: '德国科技有限公司',
        position: 'IT总监',
        experience: '8年以上',
        preferences: ['数据安全', '系统集成', '成本控制']
      }
    },
    {
      id: 'demo-3',
      name: 'Tanaka Hiroshi',
      country: { id: 2, name: '日本', code: 'JP', flag: '🇯🇵' },
      product: { id: 3, name: '电子产品', category: '电子制造' },
      createdAt: new Date(),
      updatedAt: new Date(),
      background: {
        company: '日本电子株式会社',
        position: '产品经理',
        experience: '12年以上',
        preferences: ['创新技术', '品质管理', '长期合作']
      }
    }
  ]

  const handleStartTraining = (customer: TrainingCustomer) => {
    setSelectedCustomer(customer)
    setShowTrainingDialog(true)
  }

  const handleCloseTraining = () => {
    setShowTrainingDialog(false)
    setSelectedCustomer(null)
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* 页面头部 */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <Sparkles className="w-8 h-8 text-primary" />
          <h1 className="text-3xl font-bold">AI销售培训系统</h1>
        </div>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          基于Morphik Core AI技术的智能销售培训平台，提供真实的客户对话模拟和实时评估反馈
        </p>
      </div>

      {/* 功能特色 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <Brain className="w-8 h-8 text-blue-500 mb-2" />
            <CardTitle className="text-lg">AI智能对话</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              基于大语言模型的智能客户角色扮演，提供真实的销售对话体验
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <Target className="w-8 h-8 text-green-500 mb-2" />
            <CardTitle className="text-lg">实时评估</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              多维度实时评分系统，即时反馈沟通技巧、产品知识等关键能力
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <Sparkles className="w-8 h-8 text-purple-500 mb-2" />
            <CardTitle className="text-lg">AI指导建议</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              实时分析客户话语，提供针对性回复建议、关键话术和注意事项
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <BookOpen className="w-8 h-8 text-orange-500 mb-2" />
            <CardTitle className="text-lg">会话复盘</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              生成详细的复盘分析报告，包括评分、改进建议和最佳实践
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 演示客户选择 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            选择演示客户
          </CardTitle>
          <CardDescription>
            选择不同背景的客户进行AI销售培训演示
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {demoCustomers.map((customer) => (
              <Card key={customer.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <User className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{customer.name}</h3>
                      <p className="text-sm text-muted-foreground">{customer.background?.position}</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Globe className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{customer.country.flag} {customer.country.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Package className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{customer.product.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{customer.background?.experience}</span>
                  </div>

                  {customer.background?.preferences && (
                    <div className="space-y-2">
                      <p className="text-xs text-muted-foreground">关注点：</p>
                      <div className="flex flex-wrap gap-1">
                        {customer.background.preferences.map((pref, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {pref}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  <Button
                    onClick={() => handleStartTraining(customer)}
                    className="w-full mt-4"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    开始AI培训
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 系统优势 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            系统优势
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="technology" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="technology">技术优势</TabsTrigger>
              <TabsTrigger value="features">功能特色</TabsTrigger>
              <TabsTrigger value="benefits">培训效果</TabsTrigger>
            </TabsList>

            <TabsContent value="technology" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-semibold">Morphik Core AI引擎</h4>
                  <p className="text-sm text-muted-foreground">
                    基于最新的大语言模型技术，支持多轮对话和上下文理解
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">流式输出技术</h4>
                  <p className="text-sm text-muted-foreground">
                    实时流式响应，提供自然流畅的对话体验
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">智能评估算法</h4>
                  <p className="text-sm text-muted-foreground">
                    多维度评估体系，精准分析销售技能水平
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">个性化推荐</h4>
                  <p className="text-sm text-muted-foreground">
                    基于学习数据的个性化建议和改进方案
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="features" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-semibold">多场景模拟</h4>
                  <p className="text-sm text-muted-foreground">
                    支持不同行业、不同文化背景的客户角色扮演
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">难度自适应</h4>
                  <p className="text-sm text-muted-foreground">
                    根据用户水平自动调整训练难度和挑战性
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">历史记录</h4>
                  <p className="text-sm text-muted-foreground">
                    完整保存训练记录，支持回顾和分析
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">数据导出</h4>
                  <p className="text-sm text-muted-foreground">
                    支持训练数据导出，便于进一步分析和管理
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="benefits" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-semibold">提升沟通技巧</h4>
                  <p className="text-sm text-muted-foreground">
                    通过真实对话练习，显著提升客户沟通能力
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">增强产品知识</h4>
                  <p className="text-sm text-muted-foreground">
                    在实际场景中学习和应用产品知识
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">提高成交率</h4>
                  <p className="text-sm text-muted-foreground">
                    掌握有效的销售技巧和谈判策略
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold">降低培训成本</h4>
                  <p className="text-sm text-muted-foreground">
                    AI驱动的自动化培训，大幅降低人力成本
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* AI训练对话框 */}
      {selectedCustomer && (
        <AITrainingDialog
          customer={selectedCustomer}
          open={showTrainingDialog}
          onClose={handleCloseTraining}
        />
      )}
    </div>
  )
}
