#!/usr/bin/env python3
"""
权限管理工具脚本
用于批量添加、更新、删除权限
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import AsyncSessionLocal
from app.crud.rbac import permission_crud, role_crud
from app.schemas.rbac import PermissionCreate
from app.models.role import Permission, Role, RolePermission


class PermissionManager:
    """权限管理器"""
    
    def __init__(self):
        self.db_session = None
    
    async def __aenter__(self):
        self.db_session = AsyncSessionLocal()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.db_session:
            await self.db_session.close()
    
    async def add_permissions(self, permissions_data: list):
        """
        批量添加权限
        
        Args:
            permissions_data: 权限数据列表 [(name, description, category), ...]
        """
        print("🔐 开始添加权限...")
        
        for name, description, category in permissions_data:
            try:
                # 检查权限是否已存在
                existing = await permission_crud.get_by_name(self.db_session, name=name)
                if existing:
                    print(f"⚠️  权限 {name} 已存在，跳过")
                    continue
                
                # 创建权限
                permission_in = PermissionCreate(
                    name=name,
                    description=description,
                    category=category
                )
                permission = await permission_crud.create(self.db_session, obj_in=permission_in)
                print(f"✅ 成功添加权限: {name} ({description})")
                
            except Exception as e:
                print(f"❌ 添加权限 {name} 失败: {str(e)}")
        
        print("🎉 权限添加完成！")
    
    async def assign_permissions_to_role(self, role_name: str, permission_names: list):
        """
        为角色分配权限
        
        Args:
            role_name: 角色名称
            permission_names: 权限名称列表
        """
        print(f"🎯 为角色 {role_name} 分配权限...")
        
        # 获取角色
        role = await role_crud.get_by_name(self.db_session, name=role_name)
        if not role:
            print(f"❌ 角色 {role_name} 不存在")
            return
        
        success_count = 0
        for permission_name in permission_names:
            try:
                # 获取权限
                permission = await permission_crud.get_by_name(self.db_session, name=permission_name)
                if not permission:
                    print(f"⚠️  权限 {permission_name} 不存在，跳过")
                    continue
                
                # 检查是否已分配
                existing = await self.db_session.execute(
                    f"""
                    SELECT 1 FROM role_permissions 
                    WHERE role_id = {role.id} AND permission_id = {permission.id}
                    """
                )
                if existing.scalar():
                    print(f"⚠️  权限 {permission_name} 已分配给角色 {role_name}，跳过")
                    continue
                
                # 分配权限
                role_permission = RolePermission(
                    role_id=role.id,
                    permission_id=permission.id
                )
                self.db_session.add(role_permission)
                await self.db_session.commit()
                
                print(f"✅ 成功为角色 {role_name} 分配权限: {permission_name}")
                success_count += 1
                
            except Exception as e:
                print(f"❌ 分配权限 {permission_name} 失败: {str(e)}")
                await self.db_session.rollback()
        
        print(f"🎉 权限分配完成！成功分配 {success_count} 个权限")
    
    async def list_permissions(self, category: str = None):
        """
        列出权限
        
        Args:
            category: 权限分类（可选）
        """
        print("📋 权限列表:")
        
        permissions = await permission_crud.get_multi(self.db_session)
        
        if category:
            permissions = [p for p in permissions if p.category == category]
            print(f"🏷️  分类: {category}")
        
        # 按分类分组
        categories = {}
        for permission in permissions:
            cat = permission.category or "未分类"
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(permission)
        
        for cat, perms in categories.items():
            print(f"\n📂 {cat}:")
            for perm in perms:
                print(f"   • {perm.name} - {perm.description}")
    
    async def remove_permissions(self, permission_names: list):
        """
        删除权限
        
        Args:
            permission_names: 权限名称列表
        """
        print("🗑️  开始删除权限...")
        
        for permission_name in permission_names:
            try:
                permission = await permission_crud.get_by_name(self.db_session, name=permission_name)
                if not permission:
                    print(f"⚠️  权限 {permission_name} 不存在，跳过")
                    continue
                
                await permission_crud.remove(self.db_session, id=permission.id)
                print(f"✅ 成功删除权限: {permission_name}")
                
            except Exception as e:
                print(f"❌ 删除权限 {permission_name} 失败: {str(e)}")
        
        print("🎉 权限删除完成！")


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("""
🔐 权限管理工具

用法:
  python manage_permissions.py add          # 添加示例权限
  python manage_permissions.py list         # 列出所有权限
  python manage_permissions.py list <分类>   # 列出指定分类的权限
  python manage_permissions.py assign       # 为角色分配权限
  python manage_permissions.py remove       # 删除权限
        """)
        return
    
    command = sys.argv[1]
    
    async with PermissionManager() as pm:
        if command == "add":
            # 添加示例权限
            permissions_data = [
                # 知识库管理权限
                ("knowledge:read", "查看知识库", "知识库管理"),
                ("knowledge:create", "创建知识库", "知识库管理"),
                ("knowledge:update", "更新知识库", "知识库管理"),
                ("knowledge:delete", "删除知识库", "知识库管理"),
                ("knowledge:export", "导出知识库", "知识库管理"),
                
                # AI服务权限
                ("ai:chat", "使用AI对话", "AI服务"),
                ("ai:embedding", "使用向量化服务", "AI服务"),
                ("ai:rerank", "使用重排序服务", "AI服务"),
                ("ai:config", "配置AI服务", "AI服务"),
                
                # 文档管理权限
                ("document:read", "查看文档", "文档管理"),
                ("document:upload", "上传文档", "文档管理"),
                ("document:download", "下载文档", "文档管理"),
                ("document:delete", "删除文档", "文档管理"),
            ]
            await pm.add_permissions(permissions_data)
            
        elif command == "list":
            category = sys.argv[2] if len(sys.argv) > 2 else None
            await pm.list_permissions(category)
            
        elif command == "assign":
            # 为不同角色分配权限
            role_permissions = {
                "superadmin": [
                    "knowledge:read", "knowledge:create", "knowledge:update", "knowledge:delete", "knowledge:export",
                    "ai:chat", "ai:embedding", "ai:rerank", "ai:config",
                    "document:read", "document:upload", "document:download", "document:delete"
                ],
                "admin": [
                    "knowledge:read", "knowledge:create", "knowledge:update",
                    "ai:chat", "ai:embedding", "ai:rerank",
                    "document:read", "document:upload", "document:download"
                ],
                "user": [
                    "knowledge:read",
                    "ai:chat",
                    "document:read", "document:upload"
                ]
            }
            
            for role_name, permissions in role_permissions.items():
                await pm.assign_permissions_to_role(role_name, permissions)
                
        elif command == "remove":
            # 删除示例权限（谨慎使用）
            permissions_to_remove = [
                "knowledge:export",  # 示例：删除导出权限
            ]
            await pm.remove_permissions(permissions_to_remove)
            
        else:
            print(f"❌ 未知命令: {command}")


if __name__ == "__main__":
    asyncio.run(main())
