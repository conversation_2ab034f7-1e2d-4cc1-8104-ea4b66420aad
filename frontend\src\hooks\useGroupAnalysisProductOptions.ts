import { ShoppingBag, Package, Heart, Coffee, Shirt } from 'lucide-react'
import { useProductOptionsBase, type ProductOption, type UseProductOptionsBaseReturn } from './useProductOptionsBase'

// 特定群体需求分析页面的默认产品选项
const DEFAULT_GROUP_ANALYSIS_PRODUCTS: ProductOption[] = [
  {
    id: 'group_1',
    value: 'shopping-bag',
    label: '购物袋',
    icon: ShoppingBag,
    iconName: 'ShoppingBag',
    createdAt: new Date().toISOString()
  },
  {
    id: 'group_2',
    value: 'beach-bag',
    label: '沙滩包',
    icon: Package,
    iconName: 'Package',
    createdAt: new Date().toISOString()
  },
  {
    id: 'group_3',
    value: 'medical-bag',
    label: '医疗包',
    icon: Heart,
    iconName: 'Heart',
    createdAt: new Date().toISOString()
  },
  {
    id: 'group_4',
    value: 'coffee-mug',
    label: '咖啡杯',
    icon: Coffee,
    iconName: 'Coffee',
    createdAt: new Date().toISOString()
  },
  {
    id: 'group_5',
    value: 'casual-wear',
    label: '休闲服装',
    icon: Shirt,
    iconName: 'Shirt',
    createdAt: new Date().toISOString()
  },
]

const STORAGE_KEY = 'group-analysis-product-options'

/**
 * 特定群体需求分析页面专用的产品选项Hook
 * 使用独立的localStorage存储，与其他页面完全分离
 */
export const useGroupAnalysisProductOptions = (): UseProductOptionsBaseReturn => {
  return useProductOptionsBase({
    storageKey: STORAGE_KEY,
    defaultOptions: DEFAULT_GROUP_ANALYSIS_PRODUCTS
  })
}

export default useGroupAnalysisProductOptions
