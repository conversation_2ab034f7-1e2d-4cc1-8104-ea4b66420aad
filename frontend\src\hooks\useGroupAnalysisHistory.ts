/**
 * 特定群体需求分析历史记录管理 Hook
 */

import { useState, useEffect, useCallback } from 'react';
import { GroupAnalysisRecord, AnalysisHistoryStats, HistoryFilterOptions } from '@/types/groupAnalysis';
import GroupAnalysisHistoryService from '@/services/groupAnalysisHistoryService';

export interface UseGroupAnalysisHistoryReturn {
  // 数据
  records: GroupAnalysisRecord[];
  stats: AnalysisHistoryStats;
  isLoading: boolean;
  
  // 操作方法
  saveRecord: (record: GroupAnalysisRecord) => void;
  deleteRecord: (id: string) => Promise<boolean>;
  deleteMultipleRecords: (ids: string[]) => Promise<number>;
  clearAllRecords: () => Promise<void>;
  getRecordById: (id: string) => GroupAnalysisRecord | null;
  
  // 过滤和搜索
  applyFilters: (filters: HistoryFilterOptions) => void;
  resetFilters: () => void;
  currentFilters: HistoryFilterOptions;

  // 刷新数据
  refreshData: () => void;
}

const defaultFilters: HistoryFilterOptions = {
  status: 'all',
  sortBy: 'timestamp',
  sortOrder: 'desc'
};

export function useGroupAnalysisHistory(): UseGroupAnalysisHistoryReturn {
  const [records, setRecords] = useState<GroupAnalysisRecord[]>([]);
  const [stats, setStats] = useState<AnalysisHistoryStats>({
    totalAnalyses: 0,
    successfulAnalyses: 0,
    failedAnalyses: 0,
    averageDuration: 0,
    mostAnalyzedProduct: '',
    recentAnalysisDate: ''
  });
  const [isLoading, setIsLoading] = useState(true);
  const [currentFilters, setCurrentFilters] = useState<HistoryFilterOptions>(defaultFilters);

  // 加载数据
  const loadData = useCallback(() => {
    setIsLoading(true);
    try {
      const filteredRecords = GroupAnalysisHistoryService.getFilteredRecords(currentFilters);
      const historyStats = GroupAnalysisHistoryService.getHistoryStats();
      
      setRecords(filteredRecords);
      setStats(historyStats);
    } catch (error) {
      console.error('加载历史记录失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, [currentFilters]);

  // 初始化加载
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 保存记录
  const saveRecord = useCallback((record: GroupAnalysisRecord) => {
    try {
      GroupAnalysisHistoryService.saveAnalysisRecord(record);
      loadData(); // 重新加载数据
    } catch (error) {
      console.error('保存记录失败:', error);
    }
  }, [loadData]);

  // 删除单条记录
  const deleteRecord = useCallback(async (id: string): Promise<boolean> => {
    try {
      const success = GroupAnalysisHistoryService.deleteRecord(id);
      if (success) {
        loadData(); // 重新加载数据
      }
      return success;
    } catch (error) {
      console.error('删除记录失败:', error);
      return false;
    }
  }, [loadData]);

  // 批量删除记录
  const deleteMultipleRecords = useCallback(async (ids: string[]): Promise<number> => {
    try {
      const deletedCount = GroupAnalysisHistoryService.deleteMultipleRecords(ids);
      if (deletedCount > 0) {
        loadData(); // 重新加载数据
      }
      return deletedCount;
    } catch (error) {
      console.error('批量删除记录失败:', error);
      return 0;
    }
  }, [loadData]);

  // 清空所有记录
  const clearAllRecords = useCallback(async (): Promise<void> => {
    try {
      GroupAnalysisHistoryService.clearAllRecords();
      loadData(); // 重新加载数据
    } catch (error) {
      console.error('清空记录失败:', error);
    }
  }, [loadData]);

  // 根据ID获取记录
  const getRecordById = useCallback((id: string): GroupAnalysisRecord | null => {
    return GroupAnalysisHistoryService.getRecordById(id);
  }, []);

  // 应用过滤器
  const applyFilters = useCallback((filters: HistoryFilterOptions) => {
    setCurrentFilters({ ...defaultFilters, ...filters });
  }, []);

  // 重置过滤器
  const resetFilters = useCallback(() => {
    setCurrentFilters(defaultFilters);
  }, []);

  // 刷新数据
  const refreshData = useCallback(() => {
    loadData();
  }, [loadData]);

  return {
    // 数据
    records,
    stats,
    isLoading,
    
    // 操作方法
    saveRecord,
    deleteRecord,
    deleteMultipleRecords,
    clearAllRecords,
    getRecordById,
    
    // 过滤和搜索
    applyFilters,
    resetFilters,
    currentFilters,

    // 刷新数据
    refreshData
  };
}

export default useGroupAnalysisHistory;
