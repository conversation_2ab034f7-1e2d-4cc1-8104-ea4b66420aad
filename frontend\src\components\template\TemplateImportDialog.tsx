/**
 * 话术模板导入对话框组件
 * 支持JSON格式的模板文件导入
 */

import React, { useState, useRef } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  FileText,
  CheckCircle,
  AlertCircle,
  X,
  Download,
  Info
} from 'lucide-react';
import { toast } from 'sonner';
import { templateService } from '@/services/templateService';
import { TemplateExport } from '@/types/template';

interface TemplateImportDialogProps {
  open: boolean;
  onClose: () => void;
  onImport: () => void;
}

const TemplateImportDialog: React.FC<TemplateImportDialogProps> = ({
  open,
  onClose,
  onImport
}) => {
  const [importData, setImportData] = useState<TemplateExport | null>(null);
  const [overwriteExisting, setOverwriteExisting] = useState(false);
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    console.log('选择文件:', file.name, file.size);

    if (!file.name.endsWith('.json')) {
      toast.error('请选择JSON格式的文件');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        console.log('文件内容长度:', content.length);
        console.log('文件内容预览:', content.substring(0, 200));

        const data = JSON.parse(content) as TemplateExport;
        console.log('解析后的数据:', data);

        // 验证文件格式
        if (!data.templates || !Array.isArray(data.templates)) {
          console.error('无效的模板文件格式:', data);
          throw new Error('无效的模板文件格式');
        }

        console.log('模板数量:', data.templates.length);
        setImportData(data);
        toast.success(`文件解析成功，找到 ${data.templates.length} 个模板`);
      } catch (error) {
        console.error('解析文件失败:', error);
        toast.error('文件格式错误，请检查文件内容');
        setImportData(null);
      }
    };

    reader.onerror = (error) => {
      console.error('读取文件失败:', error);
      toast.error('读取文件失败');
    };

    reader.readAsText(file);
  };

  const handleImport = async () => {
    if (!importData) return;

    setImporting(true);
    try {
      console.log('开始导入模板:', importData);
      const result = await templateService.importTemplates(importData, {
        overwrite: overwriteExisting
      });

      console.log('导入结果:', result);
      setImportResult(result);

      if (result.success > 0) {
        toast.success(`成功导入 ${result.success} 个模板`);
        onImport();
      }

      if (result.failed > 0) {
        toast.warning(`${result.failed} 个模板导入失败`);
        if (result.errors.length > 0) {
          console.log('导入错误:', result.errors);
        }
      }

      if (result.success === 0 && result.failed === 0) {
        toast.info('没有模板需要导入');
      }
    } catch (error) {
      console.error('导入模板失败:', error);
      toast.error('导入失败，请重试');
    } finally {
      setImporting(false);
    }
  };

  const handleClose = () => {
    setImportData(null);
    setImportResult(null);
    setOverwriteExisting(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onClose();
  };

  const downloadSampleFile = () => {
    const sampleData: TemplateExport = {
      version: '1.0.0',
      export_date: new Date().toISOString(),
      templates: [
        {
          id: 'sample_template',
          name: '示例模板',
          description: '这是一个示例模板，展示了模板的基本结构',
          version: '1.0.0',
          author: 'Sample Author',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          category: 'general',
          tags: ['示例', '模板'],
          role: {
            id: 'sample_role',
            name: '示例角色',
            description: '示例角色描述',
            icon: '🤖',
            category: 'general',
            system_prompt: '你是一个示例AI助手，友好且乐于助人。',
            style: 'professional',
            temperature: 0.7,
            max_tokens: 500
          },
          style: {
            id: 'sample_style',
            name: '示例风格',
            description: '示例回答风格',
            template: '请详细回答用户问题',
            category: 'detailed'
          },
          prompt_overrides: {
            query: {
              prompt_template: `你是一个友好的AI助手。

用户问题：{question}

相关信息：
{context}

请根据以上信息友好地回答用户问题：`
            }
          },
          usage_count: 0,
          rating: 5,
          is_active: false,
          is_default: false
        }
      ],
      metadata: {
        total_count: 1,
        categories: ['general'],
        tags: ['示例', '模板']
      }
    };

    const blob = new Blob([JSON.stringify(sampleData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'template_sample.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose} modal={true}>
      <DialogContent
        className="max-w-2xl max-h-[90vh] overflow-y-auto"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            导入话术模板
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 文件选择 */}
          {!importData && !importResult && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">选择模板文件</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground mb-4">
                    选择JSON格式的模板文件进行导入
                  </p>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".json"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  <Button onClick={() => fileInputRef.current?.click()}>
                    <Upload className="h-4 w-4 mr-2" />
                    选择文件
                  </Button>
                </div>

                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>需要示例文件？</span>
                  <Button variant="link" size="sm" onClick={downloadSampleFile}>
                    <Download className="h-4 w-4 mr-1" />
                    下载示例文件
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 导入预览 */}
          {importData && !importResult && (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">导入预览</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">文件版本:</span> {importData.version}
                    </div>
                    <div>
                      <span className="font-medium">导出日期:</span> {new Date(importData.export_date).toLocaleDateString('zh-CN')}
                    </div>
                    <div>
                      <span className="font-medium">模板数量:</span> {importData.templates.length}
                    </div>
                    <div>
                      <span className="font-medium">类别数量:</span> {importData.metadata.categories.length}
                    </div>
                  </div>

                  <div>
                    <span className="font-medium">包含的类别:</span>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {importData.metadata.categories.map(category => (
                        <Badge key={category} variant="outline">{category}</Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <span className="font-medium">模板列表:</span>
                    <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
                      {importData.templates.map(template => (
                        <div key={template.id} className="flex items-center justify-between p-2 bg-muted rounded">
                          <div>
                            <span className="font-medium">{template.name}</span>
                            <span className="text-sm text-muted-foreground ml-2">({template.category})</span>
                          </div>
                          <Badge variant="secondary">v{template.version}</Badge>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="overwrite"
                      checked={overwriteExisting}
                      onCheckedChange={setOverwriteExisting}
                    />
                    <Label htmlFor="overwrite">覆盖已存在的模板</Label>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={handleClose}>
                  取消
                </Button>
                <Button onClick={handleImport} disabled={importing}>
                  {importing ? '导入中...' : '开始导入'}
                </Button>
              </div>
            </div>
          )}

          {/* 导入结果 */}
          {importResult && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  {importResult.success > 0 ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  )}
                  导入结果
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{importResult.success}</div>
                    <div className="text-sm text-green-600">成功导入</div>
                  </div>
                  <div className="text-center p-4 bg-red-50 dark:bg-red-950 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">{importResult.failed}</div>
                    <div className="text-sm text-red-600">导入失败</div>
                  </div>
                </div>

                {importResult.errors.length > 0 && (
                  <div>
                    <span className="font-medium text-red-600">错误信息:</span>
                    <div className="mt-2 space-y-1 max-h-40 overflow-y-auto">
                      {importResult.errors.map((error: string, index: number) => (
                        <div key={index} className="text-sm text-red-600 p-2 bg-red-50 dark:bg-red-950 rounded">
                          {error}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex justify-end">
                  <Button onClick={handleClose}>
                    完成
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 帮助信息 */}
          {!importData && !importResult && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  导入说明
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="text-sm space-y-2 text-muted-foreground">
                  <li>• 支持导入JSON格式的模板文件</li>
                  <li>• 文件必须包含有效的模板数据结构</li>
                  <li>• 可以选择是否覆盖已存在的同名模板</li>
                  <li>• 导入前会进行格式验证和兼容性检查</li>
                  <li>• 建议先下载示例文件了解正确的格式</li>
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TemplateImportDialog;
