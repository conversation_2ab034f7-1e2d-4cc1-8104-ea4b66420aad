/**
 * 知识库错误边界和通知组件
 * 提供全局错误处理、用户友好的错误展示和操作建议
 */
import React, { Component, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorId: string;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export class KnowledgeBaseErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // 调用外部错误处理函数
    this.props.onError?.(error, errorInfo);

    // 记录错误到控制台（仅开发环境）
    if (import.meta.env.DEV) {
      console.error('Knowledge Base Error Boundary caught an error:', error, errorInfo);
    }

    // 这里可以添加错误上报逻辑
    this.reportError(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: React.ErrorInfo) => {
    // 错误上报逻辑
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorId: this.state.errorId,
    };

    // 这里可以发送到错误监控服务
    if (import.meta.env.DEV) {
      console.log('Error Report:', errorReport);
    }
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private copyErrorDetails = () => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
    };

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-100 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <CardTitle className="text-red-900">知识库系统遇到了问题</CardTitle>
                  <CardDescription>
                    很抱歉，系统出现了意外错误。我们正在努力解决这个问题。
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 错误信息 */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">错误详情</h4>
                  <Badge variant="outline" className="font-mono text-xs">
                    {this.state.errorId}
                  </Badge>
                </div>

                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-800 font-medium mb-1">
                    {this.state.error?.message || '未知错误'}
                  </p>
                  {this.state.error?.stack && (
                    <details className="mt-2">
                      <summary className="text-xs text-red-600 cursor-pointer hover:text-red-800">
                        查看技术详情
                      </summary>
                      <pre className="mt-2 text-xs text-red-700 bg-red-100 p-2 rounded overflow-auto max-h-32">
                        {this.state.error.stack}
                      </pre>
                    </details>
                  )}
                </div>
              </div>

              {/* 建议操作 */}
              <div className="space-y-3">
                <h4 className="font-medium">建议操作</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <Button
                    onClick={this.handleRetry}
                    className="flex items-center gap-2"
                    variant="default"
                  >
                    <RefreshCw className="h-4 w-4" />
                    重试
                  </Button>

                  <Button
                    onClick={this.handleReload}
                    className="flex items-center gap-2"
                    variant="outline"
                  >
                    <RefreshCw className="h-4 w-4" />
                    刷新页面
                  </Button>

                  <Button
                    onClick={this.handleGoHome}
                    className="flex items-center gap-2"
                    variant="outline"
                  >
                    <Home className="h-4 w-4" />
                    返回首页
                  </Button>
                </div>
              </div>

              {/* 帮助信息 */}
              <div className="space-y-3">
                <h4 className="font-medium">需要帮助？</h4>
                <div className="text-sm text-muted-foreground space-y-2">
                  <p>如果问题持续存在，请尝试以下操作：</p>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>清除浏览器缓存和Cookie</li>
                    <li>检查网络连接是否正常</li>
                    <li>确保Morphik Core服务正在运行</li>
                    <li>联系系统管理员</li>
                  </ul>
                </div>

                <Button
                  onClick={this.copyErrorDetails}
                  variant="ghost"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Bug className="h-4 w-4" />
                  复制错误详情
                </Button>
              </div>

              {/* 系统状态 */}
              <div className="pt-4 border-t">
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>错误时间: {new Date().toLocaleString('zh-CN')}</span>
                  <span>版本: {import.meta.env.VITE_APP_VERSION || '1.0.0'}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// 简化的错误展示组件
interface SimpleErrorDisplayProps {
  error: Error;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
}

export function SimpleErrorDisplay({
  error,
  onRetry,
  onDismiss,
  className = ''
}: SimpleErrorDisplayProps) {
  return (
    <Card className={`border-red-200 bg-red-50 ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-red-900 mb-1">
              操作失败
            </p>
            <p className="text-sm text-red-700">
              {error.message}
            </p>
          </div>
          <div className="flex gap-2">
            {onRetry && (
              <Button
                onClick={onRetry}
                size="sm"
                variant="outline"
                className="h-8 px-3"
              >
                重试
              </Button>
            )}
            {onDismiss && (
              <Button
                onClick={onDismiss}
                size="sm"
                variant="ghost"
                className="h-8 px-3"
              >
                ×
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// 加载状态组件
interface LoadingStateProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingState({
  message = '加载中...',
  size = 'md',
  className = ''
}: LoadingStateProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  return (
    <div className={`flex flex-col items-center justify-center py-8 ${className}`}>
      <RefreshCw className={`${sizeClasses[size]} animate-spin text-primary mb-4`} />
      <p className="text-sm text-muted-foreground">{message}</p>
    </div>
  );
}

// 空状态组件
interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
}

export function EmptyState({
  icon,
  title,
  description,
  action,
  className = ''
}: EmptyStateProps) {
  return (
    <div className={`text-center py-12 ${className}`}>
      {icon && (
        <div className="mb-4 flex justify-center">
          {icon}
        </div>
      )}
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      {description && (
        <p className="text-muted-foreground mb-4 max-w-md mx-auto">
          {description}
        </p>
      )}
      {action}
    </div>
  );
}

export default KnowledgeBaseErrorBoundary;
