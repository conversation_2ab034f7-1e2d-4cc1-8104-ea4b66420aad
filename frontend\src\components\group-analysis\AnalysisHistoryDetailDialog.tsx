/**
 * 特定群体需求分析历史记录详情对话框组件
 */

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import {
  Calendar,
  Clock,
  Package,
  CheckCircle,
  XCircle,
  AlertCircle,
  Search,
  Users,
  TrendingUp,
  ExternalLink,
  Copy
} from 'lucide-react';
import { GroupAnalysisRecord } from '@/types/groupAnalysis';
import { toast } from 'sonner';

// 简单的日期格式化函数
const formatTimeAgo = (timestamp: string): string => {
  const now = new Date();
  const date = new Date(timestamp);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return '刚刚';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} 分钟前`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} 小时前`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} 天前`;

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatDuration = (seconds?: number) => {
    if (!seconds) return '未知';
    if (seconds < 60) return `${seconds}秒`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  };

interface AnalysisHistoryDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  record: GroupAnalysisRecord | null;
}

export function AnalysisHistoryDetailDialog({
  open,
  onOpenChange,
  record
}: AnalysisHistoryDetailDialogProps) {
  if (!record) return null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'cancelled':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success':
        return '分析成功';
      case 'failed':
        return '分析失败';
      case 'cancelled':
        return '已取消';
      default:
        return '未知状态';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'cancelled':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('已复制到剪贴板');
    } catch (error) {
      toast.error('复制失败');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent
        className="max-w-4xl max-h-[90vh] overflow-hidden"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <Users className="w-4 h-4 text-white" />
            </div>
            <span>特定群体需求分析详情</span>
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="max-h-[calc(90vh-120px)]">
          <div className="space-y-6 pr-4">
            {/* 基本信息 */}
            <div className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                  <Package className="w-5 h-5 mr-2 text-blue-600" />
                  {record.productLabel}
                </h3>
                <Badge className={getStatusColor(record.status)}>
                  {getStatusIcon(record.status)}
                  <span className="ml-1">{getStatusText(record.status)}</span>
                </Badge>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-blue-500" />
                  <div>
                    <div className="text-gray-600 dark:text-gray-400">分析时间</div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {formatTimeAgo(record.timestamp)}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Search className="w-4 h-4 text-blue-500" />
                  <div>
                    <div className="text-gray-600 dark:text-gray-400">搜索策略</div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {record.strategyCount} 个
                    </div>
                  </div>
                </div>

                {record.duration && (
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-blue-500" />
                    <div>
                      <div className="text-gray-600 dark:text-gray-400">分析耗时</div>
                      <div className="font-medium text-gray-900 dark:text-white">
                        {formatDuration(record.duration)}
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4 text-blue-500" />
                  <div>
                    <div className="text-gray-600 dark:text-gray-400">数据源</div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {record.dataSources?.length || 0} 个
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 搜索策略 */}
            {record.searchStrategies && record.searchStrategies.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <Search className="w-5 h-5 mr-2 text-blue-600" />
                  搜索策略
                </h3>
                <div className="grid gap-4 md:grid-cols-2">
                  {record.searchStrategies.map((strategy, index) => (
                    <div key={index} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                        {strategy.category}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                        {strategy.description}
                      </p>
                      <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded">
                        {strategy.query}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 分析结果 */}
            {record.analysisResult && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
                    群体需求分析报告
                  </h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(record.analysisResult)}
                    className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    复制
                  </Button>
                </div>
                <div className="prose prose-lg dark:prose-invert max-w-none">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        h1: ({ children }) => <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">{children}</h1>,
                        h2: ({ children }) => <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 mt-6">{children}</h2>,
                        h3: ({ children }) => <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2 mt-4">{children}</h3>,
                        p: ({ children }) => <p className="text-gray-700 dark:text-gray-300 mb-3 leading-relaxed">{children}</p>,
                        ul: ({ children }) => <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 mb-3 space-y-1">{children}</ul>,
                        ol: ({ children }) => <ol className="list-decimal list-inside text-gray-700 dark:text-gray-300 mb-3 space-y-1">{children}</ol>,
                        li: ({ children }) => <li className="ml-2">{children}</li>,
                        strong: ({ children }) => <strong className="font-semibold text-gray-900 dark:text-white">{children}</strong>,
                        em: ({ children }) => <em className="italic text-gray-800 dark:text-gray-200">{children}</em>,
                        blockquote: ({ children }) => <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600 dark:text-gray-400 my-4">{children}</blockquote>,
                        code: ({ children }) => <code className="bg-gray-200 dark:bg-gray-700 px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                        pre: ({ children }) => <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto text-sm font-mono mb-4">{children}</pre>
                      }}
                    >
                      {record.analysisResult}
                    </ReactMarkdown>
                  </div>
                </div>
              </div>
            )}

            {/* 数据源 */}
            {record.dataSources && record.dataSources.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                  <ExternalLink className="w-5 h-5 mr-2 text-blue-600" />
                  数据来源
                </h3>
                <div className="grid gap-4">
                  {record.dataSources.map((source, index) => (
                    <div key={index} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {source.title}
                        </h4>
                        <a
                          href={source.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                        {source.content.substring(0, 200)}...
                      </p>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {source.url}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 错误信息 */}
            {record.status === 'failed' && record.errorMessage && (
              <div className="bg-red-50 dark:bg-red-900/20 rounded-xl p-6 border border-red-200 dark:border-red-800">
                <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-4 flex items-center">
                  <XCircle className="w-5 h-5 mr-2" />
                  错误信息
                </h3>
                <p className="text-red-700 dark:text-red-300">
                  {record.errorMessage}
                </p>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}

export default AnalysisHistoryDetailDialog;
