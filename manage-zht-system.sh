#!/bin/bash

#
# ZHT System 0624 统一管理脚本 - Linux/macOS 平台
# 用于启动、停止、重启和监控 ZHT System 完整服务栈的 Bash 脚本
# 包含前端、后端、Morphik Core 和数据库服务
#
# 作者: ZHT 系统管理员
# 版本: 2.0
# 支持服务: 前端(React) + 后端(FastAPI) + Morphik Core + PostgreSQL + Redis
#

# 脚本版本和名称
SCRIPT_VERSION="2.0"
SCRIPT_NAME="ZHT System 0624 统一管理脚本"

# 服务分组定义
DATABASE_SERVICES=("zht_db_0624" "zht_morphik_postgres_0624" "zht_morphik_redis_0624")
CORE_SERVICES=("zht_backend_0624" "zht_morphik_0624" "zht_morphik_worker_0624")
AI_SERVICES=("zht_smolagents_0624")
FRONTEND_SERVICES=("zht_frontend_0624")
ALL_SERVICES=("${DATABASE_SERVICES[@]}" "${CORE_SERVICES[@]}" "${AI_SERVICES[@]}" "${FRONTEND_SERVICES[@]}")

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${CYAN}ℹ️ $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_step() {
    echo -e "${MAGENTA}🔄 $1${NC}"
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}$SCRIPT_NAME v$SCRIPT_VERSION${NC}"
    echo ""
    echo -e "${WHITE}用法: ./manage-zht-system.sh [选项]${NC}"
    echo ""
    echo -e "${YELLOW}选项:${NC}"
    echo -e "${WHITE}  -h, --help        显示帮助信息${NC}"
    echo -e "${WHITE}  -s, --start       启动所有服务${NC}"
    echo -e "${WHITE}  -r, --restart     重启所有服务${NC}"
    echo -e "${WHITE}  -t, --stop        停止所有服务${NC}"
    echo -e "${WHITE}  -l, --logs        查看服务日志${NC}"
    echo -e "${WHITE}  --status          查看服务状态${NC}"
    echo -e "${WHITE}  --build           强制重新构建服务${NC}"
    echo -e "${WHITE}  --no-cache        清理缓存并重新构建${NC}"
    echo -e "${WHITE}  --frontend-only   仅启动前端服务${NC}"
    echo -e "${WHITE}  --backend-only    仅启动后端服务${NC}"
    echo -e "${WHITE}  --morphik-only    仅启动Morphik服务${NC}"
    echo -e "${WHITE}  --ai-only         仅启动AI服务(SmoLAgents)${NC}"
    echo -e "${WHITE}  --dev-mode        切换开发模式 (on/off)${NC}"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo -e "${WHITE}  ./manage-zht-system.sh --start           # 启动完整系统${NC}"
    echo -e "${WHITE}  ./manage-zht-system.sh --frontend-only   # 仅启动前端${NC}"
    echo -e "${WHITE}  ./manage-zht-system.sh --ai-only         # 仅启动AI服务${NC}"
    echo -e "${WHITE}  ./manage-zht-system.sh --dev-mode on     # 启用开发模式${NC}"
    echo -e "${WHITE}  ./manage-zht-system.sh --dev-mode off    # 禁用开发模式${NC}"
    echo -e "${WHITE}  ./manage-zht-system.sh --logs            # 查看日志${NC}"
    echo ""
    echo -e "${YELLOW}服务访问:${NC}"
    echo -e "${WHITE}  🌐 前端应用:     http://localhost:5173${NC}"
    echo -e "${WHITE}  🔧 后端API:      http://localhost:8001/docs${NC}"
    echo -e "${WHITE}  🧠 Morphik Core: http://localhost:8000/docs${NC}"
    echo -e "${WHITE}  🤖 SmoLAgents:   http://localhost:8002/docs${NC}"
    echo -e "${WHITE}  🗄️ PostgreSQL:   localhost:5433${NC}"
    echo ""
}

# 检查 Docker 状态
test_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装。请先安装 Docker。"
        return 1
    fi

    if ! docker version --format "{{.Server.Version}}" &> /dev/null; then
        log_error "Docker 未运行或无法访问。请启动 Docker 服务。"
        return 1
    fi

    local docker_version=$(docker version --format "{{.Server.Version}}" 2>/dev/null)
    log_info "Docker 运行中 (服务器版本: $docker_version)"
    return 0
}

# 读取 .env 文件配置
read_env_file() {
    local env_file_path="${1:-.env}"
    local script_dir=$(get_script_directory)
    local full_path="$script_dir/$env_file_path"

    if [ -f "$full_path" ]; then
        # 读取 .env 文件并设置为关联数组（如果支持）
        while IFS='=' read -r key value; do
            # 跳过注释和空行
            if [[ ! "$key" =~ ^[[:space:]]*# ]] && [[ -n "$key" ]] && [[ "$key" == *"="* ]]; then
                key=$(echo "$key" | cut -d'=' -f1 | xargs)
                value=$(echo "$key=$value" | cut -d'=' -f2- | xargs)
                # 移除引号
                value=$(echo "$value" | sed 's/^["'\'']//' | sed 's/["'\'']$//')
                eval "ENV_$key=\"$value\""
            fi
        done < "$full_path"
    fi
}

# 设置 Docker 环境
set_docker_environment() {
    local dev_mode="$1"

    log_step "配置 Docker 环境..."

    # 读取 .env 文件配置
    read_env_file

    # 如果没有指定 dev_mode，从 .env 文件读取
    if [ -z "$dev_mode" ]; then
        dev_mode="${ENV_DEV_MODE:-false}"
    fi

    # 设置 Docker 构建环境变量
    export COMPOSE_DOCKER_CLI_BUILD=1
    export DOCKER_BUILDKIT=1

    # 清除可能存在的环境变量，让 Docker Compose 读取 .env 文件
    unset NODE_ENV DEV_MODE VITE_DEV_MODE MORPHIK_DEV_MODE MORPHIK_RELOAD MORPHIK_WORKER_RELOAD
    unset MORPHIK_VOLUME_MODE FRONTEND_VOLUME_MODE BACKEND_VOLUME_MODE SMOLAGENTS_VOLUME_MODE

    # 从 .env 文件读取实际的开发模式状态
    local actual_dev_mode="${ENV_DEV_MODE:-false}"
    local actual_vite_dev_mode="${ENV_VITE_DEV_MODE:-false}"

    log_info "从 .env 文件读取配置:"
    log_info "  DEV_MODE=$actual_dev_mode"
    log_info "  VITE_DEV_MODE=$actual_vite_dev_mode"

    if [ "$actual_dev_mode" = "true" ]; then
        log_success "Docker 环境配置完成 (开发模式: 启用)"
    else
        log_success "Docker 环境配置完成 (开发模式: 禁用)"
    fi
}

# 检查前端依赖
test_frontend_dependencies() {
    log_step "检查前端依赖..."

    if [ ! -d "frontend/node_modules" ]; then
        log_info "安装前端依赖..."
        cd frontend || return 1

        if ! npm install --registry=https://registry.npmmirror.com; then
            log_error "前端依赖安装失败"
            cd ..
            return 1
        fi

        log_success "前端依赖安装完成"
        cd ..
    else
        log_success "前端依赖已存在"
    fi
    return 0
}

# 获取脚本目录
get_script_directory() {
    echo "$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
}

# 检查 GPU 支持
check_gpu_support() {
    # 检查是否是 macOS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        log_warning "macOS 系统检测到，禁用 GPU 加速"
        export COMPOSE_FILE="docker-compose.nogpu.yml"
        return 1
    fi

    # 检查是否有 nvidia-docker 支持
    if ! command -v nvidia-docker &> /dev/null && ! docker info | grep -q "nvidia"; then
        log_warning "未检测到 NVIDIA GPU 支持，禁用 GPU 加速"
        export COMPOSE_FILE="docker-compose.nogpu.yml"
        return 1
    fi

    log_success "GPU 支持已启用"
    export COMPOSE_FILE="docker-compose.yml"
    return 0
}

# 创建无 GPU 的 docker-compose 文件
create_nogpu_compose() {
    local script_dir=$(get_script_directory)
    local nogpu_file="$script_dir/docker-compose.nogpu.yml"

    if [ ! -f "$nogpu_file" ] || [ "docker-compose.yml" -nt "$nogpu_file" ]; then
        log_step "创建无 GPU 的 docker-compose 配置..."

        # 复制原文件并删除 GPU 相关配置
        cp "docker-compose.yml" "$nogpu_file"

        # 使用 sed 注释掉 GPU 相关配置
        sed -i.bak \
            -e '/# GPU 支持配置/,/capabilities: \[gpu\]/c\
    # GPU 支持已禁用 (macOS)' \
            -e 's/- NVIDIA_VISIBLE_DEVICES=all/# - NVIDIA_VISIBLE_DEVICES=all/' \
            -e 's/- NVIDIA_DRIVER_CAPABILITIES=compute,utility/# - NVIDIA_DRIVER_CAPABILITIES=compute,utility/' \
            -e 's/- CUDA_VISIBLE_DEVICES=0/# - CUDA_VISIBLE_DEVICES=0/' \
            -e 's/- PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True/# - PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True/' \
            -e 's/- CUDA_MEMORY_FRACTION=0.5/# - CUDA_MEMORY_FRACTION=0.5/' \
            "$nogpu_file"

        # 删除备份文件
        rm -f "$nogpu_file.bak"

        log_success "无 GPU 配置文件已创建: $nogpu_file"
    fi
}

# 检查必要的环境文件
check_env_files() {
    log_step "检查环境文件..."

    # 检查 morphik .env 文件
    if [ ! -f "morphik-core-main/.env" ]; then
        log_warning "morphik-core-main/.env 文件不存在，正在创建..."
        if [ -f "morphik-core-main/.env.example" ]; then
            cp "morphik-core-main/.env.example" "morphik-core-main/.env"
            log_info "已从示例文件创建 .env 文件"
        else
            log_error "找不到 .env.example 文件"
            return 1
        fi
    fi

    log_success "环境文件检查完成"
    return 0
}

# 启动所有服务
start_all_services() {
    log_info "启动完整 ZHT System 服务栈..."

    local script_dir=$(get_script_directory)

    # 配置环境
    set_docker_environment

    # 检查 GPU 支持
    check_gpu_support
    create_nogpu_compose

    # 检查环境文件
    if ! check_env_files; then
        return 1
    fi

    # 检查前端依赖
    if ! test_frontend_dependencies; then
        return 1
    fi

    # 停止现有容器
    log_step "清理现有容器..."
    docker-compose -f "$script_dir/docker-compose.nogpu.yml" down --remove-orphans &>/dev/null

    # 分阶段启动服务
    log_step "启动数据库服务..."
    if ! docker-compose -f "$script_dir/docker-compose.nogpu.yml" up -d "${DATABASE_SERVICES[@]}"; then
        log_error "启动数据库服务失败"
        return 1
    fi

    # 启动核心服务
    log_step "启动核心服务..."
    if ! docker-compose -f "$script_dir/docker-compose.nogpu.yml" up -d "${CORE_SERVICES[@]}"; then
        log_error "启动核心服务失败"
        return 1
    fi

    # 启动AI服务
    log_step "启动AI服务(SmoLAgents)..."
    if ! docker-compose -f "$script_dir/docker-compose.nogpu.yml" up -d "${AI_SERVICES[@]}"; then
        log_error "启动AI服务失败"
        return 1
    fi

    # 等待核心服务和AI服务启动
    log_info "等待核心服务和AI服务启动..."
    sleep 15

    # 启动前端服务
    log_step "启动前端服务..."
    if ! docker-compose -f "$script_dir/docker-compose.nogpu.yml" up -d "${FRONTEND_SERVICES[@]}"; then
        log_error "启动前端服务失败"
        return 1
    fi

    # 等待所有服务启动
    log_info "等待所有服务启动..."
    sleep 10

    # 显示服务状态
    show_service_status

    # 显示访问信息
    show_access_info

    log_success "ZHT System 启动完成！"
    return 0
}

# 启动前端服务
start_frontend_only() {
    log_info "启动前端服务..."

    local script_dir=$(get_script_directory)
    set_docker_environment

    if ! test_frontend_dependencies; then
        return 1
    fi

    if docker-compose -f "$script_dir/docker-compose.nogpu.yml" up -d "${FRONTEND_SERVICES[@]}"; then
        log_success "前端服务启动完成"
        echo -e "${YELLOW}🌐 前端应用: http://localhost:5173${NC}"
        return 0
    else
        log_error "启动前端服务失败"
        return 1
    fi
}

# 启动后端服务
start_backend_only() {
    log_info "启动后端服务..."

    local script_dir=$(get_script_directory)
    set_docker_environment

    local backend_services=("${DATABASE_SERVICES[@]}" "zht_backend_0624")

    if docker-compose -f "$script_dir/docker-compose.nogpu.yml" up -d "${backend_services[@]}"; then
        log_success "后端服务启动完成"
        echo -e "${YELLOW}🔧 后端API: http://localhost:8001/docs${NC}"
        return 0
    else
        log_error "启动后端服务失败"
        return 1
    fi
}

# 启动Morphik服务
start_morphik_only() {
    log_info "启动Morphik服务..."

    local script_dir=$(get_script_directory)
    set_docker_environment

    local morphik_services=("zht_morphik_postgres_0624" "zht_morphik_redis_0624" "zht_morphik_0624" "zht_morphik_worker_0624")

    if docker-compose -f "$script_dir/docker-compose.nogpu.yml" up -d "${morphik_services[@]}"; then
        log_success "Morphik服务启动完成"
        echo -e "${YELLOW}🧠 Morphik Core: http://localhost:8000/docs${NC}"
        return 0
    else
        log_error "启动Morphik服务失败"
        return 1
    fi
}

# 启动AI服务
start_ai_only() {
    log_info "启动AI服务(SmoLAgents)..."

    local script_dir=$(get_script_directory)
    set_docker_environment

    if docker-compose -f "$script_dir/docker-compose.nogpu.yml" up -d "${AI_SERVICES[@]}"; then
        log_success "AI服务启动完成"
        echo -e "${YELLOW}🤖 SmoLAgents: http://localhost:8002/docs${NC}"
        return 0
    else
        log_error "启动AI服务失败"
        return 1
    fi
}

# 切换开发模式
set_dev_mode() {
    local mode="$1"

    if [[ "$mode" != "on" && "$mode" != "off" ]]; then
        log_error "无效的开发模式参数: $mode (应为 'on' 或 'off')"
        return 1
    fi

    echo -e "${CYAN}🔧 ZHT系统模式切换工具${NC}"
    echo -e "${CYAN}================================${NC}"

    local script_dir=$(get_script_directory)
    local env_file="$script_dir/.env"
    local dev_mode_value
    if [ "$mode" = "on" ]; then
        dev_mode_value="true"
    else
        dev_mode_value="false"
    fi

    # 检查 .env 文件是否存在
    if [ ! -f "$env_file" ]; then
        log_error "找不到 .env 文件: $env_file"
        return 1
    fi

    # 更新 .env 文件配置
    log_step "更新 .env 文件配置..."

    # 读取当前配置
    local content
    content=$(cat "$env_file")

    if [ "$mode" = "on" ]; then
        echo -e "${YELLOW}🚀 切换到开发模式...${NC}"

        # 替换开发模式配置
        content=$(echo "$content" | sed 's/^SYSTEM_DEV_MODE=.*/SYSTEM_DEV_MODE=true/')
        content=$(echo "$content" | sed 's/^DEV_MODE=.*/DEV_MODE=true/')
        content=$(echo "$content" | sed 's/^VITE_DEV_MODE=.*/VITE_DEV_MODE=true/')
        content=$(echo "$content" | sed 's/^MORPHIK_DEV_MODE=.*/MORPHIK_DEV_MODE=true/')
        content=$(echo "$content" | sed 's/^DEBUG=.*/DEBUG=true/')
        content=$(echo "$content" | sed 's/^NODE_ENV=.*/NODE_ENV=development/')
        content=$(echo "$content" | sed 's/^MORPHIK_VOLUME_MODE=.*/MORPHIK_VOLUME_MODE=rw/')
        content=$(echo "$content" | sed 's/^FRONTEND_VOLUME_MODE=.*/FRONTEND_VOLUME_MODE=rw/')
        content=$(echo "$content" | sed 's/^BACKEND_VOLUME_MODE=.*/BACKEND_VOLUME_MODE=rw/')
        content=$(echo "$content" | sed 's/^LOG_LEVEL=.*/LOG_LEVEL=DEBUG/')

        echo -e "${GREEN}✅ 开发模式配置:${NC}"
        echo -e "${WHITE}   - 跳过用户认证${NC}"
        echo -e "${WHITE}   - 启用代码热重载${NC}"
        echo -e "${WHITE}   - 启用调试日志${NC}"
        echo -e "${WHITE}   - 代码目录可写挂载${NC}"
        echo -e "${WHITE}   - 数据库配置统一管理${NC}"

    else
        echo -e "${YELLOW}🔒 切换到生产模式...${NC}"

        # 替换生产模式配置
        content=$(echo "$content" | sed 's/^SYSTEM_DEV_MODE=.*/SYSTEM_DEV_MODE=false/')
        content=$(echo "$content" | sed 's/^DEV_MODE=.*/DEV_MODE=false/')
        content=$(echo "$content" | sed 's/^VITE_DEV_MODE=.*/VITE_DEV_MODE=false/')
        content=$(echo "$content" | sed 's/^MORPHIK_DEV_MODE=.*/MORPHIK_DEV_MODE=false/')
        content=$(echo "$content" | sed 's/^DEBUG=.*/DEBUG=false/')
        content=$(echo "$content" | sed 's/^NODE_ENV=.*/NODE_ENV=production/')
        content=$(echo "$content" | sed 's/^MORPHIK_VOLUME_MODE=.*/MORPHIK_VOLUME_MODE=ro/')
        content=$(echo "$content" | sed 's/^FRONTEND_VOLUME_MODE=.*/FRONTEND_VOLUME_MODE=ro/')
        content=$(echo "$content" | sed 's/^BACKEND_VOLUME_MODE=.*/BACKEND_VOLUME_MODE=ro/')
        content=$(echo "$content" | sed 's/^LOG_LEVEL=.*/LOG_LEVEL=INFO/')

        echo -e "${GREEN}✅ 生产模式配置:${NC}"
        echo -e "${WHITE}   - 需要用户认证${NC}"
        echo -e "${WHITE}   - 禁用代码热重载${NC}"
        echo -e "${WHITE}   - 标准日志级别${NC}"
        echo -e "${WHITE}   - 代码目录只读挂载${NC}"
        echo -e "${WHITE}   - 数据库配置统一管理${NC}"
    fi

    # 写入配置文件
    echo "$content" > "$env_file"
    log_success ".env 文件更新完成"

    # 配置环境（清除环境变量，让 Docker Compose 读取 .env 文件）
    unset NODE_ENV DEV_MODE VITE_DEV_MODE MORPHIK_DEV_MODE MORPHIK_RELOAD MORPHIK_WORKER_RELOAD
    unset MORPHIK_VOLUME_MODE FRONTEND_VOLUME_MODE BACKEND_VOLUME_MODE SMOLAGENTS_VOLUME_MODE

    log_step "重新构建前端服务以应用新配置..."

    # 停止前端服务
    log_info "停止前端服务..."
    docker-compose -f "$script_dir/docker-compose.nogpu.yml" stop zht_frontend_0624

    # 重新构建前端
    log_info "重新构建前端服务..."
    if ! docker-compose -f "$script_dir/docker-compose.nogpu.yml" build --no-cache zht_frontend_0624; then
        log_error "重新构建前端服务失败"
        return 1
    fi

    # 启动前端服务
    log_info "启动前端服务..."
    if ! docker-compose -f "$script_dir/docker-compose.nogpu.yml" up -d zht_frontend_0624; then
        log_error "启动前端服务失败"
        return 1
    fi

    log_success "前端服务重启完成"

    # 验证环境变量
    log_step "验证容器环境变量..."
    sleep 3

    local env_check
    if env_check=$(docker exec zht_frontend_0624 printenv VITE_DEV_MODE 2>/dev/null); then
        if [ "$env_check" = "$dev_mode_value" ]; then
            log_success "环境变量验证成功: VITE_DEV_MODE=$env_check"
        else
            log_warning "环境变量验证失败: 期望 $dev_mode_value，实际 $env_check"
        fi
    else
        log_warning "无法验证环境变量，请检查容器状态"
    fi

    echo ""
    log_success "开发模式切换完成！"
    echo -e "${CYAN}🌐 前端应用: http://localhost:5173${NC}"
    echo ""

    if [ "$mode" = "on" ]; then
        echo -e "${GREEN}✅ 开发模式已启用 - 无需登录即可访问应用${NC}"
    else
        echo -e "${YELLOW}🔒 开发模式已禁用 - 需要登录才能访问应用${NC}"
    fi

    echo ""
    echo -e "${CYAN}🔄 如需重启所有服务以应用新配置:${NC}"
    echo -e "${WHITE}   ./manage-zht-system.sh --restart${NC}"
    echo ""
    echo -e "${CYAN}🔍 检查当前状态:${NC}"
    echo -e "${WHITE}   ./check-dev-mode.sh${NC}"

    return 0
}

# 停止所有服务
stop_all_services() {
    log_info "停止所有服务..."

    local script_dir=$(get_script_directory)

    if docker-compose -f "$script_dir/docker-compose.nogpu.yml" down --remove-orphans; then
        log_success "所有服务已停止"
        return 0
    else
        log_error "停止服务失败"
        return 1
    fi
}

# 重启所有服务
restart_all_services() {
    log_info "重启所有服务..."

    if stop_all_services; then
        sleep 3
        start_all_services
        return $?
    fi
    return 1
}

# 查看服务日志
show_service_logs() {
    log_info "显示服务日志 (按 Ctrl+C 退出)..."

    local script_dir=$(get_script_directory)
    docker-compose -f "$script_dir/docker-compose.nogpu.yml" logs -f
}

# 测试端口连接
test_service_port() {
    local hostname=${1:-"localhost"}
    local port=$2
    local timeout=${3:-3}

    if timeout "$timeout" bash -c "</dev/tcp/$hostname/$port" &>/dev/null; then
        return 0
    else
        return 1
    fi
}

# 查看服务状态
show_service_status() {
    log_info "检查服务状态..."

    local script_dir=$(get_script_directory)

    # 显示容器状态表格
    docker-compose -f "$script_dir/docker-compose.nogpu.yml" ps

    echo ""
    log_info "容器运行状态:"

    # 获取容器状态信息
    local ps_output
    ps_output=$(docker-compose -f "$script_dir/docker-compose.nogpu.yml" ps --format "table {{.Name}}\t{{.State}}\t{{.Health}}")

    # 获取运行中的容器
    local running_containers
    running_containers=$(docker-compose -f "$script_dir/docker-compose.nogpu.yml" ps --services --filter "status=running" 2>/dev/null || echo "")

    # 检查每个服务状态
    for service in "${ALL_SERVICES[@]}"; do
        if docker ps --format "{{.Names}}" | grep -q "^${service}$"; then
            local health_status
            health_status=$(docker inspect "$service" --format='{{if .State.Health}}{{.State.Health.Status}}{{else}}no-health-check{{end}}' 2>/dev/null || echo "unknown")

            case "$health_status" in
                "healthy")
                    log_success "$service - 运行正常 (健康)"
                    ;;
                "no-health-check")
                    log_success "$service - 运行正常 (无健康检查)"
                    ;;
                "unhealthy")
                    log_warning "$service - 运行中但不健康"
                    ;;
                "starting")
                    log_info "$service - 正在启动中..."
                    ;;
                *)
                    log_warning "$service - 健康状态未知: $health_status"
                    ;;
            esac
        else
            log_error "$service - 未运行"
        fi
    done

    echo ""
    log_info "服务访问地址:"
    echo -e "${CYAN}  🌐 前端应用:     http://localhost:5173${NC}"
    echo -e "${CYAN}  🔧 后端API:      http://localhost:8001/docs${NC}"
    echo -e "${CYAN}  🧠 Morphik Core: http://localhost:8000/docs${NC}"
    echo -e "${CYAN}  🤖 SmoLAgents:   http://localhost:8002/docs${NC}"
    echo -e "${CYAN}  🗄️ PostgreSQL:   localhost:5433${NC}"

    echo ""
    log_info "快速测试命令:"
    echo -e "${YELLOW}  curl http://localhost:5173${NC}"
    echo -e "${YELLOW}  curl http://localhost:8001/api/v1/health${NC}"
    echo -e "${YELLOW}  curl http://localhost:8000/ping${NC}"
    echo -e "${YELLOW}  curl http://localhost:8002/health${NC}"

    # 端口连通性检查
    echo ""
    log_info "端口连通性检查:"

    local ports=(
        "5173:前端服务"
        "8001:后端API"
        "8000:Morphik Core"
        "8002:SmoLAgents"
        "5433:主数据库"
    )

    for port_info in "${ports[@]}"; do
        IFS=':' read -r port name <<< "$port_info"
        if test_service_port "127.0.0.1" "$port" 1; then
            log_success "$name 端口 $port 可访问"
        else
            log_warning "$name 端口 $port 连接失败"
        fi
    done
}

# 显示访问信息
show_access_info() {
    echo ""
    echo -e "${GREEN}🎉 ZHT System 0624 启动完成！${NC}"
    echo -e "${CYAN}======================================${NC}"
    echo ""
    echo -e "${YELLOW}📱 服务访问地址:${NC}"
    echo -e "${WHITE}  🌐 前端应用:     http://localhost:5173${NC}"
    echo -e "${WHITE}  🔧 后端API:      http://localhost:8001/docs${NC}"
    echo -e "${WHITE}  🧠 Morphik Core: http://localhost:8000/docs${NC}"
    echo -e "${WHITE}  🤖 SmoLAgents:   http://localhost:8002/docs${NC}"
    echo ""
    echo -e "${YELLOW}🗄️ 数据库信息:${NC}"
    echo -e "${WHITE}  📊 PostgreSQL:   localhost:5433${NC}"
    echo -e "${WHITE}  👤 用户名:       zht_user${NC}"
    echo -e "${WHITE}  🔑 密码:         zht_password${NC}"
    echo -e "${WHITE}  🗃️ 数据库:       zht_db${NC}"
    echo ""
    echo -e "${YELLOW}🔧 管理命令:${NC}"
    echo -e "${WHITE}  查看日志:        ./manage-zht-system.sh --logs${NC}"
    echo -e "${WHITE}  停止服务:        ./manage-zht-system.sh --stop${NC}"
    echo -e "${WHITE}  重启服务:        ./manage-zht-system.sh --restart${NC}"
    echo -e "${WHITE}  查看状态:        ./manage-zht-system.sh --status${NC}"
    echo ""
    echo -e "${YELLOW}📋 功能特性:${NC}"
    echo -e "${WHITE}  ✅ 任务管理CRUD操作${NC}"
    echo -e "${WHITE}  ✅ 异步SQLAlchemy 2.0${NC}"
    echo -e "${WHITE}  ✅ PostgreSQL数据库${NC}"
    echo -e "${WHITE}  ✅ React + TypeScript前端${NC}"
    echo -e "${WHITE}  ✅ TanStack Query状态管理${NC}"
    echo -e "${WHITE}  ✅ shadcn/ui组件库${NC}"
    echo -e "${WHITE}  ✅ Morphik AI服务集成${NC}"
    echo -e "${WHITE}  ✅ SmoLAgents智能代理${NC}"
    echo -e "${CYAN}======================================${NC}"
}

# 强制重建服务
build_all_services() {
    log_info "强制重建并启动所有服务..."

    local script_dir=$(get_script_directory)
    set_docker_environment

    # 检查前端依赖
    if ! test_frontend_dependencies; then
        return 1
    fi

    # 停止现有容器
    log_step "停止现有容器..."
    docker-compose -f "$script_dir/docker-compose.nogpu.yml" down --remove-orphans

    # 构建镜像
    log_step "构建镜像（这可能需要几分钟时间）..."
    if docker-compose -f "$script_dir/docker-compose.nogpu.yml" build; then
        log_success "构建成功完成"
        start_all_services
        return $?
    else
        log_error "构建失败"
        log_info "故障排除提示:"
        log_info "  • 检查 Docker 是否运行"
        log_info "  • 确保有足够的磁盘空间"
        log_info "  • 尝试: docker system prune -f"
        log_info "  • 检查网络连接"
        return 1
    fi
}

# 清理缓存并重建
build_no_cache() {
    log_info "清理缓存并重建..."

    local script_dir=$(get_script_directory)
    set_docker_environment

    # 检查前端依赖
    if ! test_frontend_dependencies; then
        return 1
    fi

    # 清理 Docker 系统
    log_step "清理 Docker 系统缓存..."
    docker system prune -f

    # 移除未使用的镜像
    log_step "移除未使用的镜像..."
    docker image prune -f

    # 构建并启动
    log_step "开始无缓存构建..."
    if docker-compose -f "$script_dir/docker-compose.nogpu.yml" build --no-cache; then
        log_success "构建成功完成"
        start_all_services
        return $?
    else
        log_error "构建失败"
        return 1
    fi
}

# 主函数
main() {
    # 显示脚本标题
    echo -e "${CYAN}$SCRIPT_NAME v$SCRIPT_VERSION${NC}"
    echo ""

    # 解析参数
    local action=""
    local dev_mode_value=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                action="help"
                shift
                ;;
            -s|--start)
                action="start"
                shift
                ;;
            -r|--restart)
                action="restart"
                shift
                ;;
            -l|--logs)
                action="logs"
                shift
                ;;
            -t|--stop)
                action="stop"
                shift
                ;;
            --status)
                action="status"
                shift
                ;;
            --build)
                action="build"
                shift
                ;;
            --no-cache)
                action="no-cache"
                shift
                ;;
            --frontend-only)
                action="frontend-only"
                shift
                ;;
            --backend-only)
                action="backend-only"
                shift
                ;;
            --morphik-only)
                action="morphik-only"
                shift
                ;;
            --ai-only)
                action="ai-only"
                shift
                ;;
            --dev-mode)
                action="dev-mode"
                if [[ $# -gt 1 && ($2 == "on" || $2 == "off") ]]; then
                    dev_mode_value="$2"
                    shift 2
                else
                    log_error "--dev-mode 需要参数 'on' 或 'off'"
                    echo "使用方法: ./manage-zht-system.sh --dev-mode on|off"
                    exit 1
                fi
                ;;
            *)
                log_error "未知参数: $1"
                echo "使用 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done

    # 检查 Docker 状态（跳过帮助命令）
    if [ "$action" != "help" ]; then
        if ! test_docker; then
            exit 1
        fi
    fi

    # 执行相应操作
    case $action in
        help)
            show_help
            ;;
        start)
            start_all_services
            ;;
        frontend-only)
            start_frontend_only
            ;;
        backend-only)
            start_backend_only
            ;;
        morphik-only)
            start_morphik_only
            ;;
        ai-only)
            start_ai_only
            ;;
        dev-mode)
            set_dev_mode "$dev_mode_value"
            ;;
        restart)
            restart_all_services
            ;;
        logs)
            show_service_logs
            ;;
        stop)
            stop_all_services
            ;;
        status)
            show_service_status
            ;;
        build)
            build_all_services
            ;;
        no-cache)
            build_no_cache
            ;;
        *)
            log_info "使用 --help 查看帮助信息"
            echo ""
            show_service_status
            ;;
    esac
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
