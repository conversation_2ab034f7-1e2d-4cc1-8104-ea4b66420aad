/**
 * 销冠实战训练相关的TypeScript类型定义
 */

// 国家选项
export interface Country {
  id: number
  name: string
  code: string
  flag?: string
  is_active?: boolean
  created_at?: string
  updated_at?: string
}

// 产品选项
export interface Product {
  id: number
  name: string
  category: string
  description?: string
  is_active?: boolean
  created_at?: string
  updated_at?: string
}



// 客户数据（数据库存储）
export interface Customer {
  id: number
  name: string
  company?: string
  phone?: string
  email?: string
  industry?: string
  region?: string
  country_code?: string

  training_scenario?: string
  notes?: string
  is_active?: boolean
  created_at?: string
  updated_at?: string
}

// 客户创建数据
export interface CustomerCreate {
  name: string
  company?: string
  phone?: string
  email?: string
  industry?: string
  level?: string
  region?: string
  country_code?: string

  training_scenario?: string
  notes?: string
}

// 客户更新数据
export interface CustomerUpdate {
  name?: string
  company?: string
  phone?: string
  email?: string
  industry?: string
  level?: string
  region?: string
  country_code?: string

  training_scenario?: string
  notes?: string
  is_active?: boolean
}

// 训练客户
export interface TrainingCustomer {
  id: string
  name: string
  country: Country
  product: Product
  createdAt: Date
  updatedAt: Date
  // 客户背景信息
  background?: {
    company?: string
    position?: string
    experience?: string
    preferences?: string[]
  }
}

// 对话消息
export interface ChatMessage {
  id: string
  role: 'user' | 'customer' | 'system'
  content: string
  timestamp: Date
  // 消息元数据
  metadata?: {
    type?: 'text' | 'suggestion' | 'evaluation'
    confidence?: number
    suggestions?: string[]
  }
}

// 对话会话
export interface ChatSession {
  id: string
  customerId: string
  messages: ChatMessage[]
  startTime: Date
  endTime?: Date
  status: 'active' | 'completed' | 'paused'
  // 会话统计
  stats?: {
    messageCount: number
    duration: number
    customerSatisfaction?: number
  }
}

// 评价维度
export interface EvaluationDimension {
  id: string
  name: string
  description: string
  score: number // 1-5分
  maxScore: number
  feedback?: string
  suggestions?: string[]
}

// 对话评价
export interface ChatEvaluation {
  id: string
  sessionId: string
  customerId: string
  dimensions: {
    needsUnderstanding: EvaluationDimension      // 需求理解与把握
    professionalism: EvaluationDimension         // 专业度
    problemSolving: EvaluationDimension          // 客户问题处理能力
    persuasiveness: EvaluationDimension          // 说服力
    culturalSensitivity: EvaluationDimension     // 跨文化敏感度
    termControl: EvaluationDimension             // 条款把控
    customerSatisfaction: EvaluationDimension    // 客户感受与满意度
  }
  overallScore: number
  overallFeedback: string
  createdAt: Date
  // 改进建议
  improvements: {
    priority: 'high' | 'medium' | 'low'
    area: string
    suggestion: string
    resources?: string[]
  }[]
}

// 学习提示
export interface LearningTip {
  id: string
  title: string
  content: string
  category: 'communication' | 'negotiation' | 'cultural' | 'product' | 'general'
  priority: 'high' | 'medium' | 'low'
  trigger?: {
    keywords?: string[]
    context?: string
    timing?: 'before' | 'during' | 'after'
  }
}

// 知识点卡片
export interface KnowledgeCard {
  id: string
  title: string
  content: string
  category: string
  tags: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedReadTime: number // 分钟
  resources?: {
    type: 'link' | 'document' | 'video'
    title: string
    url: string
  }[]
}



// 训练场景
export interface TrainingScenario {
  id: string
  name: string
  description: string
  industry: string
  customerType: 'decision_maker' | 'technical_expert' | 'procurement_manager' | 'end_user'
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  duration: number // 预计时长（分钟）
  objectives: string[]
  challenges: string[]
  tags: string[]
  icon?: string
  color?: string
}



// 消息状态
export interface MessageStatus {
  id: string
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'typing'
  timestamp: Date
}

// 快捷回复
export interface QuickReply {
  id: string
  text: string
  category: 'greeting' | 'question' | 'objection' | 'closing' | 'custom'
  context?: string[]
  usage_count?: number
}

// 表单状态
export interface CustomerFormData {
  name: string
  countryId: string
  productId: string
  company: string
}

// 页面状态
export interface SalesTrainingState {
  // 当前页面
  currentView: 'management' | 'training' | 'history'
  // 选中的客户
  selectedCustomer?: TrainingCustomer
  // 当前会话
  currentSession?: ChatSession
  // 是否显示学习提示
  showLearningTips: boolean
  // 是否显示评价面板
  showEvaluation: boolean
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 训练场景数据（数据库存储）
export interface TrainingScenarioData {
  id: number
  name: string
  description?: string
  category: string
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  duration?: number
  objectives?: string
  challenges?: string
  tags?: string
  icon?: string
  color?: string
  is_active?: boolean
  created_at?: string
  updated_at?: string
}

// 训练场景创建数据
export interface TrainingScenarioCreate {
  name: string
  description?: string
  category: string
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  duration?: number
  objectives?: string
  challenges?: string
  tags?: string
  icon?: string
  color?: string
}

// 训练场景更新数据
export interface TrainingScenarioUpdate {
  name?: string
  description?: string
  category?: string
  difficulty?: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  duration?: number
  objectives?: string
  challenges?: string
  tags?: string
  icon?: string
  color?: string
  is_active?: boolean
}

// 分页数据
export interface PaginatedData<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
  hasNext: boolean
  hasPrev: boolean
}
