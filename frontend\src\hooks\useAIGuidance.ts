/**
 * AI指导Hook - 处理基于真实AI的指导分析
 * 修复角色混淆问题，确保只分析销售员回复
 */

import { useState, useCallback, useRef } from 'react'
import type { ChatMessage, TrainingCustomer } from '@/types/salesTraining'
import { 
    performAIGuidanceAnalysis,
    type ReplyAnalysis,
    type OverallAnalysis,
    type AIGuidanceResult
} from '@/services/aiGuidanceService'

export function useAIGuidance(messages: ChatMessage[], customer: TrainingCustomer) {
    const [overallAnalysis, setOverallAnalysis] = useState<OverallAnalysis | null>(null)
    const [replyAnalyses, setReplyAnalyses] = useState<ReplyAnalysis[]>([])
    const [isAnalyzing, setIsAnalyzing] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const abortControllerRef = useRef<AbortController | null>(null)

    // 执行AI指导分析（使用新的服务）
    const performGuidanceAnalysis = useCallback(async () => {
        console.log('开始执行AI指导分析', {
            messagesLength: messages.length,
            userMessagesCount: messages.filter(m => m.role === 'user').length
        })

        const userMessages = messages.filter(m => m.role === 'user')
        if (userMessages.length === 0) {
            throw new Error('需要至少一轮对话才能进行分析')
        }

        if (messages.length < 2) {
            throw new Error('至少需要2条消息才能进行分析')
        }

        // 取消之前的请求
        if (abortControllerRef.current) {
            abortControllerRef.current.abort()
        }

        setIsAnalyzing(true)
        setError(null)

        abortControllerRef.current = new AbortController()

        try {
            // 使用新的AI指导分析服务
            const result = await performAIGuidanceAnalysis(
                messages,
                customer,
                abortControllerRef.current.signal
            )

            console.log('AI指导分析完成', result)

            // 更新分析结果
            setOverallAnalysis(result.overallAnalysis)
            setReplyAnalyses(result.replyAnalyses)

            return result

        } catch (err: any) {
            if (err.name === 'AbortError') {
                return // 请求被取消，不处理错误
            }

            console.error('AI指导分析失败:', err)

            let errorMessage = '分析失败，请重试'

            if (err.name === 'TypeError' && err.message.includes('fetch')) {
                errorMessage = '网络连接失败，请检查SmoLAgents服务状态'
            } else if (err.message.includes('timeout')) {
                errorMessage = 'AI分析超时，请稍后重试'
            } else if (err.message.includes('SmoLAgents API调用失败')) {
                errorMessage = 'AI服务暂时不可用，请稍后重试'
            } else if (err.message) {
                errorMessage = err.message
            }

            setError(errorMessage)
            throw err
        } finally {
            setIsAnalyzing(false)
            abortControllerRef.current = null
        }
    }, [messages, customer])

    // 兼容旧的generateGuidance方法（已废弃，建议使用performGuidanceAnalysis）
    const generateGuidance = useCallback(async () => {
        console.warn('generateGuidance已废弃，请使用performGuidanceAnalysis')
        return performGuidanceAnalysis()
    }, [performGuidanceAnalysis])

    // 导出指导报告
    const exportGuidance = useCallback(async () => {
        if (!overallAnalysis && replyAnalyses.length === 0) {
            throw new Error('没有可导出的分析数据')
        }

        // 动态导入导出工具
        const { exportToTXT } = await import('@/utils/documentExport')

        const exportData = {
            customer: {
                name: customer.name,
                company: customer.background?.company || '未知公司',
                country: customer.country.name,
                industry: '通用行业',
                product: customer.product.name
            },
            overallAnalysis,
            replyAnalyses,
            generatedAt: new Date().toISOString()
        }

        exportToTXT(exportData)
    }, [overallAnalysis, replyAnalyses, customer])

    // 重置AI指导状态
    const resetGuidance = useCallback(() => {
        setOverallAnalysis(null)
        setReplyAnalyses([])
        setError(null)
        
        // 取消正在进行的分析
        if (abortControllerRef.current) {
            abortControllerRef.current.abort()
            abortControllerRef.current = null
        }
        
        setIsAnalyzing(false)
    }, [])

    return {
        overallAnalysis,
        replyAnalyses,
        isAnalyzing,
        error,
        lastAnalyzedCount: messages.filter(m => m.role === 'user').length, // 兼容性
        performGuidanceAnalysis, // 主要方法：执行AI指导分析
        generateGuidance, // 兼容方法（已废弃）
        exportGuidance,
        resetGuidance // 新增：重置AI指导状态
    }
}