"""
密码重置Pydantic模式
定义密码重置相关API请求和响应的数据结构
"""
from pydantic import BaseModel, Field, EmailStr
from typing import Optional
from datetime import datetime


class PasswordResetRequest(BaseModel):
    """申请密码重置的请求模式"""
    email: EmailStr = Field(..., description="邮箱地址")


class PasswordResetResponse(BaseModel):
    """申请密码重置的响应模式"""
    message: str = Field(..., description="响应消息")
    email: str = Field(..., description="邮箱地址")


class PasswordResetVerify(BaseModel):
    """验证重置令牌的请求模式"""
    token: str = Field(..., description="重置令牌")


class PasswordResetVerifyResponse(BaseModel):
    """验证重置令牌的响应模式"""
    valid: bool = Field(..., description="令牌是否有效")
    message: str = Field(..., description="响应消息")
    email: Optional[str] = Field(None, description="关联的邮箱地址")


class PasswordResetConfirm(BaseModel):
    """确认密码重置的请求模式"""
    token: str = Field(..., description="重置令牌")
    new_password: str = Field(..., min_length=6, max_length=50, description="新密码")


class PasswordResetConfirmResponse(BaseModel):
    """确认密码重置的响应模式"""
    message: str = Field(..., description="响应消息")


class PasswordResetToken(BaseModel):
    """密码重置令牌响应模式"""
    id: int = Field(..., description="令牌ID")
    user_id: int = Field(..., description="用户ID")
    token: str = Field(..., description="重置令牌")
    expires_at: datetime = Field(..., description="过期时间")
    used: bool = Field(..., description="是否已使用")
    created_at: datetime = Field(..., description="创建时间")
    
    class Config:
        from_attributes = True
