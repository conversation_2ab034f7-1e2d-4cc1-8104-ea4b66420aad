/**
 * 话术模板管理页面
 * 提供模板的增删改查、激活管理、导入导出等功能
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ScrollText,
  Plus,
  Search,
  Filter,
  Upload,
  Download,
  Settings,
  Play,
  Pause,
  Edit,
  Trash2,
  Eye,
  Star,
  TrendingUp,
  BarChart3
} from 'lucide-react';
import { toast } from 'sonner';
import { templateService } from '@/services/templateService';
import { Template, TemplateFilter } from '@/types/template';
import TemplateCard from '@/components/template/TemplateCard';
import TemplateEditor from '@/components/template/TemplateEditor';
import TemplatePreview from '@/components/template/TemplatePreview';
import TemplateImportDialog from '@/components/template/TemplateImportDialog';
import TemplateAnalyticsDashboard from '@/components/template/TemplateAnalyticsDashboard';

const TemplateManagementPage: React.FC = () => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);
  const [activeTemplate, setActiveTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showEditor, setShowEditor] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [previewTemplate, setPreviewTemplate] = useState<Template | null>(null);

  // 加载模板数据
  useEffect(() => {
    loadTemplates();
    loadActiveTemplate();
  }, []);

  // 应用过滤器
  useEffect(() => {
    applyFilters();
  }, [templates, searchQuery, selectedCategory]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const templateList = await templateService.getTemplates();
      console.log('加载模板数据:', templateList.length, '个模板');
      console.log('模板列表:', templateList.map(t => ({ id: t.id, name: t.name, category: t.category })));
      setTemplates(templateList);
    } catch (error) {
      console.error('加载模板失败:', error);
      toast.error('加载模板失败');
    } finally {
      setLoading(false);
    }
  };

  const loadActiveTemplate = () => {
    const active = templateService.getActiveTemplate();
    setActiveTemplate(active);
  };

  const applyFilters = () => {
    let filtered = templates;

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // 类别过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    setFilteredTemplates(filtered);
  };

  const handleActivateTemplate = async (template: Template) => {
    try {
      const success = await templateService.activateTemplate(template.id);
      if (success) {
        setActiveTemplate(template);
        await loadTemplates(); // 重新加载以更新激活状态
        toast.success(`已激活模板: ${template.name}`);
      } else {
        toast.error('激活模板失败');
      }
    } catch (error) {
      console.error('激活模板失败:', error);
      toast.error('激活模板失败');
    }
  };

  const handleDeactivateTemplate = () => {
    try {
      templateService.deactivateTemplate();
      setActiveTemplate(null);
      loadTemplates(); // 重新加载以更新激活状态
      toast.success('已取消激活模板');
    } catch (error) {
      console.error('取消激活失败:', error);
      toast.error('取消激活失败');
    }
  };

  const handleDeleteTemplate = async (template: Template) => {
    if (!confirm(`确定要删除模板 "${template.name}" 吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const success = await templateService.deleteTemplate(template.id);
      if (success) {
        await loadTemplates();
        loadActiveTemplate();
        toast.success(`已删除模板: ${template.name}`);
      } else {
        toast.error('删除模板失败');
      }
    } catch (error) {
      console.error('删除模板失败:', error);
      toast.error('删除模板失败');
    }
  };

  const handleEditTemplate = (template: Template) => {
    setEditingTemplate(template);
    setShowEditor(true);
  };

  const handlePreviewTemplate = (template: Template) => {
    setPreviewTemplate(template);
    setShowPreview(true);
  };

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    setShowEditor(true);
  };

  const handleExportTemplates = async () => {
    try {
      const exportData = await templateService.exportTemplates();
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `templates_export_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('模板导出成功');
    } catch (error) {
      console.error('导出模板失败:', error);
      toast.error('导出模板失败');
    }
  };

  const getCategories = () => {
    const categories = [...new Set(templates.map(t => t.category))];
    return [
      { value: 'all', label: '全部类别' },
      ...categories.map(cat => ({ value: cat, label: cat }))
    ];
  };

  const getStats = () => {
    return {
      total: templates.length,
      active: templates.filter(t => t.is_active).length,
      categories: new Set(templates.map(t => t.category)).size,
      totalUsage: templates.reduce((sum, t) => sum + t.usage_count, 0)
    };
  };

  const stats = getStats();

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <ScrollText className="h-8 w-8" />
            话术模板管理
          </h2>
          <p className="text-muted-foreground">
            管理AI对话的话术模板，自定义角色和回答风格
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setShowImportDialog(true)}>
            <Upload className="h-4 w-4 mr-2" />
            导入
          </Button>
          <Button variant="outline" onClick={handleExportTemplates}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button onClick={handleCreateTemplate}>
            <Plus className="h-4 w-4 mr-2" />
            新建模板
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总模板数</CardTitle>
            <ScrollText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">激活模板</CardTitle>
            <Play className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.active}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">模板类别</CardTitle>
            <Filter className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.categories}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总使用次数</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsage}</div>
          </CardContent>
        </Card>
      </div>

      {/* 当前激活模板 */}
      {activeTemplate && (
        <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-700 dark:text-green-300">
              <Play className="h-5 w-5" />
              当前激活模板
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">{activeTemplate.name}</h3>
                <p className="text-sm text-muted-foreground">{activeTemplate.description}</p>
                <div className="flex gap-2 mt-2">
                  <Badge variant="secondary">{activeTemplate.category}</Badge>
                  <Badge variant="outline">使用 {activeTemplate.usage_count} 次</Badge>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => handlePreviewTemplate(activeTemplate)}>
                  <Eye className="h-4 w-4 mr-1" />
                  预览
                </Button>
                <Button variant="outline" size="sm" onClick={handleDeactivateTemplate}>
                  <Pause className="h-4 w-4 mr-1" />
                  取消激活
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 主要内容标签页 */}
      <Tabs defaultValue="templates" className="space-y-4">
        <TabsList>
          <TabsTrigger value="templates">模板管理</TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChart3 className="h-4 w-4 mr-2" />
            数据分析
          </TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-4">
          {/* 搜索和过滤 */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索模板..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              {getCategories().map(cat => (
                <option key={cat.value} value={cat.value}>{cat.label}</option>
              ))}
            </select>
          </div>

          {/* 模板列表 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {loading ? (
          Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-muted rounded"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredTemplates.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <ScrollText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">暂无模板</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery || selectedCategory !== 'all'
                ? '没有找到匹配的模板，请调整搜索条件'
                : '还没有创建任何模板，点击上方按钮开始创建'}
            </p>
            {!searchQuery && selectedCategory === 'all' && (
              <Button onClick={handleCreateTemplate}>
                <Plus className="h-4 w-4 mr-2" />
                创建第一个模板
              </Button>
            )}
          </div>
        ) : (
          filteredTemplates.map(template => (
            <TemplateCard
              key={template.id}
              template={template}
              isActive={activeTemplate?.id === template.id}
              onActivate={() => handleActivateTemplate(template)}
              onDeactivate={handleDeactivateTemplate}
              onEdit={() => handleEditTemplate(template)}
              onPreview={() => handlePreviewTemplate(template)}
              onDelete={() => handleDeleteTemplate(template)}
            />
          ))
        )}
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <TemplateAnalyticsDashboard />
        </TabsContent>
      </Tabs>

      {/* 模板编辑器对话框 */}
      {showEditor && (
        <TemplateEditor
          template={editingTemplate}
          open={showEditor}
          onClose={() => {
            setShowEditor(false);
            setEditingTemplate(null);
          }}
          onSave={async () => {
            await loadTemplates();
            loadActiveTemplate();
            setShowEditor(false);
            setEditingTemplate(null);
          }}
        />
      )}

      {/* 模板预览对话框 */}
      {showPreview && previewTemplate && (
        <TemplatePreview
          template={previewTemplate}
          open={showPreview}
          onClose={() => {
            setShowPreview(false);
            setPreviewTemplate(null);
          }}
        />
      )}

      {/* 模板导入对话框 */}
      {showImportDialog && (
        <TemplateImportDialog
          open={showImportDialog}
          onClose={() => setShowImportDialog(false)}
          onImport={async () => {
            await loadTemplates();
            loadActiveTemplate();
            // 重置筛选条件以确保能看到新导入的模板
            setSearchQuery('');
            setSelectedCategory('all');
            setShowImportDialog(false);
            console.log('导入完成，已重置筛选条件');
          }}
        />
      )}
    </div>
  );
};

export default TemplateManagementPage;
