"""
任务管理CRUD操作
实现任务的增删改查功能
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from sqlalchemy.orm import selectinload
from typing import Optional, List
from app.models.task import Task, TaskStatus, TaskPriority
from app.schemas.task import TaskCreate, TaskUpdate


class TaskCRUD:
    """任务CRUD操作类"""
    
    async def create(self, db: AsyncSession, *, obj_in: TaskCreate) -> Task:
        """创建新任务"""
        db_obj = Task(**obj_in.model_dump())
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def get(self, db: AsyncSession, id: int) -> Optional[Task]:
        """根据ID获取任务"""
        result = await db.execute(select(Task).where(Task.id == id))
        return result.scalar_one_or_none()
    
    async def get_multi(
        self, 
        db: AsyncSession, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        status: Optional[TaskStatus] = None,
        priority: Optional[TaskPriority] = None,
        assignee: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[Task]:
        """获取任务列表（支持过滤）"""
        query = select(Task)
        
        # 构建过滤条件
        conditions = []
        if status is not None:
            conditions.append(Task.status == status)
        if priority is not None:
            conditions.append(Task.priority == priority)
        if assignee is not None:
            conditions.append(Task.assignee == assignee)
        if is_active is not None:
            conditions.append(Task.is_active == is_active)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        query = query.offset(skip).limit(limit).order_by(Task.created_at.desc())
        result = await db.execute(query)
        return result.scalars().all()
    
    async def count(
        self, 
        db: AsyncSession,
        *,
        status: Optional[TaskStatus] = None,
        priority: Optional[TaskPriority] = None,
        assignee: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> int:
        """统计任务数量（支持过滤）"""
        query = select(func.count(Task.id))
        
        # 构建过滤条件
        conditions = []
        if status is not None:
            conditions.append(Task.status == status)
        if priority is not None:
            conditions.append(Task.priority == priority)
        if assignee is not None:
            conditions.append(Task.assignee == assignee)
        if is_active is not None:
            conditions.append(Task.is_active == is_active)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        result = await db.execute(query)
        return result.scalar()
    
    async def update(self, db: AsyncSession, *, db_obj: Task, obj_in: TaskUpdate) -> Task:
        """更新任务"""
        update_data = obj_in.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj
    
    async def remove(self, db: AsyncSession, *, id: int) -> Optional[Task]:
        """删除任务"""
        db_obj = await self.get(db, id=id)
        if db_obj:
            await db.delete(db_obj)
            await db.commit()
        return db_obj
    
    async def get_stats(self, db: AsyncSession) -> dict:
        """获取任务统计信息"""
        # 总数统计
        total_result = await db.execute(select(func.count(Task.id)))
        total = total_result.scalar()
        
        # 按状态统计
        status_query = select(Task.status, func.count(Task.id)).group_by(Task.status)
        status_result = await db.execute(status_query)
        status_counts = {status.value: 0 for status in TaskStatus}
        for status, count in status_result:
            status_counts[status.value] = count
        
        # 按优先级统计
        priority_query = select(Task.priority, func.count(Task.id)).group_by(Task.priority)
        priority_result = await db.execute(priority_query)
        priority_counts = {priority.value: 0 for priority in TaskPriority}
        for priority, count in priority_result:
            priority_counts[priority.value] = count
        
        return {
            "total": total,
            "pending": status_counts.get("pending", 0),
            "in_progress": status_counts.get("in_progress", 0),
            "completed": status_counts.get("completed", 0),
            "cancelled": status_counts.get("cancelled", 0),
            "by_priority": priority_counts
        }


# 创建全局实例
task_crud = TaskCRUD()
