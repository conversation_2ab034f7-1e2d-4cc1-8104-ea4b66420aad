/**
 * 文档导出工具
 * 支持TXT文本导出
 */

// 回复分析类型
interface ReplyAnalysis {
  messageIndex: number
  userMessage: string
  strengths: string[]
  weaknesses: string[]
  score: number
  suggestedReply: string
  reasoning: string
}

// 整体分析类型
interface OverallAnalysis {
  conversationFlow: string
  communicationStyle: string
  needsIdentification: string
  relationshipBuilding: string
  culturalAdaptation: string
  improvementAreas: string[]
  nextSteps: string[]
}

interface ExportData {
  customer: {
    name: string
    company: string
    country: string
    industry: string
    product: string
  }
  overallAnalysis: OverallAnalysis | null
  replyAnalyses: ReplyAnalysis[]
  generatedAt: string
}



/**
 * 导出为TXT文本格式
 */
export function exportToTXT(data: ExportData): void {
  try {
    // 生成文本内容
    const txtContent = generateTXTContent(data)
    
    // 创建文件名
    const fileName = `AI指导报告_${data.customer.name}_${new Date().toISOString().split('T')[0]}.txt`
    
    // 创建Blob对象
    const blob = new Blob([txtContent], { 
      type: 'text/plain;charset=utf-8' 
    })
    
    // 创建下载链接
    const url = URL.createObjectURL(blob)
    
    // 创建临时下载元素
    const downloadLink = document.createElement('a')
    downloadLink.href = url
    downloadLink.download = fileName
    downloadLink.style.display = 'none'
    
    // 添加到DOM并触发下载
    document.body.appendChild(downloadLink)
    downloadLink.click()
    
    // 清理
    document.body.removeChild(downloadLink)
    URL.revokeObjectURL(url)
    
  } catch (error) {
    console.error('TXT导出失败:', error)
    throw new Error('导出失败，请重试')
  }
}

/**
 * 生成TXT文本内容
 */
function generateTXTContent(data: ExportData): string {
  let content = ''
  
  // 标题和基本信息
  content += '='.repeat(50) + '\n'
  content += '           AI智能指导报告\n'
  content += '='.repeat(50) + '\n\n'
  content += `生成时间: ${new Date(data.generatedAt).toLocaleString()}\n\n`

  // 客户信息
  content += '【客户信息】\n'
  content += '-'.repeat(30) + '\n'
  content += `客户姓名: ${data.customer.name}\n`
  content += `公司名称: ${data.customer.company}\n`
  content += `所在国家: ${data.customer.country}\n`
  content += `所属行业: ${data.customer.industry}\n`
  content += `产品兴趣: ${data.customer.product}\n\n`

  // 整体分析
  if (data.overallAnalysis) {
    content += '【整体分析】\n'
    content += '-'.repeat(30) + '\n\n'

    content += '◆ 对话流程分析\n'
    content += `${data.overallAnalysis.conversationFlow}\n\n`

    content += '◆ 沟通风格评价\n'
    content += `${data.overallAnalysis.communicationStyle}\n\n`

    content += '◆ 需求识别能力\n'
    content += `${data.overallAnalysis.needsIdentification}\n\n`

    content += '◆ 关系建立\n'
    content += `${data.overallAnalysis.relationshipBuilding}\n\n`

    content += '◆ 跨文化适应\n'
    content += `${data.overallAnalysis.culturalAdaptation}\n\n`

    content += '◆ 改进建议\n'
    data.overallAnalysis.improvementAreas.forEach((area, index) => {
      content += `${index + 1}. ${area}\n`
    })
    content += '\n'

    content += '◆ 下步行动建议\n'
    data.overallAnalysis.nextSteps.forEach((step, index) => {
      content += `${index + 1}. ${step}\n`
    })
    content += '\n'
  }

  // 回复评价
  if (data.replyAnalyses.length > 0) {
    content += '【回复评价】\n'
    content += '-'.repeat(30) + '\n\n'

    data.replyAnalyses.forEach((analysis, index) => {
      content += `▼ 第 ${analysis.messageIndex + 1} 轮回复 (得分: ${analysis.score}/100)\n`
      content += '─'.repeat(25) + '\n'
      
      content += `原始回复: "${analysis.userMessage}"\n\n`

      if (analysis.strengths.length > 0) {
        content += '✓ 优点:\n'
        analysis.strengths.forEach(strength => {
          content += `  • ${strength}\n`
        })
        content += '\n'
      }

      if (analysis.weaknesses.length > 0) {
        content += '✗ 待改进:\n'
        analysis.weaknesses.forEach(weakness => {
          content += `  • ${weakness}\n`
        })
        content += '\n'
      }

      content += '💡 建议回复:\n'
      content += `"${analysis.suggestedReply}"\n\n`

      content += '📝 评价理由:\n'
      content += `${analysis.reasoning}\n\n`

      if (index < data.replyAnalyses.length - 1) {
        content += '─'.repeat(50) + '\n\n'
      }
    })
  }

  // 结尾
  content += '='.repeat(50) + '\n'
  content += '报告结束\n'
  content += '='.repeat(50) + '\n'

  return content
}