import React from 'react'
import { LucideIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface KnowledgeCategoryCardProps {
  id: string
  title: string
  icon: LucideIcon
  color: string
  description: string
  count: number
  isActive: boolean
  onClick: (categoryId: string) => void
}

export function KnowledgeCategoryCard({
  id,
  title,
  icon: IconComponent,
  color,
  description,
  count,
  isActive,
  onClick
}: KnowledgeCategoryCardProps) {
  return (
    <Button
      variant={isActive ? "default" : "ghost"}
      className="w-full justify-start h-auto p-0"
      onClick={() => onClick(id)}
    >
      <Card className={`w-full border-0 shadow-none ${isActive ? 'bg-primary text-primary-foreground' : 'hover:bg-accent'}`}>
        <CardContent className="p-3">
          <div className="flex items-center space-x-3 w-full">
            <div className={`p-2 rounded-lg ${isActive ? 'bg-primary-foreground/20' : color} ${isActive ? 'text-primary-foreground' : 'text-white'}`}>
              <IconComponent className="h-4 w-4" />
            </div>
            <div className="flex-1 text-left">
              <div className="font-medium">{title}</div>
              <div className={`text-xs ${isActive ? 'text-primary-foreground/70' : 'text-muted-foreground'}`}>
                {count} 个文档
              </div>
            </div>
            <Badge 
              variant={isActive ? "secondary" : "outline"} 
              className={`text-xs ${isActive ? 'bg-primary-foreground/20 text-primary-foreground border-primary-foreground/20' : ''}`}
            >
              {count}
            </Badge>
          </div>
        </CardContent>
      </Card>
    </Button>
  )
}
