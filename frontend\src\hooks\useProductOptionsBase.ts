import { useState, useEffect, useCallback } from 'react'
import { 
  Package, 
  ShoppingBag, 
  Briefcase, 
  Heart, 
  Car, 
  Home, 
  Smartphone, 
  Laptop, 
  Watch, 
  Camera,
  Headphones,
  Gamepad2,
  Book,
  Coffee,
  Shirt,
  Scissors
} from 'lucide-react'
import type { LucideIcon } from 'lucide-react'

// 产品选项类型定义
export interface ProductOption {
  id: string
  value: string
  label: string
  icon: LucideIcon
  iconName: string
  createdAt: string
}

// 可用图标映射
export const AVAILABLE_ICONS: Record<string, LucideIcon> = {
  'Package': Package,
  'ShoppingBag': ShoppingBag,
  'Briefcase': Briefcase,
  'Heart': Heart,
  'Car': Car,
  'Home': Home,
  'Smartphone': Smartphone,
  'Laptop': Laptop,
  'Watch': Watch,
  'Camera': Camera,
  'Headphones': Headphones,
  'Gamepad2': Gamepad2,
  'Book': Book,
  'Coffee': Coffee,
  'Shirt': Shirt,
  'Scissors': Scissors,
}

// 生成唯一ID
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 从localStorage加载数据
const loadFromStorage = (storageKey: string, defaultOptions: ProductOption[]): ProductOption[] => {
  try {
    const stored = localStorage.getItem(storageKey)
    if (stored) {
      const parsed = JSON.parse(stored)
      // 恢复图标引用
      return parsed.map((option: any) => ({
        ...option,
        icon: AVAILABLE_ICONS[option.iconName] || Package
      }))
    }
  } catch (error) {
    console.error(`加载产品选项失败 (${storageKey}):`, error)
  }
  return defaultOptions
}

// 保存到localStorage
const saveToStorage = (storageKey: string, options: ProductOption[]): void => {
  try {
    // 保存时移除图标引用，只保存图标名称
    const toSave = options.map(({ icon, ...option }) => option)
    localStorage.setItem(storageKey, JSON.stringify(toSave))
  } catch (error) {
    console.error(`保存产品选项失败 (${storageKey}):`, error)
  }
}

export interface UseProductOptionsBaseReturn {
  productOptions: ProductOption[]
  addProductOption: (label: string, iconName: string) => Promise<{ success: boolean; error?: string; value?: string }>
  removeProductOption: (id: string) => Promise<{ success: boolean; error?: string }>
  updateProductOption: (id: string, updates: Partial<Pick<ProductOption, 'label' | 'iconName'>>) => Promise<{ success: boolean; error?: string }>
  isLoading: boolean
  error: string | null
}

export interface UseProductOptionsBaseConfig {
  storageKey: string
  defaultOptions: ProductOption[]
}

export const useProductOptionsBase = (config: UseProductOptionsBaseConfig): UseProductOptionsBaseReturn => {
  const { storageKey, defaultOptions } = config
  const [productOptions, setProductOptions] = useState<ProductOption[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 初始化加载数据
  useEffect(() => {
    try {
      const options = loadFromStorage(storageKey, defaultOptions)
      setProductOptions(options)
      setError(null)
    } catch (err) {
      setError('加载产品选项失败')
      console.error(`初始化产品选项失败 (${storageKey}):`, err)
    } finally {
      setIsLoading(false)
    }
  }, [storageKey, defaultOptions])

  // 添加产品选项
  const addProductOption = useCallback(async (label: string, iconName: string): Promise<{ success: boolean; error?: string; value?: string }> => {
    try {
      // 验证输入
      if (!label.trim()) {
        return { success: false, error: '产品名称不能为空' }
      }

      if (!AVAILABLE_ICONS[iconName]) {
        return { success: false, error: '无效的图标选择' }
      }

      // 检查是否重复
      const exists = productOptions.some(option =>
        option.label.toLowerCase() === label.trim().toLowerCase()
      )

      if (exists) {
        return { success: false, error: '产品名称已存在' }
      }

      // 生成value（基于label，支持中文）
      const value = label.trim().toLowerCase().replace(/\s+/g, '-')

      const newOption: ProductOption = {
        id: generateId(),
        value,
        label: label.trim(),
        icon: AVAILABLE_ICONS[iconName],
        iconName,
        createdAt: new Date().toISOString()
      }

      const updatedOptions = [...productOptions, newOption]
      setProductOptions(updatedOptions)
      saveToStorage(storageKey, updatedOptions)

      return { success: true, value }
    } catch (err) {
      const errorMessage = '添加产品选项失败'
      setError(errorMessage)
      console.error(`添加产品选项失败 (${storageKey}):`, err)
      return { success: false, error: errorMessage }
    }
  }, [productOptions, storageKey])

  // 删除产品选项
  const removeProductOption = useCallback(async (id: string): Promise<{ success: boolean; error?: string }> => {
    try {
      const optionToRemove = productOptions.find(option => option.id === id)
      if (!optionToRemove) {
        return { success: false, error: '产品选项不存在' }
      }

      const updatedOptions = productOptions.filter(option => option.id !== id)
      setProductOptions(updatedOptions)
      saveToStorage(storageKey, updatedOptions)
      
      return { success: true }
    } catch (err) {
      const errorMessage = '删除产品选项失败'
      setError(errorMessage)
      console.error(`删除产品选项失败 (${storageKey}):`, err)
      return { success: false, error: errorMessage }
    }
  }, [productOptions, storageKey])

  // 更新产品选项
  const updateProductOption = useCallback(async (
    id: string, 
    updates: Partial<Pick<ProductOption, 'label' | 'iconName'>>
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const optionIndex = productOptions.findIndex(option => option.id === id)
      if (optionIndex === -1) {
        return { success: false, error: '产品选项不存在' }
      }

      // 验证更新数据
      if (updates.label !== undefined && !updates.label.trim()) {
        return { success: false, error: '产品名称不能为空' }
      }

      if (updates.iconName && !AVAILABLE_ICONS[updates.iconName]) {
        return { success: false, error: '无效的图标选择' }
      }

      // 检查名称重复（排除当前项）
      if (updates.label) {
        const exists = productOptions.some((option, index) => 
          index !== optionIndex && 
          option.label.toLowerCase() === updates.label!.trim().toLowerCase()
        )
        
        if (exists) {
          return { success: false, error: '产品名称已存在' }
        }
      }

      const updatedOptions = [...productOptions]
      const currentOption = updatedOptions[optionIndex]
      
      // 应用更新
      if (updates.label) {
        currentOption.label = updates.label.trim()
        currentOption.value = updates.label.trim().toLowerCase().replace(/\s+/g, '-')
      }
      
      if (updates.iconName) {
        currentOption.iconName = updates.iconName
        currentOption.icon = AVAILABLE_ICONS[updates.iconName]
      }

      setProductOptions(updatedOptions)
      saveToStorage(storageKey, updatedOptions)
      
      return { success: true }
    } catch (err) {
      const errorMessage = '更新产品选项失败'
      setError(errorMessage)
      console.error(`更新产品选项失败 (${storageKey}):`, err)
      return { success: false, error: errorMessage }
    }
  }, [productOptions, storageKey])

  return {
    productOptions,
    addProductOption,
    removeProductOption,
    updateProductOption,
    isLoading,
    error
  }
}
