/**
 * 懒加载组件
 * 提供性能优化的组件懒加载和代码分割
 */
import React, { Suspense, lazy } from 'react';
import { LoadingState } from './ErrorBoundary';

// 懒加载知识库组件
export const LazyDocumentUpload = lazy(() =>
  import('./DocumentUpload')
);

export const LazySmartSearch = lazy(() =>
  import('./SmartSearch')
);

export const LazyDocumentList = lazy(() =>
  import('./DocumentList')
);

export const LazyChatInterface = lazy(() =>
  import('./ChatInterface').then(module => ({ default: module.default }))
);

export const LazyKnowledgeGraph = lazy(() =>
  import('./KnowledgeGraph')
);

export const LazyMorphikConnectionTest = lazy(() =>
  import('./MorphikConnectionTest')
);

export const LazyGraphManager = lazy(() =>
  import('./GraphManager').then(module => ({ default: module.GraphManager }))
);

// 带加载状态的懒加载包装器
interface LazyWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  errorMessage?: string;
}

export function LazyWrapper({
  children,
  fallback,
  errorMessage = '组件加载失败'
}: LazyWrapperProps) {
  const defaultFallback = (
    <LoadingState
      message="组件加载中..."
      size="md"
      className="min-h-[200px]"
    />
  );

  return (
    <Suspense fallback={fallback || defaultFallback}>
      {children}
    </Suspense>
  );
}

// 预制的懒加载组件包装器
export function LazyDocumentUploadWrapper(props: any) {
  return (
    <LazyWrapper>
      <LazyDocumentUpload {...props} />
    </LazyWrapper>
  );
}

export function LazySmartSearchWrapper(props: any) {
  return (
    <LazyWrapper>
      <LazySmartSearch {...props} />
    </LazyWrapper>
  );
}

export function LazyDocumentListWrapper(props: any) {
  return (
    <LazyWrapper>
      <LazyDocumentList {...props} />
    </LazyWrapper>
  );
}

export function LazyChatInterfaceWrapper(props: any) {
  return (
    <LazyWrapper>
      <LazyChatInterface {...props} />
    </LazyWrapper>
  );
}

export function LazyKnowledgeGraphWrapper(props: any) {
  return (
    <LazyWrapper>
      <LazyKnowledgeGraph {...props} />
    </LazyWrapper>
  );
}

export function LazyMorphikConnectionTestWrapper(props: any) {
  return (
    <LazyWrapper>
      <LazyMorphikConnectionTest {...props} />
    </LazyWrapper>
  );
}

export function LazyGraphManagerWrapper(props: any) {
  return (
    <LazyWrapper>
      <LazyGraphManager {...props} />
    </LazyWrapper>
  );
}

// 组件预加载函数
export const preloadComponents = {
  documentUpload: () => import('./DocumentUpload'),
  smartSearch: () => import('./SmartSearch'),
  documentList: () => import('./DocumentList'),
  chatInterface: () => import('./ChatInterface'),
  knowledgeGraph: () => import('./KnowledgeGraph'),
  morphikConnectionTest: () => import('./MorphikConnectionTest'),
  graphManager: () => import('./GraphManager'),
};

// 预加载所有组件
export function preloadAllComponents() {
  Object.values(preloadComponents).forEach(preload => {
    preload().catch(error => {
      // console.warn('Failed to preload component:', error);
    });
  });
}

// 根据路由预加载相关组件
export function preloadComponentsByTab(tab: string) {
  switch (tab) {
    case 'documents':
      preloadComponents.documentUpload();
      preloadComponents.documentList();
      break;
    case 'search':
      preloadComponents.smartSearch();
      break;
    case 'chat':
      preloadComponents.chatInterface();
      break;
    case 'graph':
      preloadComponents.knowledgeGraph();
      preloadComponents.graphManager();
      break;
    case 'system':
      preloadComponents.morphikConnectionTest();
      break;
    default:
      // 默认预加载文档管理组件
      preloadComponents.documentUpload();
      preloadComponents.documentList();
      break;
  }
}

export default LazyWrapper;
