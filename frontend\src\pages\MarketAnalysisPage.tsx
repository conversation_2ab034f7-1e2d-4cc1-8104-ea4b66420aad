import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

import { Search, BarChart3, Globe, TrendingUp, Users, Package, AlertCircle, RefreshCw, Trash2, Edit, Plus, History, X } from 'lucide-react'
import {
  smolagentsService,
  type StrategyGenerationRequest,
  type AnalysisExecutionRequest,
  type SearchStrategy,
  type MarketDataSource
} from '@/services/smolagentsService'
import { useMarketAnalysisProductOptions } from '@/hooks/useMarketAnalysisProductOptions'
import { useMarketAnalysisHistory } from '@/hooks/useMarketAnalysisHistory'
import { MarketAnalysisRecord } from '@/types/marketAnalysis'
import { ProductSelector } from '@/components/ProductSelector'
import AddProductDialog from '@/components/AddProductDialog'
import AnalysisHistoryList from '@/components/market-analysis/AnalysisHistoryList'
import { toast } from 'sonner'

// 简化的动态进度指示器组件
function DynamicProgressIndicator({ message }: { message: string }) {
  return (
    <div className="flex items-center space-x-3">
      {/* 简单的旋转指示器 */}
      <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>

      {/* 消息文本 - 去除明暗变化，调小字体 */}
      <span className="text-blue-700 dark:text-blue-300 font-medium text-base">
        {message}
      </span>

      {/* 简单的跳动点 */}
      {/* <div className="flex space-x-1">
        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '200ms'}}></div>
        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '400ms'}}></div>
      </div> */}
    </div>
  )
}

// 策略编辑表单组件
function StrategyEditForm({
  strategy,
  onSave,
  onCancel
}: {
  strategy: SearchStrategy
  onSave: (strategy: SearchStrategy) => void
  onCancel: () => void
}) {
  const [editedStrategy, setEditedStrategy] = useState<SearchStrategy>(strategy)

  const handleSave = () => {
    if (!editedStrategy.query.trim() || !editedStrategy.category.trim() || !editedStrategy.description.trim()) {
      toast.error('请填写完整的策略信息')
      return
    }
    onSave(editedStrategy)
  }

  return (
    <div className="space-y-3">
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          策略分类
        </label>
        <Input
          value={editedStrategy.category}
          onChange={(e) => setEditedStrategy(prev => ({ ...prev, category: e.target.value }))}
          className="text-sm"
        />
      </div>
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          搜索查询
        </label>
        <Input
          value={editedStrategy.query}
          onChange={(e) => setEditedStrategy(prev => ({ ...prev, query: e.target.value }))}
          className="text-sm font-mono"
        />
      </div>
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          策略描述
        </label>
        <Input
          value={editedStrategy.description}
          onChange={(e) => setEditedStrategy(prev => ({ ...prev, description: e.target.value }))}
          className="text-sm"
        />
      </div>
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          优先级
        </label>
        <Select
          value={editedStrategy.priority.toString()}
          onValueChange={(value) => setEditedStrategy(prev => ({ ...prev, priority: parseInt(value) }))}
        >
          <SelectTrigger className="text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">优先级 1 (最高)</SelectItem>
            <SelectItem value="2">优先级 2 (中等)</SelectItem>
            <SelectItem value="3">优先级 3 (较低)</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex space-x-2 pt-2">
        <Button onClick={handleSave} size="sm" className="flex items-center space-x-1">
          <span>保存</span>
        </Button>
        <Button onClick={onCancel} variant="outline" size="sm">
          取消
        </Button>
      </div>
    </div>
  )
}

export default function MarketAnalysisPage() {
  const [selectedProduct, setSelectedProduct] = useState('')
  const [searchResults, setSearchResults] = useState('')

  const [progressMessage, setProgressMessage] = useState('')
  const [currentProgressStep, setCurrentProgressStep] = useState(0)
  const [progressSteps, setProgressSteps] = useState<string[]>([])
  const [progressTimer, setProgressTimer] = useState<number | null>(null)
  const [serviceAvailable, setServiceAvailable] = useState<boolean | null>(null)
  const [error, setError] = useState('')
  const [dataSources, setDataSources] = useState<MarketDataSource[]>([])

  // 新增：两步式流程状态
  const [currentStep, setCurrentStep] = useState<'input' | 'strategies' | 'analysis'>('input')
  const [generatedStrategies, setGeneratedStrategies] = useState<SearchStrategy[]>([])
  const [isGeneratingStrategies, setIsGeneratingStrategies] = useState(false)
  const [isExecutingAnalysis, setIsExecutingAnalysis] = useState(false)
  const [strategyCount, setStrategyCount] = useState<4 | 7>(4)

  // 任务管理状态
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null)
  const [isCancelling, setIsCancelling] = useState(false)

  // 策略数量限制常量
  const MAX_STRATEGIES = 8

  // 策略编辑相关状态
  const [editingStrategy, setEditingStrategy] = useState<SearchStrategy | null>(null)
  const [showAddStrategyDialog, setShowAddStrategyDialog] = useState(false)
  const [newStrategy, setNewStrategy] = useState<Partial<SearchStrategy>>({
    query: '',
    category: '',
    priority: 2,
    description: ''
  })

  // 使用全球市场分析专用的产品选项Hook
  const { productOptions, isLoading: optionsLoading, removeProductOption, addProductOption } = useMarketAnalysisProductOptions()

  // 删除确认对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [productToDelete, setProductToDelete] = useState<{ id: string; label: string } | null>(null)

  // 添加产品对话框状态
  const [addDialogOpen, setAddDialogOpen] = useState(false)

  // 分析相关状态
  const [analysisStartTime, setAnalysisStartTime] = useState<Date | null>(null)

  // 历史记录对话框状态
  const [showHistoryDialog, setShowHistoryDialog] = useState(false)

  // 使用历史记录Hook
  const historyHook = useMarketAnalysisHistory()

  // 临时：添加测试数据（仅用于调试）
  useEffect(() => {
    // 检查localStorage中是否已有历史记录
    const existingRecords = localStorage.getItem('market_analysis_history');
    if (!existingRecords || JSON.parse(existingRecords).length === 0) {
      // 添加一些测试数据
      const testRecord: MarketAnalysisRecord = {
        id: `test_${Date.now()}`,
        productType: 'smartphone',
        productLabel: '智能手机',
        searchStrategies: [
          {
            id: '1',
            query: '全球智能手机市场规模',
            category: '市场规模',
            priority: 1,
            description: '分析全球智能手机市场的整体规模和增长趋势'
          },
          {
            id: '2',
            query: '智能手机品牌竞争格局',
            category: '竞争分析',
            priority: 2,
            description: '研究主要智能手机品牌的市场份额和竞争态势'
          }
        ],
        analysisResult: '# 智能手机市场分析报告\n\n## 市场规模\n全球智能手机市场预计在2024年达到5000亿美元...\n\n## 竞争格局\n苹果、三星、小米等品牌占据主要市场份额...',
        dataSources: [
          {
            title: 'Statista - Global Smartphone Market',
            url: 'https://www.statista.com/outlook/tmo/consumer-electronics/smartphones/worldwide',
            content: '全球智能手机市场数据统计',
            dataType: 'market_data'
          }
        ],
        timestamp: new Date().toISOString(),
        status: 'success',
        strategyCount: 2,
        analysisType: 'market_analysis',
        duration: 45
      };

      historyHook.saveRecord(testRecord);

      // 添加第二条测试记录
      setTimeout(() => {
        const testRecord2: MarketAnalysisRecord = {
          id: `test_${Date.now() + 1}`,
          productType: 'laptop',
          productLabel: '笔记本电脑',
          searchStrategies: [
            {
              id: '3',
              query: '笔记本电脑市场趋势',
              category: '市场趋势',
              priority: 1,
              description: '分析笔记本电脑市场的发展趋势和消费者偏好'
            }
          ],
          analysisResult: '# 笔记本电脑市场分析\n\n## 市场趋势\n轻薄本和游戏本成为主流...',
          dataSources: [
            {
              title: 'IDC - PC Market Report',
              url: 'https://www.idc.com/pc-market',
              content: 'PC市场报告数据',
              dataType: 'market_report'
            }
          ],
          timestamp: new Date(Date.now() - 86400000).toISOString(), // 1天前
          status: 'success',
          strategyCount: 1,
          analysisType: 'market_analysis',
          duration: 32
        };
        historyHook.saveRecord(testRecord2);
      }, 100);
    }
  }, [])

  // 检查服务可用性
  useEffect(() => {
    const checkService = async () => {
      const healthResult = await smolagentsService.healthCheck()
      const available = !healthResult.error
      setServiceAvailable(available)
    }
    checkService()
  }, [])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (progressTimer) {
        clearInterval(progressTimer)
      }
    }
  }, [progressTimer])

  // 处理删除产品选项
  const handleDeleteProduct = (productId: string, productLabel: string) => {
    // 防止删除最后一个产品选项
    if (productOptions.length <= 1) {
      toast.error('至少需要保留一个产品选项')
      return
    }

    setProductToDelete({ id: productId, label: productLabel })
    setDeleteDialogOpen(true)
  }

  // 确认删除产品选项
  const confirmDeleteProduct = async () => {
    if (!productToDelete) return

    const result = await removeProductOption(productToDelete.id)
    if (result.success) {
      // 如果删除的是当前选中的产品，清空选择
      if (selectedProduct === productOptions.find(p => p.id === productToDelete.id)?.value) {
        setSelectedProduct('')
      }
      toast.success(`产品选项 "${productToDelete.label}" 已成功删除`)
    } else {
      setError(result.error || '删除产品选项失败')
      toast.error(result.error || '删除产品选项失败')
    }

    setDeleteDialogOpen(false)
    setProductToDelete(null)
  }

  // 取消删除
  const cancelDeleteProduct = () => {
    setDeleteDialogOpen(false)
    setProductToDelete(null)
  }

  // 处理添加产品
  const handleAddProduct = () => {
    setAddDialogOpen(true)
  }

  // 处理产品添加成功后自动选择
  const handleProductAdded = (productValue: string) => {
    setSelectedProduct(productValue)
  }

  // 第一步：生成搜索策略
  const handleGenerateStrategies = async () => {
    if (!selectedProduct) {
      setError('请选择产品类型')
      return
    }

    if (serviceAvailable === false) {
      setError('AI服务不可用，请确保Ollama服务正在运行并且模型已安装')
      return
    }

    setIsGeneratingStrategies(true)
    setError('')
    setAnalysisStartTime(new Date())

    // 生成任务ID
    const taskId = `strategy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    setCurrentTaskId(taskId)

    // 启动策略生成的真实进度步骤
    const strategySteps = [
      '正在初始化AI分析引擎...',
      '正在分析产品特征和市场定位...',
      '正在生成多维度搜索策略...',
      '正在优化策略查询关键词...',
      '正在验证策略完整性和有效性...',
      '正在分析数据并生成报告...'
    ]
    startProgressSteps(strategySteps, false)

    try {
      const selectedProductLabel = productOptions.find(p => p.value === selectedProduct)?.label || selectedProduct

      // 基于产品类型生成全球市场分析查询
      const globalMarketQuery = `${selectedProductLabel} 全球市场分析`

      const request: StrategyGenerationRequest = {
        product: selectedProductLabel,
        query: globalMarketQuery,
        analysisType: 'market_analysis',
        strategyCount: strategyCount,
        language: 'zh-CN'  // 明确指定中文输出
      }

      const result = await smolagentsService.generateSearchStrategies(request)

      if (result.success && result.strategies) {
        setGeneratedStrategies(result.strategies)
        setCurrentStep('strategies')
        stopProgressSteps() // 停止进度步骤
        toast.success(`成功生成 ${result.strategies.length} 个搜索策略`)
      } else {
        setError(result.error || '策略生成失败，请稍后重试')
        stopProgressSteps() // 停止进度步骤
      }
    } catch (error) {
      console.error('策略生成失败:', error)
      setError('策略生成过程中发生错误，请检查AI服务状态或稍后重试')
      stopProgressSteps() // 停止进度步骤
    } finally {
      setIsGeneratingStrategies(false)
      setCurrentTaskId(null)
    }
  }

  // 第二步：执行市场分析
  const handleExecuteAnalysis = async () => {
    // 验证策略完整性
    if (!validateStrategies()) {
      return
    }

    setIsExecutingAnalysis(true)
    setError('')
    setSearchResults('')

    // 生成任务ID
    const taskId = `analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    setCurrentTaskId(taskId)

    // 启动分析执行的真实进度步骤
    const analysisSteps = [
      '正在初始化市场分析引擎...',
      '正在执行多维度数据搜索...',
      '正在收集全球市场数据...',
      '正在分析竞争对手信息...',
      '正在处理价格趋势数据...',
      '正在整合消费者洞察...',
      '正在生成综合分析报告...'
    ]
    startProgressSteps(analysisSteps, true)

    try {
      const selectedProductLabel = productOptions.find(p => p.value === selectedProduct)?.label || selectedProduct

      // 基于产品类型生成全球市场分析查询
      const globalMarketQuery = `${selectedProductLabel} 全球市场分析`

      const request: AnalysisExecutionRequest = {
        product: selectedProductLabel,
        query: globalMarketQuery,
        strategies: generatedStrategies,
        analysisType: 'market_analysis'
      }

      const result = await smolagentsService.executeMarketAnalysis(
        request,
        (progress) => {
          // 保持后端的进度消息，但不覆盖我们的步骤进度
          console.log('后端进度:', progress)
        }
      )

      if (result.success && result.data) {
        setSearchResults(result.data)
        setDataSources(result.dataSources || [])
        setCurrentStep('analysis')
        stopProgressSteps() // 停止进度步骤

        // 保存成功的分析记录 - 优先使用后端返回的execution_time
        const backendDuration = result.execution_time ? Math.floor(result.execution_time) : undefined;
        const frontendDuration = analysisStartTime ? Math.floor((new Date().getTime() - analysisStartTime.getTime()) / 1000) : undefined;
        const duration = backendDuration || frontendDuration;
        const analysisRecord: MarketAnalysisRecord = {
          id: `analysis_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          productType: selectedProduct,
          productLabel: selectedProductLabel,
          searchStrategies: generatedStrategies,
          analysisResult: result.data,
          dataSources: result.dataSources || [],
          timestamp: new Date().toISOString(),
          status: 'success',
          strategyCount: generatedStrategies.length,
          analysisType: 'market_analysis',
          duration
        };

        historyHook.saveRecord(analysisRecord);
        toast.success('市场分析完成并已保存到历史记录')
      } else {
        const errorMessage = result.error || '分析执行失败，请稍后重试';
        setError(errorMessage);
        stopProgressSteps(); // 停止进度步骤

        // 保存失败的分析记录 - 优先使用后端返回的execution_time
        const backendDuration = result.execution_time ? Math.floor(result.execution_time) : undefined;
        const frontendDuration = analysisStartTime ? Math.floor((new Date().getTime() - analysisStartTime.getTime()) / 1000) : undefined;
        const duration = backendDuration || frontendDuration;
        const analysisRecord: MarketAnalysisRecord = {
          id: `analysis_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          productType: selectedProduct,
          productLabel: selectedProductLabel,
          searchStrategies: generatedStrategies,
          analysisResult: '',
          dataSources: [],
          timestamp: new Date().toISOString(),
          status: 'failed',
          strategyCount: generatedStrategies.length,
          analysisType: 'market_analysis',
          duration,
          errorMessage
        };

        historyHook.saveRecord(analysisRecord);
      }
    } catch (error) {
      console.error('市场分析执行失败:', error)
      setError('分析执行过程中发生错误，请检查AI服务状态或稍后重试')
      stopProgressSteps() // 停止进度步骤
    } finally {
      setIsExecutingAnalysis(false)
      setCurrentTaskId(null)
    }
  }

  // 新增：一键执行完整分析（策略生成+分析执行）
  const handleExecuteFullAnalysis = async () => {
    if (!selectedProduct) {
      setError('请选择产品类型')
      return
    }

    if (serviceAvailable === false) {
      setError('AI服务不可用，请确保Ollama服务正在运行并且模型已安装')
      return
    }

    setIsExecutingAnalysis(true)
    setError('')
    setSearchResults('')
    setAnalysisStartTime(new Date())

    // 生成任务ID
    const taskId = `full_analysis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    setCurrentTaskId(taskId)

    // 启动一键完整分析的真实进度步骤
    const fullAnalysisSteps = [
      '正在初始化AI分析系统...',
      '正在分析产品特征和市场定位...',
      '正在生成智能搜索策略...',
      '正在执行全球数据搜索...',
      '正在收集竞争对手信息...',
      '正在分析价格趋势和市场规模...',
      '正在整合消费者洞察数据...',
      '正在生成综合市场分析报告...'
    ]
    startProgressSteps(fullAnalysisSteps, true)

    try {
      const selectedProductLabel = productOptions.find(p => p.value === selectedProduct)?.label || selectedProduct
      const globalMarketQuery = `${selectedProductLabel} 全球市场分析`

      // 使用统一分析方法，不传入策略，让后端自动生成
      const result = await smolagentsService.executeUnifiedMarketAnalysis(
        selectedProductLabel,
        globalMarketQuery,
        undefined, // 不传入策略，让后端自动生成
        strategyCount, // 传递策略数量
        (progress) => {
          // 保持后端的进度消息，但不覆盖我们的步骤进度
          console.log('后端进度:', progress)
        }
      )

      if (result.success && result.data) {
        setSearchResults(result.data)
        setDataSources(result.dataSources || [])
        setGeneratedStrategies(result.generatedStrategies || []) // 设置生成的策略到状态中
        setCurrentStep('analysis')
        stopProgressSteps() // 停止进度步骤

        // 保存到历史记录 - 优先使用后端返回的execution_time
        const backendDuration = result.execution_time ? Math.floor(result.execution_time) : undefined;
        const frontendDuration = analysisStartTime ? Math.floor((new Date().getTime() - analysisStartTime.getTime()) / 1000) : undefined;
        const duration = backendDuration || frontendDuration;
        const analysisRecord: MarketAnalysisRecord = {
          id: `analysis_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          productType: selectedProduct,
          productLabel: selectedProductLabel,
          searchStrategies: result.generatedStrategies || [], // 使用后端生成的策略
          analysisResult: result.data,
          dataSources: result.dataSources || [],
          timestamp: new Date().toISOString(),
          status: 'success',
          strategyCount: result.generatedStrategies?.length || strategyCount,
          analysisType: 'market_analysis',
          duration
        }

        historyHook.saveRecord(analysisRecord)
        toast.success('全面分析完成并已保存到历史记录！')
      } else {
        const errorMessage = result.error || '全面分析失败，请稍后重试';
        setError(errorMessage);
        stopProgressSteps(); // 停止进度步骤

        // 保存失败的分析记录 - 优先使用后端返回的execution_time
        const backendDuration = result.execution_time ? Math.floor(result.execution_time) : undefined;
        const frontendDuration = analysisStartTime ? Math.floor((new Date().getTime() - analysisStartTime.getTime()) / 1000) : undefined;
        const duration = backendDuration || frontendDuration;
        const analysisRecord: MarketAnalysisRecord = {
          id: `analysis_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          productType: selectedProduct,
          productLabel: selectedProductLabel,
          searchStrategies: [], // 失败时没有策略信息
          analysisResult: '',
          dataSources: [],
          timestamp: new Date().toISOString(),
          status: 'failed',
          strategyCount: strategyCount,
          analysisType: 'market_analysis',
          duration,
          errorMessage
        };

        historyHook.saveRecord(analysisRecord);
      }
    } catch (error) {
      console.error('全面分析失败:', error)
      setError('全面分析过程中发生错误，请检查AI服务状态或稍后重试')
      stopProgressSteps() // 停止进度步骤
    } finally {
      setIsExecutingAnalysis(false)
      setCurrentTaskId(null)
    }
  }

  // 启动真实进度步骤模拟
  const startProgressSteps = (steps: string[], isAnalysis: boolean = false) => {
    setProgressSteps(steps)
    setCurrentProgressStep(0)
    setProgressMessage(steps[0])

    // 清除之前的定时器
    if (progressTimer) {
      clearInterval(progressTimer)
    }

    // 根据任务类型设置不同的步骤间隔
    const stepInterval = isAnalysis ? 8000 : 5000 // 分析任务步骤间隔更长

    const timer = window.setInterval(() => {
      setCurrentProgressStep(prev => {
        const nextStep = prev + 1
        if (nextStep < steps.length) {
          setProgressMessage(steps[nextStep])
          return nextStep
        } else {
          // 到达最后一步，保持显示
          return prev
        }
      })
    }, stepInterval)

    setProgressTimer(timer)
  }

  // 停止进度步骤
  const stopProgressSteps = () => {
    if (progressTimer) {
      clearInterval(progressTimer)
      setProgressTimer(null)
    }
    setProgressSteps([])
    setCurrentProgressStep(0)
    setProgressMessage('')
  }

  // 取消当前任务
  const handleCancelTask = async () => {
    if (!currentTaskId) {
      return
    }

    setIsCancelling(true)
    try {
      const result = await smolagentsService.cancelTask(currentTaskId)
      if (result.success) {
        setIsGeneratingStrategies(false)
        setIsExecutingAnalysis(false)
        stopProgressSteps() // 停止进度步骤
        setCurrentTaskId(null)
        setError('任务已取消')
        toast.success('任务已成功取消')
      } else {
        setError(result.error || '取消任务失败')
        toast.error('取消任务失败')
      }
    } catch (error) {
      console.error('取消任务失败:', error)
      setError('取消任务失败')
      toast.error('取消任务失败')
    } finally {
      setIsCancelling(false)
    }
  }

  // 重置流程
  const handleReset = () => {
    setCurrentStep('input')
    setGeneratedStrategies([])
    setSearchResults('')
    setDataSources([])
    setError('')
    setProgressMessage('')
    setEditingStrategy(null)
    setShowAddStrategyDialog(false)
    setAnalysisStartTime(null)
    setCurrentTaskId(null)
    setIsCancelling(false)
  }

  // 策略编辑功能
  const handleEditStrategy = (strategy: SearchStrategy) => {
    setEditingStrategy(strategy)
  }

  const handleSaveStrategy = (updatedStrategy: SearchStrategy) => {
    setGeneratedStrategies(prev =>
      prev.map(s => s.id === updatedStrategy.id ? updatedStrategy : s)
    )
    setEditingStrategy(null)
    toast.success('策略已更新')
  }

  const handleDeleteStrategy = (strategyId: string) => {
    setGeneratedStrategies(prev => prev.filter(s => s.id !== strategyId))
    toast.success('策略已删除')
  }

  const handleAddStrategy = () => {
    // 检查策略数量限制
    if (generatedStrategies.length >= MAX_STRATEGIES) {
      toast.error(`最多只能添加${MAX_STRATEGIES}个策略`)
      return
    }

    if (!newStrategy.query || !newStrategy.category || !newStrategy.description) {
      toast.error('请填写完整的策略信息')
      return
    }

    // 检查是否有重复的策略分类
    const existingCategories = generatedStrategies.map(s => s.category.toLowerCase())
    if (existingCategories.includes(newStrategy.category!.toLowerCase())) {
      toast.error('该策略分类已存在，请使用不同的分类名称')
      return
    }

    const strategy: SearchStrategy = {
      id: `custom_${Date.now()}`,
      query: newStrategy.query!,
      category: newStrategy.category!,
      priority: newStrategy.priority || 2,
      description: newStrategy.description!
    }

    setGeneratedStrategies(prev => [...prev, strategy])
    setNewStrategy({
      query: '',
      category: '',
      priority: 2,
      description: ''
    })
    setShowAddStrategyDialog(false)
    toast.success('策略已添加')
  }

  // 验证策略完整性
  const validateStrategies = (): boolean => {
    if (generatedStrategies.length === 0) {
      toast.error('至少需要一个搜索策略才能执行分析')
      return false
    }

    const incompleteStrategies = generatedStrategies.filter(
      s => !s.query.trim() || !s.category.trim() || !s.description.trim()
    )

    if (incompleteStrategies.length > 0) {
      toast.error('存在不完整的策略，请检查所有策略的信息是否完整')
      return false
    }

    return true
  }



  // 重新检查服务状态
  const handleRefreshService = async () => {
    setServiceAvailable(null)
    const healthResult = await smolagentsService.healthCheck()
    const available = !healthResult.error
    setServiceAvailable(available)
  }

  // 测试AI连接
  const handleTestConnection = async () => {
    setError('')
    setProgressMessage('正在测试AI连接...')

    try {
      const result = await smolagentsService.healthCheck()
      if (!result.error) {
        setProgressMessage('')
        alert(`连接测试成功！\n服务: ${result.service}\n模型: ${result.model}`)
      } else {
        setError(`连接测试失败: ${result.error}`)
        setProgressMessage('')
      }
    } catch (error) {
      setError(`测试过程中发生错误: ${error instanceof Error ? error.message : '未知错误'}`)
      setProgressMessage('')
    }
  }

  return (
    <div className="flex-1 p-8 pt-6 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* 页面头部 - 标题居中，历史记录按钮在描述右侧 */}
        <div className="w-full">
          {/* 居中的标题区域 */}
          <div className="text-center w-full">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">全球市场分析</h1>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
                <Globe className="w-6 h-6 text-white" />
              </div>
            </div>

            {/* 描述文字居中，历史记录按钮在右侧 */}
            <div className="relative flex items-center justify-center mb-6">
              <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl text-center">
                基于真实网络数据源进行AI驱动的市场分析，包括竞争态势、价格趋势、市场规模等
              </p>

              {/* 历史记录按钮 - 绝对定位到右侧，向左偏移 */}
              <div className="absolute right-6 top-1/2 transform -translate-y-1/2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowHistoryDialog(true)}
                  className="group relative flex items-center space-x-3 bg-white/95 dark:bg-gray-800/95 hover:bg-blue-50 dark:hover:bg-blue-900/30 border border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600 shadow-md hover:shadow-lg transition-all duration-200 rounded-full px-6 py-2 min-w-[140px] h-9"
                >
                  <div className="w-5 h-5 bg-gradient-to-r from-purple-500 to-violet-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                    <History className="w-3 h-3 text-white" />
                  </div>
                  <div className="flex items-center space-x-2 flex-1">
                    <span className="text-sm font-medium text-blue-700 dark:text-blue-300 group-hover:text-blue-800 dark:group-hover:text-blue-200 whitespace-nowrap">
                      分析历史
                    </span>
                    <span className="text-xs text-blue-500 dark:text-blue-400 group-hover:text-blue-600 dark:group-hover:text-blue-300 font-medium whitespace-nowrap">
                      {historyHook.stats.totalAnalyses > 0 ? `${historyHook.stats.totalAnalyses} 条` : '查看'}
                    </span>
                  </div>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 主要内容区域 - 市场分析 */}
        <div className="space-y-8">
            {/* AI服务状态详情 */}
            {serviceAvailable === false && (
          <div className="bg-red-50 dark:bg-red-900/20 rounded-2xl p-6 border border-red-200 dark:border-red-800">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <AlertCircle className="w-6 h-6 text-red-500" />
                  <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">AI服务连接失败</h3>
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={handleTestConnection}
                    variant="outline"
                    size="sm"
                    className="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-800"
                  >
                    测试连接
                  </Button>
                  <Button
                    onClick={handleRefreshService}
                    variant="outline"
                    size="sm"
                    className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-300 dark:hover:bg-red-800"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    重新检查
                  </Button>
                </div>
              </div>
              <div className="text-red-700 dark:text-red-300 space-y-2">
                <p>无法连接到本地Ollama服务，请检查以下项目：</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>确保Ollama服务正在运行 (端口: 11434)</li>
                  <li>确保模型 <code className="bg-red-100 dark:bg-red-800 px-2 py-1 rounded">Qwen3-8B-M:latest</code> 已安装</li>
                  <li>检查防火墙设置是否阻止了本地连接</li>
                </ul>
                <div className="mt-4 p-3 bg-red-100 dark:bg-red-800 rounded-lg">
                  <p className="font-medium">安装命令:</p>
                  <code className="block mt-1 text-sm">ollama pull Qwen3-8B-M:latest</code>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 搜索功能区域 */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl border border-gray-200 dark:border-gray-700 backdrop-blur-sm">
          <div className="space-y-8">
            {/* 产品选择器 */}
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center">
                  <Package className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    选择产品类型
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">选择要进行全球市场分析的产品</p>
                </div>
              </div>
              <ProductSelector
                productOptions={productOptions}
                selectedProduct={selectedProduct}
                onProductChange={setSelectedProduct}
                onDeleteProduct={handleDeleteProduct}
                onAddProduct={handleAddProduct}
                isLoading={optionsLoading}
                placeholder="请选择要分析的产品..."
                className="bg-white dark:bg-gray-800"
              />
            </div>

            {/* 策略生成控制区域 */}
            <div className="space-y-4">
              {/* 策略数量选择 */}
              {currentStep === 'input' && (
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center">
                      <Search className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                        策略数量
                      </label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">选择生成的搜索策略数量</p>
                    </div>
                  </div>
                  <Select value={strategyCount.toString()} onValueChange={(value) => setStrategyCount(Number(value) as 4 | 7)}>
                    <SelectTrigger className="w-36 h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="4">4个策略 (推荐)</SelectItem>
                      <SelectItem value="7">7个策略 (详细)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* 操作按钮区域 */}
              <div className="flex justify-center">
                {/* 第一步：生成策略按钮 */}
                {currentStep === 'input' && (
                  <div className="space-y-4">
                    {/* 默认状态按钮区域 */}
                    {!isGeneratingStrategies && !isExecutingAnalysis && (
                      <div className="flex space-x-4 justify-center">
                        <Button
                          onClick={handleGenerateStrategies}
                          disabled={!selectedProduct || serviceAvailable === false}
                          size="lg"
                          variant="outline"
                          className="h-14 px-8 min-w-[180px] border-green-300 text-green-700 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900/20 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-200"
                        >
                          <div className="flex items-center space-x-2">
                            <Search className="w-5 h-5" />
                            <span className="font-medium">
                              {serviceAvailable === false ? 'AI服务不可用' : '生成搜索策略'}
                            </span>
                          </div>
                        </Button>
                        <Button
                          onClick={handleExecuteFullAnalysis}
                          disabled={!selectedProduct || serviceAvailable === false}
                          size="lg"
                          className="h-14 px-8 min-w-[180px] bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-200"
                        >
                          <div className="flex items-center space-x-2">
                            <BarChart3 className="w-5 h-5" />
                            <span className="font-medium">
                              {serviceAvailable === false ? 'AI服务不可用' : '一键完整分析'}
                            </span>
                          </div>
                        </Button>
                      </div>
                    )}
                  </div>
                )}

                {/* 第二步：执行分析按钮 */}
                {currentStep === 'strategies' && !isExecutingAnalysis && (
                  <div className="flex space-x-4">
                    <Button
                      onClick={handleReset}
                      variant="outline"
                      size="lg"
                      className="h-14 px-6"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      重新开始
                    </Button>
                    <Button
                      onClick={handleExecuteAnalysis}
                      size="lg"
                      className="h-14 px-8 min-w-[200px] bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      <div className="flex items-center space-x-2">
                        <BarChart3 className="w-5 h-5" />
                        <span className="font-medium">执行全面分析</span>
                      </div>
                    </Button>
                  </div>
                )}

                {/* 分析完成后的重置按钮 */}
                {currentStep === 'analysis' && (
                  <Button
                    onClick={handleReset}
                    variant="outline"
                    size="lg"
                    className="h-14 px-8 min-w-[200px]"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    重新分析
                  </Button>
                )}
              </div>
            </div>
            {/* 进度显示区域 */}
            {(isGeneratingStrategies || isExecutingAnalysis) && progressMessage && (
              <div className="p-8 bg-gradient-to-br from-blue-50 via-cyan-50 to-indigo-50 dark:from-blue-900/20 dark:via-cyan-900/20 dark:to-indigo-900/20 rounded-2xl border border-blue-200 dark:border-blue-800 shadow-lg backdrop-blur-sm">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                  <div className="flex-1">
                    <DynamicProgressIndicator message={progressMessage} />
                  </div>
                  <div className="flex-shrink-0">
                    <Button
                      onClick={handleCancelTask}
                      disabled={isCancelling}
                      size="sm"
                      variant="outline"
                      className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex items-center space-x-1">
                        {isCancelling ? (
                          <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
                        ) : (
                          <X className="w-4 h-4" />
                        )}
                        <span className="font-medium">
                          {isCancelling ? '取消中...' : '取消任务'}
                        </span>
                      </div>
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* 错误提示区域 */}
            {error && (
              <div className="p-6 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-xl border border-red-200 dark:border-red-800 shadow-sm">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-red-100 dark:bg-red-900/50 rounded-lg flex items-center justify-center flex-shrink-0">
                      <AlertCircle className="w-5 h-5 text-red-500" />
                    </div>
                    <span className="text-red-700 dark:text-red-300 font-medium">{error}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setError('')}
                    className="text-red-500 hover:text-red-700 hover:bg-red-100 dark:hover:bg-red-900/50 h-8 w-8 p-0"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 策略显示区域 */}
        {currentStep === 'strategies' && generatedStrategies.length > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                  <Search className="w-4 h-4 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">智能搜索策略</h2>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                  generatedStrategies.length >= MAX_STRATEGIES
                    ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300'
                    : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                }`}>
                  {generatedStrategies.length}/{MAX_STRATEGIES} 个策略
                </div>
              </div>
              <Button
                onClick={() => {
                  if (generatedStrategies.length >= MAX_STRATEGIES) {
                    toast.error(`最多只能添加${MAX_STRATEGIES}个策略`)
                  } else {
                    setShowAddStrategyDialog(true)
                  }
                }}
                variant="outline"
                size="sm"
                disabled={generatedStrategies.length >= MAX_STRATEGIES}
                className="group relative flex items-center space-x-2 bg-white dark:bg-gray-800 hover:bg-green-50 dark:hover:bg-green-900/10 border border-green-200 dark:border-green-700 hover:border-green-300 dark:hover:border-green-600 shadow-sm hover:shadow-md transition-all duration-200 rounded-lg px-4 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
                title={generatedStrategies.length >= MAX_STRATEGIES ? `最多只能添加${MAX_STRATEGIES}个策略` : '添加新的搜索策略'}
              >
                <div className="w-5 h-5 bg-green-500 rounded-md flex items-center justify-center">
                  <Plus className="w-3 h-3 text-white" />
                </div>
                <span className="font-medium text-gray-700 dark:text-gray-300 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-150">
                  添加策略
                </span>
                {generatedStrategies.length >= MAX_STRATEGIES && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">({generatedStrategies.length}/{MAX_STRATEGIES})</span>
                )}
              </Button>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              {generatedStrategies.map((strategy, index) => (
                <div
                  key={strategy.id}
                  className="p-4 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-700 dark:to-blue-900/20 rounded-lg border border-gray-200 dark:border-gray-600"
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
                        {index + 1}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      {editingStrategy?.id === strategy.id ? (
                        <StrategyEditForm
                          strategy={strategy}
                          onSave={handleSaveStrategy}
                          onCancel={() => setEditingStrategy(null)}
                        />
                      ) : (
                        <>
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                                {strategy.category}
                              </h3>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                strategy.priority === 1
                                  ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
                                  : strategy.priority === 2
                                  ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
                                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                              }`}>
                                优先级 {strategy.priority}
                              </span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Button
                                onClick={() => handleEditStrategy(strategy)}
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                              >
                                <Edit className="w-3 h-3" />
                              </Button>
                              <Button
                                onClick={() => handleDeleteStrategy(strategy.id)}
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                            {strategy.description}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 font-mono bg-white dark:bg-gray-800 p-2 rounded border">
                            {strategy.query}
                          </p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 space-y-4">
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-xs font-bold text-white">✓</span>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-1">
                      策略生成完成！
                    </p>
                    <p className="text-xs text-blue-600 dark:text-blue-400 mb-2">
                      基于AI智能分析，已为您生成 {generatedStrategies.length} 个搜索策略。您可以：
                    </p>
                    <ul className="text-xs text-blue-600 dark:text-blue-400 space-y-1">
                      <li className="flex items-center space-x-1">
                        <Edit className="w-3 h-3" />
                        <span>点击编辑按钮修改策略内容</span>
                      </li>
                      <li className="flex items-center space-x-1">
                        <Trash2 className="w-3 h-3" />
                        <span>点击删除按钮移除不需要的策略</span>
                      </li>
                      <li className="flex items-center space-x-1">
                        <Plus className="w-3 h-3" />
                        <span>点击"添加策略"按钮创建自定义策略（最多{MAX_STRATEGIES}个）</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 执行分析按钮区域 */}
              {/* <div className="flex items-center justify-center">
                {isExecutingAnalysis ? (
                  <div className="flex items-center space-x-3 text-blue-600 dark:text-blue-400">
                    <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-lg font-medium">正在执行分析...</span>
                  </div>
                ) : (
                  <Button
                    onClick={handleExecuteAnalysis}
                    className="h-12 px-8 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-lg"
                    disabled={generatedStrategies.length === 0}
                  >
                    <div className="flex items-center space-x-2">
                      <BarChart3 className="w-5 h-5" />
                      <span className="font-medium">执行全面分析</span>
                      <span className="text-xs opacity-75">({generatedStrategies.length} 个策略)</span>
                    </div>
                  </Button>
                )}
              </div> */}
            </div>
          </div>
        )}

        {/* 添加策略对话框 */}
        <Dialog open={showAddStrategyDialog} onOpenChange={setShowAddStrategyDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>添加新策略</DialogTitle>
              <DialogDescription>
                创建一个新的搜索策略来补充现有的分析维度
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  策略分类
                </label>
                <Input
                  value={newStrategy.category || ''}
                  onChange={(e) => setNewStrategy(prev => ({ ...prev, category: e.target.value }))}
                  placeholder="例如：技术分析、用户反馈等"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  搜索查询
                </label>
                <Input
                  value={newStrategy.query || ''}
                  onChange={(e) => setNewStrategy(prev => ({ ...prev, query: e.target.value }))}
                  placeholder="输入具体的搜索关键词"
                  className="font-mono"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  策略描述
                </label>
                <Input
                  value={newStrategy.description || ''}
                  onChange={(e) => setNewStrategy(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="描述这个策略的目的和预期结果"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  优先级
                </label>
                <Select
                  value={newStrategy.priority?.toString() || '2'}
                  onValueChange={(value) => setNewStrategy(prev => ({ ...prev, priority: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">优先级 1 (最高)</SelectItem>
                    <SelectItem value="2">优先级 2 (中等)</SelectItem>
                    <SelectItem value="3">优先级 3 (较低)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowAddStrategyDialog(false)}>
                取消
              </Button>
              <Button onClick={handleAddStrategy}>
                添加策略
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 结果展示区域 */}
        {searchResults && (
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">AI分析结果</h2>
            </div>
            <div className="prose prose-lg dark:prose-invert max-w-none">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    h1: ({ children }) => <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">{children}</h1>,
                    h2: ({ children }) => <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 mt-6">{children}</h2>,
                    h3: ({ children }) => <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2 mt-4">{children}</h3>,
                    p: ({ children }) => <p className="text-gray-700 dark:text-gray-300 mb-3 leading-relaxed">{children}</p>,
                    ul: ({ children }) => <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 mb-3 space-y-1">{children}</ul>,
                    ol: ({ children }) => <ol className="list-decimal list-inside text-gray-700 dark:text-gray-300 mb-3 space-y-1">{children}</ol>,
                    li: ({ children }) => <li className="ml-2">{children}</li>,
                    strong: ({ children }) => <strong className="font-semibold text-gray-900 dark:text-white">{children}</strong>,
                    em: ({ children }) => <em className="italic text-gray-800 dark:text-gray-200">{children}</em>,
                    blockquote: ({ children }) => <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600 dark:text-gray-400 my-4">{children}</blockquote>,
                    code: ({ children }) => <code className="bg-gray-200 dark:bg-gray-700 px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                    pre: ({ children }) => <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto text-sm font-mono mb-4">{children}</pre>
                  }}
                >
                  {searchResults}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        )}

        {/* 功能说明卡片 */}
        {!searchResults && currentStep === 'input' && (
          <div className="mt-12">
            {/* <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">强大的分析能力</h2>
              <p className="text-gray-600 dark:text-gray-400">基于AI驱动的全球市场分析系统</p>
            </div> */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="group bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-2xl hover:scale-105 transition-all duration-300 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 mx-auto">
                  <BarChart3 className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">真实数据分析</h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  基于真实网络数据源进行深度分析，涵盖市场规模、竞争态势、价格趋势等多维度信息
                </p>
              </div>

              <div className="group bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-2xl hover:scale-105 transition-all duration-300 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 mx-auto">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">多源数据整合</h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  自动搜索并整合多个权威数据源，确保分析结果的准确性和全面性，提供可靠的决策依据
                </p>
              </div>

              <div className="group bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-2xl hover:scale-105 transition-all duration-300 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 mx-auto">
                  <TrendingUp className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">AI智能分析</h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  结合先进的AI分析能力，生成专业的市场分析报告和预测，助力商业决策
                </p>
              </div>
            </div>
          </div>
        )}
        </div>
      </div>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={(open) => open ? null : cancelDeleteProduct()} modal={true}>
        <DialogContent
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle>确认删除产品选项</DialogTitle>
            <DialogDescription>
              您确定要删除产品选项 "{productToDelete?.label}" 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={cancelDeleteProduct}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDeleteProduct}>
              删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加产品对话框 */}
      <AddProductDialog
        open={addDialogOpen}
        onOpenChange={setAddDialogOpen}
        onAddProduct={addProductOption}
        onProductAdded={handleProductAdded}
      />

      {/* 历史记录对话框 */}
      <Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog} modal={true}>
        <DialogContent
          className="max-w-6xl max-h-[90vh] overflow-hidden"
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <History className="w-4 h-4 text-white" />
              </div>
              <span>分析历史记录</span>
            </DialogTitle>
            <DialogDescription>
              查看和管理您的市场分析历史记录
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            <div className="h-[70vh] overflow-y-auto">
              <AnalysisHistoryList
                records={historyHook.records}
                isLoading={historyHook.isLoading}
                onDeleteRecord={historyHook.deleteRecord}
                onDeleteMultipleRecords={historyHook.deleteMultipleRecords}
                onClearAllRecords={historyHook.clearAllRecords}
                onApplyFilters={historyHook.applyFilters}
                onResetFilters={historyHook.resetFilters}
                onRefreshData={historyHook.refreshData}
                currentFilters={historyHook.currentFilters}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
