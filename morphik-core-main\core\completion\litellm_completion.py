import logging
import re  # Import re for parsing model name
from typing import Any, AsyncGenerator, Dict, List, Optional, Tu<PERSON>, Union

import litellm

try:
    import ollama
except ImportError:
    ollama = None  # Make ollama import optional

from pydantic import BaseModel

from core.config import get_settings
from core.models.completion import CompletionRequest, CompletionResponse

from .base_completion import BaseCompletionModel

logger = logging.getLogger(__name__)


def get_system_message() -> Dict[str, str]:
    """Return the standard system message for <PERSON><PERSON><PERSON><PERSON>'s query agent."""
    return {
        "role": "system",
        "content": """You are <PERSON><PERSON>hi<PERSON>'s powerful query agent. Your role is to:

1. Analyze the provided context chunks from documents carefully
2. Use the context to answer questions accurately and comprehensively
3. Be clear and concise in your answers
4. When relevant, cite specific parts of the context to support your answers
5. For image-based queries, analyze the visual content in conjunction with any text context provided
6. Format your responses using Markdown.

Remember: Your primary goal is to provide accurate, context-aware responses that help users understand
and utilize the information in their documents effectively.""",
    }


def process_context_chunks(context_chunks: List[str], is_ollama: bool) -> Tuple[List[str], List[str], List[str]]:
    """
    Process context chunks and separate text from images.

    Args:
        context_chunks: List of context chunks which may include images
        is_ollama: Whether we're using Ollama (affects image processing)

    Returns:
        Tuple of (context_text, image_urls, ollama_image_data)
    """
    context_text = []
    image_urls = []  # For non-Ollama models (full data URI)
    ollama_image_data = []  # For Ollama models (raw base64)

    for chunk in context_chunks:
        if chunk.startswith("data:image/"):
            if is_ollama:
                # For Ollama, strip the data URI prefix and just keep the base64 data
                try:
                    base64_data = chunk.split(",", 1)[1]
                    ollama_image_data.append(base64_data)
                except IndexError:
                    logger.warning(f"Could not parse base64 data from image chunk: {chunk[:50]}...")
            else:
                image_urls.append(chunk)
        else:
            context_text.append(chunk)

    return context_text, image_urls, ollama_image_data


def format_user_content(context_text: List[str], query: str, prompt_template: Optional[str] = None) -> str:
    """
    Format the user content based on context and query.

    Args:
        context_text: List of context text chunks
        query: The user query
        prompt_template: Optional template to format the content

    Returns:
        Formatted user content string
    """
    context = "\n" + "\n\n".join(context_text) + "\n\n" if context_text else ""

    if prompt_template:
        return prompt_template.format(
            context=context,
            question=query,
            query=query,
        )
    elif context_text:
        return f"Context: {context} Question: {query}"
    else:
        return query


def create_dynamic_model_from_schema(schema: Union[type, Dict]) -> Optional[type]:
    """
    Create a dynamic Pydantic model from a schema definition.

    Args:
        schema: Either a Pydantic BaseModel class or a JSON schema dict

    Returns:
        A Pydantic model class or None if schema format is not recognized
    """
    from pydantic import create_model

    if isinstance(schema, type) and issubclass(schema, BaseModel):
        return schema
    elif isinstance(schema, dict) and "properties" in schema:
        # Create a dynamic model from JSON schema
        field_definitions = {}
        schema_dict = schema

        for field_name, field_info in schema_dict.get("properties", {}).items():
            if isinstance(field_info, dict) and "type" in field_info:
                field_type = field_info.get("type")
                # Convert schema types to Python types
                if field_type == "string":
                    field_definitions[field_name] = (str, None)
                elif field_type == "number":
                    field_definitions[field_name] = (float, None)
                elif field_type == "integer":
                    field_definitions[field_name] = (int, None)
                elif field_type == "boolean":
                    field_definitions[field_name] = (bool, None)
                elif field_type == "array":
                    field_definitions[field_name] = (list, None)
                elif field_type == "object":
                    field_definitions[field_name] = (dict, None)
                else:
                    # Default to Any for unknown types
                    field_definitions[field_name] = (Any, None)

        # Create the dynamic model
        return create_model("DynamicQueryModel", **field_definitions)
    else:
        logger.warning(f"Unrecognized schema format: {schema}")
        return None


class LiteLLMCompletionModel(BaseCompletionModel):
    """
    LiteLLM completion model implementation that provides unified access to various LLM providers.
    Uses registered models from the config file. Can optionally use direct Ollama client.
    """

    def __init__(self, model_key: str):
        """
        Initialize LiteLLM completion model with a model key from registered_models.

        Args:
            model_key: The key of the model in the registered_models config
        """
        settings = get_settings()
        self.model_key = model_key

        # Get the model configuration from registered_models
        if not hasattr(settings, "REGISTERED_MODELS") or model_key not in settings.REGISTERED_MODELS:
            raise ValueError(f"Model '{model_key}' not found in registered_models configuration")

        self.model_config = settings.REGISTERED_MODELS[model_key]

        # Check if it's an Ollama model for potential direct usage
        self.is_ollama = "ollama" in self.model_config.get("model_name", "").lower()
        self.ollama_api_base = None
        self.ollama_base_model_name = None

        if self.is_ollama:
            if ollama is None:
                logger.warning("Ollama model selected, but 'ollama' library not installed. Falling back to LiteLLM.")
                self.is_ollama = False  # Fallback to LiteLLM if library missing
            else:
                self.ollama_api_base = self.model_config.get("api_base")
                if not self.ollama_api_base:
                    logger.warning(
                        f"Ollama model {self.model_key} selected for direct use, "
                        "but 'api_base' is missing in config. Falling back to LiteLLM."
                    )
                    self.is_ollama = False  # Fallback if api_base is missing
                else:
                    # Extract base model name (e.g., 'llama3.2' from 'ollama_chat/llama3.2')
                    match = re.search(r"[^/]+$", self.model_config["model_name"])
                    if match:
                        self.ollama_base_model_name = match.group(0)
                    else:
                        logger.warning(
                            f"Could not parse base model name from Ollama model "
                            f"{self.model_config['model_name']}. Falling back to LiteLLM."
                        )
                        self.is_ollama = False  # Fallback if name parsing fails

        logger.info(
            f"Initialized LiteLLM completion model with model_key={model_key}, "
            f"config={self.model_config}, is_ollama_direct={self.is_ollama}"
        )

    async def _handle_structured_ollama(
        self,
        dynamic_model: type,
        system_message: Dict[str, str],
        user_content: str,
        ollama_image_data: List[str],
        request: CompletionRequest,
        history_messages: List[Dict[str, str]],
    ) -> CompletionResponse:
        """Handle structured output generation with Ollama."""
        try:
            client = ollama.AsyncClient(host=self.ollama_api_base)

            # Add images directly to content if available
            content_data = user_content
            if ollama_image_data and len(ollama_image_data) > 0:
                # Ollama image handling is limited; we can use only the first image
                content_data = {"content": user_content, "images": [ollama_image_data[0]]}

            # Create messages for Ollama
            messages = [system_message] + history_messages + [{"role": "user", "content": content_data}]

            # Get the JSON schema from the dynamic model
            format_schema = dynamic_model.model_json_schema()

            # Call Ollama directly with format parameter
            response = await client.chat(
                model=self.ollama_base_model_name,
                messages=messages,
                format=format_schema,
                options={
                    "temperature": request.temperature or 0.1,  # Lower temperature for structured output
                    "num_predict": request.max_tokens,
                },
            )

            # Parse the response into the dynamic model
            parsed_response = dynamic_model.model_validate_json(response["message"]["content"])

            # Extract token usage information
            usage = {
                "prompt_tokens": response.get("prompt_eval_count", 0),
                "completion_tokens": response.get("eval_count", 0),
                "total_tokens": response.get("prompt_eval_count", 0) + response.get("eval_count", 0),
            }

            return CompletionResponse(
                completion=parsed_response,
                usage=usage,
                finish_reason=response.get("done_reason", "stop"),
            )

        except Exception as e:
            logger.error(f"Error using Ollama for structured output: {e}")
            # Fall back to standard completion if structured output fails
            logger.warning("Falling back to standard Ollama completion without structured output")
            return None

    async def _handle_structured_litellm(
        self,
        dynamic_model: type,
        system_message: Dict[str, str],
        user_content: str,
        image_urls: List[str],
        request: CompletionRequest,
        history_messages: List[Dict[str, str]],
    ) -> CompletionResponse:
        """Handle structured output generation with LiteLLM."""
        import instructor
        from instructor import Mode

        try:
            # Use instructor with litellm
            client = instructor.from_litellm(litellm.acompletion, mode=Mode.JSON)

            # Create content list with text and images
            content_list = [{"type": "text", "text": user_content}]

            # Add images if available
            if image_urls:
                NUM_IMAGES = min(5, len(image_urls))
                for img_url in image_urls[:NUM_IMAGES]:
                    content_list.append({"type": "image_url", "image_url": {"url": img_url}})

            # Create messages for instructor
            messages = [system_message] + history_messages + [{"role": "user", "content": content_list}]

            # Extract model configuration
            model = self.model_config.get("model_name")
            model_kwargs = {k: v for k, v in self.model_config.items() if k != "model_name"}

            # Override with completion request parameters
            if request.temperature is not None:
                model_kwargs["temperature"] = request.temperature
            if request.max_tokens is not None:
                model_kwargs["max_tokens"] = request.max_tokens

            # Add format forcing for structured output
            model_kwargs["response_format"] = {"type": "json_object"}

            # Call instructor with litellm
            response = await client.chat.completions.create(
                model=model,
                messages=messages,
                response_model=dynamic_model,
                **model_kwargs,
            )

            # Get token usage from response
            completion_tokens = model_kwargs.get("response_tokens", 0)
            prompt_tokens = model_kwargs.get("prompt_tokens", 0)

            return CompletionResponse(
                completion=response,
                usage={
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": prompt_tokens + completion_tokens,
                },
                finish_reason="stop",
            )

        except Exception as e:
            logger.error(f"Error using instructor with LiteLLM: {e}")
            # Fall back to standard completion if instructor fails
            logger.warning("Falling back to standard LiteLLM completion without structured output")
            return None

    async def _handle_standard_ollama(
        self,
        user_content: str,
        ollama_image_data: List[str],
        request: CompletionRequest,
        history_messages: List[Dict[str, str]],
    ) -> CompletionResponse:
        """Handle standard (non-structured) output generation with Ollama."""
        logger.info(f"🔥 ENTERING _handle_standard_ollama for model: {self.ollama_base_model_name}")
        logger.debug(f"Using direct Ollama client for model: {self.ollama_base_model_name}")
        client = ollama.AsyncClient(host=self.ollama_api_base)

        # Construct Ollama messages with model-specific optimizations
        system_content = get_system_message()["content"]

        # Optimize for DeepSeek models
        if "deepseek" in self.ollama_base_model_name.lower():
            system_content = self._optimize_system_message_for_deepseek(system_content)
            user_content = self._optimize_user_content_for_deepseek(user_content)

        system_message = {"role": "system", "content": system_content}
        user_message_data = {"role": "user", "content": user_content}

        # Add images directly to the user message if available
        if ollama_image_data:
            # Add all images to the user message
            user_message_data["images"] = ollama_image_data

        ollama_messages = [system_message] + history_messages + [user_message_data]

        # Construct Ollama options
        options = {
            "temperature": request.temperature,
            "num_predict": (
                request.max_tokens if request.max_tokens is not None else -1
            ),  # Default to model's default if None
        }

        try:
            logger.info(f"🔥 OLLAMA MESSAGES: {ollama_messages}")
            logger.info(f"🔥 OLLAMA OPTIONS: {options}")
            response = await client.chat(model=self.ollama_base_model_name, messages=ollama_messages, options=options)
            logger.info(f"🔥 OLLAMA RESPONSE: {response}")

            # Map Ollama response to CompletionResponse
            prompt_tokens = response.get("prompt_eval_count", 0)
            completion_tokens = response.get("eval_count", 0)

            # Get the raw completion content
            raw_completion = response["message"]["content"]
            logger.info(f"🔥 RAW COMPLETION: {repr(raw_completion)}")

            # Filter DeepSeek thinking process if present
            logger.info(f"🔥 CALLING _filter_deepseek_thinking...")
            filtered_completion = self._filter_deepseek_thinking(raw_completion)
            logger.info(f"🔥 FILTERED COMPLETION: {repr(filtered_completion)}")

            return CompletionResponse(
                completion=filtered_completion,
                usage={
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": prompt_tokens + completion_tokens,
                },
                finish_reason=response.get("done_reason", "unknown"),  # Map done_reason if available
            )

        except Exception as e:
            logger.error(f"Error during direct Ollama call: {e}")
            raise

    def _filter_deepseek_thinking(self, content: str) -> str:
        """
        Filter DeepSeek thinking process from completion content.
        DeepSeek-R1 models output thinking process in the format:
        Thinking...
        [thinking content]
        ...done thinking.
        [actual response]
        """
        if not content or not isinstance(content, str):
            return content or ""

        import re

        # Log original content for debugging
        logger.info(f"DeepSeek filter - Original content length: {len(content)}")
        logger.info(f"DeepSeek filter - Original content preview: {repr(content[:200])}")

        # Filter "Thinking..." to "...done thinking." pattern (handle multiple occurrences)
        filtered = re.sub(r'Thinking\.\.\..*?\.\.\.done thinking\.', '', content, flags=re.DOTALL)

        # Filter any remaining "...done thinking." fragments
        filtered = re.sub(r'\.\.\.done thinking\.', '', filtered, flags=re.DOTALL)

        # Filter standalone "Thinking..." fragments
        filtered = re.sub(r'Thinking\.\.\.', '', filtered, flags=re.DOTALL)

        # Filter <think>...</think> tags (alternative format)
        filtered = re.sub(r'<think>.*?</think>', '', filtered, flags=re.DOTALL)

        # Clean up any repeated fragments or partial content
        filtered = re.sub(r'(Hello! 😊 How.*?)\1+', r'\1', filtered, flags=re.DOTALL)  # Remove duplicates

        # Clean up extra whitespace and newlines
        filtered = re.sub(r'\n\s*\n\s*\n+', '\n\n', filtered)  # Merge multiple empty lines
        filtered = re.sub(r'\s+', ' ', filtered)  # Normalize whitespace
        filtered = filtered.strip()

        # Log filtered result
        logger.info(f"DeepSeek filter - Filtered content length: {len(filtered)}")
        logger.info(f"DeepSeek filter - Filtered content preview: {repr(filtered[:200])}")

        # If filtering removed everything, log warning and return original
        if not filtered and content:
            logger.warning("DeepSeek thinking filter removed all content, returning original")
            logger.warning(f"Original content was: {repr(content)}")
            return content

        # If significant content was removed, log info
        if len(filtered) < len(content) * 0.8:
            logger.info(f"DeepSeek thinking process filtered: {len(content)} -> {len(filtered)} chars")

        return filtered

    def _optimize_system_message_for_deepseek(self, system_content: str) -> str:
        """Optimize system message for DeepSeek models."""
        # DeepSeek models work better with shorter, more direct instructions
        # Original system_content is available but we use a simplified version
        optimized = """You are a helpful AI assistant. Your task is to:

1. Read the provided context carefully
2. Answer the user's question based on the context
3. Be concise and direct in your response
4. If the context doesn't contain relevant information, say so clearly

Always provide a helpful response."""

        logger.info("🔥 Using optimized system message for DeepSeek")
        return optimized

    def _optimize_user_content_for_deepseek(self, user_content: str) -> str:
        """Optimize user content for DeepSeek models."""
        # DeepSeek models can struggle with very long contexts
        # Try to truncate or simplify if needed

        if len(user_content) > 8000:  # If content is very long
            logger.info(f"🔥 Truncating long content for DeepSeek: {len(user_content)} -> 8000 chars")
            # Keep the question at the end and truncate context
            lines = user_content.split('\n')
            question_line = None

            # Find the question line
            for line in lines:
                if line.strip().startswith('Question:'):
                    question_line = line
                    break

            if question_line:
                # Keep some context and the question
                context_part = '\n'.join(lines[:50])  # Keep first 50 lines of context
                return f"{context_part}\n\n{question_line}"
            else:
                # Just truncate
                return user_content[:8000]

        return user_content

    async def _handle_standard_litellm(
        self,
        user_content: str,
        image_urls: List[str],
        request: CompletionRequest,
        history_messages: List[Dict[str, str]],
    ) -> CompletionResponse:
        """Handle standard (non-structured) output generation with LiteLLM."""
        logger.debug(f"Using LiteLLM for model: {self.model_config['model_name']}")
        # Build messages for LiteLLM
        content_list = [{"type": "text", "text": user_content}]
        include_images = image_urls  # Use the collected full data URIs

        if include_images:
            NUM_IMAGES = min(5, len(image_urls))
            for img_url in image_urls[:NUM_IMAGES]:
                content_list.append({"type": "image_url", "image_url": {"url": img_url}})

        # LiteLLM uses list content format
        user_message = {"role": "user", "content": content_list}
        # Use the system prompt defined earlier
        litellm_messages = [get_system_message()] + history_messages + [user_message]

        # Prepare LiteLLM parameters
        model_params = {
            "model": self.model_config["model_name"],
            "messages": litellm_messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "num_retries": 3,
        }

        for key, value in self.model_config.items():
            if key != "model_name":
                model_params[key] = value

        logger.debug(f"Calling LiteLLM with params: {model_params}")
        response = await litellm.acompletion(**model_params)

        return CompletionResponse(
            completion=response.choices[0].message.content,
            usage={
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens,
            },
            finish_reason=response.choices[0].finish_reason,
        )

    async def _handle_streaming_litellm(
        self,
        user_content: str,
        image_urls: List[str],
        request: CompletionRequest,
        history_messages: List[Dict[str, str]],
    ) -> AsyncGenerator[str, None]:
        """Handle streaming output generation with LiteLLM."""
        logger.debug(f"Using LiteLLM streaming for model: {self.model_config['model_name']}")
        # Build messages for LiteLLM
        content_list = [{"type": "text", "text": user_content}]
        include_images = image_urls  # Use the collected full data URIs

        if include_images:
            NUM_IMAGES = min(5, len(image_urls))
            for img_url in image_urls[:NUM_IMAGES]:
                content_list.append({"type": "image_url", "image_url": {"url": img_url}})

        # LiteLLM uses list content format
        user_message = {"role": "user", "content": content_list}
        # Use the system prompt defined earlier
        litellm_messages = [get_system_message()] + history_messages + [user_message]

        # Prepare LiteLLM parameters
        model_params = {
            "model": self.model_config["model_name"],
            "messages": litellm_messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "stream": True,  # Enable streaming
            "num_retries": 3,
        }

        for key, value in self.model_config.items():
            if key != "model_name":
                model_params[key] = value

        logger.debug(f"Calling LiteLLM streaming with params: {model_params}")
        response = await litellm.acompletion(**model_params)

        # Stream the response chunks
        async for chunk in response:
            if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content

    async def _handle_streaming_ollama(
        self,
        user_content: str,
        ollama_image_data: List[str],
        request: CompletionRequest,
        history_messages: List[Dict[str, str]],
    ) -> AsyncGenerator[str, None]:
        """Handle streaming output generation with Ollama."""
        logger.debug(f"Using direct Ollama streaming for model: {self.ollama_base_model_name}")
        client = ollama.AsyncClient(host=self.ollama_api_base)

        # Construct Ollama messages
        system_message = {"role": "system", "content": get_system_message()["content"]}
        user_message_data = {"role": "user", "content": user_content}

        # Add images directly to the user message if available
        if ollama_image_data:
            # Add all images to the user message
            user_message_data["images"] = ollama_image_data

        ollama_messages = [system_message] + history_messages + [user_message_data]

        # Construct Ollama options
        options = {
            "temperature": request.temperature,
            "num_predict": (
                request.max_tokens if request.max_tokens is not None else -1
            ),  # Default to model's default if None
        }

        try:
            response = await client.chat(
                model=self.ollama_base_model_name,
                messages=ollama_messages,
                options=options,
                stream=True,  # Enable streaming
            )

            # For streaming, we need to accumulate content and filter at the end
            # This is a limitation of the thinking process filtering with streaming
            accumulated_content = ""
            async for chunk in response:
                if chunk.get("message", {}).get("content"):
                    chunk_content = chunk["message"]["content"]
                    accumulated_content += chunk_content

                    # For now, yield the chunk as-is during streaming
                    # The thinking process will be filtered on the frontend
                    yield chunk_content

            # Note: For complete thinking process filtering in streaming,
            # we would need to buffer the entire response and filter at the end,
            # which defeats the purpose of streaming. The frontend filtering
            # handles this case.

        except Exception as e:
            logger.error(f"Error during direct Ollama streaming call: {e}")
            raise

    async def complete(self, request: CompletionRequest) -> Union[CompletionResponse, AsyncGenerator[str, None]]:
        """
        Generate completion using LiteLLM or direct Ollama client if configured.

        Args:
            request: CompletionRequest object containing query, context, and parameters

        Returns:
            CompletionResponse object with the generated text and usage statistics or
            AsyncGenerator for streaming responses
        """
        # Process context chunks and handle images
        context_text, image_urls, ollama_image_data = process_context_chunks(request.context_chunks, self.is_ollama)

        # Format user content
        user_content = format_user_content(context_text, request.query, request.prompt_template)

        history_messages = [{"role": m.role, "content": m.content} for m in (request.chat_history or [])]

        # Check if structured output is requested
        structured_output = request.schema is not None

        # Streaming is not supported with structured output
        if request.stream_response and structured_output:
            logger.warning("Streaming is not supported with structured output. Falling back to non-streaming.")
            request.stream_response = False

        # If streaming is requested and no structured output
        if request.stream_response and not structured_output:
            if self.is_ollama:
                return self._handle_streaming_ollama(user_content, ollama_image_data, request, history_messages)
            else:
                return self._handle_streaming_litellm(user_content, image_urls, request, history_messages)

        # If structured output is requested, use instructor to handle it
        if structured_output:
            # Get dynamic model from schema
            dynamic_model = create_dynamic_model_from_schema(request.schema)

            # If schema format is not recognized, log warning and fall back to text completion
            if not dynamic_model:
                logger.warning(f"Unrecognized schema format: {request.schema}. Falling back to text completion.")
                structured_output = False
            else:
                logger.info(f"Using structured output with model: {dynamic_model.__name__}")

                # Create system and user messages with enhanced instructions for structured output
                system_message = {
                    "role": "system",
                    "content": get_system_message()["content"]
                    + "\n\nYou MUST format your response according to the required schema.",
                }

                # Create enhanced user message that includes schema information
                enhanced_user_content = (
                    user_content + "\n\nPlease format your response according to the required schema."
                )

                # Try structured output based on model type
                if self.is_ollama:
                    response = await self._handle_structured_ollama(
                        dynamic_model,
                        system_message,
                        enhanced_user_content,
                        ollama_image_data,
                        request,
                        history_messages,
                    )
                    if response:
                        return response
                    structured_output = False  # Fall back if structured output failed
                else:
                    response = await self._handle_structured_litellm(
                        dynamic_model,
                        system_message,
                        enhanced_user_content,
                        image_urls,
                        request,
                        history_messages,
                    )
                    if response:
                        return response
                    structured_output = False  # Fall back if structured output failed

        # If we're here, either structured output wasn't requested or instructor failed
        # Proceed with standard completion based on model type
        logger.info(f"🔥 CHOOSING COMPLETION PATH: is_ollama={self.is_ollama}, model_key={self.model_key}")
        if self.is_ollama:
            logger.info(f"🔥 USING OLLAMA PATH for {self.ollama_base_model_name}")
            return await self._handle_standard_ollama(user_content, ollama_image_data, request, history_messages)
        else:
            logger.info(f"🔥 USING LITELLM PATH for {self.model_config['model_name']}")
            return await self._handle_standard_litellm(user_content, image_urls, request, history_messages)
