#!/usr/bin/env python3
"""
智能市场分析演示系统
基于 SmoLAgents 微服务实现的智能市场分析工具

功能特性：
- 自动解析复杂搜索词（产品、地区、时间、分析维度）
- 智能生成多角度搜索策略
- 使用 DuckDuckGoSearchTool 获取真实网络数据
- AI 驱动的综合分析和报告生成
"""

import asyncio
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
import httpx
from dataclasses import dataclass


@dataclass
class SearchQuery:
    """搜索查询结构"""
    query: str
    category: str
    priority: int
    description: str


@dataclass
class AnalysisContext:
    """分析上下文"""
    product: str
    region: str
    time_period: str
    analysis_type: str
    keywords: List[str]


class MarketAnalysisDemo:
    """智能市场分析演示系统"""
    
    def __init__(self, base_url: str = "http://localhost:8002"):
        self.base_url = base_url
        self.session: Optional[httpx.AsyncClient] = None
        
    async def __aenter__(self):
        self.session = httpx.AsyncClient(timeout=180.0)  # 减少超时时间
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.aclose()
    
    def parse_search_term(self, search_term: str) -> AnalysisContext:
        """
        智能解析搜索词，提取关键信息
        
        Args:
            search_term: 原始搜索词
            
        Returns:
            AnalysisContext: 解析后的分析上下文
        """
        print(f"🔍 正在解析搜索词: {search_term}")
        
        # 产品识别
        product_patterns = [
            r'([^0-9\s]+?)(?=\s*20\d{2}|\s*市场|\s*销售|\s*分析)',
            r'^([^0-9]+?)(?=\s)',
        ]
        product = "未知产品"
        for pattern in product_patterns:
            match = re.search(pattern, search_term)
            if match:
                product = match.group(1).strip()
                break
        
        # 地区识别
        region_keywords = {
            '亚洲': ['亚洲', 'Asia', 'Asian'],
            '中国': ['中国', 'China', 'Chinese'],
            '美国': ['美国', 'USA', 'US', 'America'],
            '欧洲': ['欧洲', 'Europe', 'European'],
            '全球': ['全球', 'global', 'worldwide', 'international']
        }
        
        region = "全球"
        for region_name, keywords in region_keywords.items():
            if any(keyword in search_term for keyword in keywords):
                region = region_name
                break
        
        # 时间识别
        time_match = re.search(r'20\d{2}', search_term)
        time_period = time_match.group(0) if time_match else "2024"
        
        # 分析类型识别
        analysis_keywords = {
            '销售分析': ['销售', '销量', 'sales', 'revenue'],
            '市场趋势': ['趋势', '发展', 'trend', 'growth'],
            '竞争分析': ['竞争', '对手', 'competition', 'competitor'],
            '价格分析': ['价格', '定价', 'price', 'pricing'],
            '预测分析': ['预测', '预期', 'forecast', 'prediction']
        }
        
        analysis_type = "综合分析"
        for analysis_name, keywords in analysis_keywords.items():
            if any(keyword in search_term for keyword in keywords):
                analysis_type = analysis_name
                break
        
        # 提取关键词
        keywords = [word for word in search_term.split() if len(word) > 1]
        
        context = AnalysisContext(
            product=product,
            region=region,
            time_period=time_period,
            analysis_type=analysis_type,
            keywords=keywords
        )
        
        print(f"✅ 解析结果:")
        print(f"   产品: {context.product}")
        print(f"   地区: {context.region}")
        print(f"   时间: {context.time_period}")
        print(f"   分析类型: {context.analysis_type}")
        print()
        
        return context
    
    def generate_search_queries(self, context: AnalysisContext, strategy_mode: str = "optimized") -> List[SearchQuery]:
        """
        基于分析上下文生成多角度搜索查询

        Args:
            context: 分析上下文
            strategy_mode: 搜索策略模式 ("optimized" 4个策略 或 "comprehensive" 7个策略)

        Returns:
            List[SearchQuery]: 搜索查询列表
        """
        print(f"🎯 生成智能搜索策略 ({strategy_mode} 模式)...")

        queries = []

        if strategy_mode == "optimized":
            # 优化模式：4个合并策略
            queries.extend([
                SearchQuery(
                    query=f"{context.product} {context.region} market size {context.time_period} industry report",
                    category="市场规模",
                    priority=1,
                    description="获取市场规模和基础行业数据"
                ),
                SearchQuery(
                    query=f"{context.product} sales trends {context.region} {context.time_period} growth forecast 2025",
                    category="销售趋势",
                    priority=1,
                    description="获取销售数据和市场趋势"
                ),
                SearchQuery(
                    query=f"{context.product} {context.region} competitors market share price analysis {context.time_period}",
                    category="竞争分析",
                    priority=1,
                    description="分析竞争格局和价格趋势"
                ),
                SearchQuery(
                    query=f"{context.product} consumer demand {context.region} {context.time_period} 2025 outlook",
                    category="需求预测",
                    priority=2,
                    description="分析消费者需求和未来预测"
                )
            ])
        else:
            # 全面模式：7个详细策略
            queries.extend([
                SearchQuery(
                    query=f"{context.product} {context.region} market size {context.time_period} industry report",
                    category="市场规模",
                    priority=1,
                    description="获取市场规模和基础行业数据"
                ),
                SearchQuery(
                    query=f"{context.product} sales data {context.region} {context.time_period} statistics",
                    category="销售数据",
                    priority=1,
                    description="获取销售数据和统计信息"
                ),
                SearchQuery(
                    query=f"{context.product} market trends {context.time_period} growth forecast 2025",
                    category="趋势预测",
                    priority=1,
                    description="获取市场趋势和未来预测"
                ),
                SearchQuery(
                    query=f"{context.product} {context.region} top companies market share competitors",
                    category="竞争分析",
                    priority=2,
                    description="分析竞争格局和主要参与者"
                ),
                SearchQuery(
                    query=f"{context.product} price analysis {context.region} wholesale retail {context.time_period}",
                    category="价格分析",
                    priority=2,
                    description="获取价格趋势和定价策略"
                ),
                SearchQuery(
                    query=f"{context.product} consumer behavior {context.region} demand analysis {context.time_period}",
                    category="消费者分析",
                    priority=2,
                    description="分析消费者行为和需求变化"
                ),
                SearchQuery(
                    query=f"{context.product} industry news {context.region} {context.time_period} latest developments",
                    category="行业动态",
                    priority=3,
                    description="获取最新行业新闻和发展动态"
                )
            ])

        print(f"✅ 生成了 {len(queries)} 个搜索策略:")
        for i, query in enumerate(queries, 1):
            print(f"   {i}. [{query.category}] {query.description}")
        print()

        return queries
    
    async def execute_intelligent_search(self, queries: List[SearchQuery]) -> Dict[str, Any]:
        """
        执行智能搜索，使用 SmoLAgents 的网络搜索能力
        
        Args:
            queries: 搜索查询列表
            
        Returns:
            Dict: 搜索结果汇总
        """
        print("🌐 开始执行智能网络搜索...")
        
        search_results = {}
        successful_searches = 0
        
        for i, query in enumerate(queries, 1):
            print(f"   [{i}/{len(queries)}] 搜索: {query.category}")
            
            try:
                # 构建搜索提示词，让 AI 代理执行网络搜索
                search_prompt = f"""
请使用网络搜索工具查找以下信息：

搜索查询: {query.query}
搜索目的: {query.description}

请执行网络搜索，并提供以下格式的结果：
1. 找到的关键信息和数据
2. 信息来源的可信度评估
3. 相关的数字、统计数据或趋势
4. 重要的市场洞察

请确保搜索真实的网络数据，不要使用模拟数据。
"""
                
                # 调用 SmoLAgents 服务（优化参数）
                data = {
                    "query": search_prompt,
                    "max_steps": 10,  # 减少步骤数
                    "use_cache": False,
                    "enable_web_search": True,
                    "timeout": 120  # 减少单次搜索超时时间
                }

                response = await self.session.post(f"{self.base_url}/api/v1/agent/query", json=data)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        search_results[query.category] = {
                            'query': query.query,
                            'result': result.get('result'),
                            'priority': query.priority,
                            'description': query.description
                        }
                        successful_searches += 1
                        print(f"      ✅ 成功获取数据")
                    else:
                        print(f"      ❌ 搜索失败: {result.get('error', '未知错误')}")
                else:
                    print(f"      ❌ 请求失败: HTTP {response.status_code}")

            except asyncio.TimeoutError:
                print(f"      ⏰ 搜索超时，跳过此项")
            except Exception as e:
                print(f"      ❌ 搜索异常: {e}")

            # 添加延迟避免过于频繁的请求
            if i < len(queries):
                await asyncio.sleep(3)  # 增加延迟时间
        
        print(f"✅ 搜索完成，成功获取 {successful_searches}/{len(queries)} 个数据源")
        print()
        
        return search_results

    async def generate_comprehensive_analysis(self, context: AnalysisContext, search_results: Dict[str, Any]) -> str:
        """
        基于搜索结果生成综合市场分析报告

        Args:
            context: 分析上下文
            search_results: 搜索结果数据

        Returns:
            str: 格式化的分析报告
        """
        print("📊 正在生成综合市场分析报告...")

        # 构建简化的分析提示词，避免过长
        analysis_prompt = f"""
作为专业市场分析师，基于真实搜索数据为"{context.product}"在{context.region}市场生成分析报告。

## 分析背景
产品: {context.product} | 市场: {context.region} | 时间: {context.time_period}

## 关键数据摘要
"""

        # 添加搜索结果摘要（限制长度）
        for category, data in search_results.items():
            result_summary = str(data['result'])[:500] if data['result'] else "无数据"
            analysis_prompt += f"""
{category}: {result_summary}
"""

        analysis_prompt += f"""

## 分析要求
基于以上数据生成专业市场分析报告，并将完整报告放在final_answer()中：

请生成以下结构的完整分析报告：

# {context.product} - {context.region}市场分析报告

## 1. 市场现状
- 市场规模和增长
- 主要参与者
- 价格趋势

## 2. 竞争分析
- 主要竞争对手
- 市场份额
- 竞争优势

## 3. 趋势预测
- {context.time_period}年表现
- 2025年预期
- 关键驱动因素

## 4. 结论建议
- 核心洞察
- 战略建议
- 行动计划

**重要提示**：请将完整的详细分析报告内容放在final_answer()函数中，不要只放简短总结。用户需要看到完整的分析内容。

要求：基于真实数据，提供具体数字，逻辑清晰，适合商业决策。
"""

        try:
            # 调用 SmoLAgents 生成分析报告（优化参数）
            data = {
                "query": analysis_prompt,
                "max_steps": 15,  # 减少步骤数
                "use_cache": False,
                "enable_web_search": False,  # 不需要额外搜索
                "timeout": 180  # 减少超时时间
            }

            print(f"   提示词长度: {len(analysis_prompt)} 字符")

            response = await self.session.post(f"{self.base_url}/api/v1/agent/query", json=data)

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    analysis_report = result.get('result', '')
                    if analysis_report:
                        print("✅ 分析报告生成完成")
                        return analysis_report
                    else:
                        error_msg = "分析报告为空"
                        print(f"❌ {error_msg}")
                        return f"分析报告生成失败: {error_msg}"
                else:
                    error_msg = result.get('error', '未知错误')
                    print(f"❌ 分析生成失败: {error_msg}")
                    return f"分析报告生成失败: {error_msg}"
            else:
                error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                print(f"❌ 请求失败: {error_msg}")
                return f"分析报告生成失败: {error_msg}"

        except asyncio.TimeoutError:
            error_msg = "请求超时，可能是分析内容过于复杂"
            print(f"❌ {error_msg}")
            return f"分析报告生成失败: {error_msg}"
        except Exception as e:
            error_msg = f"异常: {str(e)}"
            print(f"❌ 分析生成异常: {error_msg}")
            return f"分析报告生成失败: {error_msg}"

    async def run_market_analysis(self, search_term: str, strategy_mode: str = "optimized") -> Dict[str, Any]:
        """
        运行完整的市场分析流程

        Args:
            search_term: 原始搜索词

        Returns:
            Dict: 完整的分析结果
        """
        start_time = datetime.now()
        print("🚀 启动智能市场分析系统")
        print("=" * 60)
        print(f"搜索词: {search_term}")
        print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        print()

        try:
            # 第一步：解析搜索词
            context = self.parse_search_term(search_term)

            # 第二步：生成搜索策略
            queries = self.generate_search_queries(context, strategy_mode)

            # 第三步：执行智能搜索
            search_results = await self.execute_intelligent_search(queries)

            if not search_results:
                return {
                    'success': False,
                    'error': '未能获取有效的市场数据，请检查网络连接或稍后重试',
                    'context': context.__dict__
                }

            # 第四步：生成综合分析报告
            analysis_report = await self.generate_comprehensive_analysis(context, search_results)

            # 计算执行时间
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            print("=" * 60)
            print("🎉 市场分析完成!")
            print(f"执行时间: {execution_time:.2f} 秒")
            print(f"数据源数量: {len(search_results)}")
            print("=" * 60)

            return {
                'success': True,
                'context': context.__dict__,
                'search_queries': [q.__dict__ for q in queries],
                'search_results': search_results,
                'analysis_report': analysis_report,
                'execution_time': execution_time,
                'data_sources_count': len(search_results),
                'timestamp': end_time.isoformat()
            }

        except Exception as e:
            error_msg = f"市场分析执行失败: {e}"
            print(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'context': context.__dict__ if 'context' in locals() else None
            }


async def main():
    """主演示函数"""
    # 示例搜索词
    demo_search_terms = [
        "沙滩包2024亚洲市场销售情况分析及2025市场增长预期",
        "智能手表欧洲市场2024年竞争格局分析",
        "电动汽车中国市场2024销售数据及价格趋势",
        "咖啡机全球市场2024年消费者需求分析"
    ]

    print("🌟 智能市场分析演示系统")
    print("基于 SmoLAgents 微服务 + Qwen3-8B-M 模型")
    print("=" * 80)
    print()

    # 让用户选择或输入搜索词
    print("📝 可选择的演示搜索词:")
    for i, term in enumerate(demo_search_terms, 1):
        print(f"   {i}. {term}")
    print(f"   {len(demo_search_terms) + 1}. 自定义输入")
    print()

    try:
        choice = input("请选择搜索词 (输入数字): ").strip()

        if choice.isdigit():
            choice_num = int(choice)
            if 1 <= choice_num <= len(demo_search_terms):
                search_term = demo_search_terms[choice_num - 1]
            elif choice_num == len(demo_search_terms) + 1:
                search_term = input("请输入自定义搜索词: ").strip()
                if not search_term:
                    search_term = demo_search_terms[0]  # 默认使用第一个
            else:
                search_term = demo_search_terms[0]  # 默认使用第一个
        else:
            search_term = demo_search_terms[0]  # 默认使用第一个

    except (KeyboardInterrupt, EOFError):
        search_term = demo_search_terms[0]  # 默认使用第一个

    print(f"✅ 选择的搜索词: {search_term}")
    print()

    # 让用户选择搜索策略模式
    print("🎯 选择搜索策略模式:")
    print("   1. 优化模式 (4个搜索策略，快速稳定，推荐)")
    print("   2. 全面模式 (7个搜索策略，详细全面，耗时较长)")
    print()

    try:
        strategy_choice = input("请选择策略模式 (输入数字，默认1): ").strip()
        if strategy_choice == "2":
            strategy_mode = "comprehensive"
            print("✅ 选择全面模式 (7个搜索策略)")
        else:
            strategy_mode = "optimized"
            print("✅ 选择优化模式 (4个搜索策略)")
    except (KeyboardInterrupt, EOFError):
        strategy_mode = "optimized"
        print("✅ 默认使用优化模式 (4个搜索策略)")

    print()

    # 执行市场分析
    async with MarketAnalysisDemo() as analyzer:
        result = await analyzer.run_market_analysis(search_term, strategy_mode)

        # 输出结果
        if result['success']:
            print("\n" + "=" * 80)
            print("📊 市场分析报告")
            print("=" * 80)
            print(result['analysis_report'])
            print("\n" + "=" * 80)
            print("📈 分析统计")
            print(f"数据源数量: {result['data_sources_count']}")
            print(f"执行时间: {result['execution_time']:.2f} 秒")
            print(f"完成时间: {result['timestamp']}")
        else:
            print(f"\n❌ 分析失败: {result['error']}")


if __name__ == "__main__":
    asyncio.run(main())
