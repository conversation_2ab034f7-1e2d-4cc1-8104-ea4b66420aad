import { useState, useEffect } from 'react'
import { Users, User, Baby, Z<PERSON>, Heart, Dumbbell } from 'lucide-react'
import type { SelectorOption } from '@/components/ProductSelector'

// 默认人群选项 - 使用固定时间戳避免重新渲染问题
const FIXED_TIMESTAMP = '2024-01-01T00:00:00.000Z'

const DEFAULT_AUDIENCES: SelectorOption[] = [
  {
    id: 'audience_1',
    value: 'female',
    label: '女性',
    icon: User,
    iconName: 'User',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'audience_2',
    value: 'male',
    label: '男性',
    icon: User,
    iconName: 'User',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'audience_3',
    value: 'children',
    label: '儿童',
    icon: Baby,
    iconName: 'Baby',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'audience_4',
    value: 'teenagers',
    label: '青少年',
    icon: Zap,
    iconName: 'Zap',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'audience_5',
    value: 'middle-aged',
    label: '中老年',
    icon: Heart,
    iconName: 'Heart',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'audience_6',
    value: 'athletes',
    label: '运动族',
    icon: Dumbbell,
    iconName: 'Dumbbell',
    createdAt: FIXED_TIMESTAMP
  }
]

const STORAGE_KEY = 'group-analysis-audience-options'

// 图标映射函数
const getIconByName = (iconName: string) => {
  switch (iconName) {
    case 'User': return User
    case 'Baby': return Baby
    case 'Zap': return Zap
    case 'Heart': return Heart
    case 'Dumbbell': return Dumbbell
    case 'Users': return Users
    default: return Users
  }
}

export interface UseAudienceOptionsReturn {
  audienceOptions: SelectorOption[]
  addAudienceOption: (label: string) => Promise<{ success: boolean; error?: string; value?: string }>
  removeAudienceOption: (audienceId: string) => Promise<{ success: boolean; error?: string }>
  isLoading: boolean
}

export const useAudienceOptions = (): UseAudienceOptionsReturn => {
  const [audienceOptions, setAudienceOptions] = useState<SelectorOption[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // 初始化加载选项
  useEffect(() => {
    const loadOptions = () => {
      try {
        const stored = localStorage.getItem(STORAGE_KEY)
        if (stored) {
          const parsedOptions = JSON.parse(stored)
          // 验证数据结构并恢复图标引用
          if (Array.isArray(parsedOptions) && parsedOptions.length > 0) {
            const optionsWithIcons = parsedOptions.map((option: any) => ({
              ...option,
              icon: getIconByName(option.iconName)
            }))
            setAudienceOptions(optionsWithIcons)
          } else {
            // 如果存储的数据无效，使用默认选项
            setAudienceOptions(DEFAULT_AUDIENCES)
            localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_AUDIENCES))
          }
        } else {
          // 首次使用，设置默认选项
          setAudienceOptions(DEFAULT_AUDIENCES)
          localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_AUDIENCES))
        }
      } catch (error) {
        console.error('加载人群选项失败:', error)
        // 出错时使用默认选项
        setAudienceOptions(DEFAULT_AUDIENCES)
        localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_AUDIENCES))
      } finally {
        setIsLoading(false)
      }
    }

    loadOptions()
  }, [])

  // 保存选项到localStorage
  const saveOptions = (options: SelectorOption[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(options))
      return true
    } catch (error) {
      console.error('保存人群选项失败:', error)
      return false
    }
  }

  // 添加新的人群选项
  const addAudienceOption = async (label: string): Promise<{ success: boolean; error?: string; value?: string }> => {
    try {
      // 检查是否已存在相同标签
      const existingOption = audienceOptions.find(option => 
        option.label.toLowerCase() === label.toLowerCase()
      )
      
      if (existingOption) {
        return { success: false, error: '该人群选项已存在' }
      }

      // 生成新选项
      const newOption: SelectorOption = {
        id: `audience_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        value: label.toLowerCase().replace(/\s+/g, '-'),
        label: label,
        icon: Users, // 新添加的使用Users图标
        iconName: 'Users',
        createdAt: new Date().toISOString()
      }

      const updatedOptions = [...audienceOptions, newOption]
      
      if (saveOptions(updatedOptions)) {
        setAudienceOptions(updatedOptions)
        return { success: true, value: newOption.value }
      } else {
        return { success: false, error: '保存失败' }
      }
    } catch (error) {
      console.error('添加人群选项失败:', error)
      return { success: false, error: '添加过程中发生错误' }
    }
  }

  // 删除人群选项
  const removeAudienceOption = async (audienceId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // 防止删除最后一个选项
      if (audienceOptions.length <= 1) {
        return { success: false, error: '至少需要保留一个人群选项' }
      }

      const updatedOptions = audienceOptions.filter(option => option.id !== audienceId)
      
      if (saveOptions(updatedOptions)) {
        setAudienceOptions(updatedOptions)
        return { success: true }
      } else {
        return { success: false, error: '保存失败' }
      }
    } catch (error) {
      console.error('删除人群选项失败:', error)
      return { success: false, error: '删除过程中发生错误' }
    }
  }

  return {
    audienceOptions,
    addAudienceOption,
    removeAudienceOption,
    isLoading
  }
}

export default useAudienceOptions
