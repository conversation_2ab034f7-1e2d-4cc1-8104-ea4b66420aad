{"name": "@morphik/ui", "version": "0.2.33", "private": true, "description": "Modern UI component for Morphik - A powerful document processing and querying system", "author": "Morphik Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/morphik-org/morphik-core"}, "keywords": ["morphik", "ui", "react", "document-processing", "chat", "search"], "homepage": "https://github.com/morphik-org/morphik-core", "bugs": {"url": "https://github.com/morphik-org/morphik-core/issues"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "next dev", "build": "next build", "build:package": "tsup", "prepublishOnly": "npm run build:package", "start": "next start", "lint": "next lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\""}, "dependencies": {"@radix-ui/primitive": "^1.1.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "accessor-fn": "^1.5.3", "accordion": "^3.0.2", "alert": "^6.0.2", "caniuse-lite": "^1.0.30001714", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "force-graph": "^1.49.4", "formdata-node": "^6.0.3", "kapsule": "^1.16.3", "label": "^0.2.2", "lucide-react": "^0.469.0", "next": "^14", "next-themes": "^0.4.6", "openai": "^4.28.4", "react": "^18", "react-dom": "^18", "react-force-graph": "^1.29.3", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "shadcn": "^2.4.0-canary.11", "shadcn-ui": "^0.9.4", "sharp": "^0.34.1", "sheet": "^0.2.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "web-streams-polyfill": "^4.1.0"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^8", "eslint-config-next": "14.2.16", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.4.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "tsup": "^8.4.0", "typescript": "^5.8.3"}, "peerDependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "next": "^14", "next-themes": "^0.4.6", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "engines": {"node": ">=18"}}