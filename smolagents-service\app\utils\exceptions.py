"""
自定义异常类
定义服务特定的异常类型
"""


class SmoLAgentsServiceError(Exception):
    """SmoLAgents 服务基础异常类"""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class AgentServiceError(SmoLAgentsServiceError):
    """智能代理服务异常"""
    pass


class ModelNotAvailableError(SmoLAgentsServiceError):
    """模型不可用异常"""
    pass


class RedisConnectionError(SmoLAgentsServiceError):
    """Redis 连接异常"""
    pass


class ConfigurationError(SmoLAgentsServiceError):
    """配置错误异常"""
    pass


class ValidationError(SmoLAgentsServiceError):
    """数据验证异常"""
    pass


class TimeoutError(SmoLAgentsServiceError):
    """超时异常"""
    pass


class RateLimitError(SmoLAgentsServiceError):
    """速率限制异常"""
    pass


class AuthenticationError(SmoLAgentsServiceError):
    """认证异常"""
    pass


class AuthorizationError(SmoLAgentsServiceError):
    """授权异常"""
    pass


class ResourceNotFoundError(SmoLAgentsServiceError):
    """资源未找到异常"""
    pass


class ServiceUnavailableError(SmoLAgentsServiceError):
    """服务不可用异常"""
    pass
