#
# id_modifier.ps1
#
# Description: Modifies telemetry IDs in VS Code storage.json file
# This script generates random values for machineId and devDeviceId

# Set strict mode for better error handling
Set-StrictMode -Version Latest
$ErrorActionPreference = "Stop"

# Check PowerShell version compatibility
if ($PSVersionTable.PSVersion.Major -lt 5) {
    Write-Host "Note: Running on PowerShell version $($PSVersionTable.PSVersion)" -ForegroundColor Yellow
    Write-Host "Some features may be limited, but the script will attempt to continue." -ForegroundColor Yellow
}

# Log functions using Write-Host with colors
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if required tools are available
function Test-Dependencies {
    # Check if we can work with JSON (PowerShell 3.0+)
    try {
        $testJson = '{"test": "value"}' | ConvertFrom-Json
        return $true
    } catch {
        Write-Error "PowerShell JSON support is not available. Please upgrade to PowerShell 3.0 or later."
        return $false
    }
}

# Get storage.json path for Windows
function Get-StoragePath {
    $appDataPath = $env:APPDATA
    if (-not $appDataPath) {
        Write-Error "APPDATA environment variable not found"
        return $null
    }
    
    $storagePath = Join-Path $appDataPath "Code\User\globalStorage\storage.json"
    
    # Check if file exists
    if (-not (Test-Path $storagePath)) {
        # Try VS Code Insiders
        $storagePathInsiders = Join-Path $appDataPath "Code - Insiders\User\globalStorage\storage.json"
        if (Test-Path $storagePathInsiders) {
            return $storagePathInsiders
        }
        
        # Try Cursor
        $storagePathCursor = Join-Path $appDataPath "Cursor\User\globalStorage\storage.json"
        if (Test-Path $storagePathCursor) {
            return $storagePathCursor
        }
        
        Write-Error "Storage file not found at: $storagePath"
        Write-Info "Also checked:"
        Write-Info "  - $storagePathInsiders"
        Write-Info "  - $storagePathCursor"
        return $null
    }
    
    return $storagePath
}

# Generate a random 64-character hex string for machineId
function New-MachineId {
    $bytes = New-Object byte[] 32
    $rng = [System.Security.Cryptography.RNGCryptoServiceProvider]::Create()
    $rng.GetBytes($bytes)
    $rng.Dispose()
    
    $hexString = ""
    foreach ($byte in $bytes) {
        $hexString += $byte.ToString("x2")
    }
    
    return $hexString
}

# Generate a random UUID v4 for devDeviceId
function New-DeviceId {
    # Use .NET Guid class to generate a proper UUID
    $guid = [System.Guid]::NewGuid()
    return $guid.ToString().ToLower()
}

# Modify the storage.json file
function Update-StorageFile {
    param(
        [string]$StoragePath,
        [string]$MachineId,
        [string]$DeviceId
    )
    
    Write-Info "Modifying storage file at: $StoragePath"
    
    # Create backup
    $backupPath = "$StoragePath.backup"
    try {
        Copy-Item $StoragePath $backupPath -Force
        Write-Success "Created backup at: $backupPath"
    } catch {
        Write-Error "Failed to create backup: $($_.Exception.Message)"
        return $false
    }
    
    # Read the current file
    try {
        $content = Get-Content $StoragePath -Raw -Encoding UTF8
    } catch {
        Write-Error "Failed to read storage file: $($_.Exception.Message)"
        return $false
    }
    
    # Parse JSON
    try {
        $jsonObject = $content | ConvertFrom-Json
    } catch {
        Write-Error "The storage file is not valid JSON: $($_.Exception.Message)"
        return $false
    }
    
    # Update the values
    try {
        # Convert to hashtable for easier manipulation
        $jsonHash = @{}
        $jsonObject.PSObject.Properties | ForEach-Object {
            $jsonHash[$_.Name] = $_.Value
        }
        
        # Update telemetry IDs
        $jsonHash["telemetry.machineId"] = $MachineId
        $jsonHash["telemetry.devDeviceId"] = $DeviceId
        
        # Convert back to JSON
        $updatedContent = $jsonHash | ConvertTo-Json -Depth 10
        
        # Write the updated content back to the file
        Set-Content -Path $StoragePath -Value $updatedContent -Encoding UTF8
        
        Write-Success "Successfully updated telemetry IDs"
        Write-Info "New machineId: $MachineId"
        Write-Info "New devDeviceId: $DeviceId"
        
        return $true
    } catch {
        Write-Error "Failed to update storage file: $($_.Exception.Message)"
        return $false
    }
}

# Main function
function Start-TelemetryModification {
    Write-Info "Starting VS Code telemetry ID modification"
    
    # Check dependencies
    if (-not (Test-Dependencies)) {
        exit 1
    }
    
    # Get storage.json path
    $storagePath = Get-StoragePath
    if (-not $storagePath) {
        exit 1
    }
    
    Write-Info "Found storage file at: $storagePath"
    
    # Generate new IDs
    Write-Info "Generating new telemetry IDs..."
    $machineId = New-MachineId
    $deviceId = New-DeviceId
    
    # Modify the file
    if (Update-StorageFile -StoragePath $storagePath -MachineId $machineId -DeviceId $deviceId) {
        Write-Success "VS Code telemetry IDs have been successfully modified"
        Write-Info "You may need to restart VS Code for changes to take effect"
    } else {
        Write-Error "Failed to modify telemetry IDs"
        exit 1
    }
}

# Execute main function
Start-TelemetryModification
