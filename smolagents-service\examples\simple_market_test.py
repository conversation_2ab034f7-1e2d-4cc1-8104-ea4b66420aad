#!/usr/bin/env python3
"""
简化版市场分析测试
用于调试和验证核心功能
"""

import asyncio
import httpx
from datetime import datetime


async def test_simple_market_search():
    """测试简单的市场搜索功能"""
    
    print("🔍 测试简单市场搜索功能")
    print("=" * 50)
    
    # 简单的搜索查询
    search_query = """
请使用网络搜索工具查找以下信息：

搜索查询: 沙滩包亚洲市场2024年销售数据
搜索目的: 获取沙滩包在亚洲市场的基本销售情况

请执行网络搜索，并提供：
1. 找到的关键信息和数据
2. 相关的数字、统计数据
3. 重要的市场洞察

请确保搜索真实的网络数据。
"""
    
    async with httpx.AsyncClient(timeout=120.0) as client:
        try:
            print("🌐 开始网络搜索...")
            start_time = datetime.now()
            
            data = {
                "query": search_query,
                "max_steps": 10,
                "use_cache": False,
                "enable_web_search": True,
                "timeout": 120
            }
            
            response = await client.post("http://localhost:8002/api/v1/agent/query", json=data)
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            print(f"⏱️  执行时间: {execution_time:.2f} 秒")
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    print("✅ 搜索成功!")
                    print("=" * 60)
                    print("🔍 搜索结果")
                    print("=" * 60)
                    
                    search_result = result.get('result', '')
                    print(search_result)
                    
                    return search_result
                else:
                    error_msg = result.get('error', '未知错误')
                    print(f"❌ 搜索失败: {error_msg}")
                    return None
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 搜索异常: {e}")
            return None


async def test_simple_analysis(search_data: str):
    """测试简单的分析功能"""
    
    print("\n📊 测试简单分析功能")
    print("=" * 50)
    
    # 简化的分析查询
    analysis_query = f"""
基于以下搜索数据，生成一个简短的市场分析摘要：

搜索数据:
{search_data[:1000]}

请生成一个包含以下内容的简短分析：
1. 市场现状（2-3句话）
2. 关键数据（如果有的话）
3. 主要趋势（1-2句话）
4. 简单结论（1句话）

请保持分析简洁明了，不超过200字。
"""
    
    async with httpx.AsyncClient(timeout=90.0) as client:
        try:
            print("🧠 开始分析...")
            start_time = datetime.now()
            
            data = {
                "query": analysis_query,
                "max_steps": 8,
                "use_cache": False,
                "enable_web_search": False,
                "timeout": 90
            }
            
            response = await client.post("http://localhost:8002/api/v1/agent/query", json=data)
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            print(f"⏱️  执行时间: {execution_time:.2f} 秒")
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    print("✅ 分析成功!")
                    print("=" * 60)
                    print("📈 分析结果")
                    print("=" * 60)
                    
                    analysis_result = result.get('result', '')
                    print(analysis_result)
                    
                    return analysis_result
                else:
                    error_msg = result.get('error', '未知错误')
                    print(f"❌ 分析失败: {error_msg}")
                    return None
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 分析异常: {e}")
            return None


async def test_service_status():
    """测试服务状态"""
    print("🏥 检查服务状态...")
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        try:
            # 健康检查
            response = await client.get("http://localhost:8002/api/v1/health")
            
            if response.status_code == 200:
                health_data = response.json()
                print("✅ 服务健康状态:")
                print(f"   服务: {health_data.get('service', 'unknown')}")
                print(f"   模型: {health_data.get('model', 'unknown')}")
                print(f"   工具: {health_data.get('tools', 'unknown')}")
                print(f"   Redis: {health_data.get('redis', 'unknown')}")
                return True
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 服务连接失败: {e}")
            return False


async def main():
    """主函数"""
    print("🌟 简化版市场分析测试")
    print("用于调试和验证核心功能")
    print("=" * 60)
    print()
    
    # 1. 检查服务状态
    if not await test_service_status():
        print("❌ 服务不可用，请检查 SmoLAgents 微服务")
        return
    
    print()
    
    # 2. 测试搜索功能
    search_result = await test_simple_market_search()
    
    if search_result:
        # 3. 测试分析功能
        analysis_result = await test_simple_analysis(search_result)
        
        if analysis_result:
            print("\n" + "=" * 60)
            print("🎉 所有测试完成!")
            print("✅ 搜索功能正常")
            print("✅ 分析功能正常")
        else:
            print("\n" + "=" * 60)
            print("⚠️  测试部分完成")
            print("✅ 搜索功能正常")
            print("❌ 分析功能异常")
    else:
        print("\n" + "=" * 60)
        print("❌ 测试失败")
        print("❌ 搜索功能异常")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
