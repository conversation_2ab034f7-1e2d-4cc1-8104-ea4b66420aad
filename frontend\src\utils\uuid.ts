/**
 * UUID生成工具函数
 * 使用现代浏览器原生API，避免外部依赖问题
 */

/**
 * 生成UUID v4
 * 优先使用原生crypto.randomUUID()，降级到手动实现
 */
export function generateUUID(): string {
  // 优先使用原生crypto.randomUUID()
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  
  // 降级方案：手动生成UUID v4
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 生成短UUID（用于显示）
 * @param length 长度，默认8位
 */
export function generateShortUUID(length: number = 8): string {
  const fullUUID = generateUUID();
  return fullUUID.replace(/-/g, '').substring(0, length);
}

/**
 * 验证UUID格式
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}
