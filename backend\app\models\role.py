"""
角色管理数据模型
定义角色和权限的数据库表结构
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class Role(Base):
    """
    角色数据模型
    定义系统中的各种角色
    """
    __tablename__ = "roles"
    
    # 主键ID
    id = Column(Integer, primary_key=True, index=True, comment="角色ID")
    
    # 角色名称（唯一）
    name = Column(
        String(50), 
        unique=True, 
        nullable=False, 
        index=True, 
        comment="角色名称"
    )
    
    # 角色描述
    description = Column(Text, nullable=True, comment="角色描述")
    
    # 是否激活
    is_active = Column(Boolean, default=True, comment="是否激活")
    
    # 创建时间
    created_at = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        comment="创建时间"
    )
    
    # 关联用户角色
    user_roles = relationship("UserRole", back_populates="role", cascade="all, delete-orphan")
    
    # 关联角色权限
    role_permissions = relationship("RolePermission", back_populates="role", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}')>"


class Permission(Base):
    """
    权限数据模型
    定义系统中的各种权限
    """
    __tablename__ = "permissions"
    
    # 主键ID
    id = Column(Integer, primary_key=True, index=True, comment="权限ID")
    
    # 权限名称（唯一）
    name = Column(
        String(100), 
        unique=True, 
        nullable=False, 
        index=True, 
        comment="权限名称"
    )
    
    # 权限描述
    description = Column(Text, nullable=True, comment="权限描述")
    
    # 权限分类
    category = Column(String(50), nullable=True, index=True, comment="权限分类")
    
    # 创建时间
    created_at = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        comment="创建时间"
    )
    
    # 关联角色权限
    role_permissions = relationship("RolePermission", back_populates="permission", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Permission(id={self.id}, name='{self.name}', category='{self.category}')>"
