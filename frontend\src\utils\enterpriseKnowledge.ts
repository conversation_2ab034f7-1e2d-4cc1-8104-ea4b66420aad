/**
 * 企业知识库工具函数
 * 提供元数据处理、筛选条件、验证等功能
 */

// 企业知识库分类类型
export type EnterpriseCategory = 'enterprise' | 'product' | 'industry';

// 企业知识库元数据接口
export interface EnterpriseDocumentMetadata {
  source: 'enterprise-knowledge';
  enterprise_category: EnterpriseCategory;
  category_display_name: string;
  created_by?: string;
  department?: string;
  tags?: string[];
  version?: string;
  [key: string]: any;
}

// 分类配置
export const enterpriseCategories = [
  {
    id: 'enterprise' as EnterpriseCategory,
    title: '企业知识',
    description: '公司内部知识和资料'
  },
  {
    id: 'product' as EnterpriseCategory,
    title: '产品知识',
    description: '产品相关信息和文档'
  },
  {
    id: 'industry' as EnterpriseCategory,
    title: '产业知识',
    description: '行业趋势和市场信息'
  }
];

/**
 * 创建企业知识库上传元数据
 */
export function createUploadMetadata(
  category: EnterpriseCategory,
  title?: string,
  additionalMetadata?: Record<string, any>
): EnterpriseDocumentMetadata {
  const categoryInfo = enterpriseCategories.find(c => c.id === category);
  
  return {
    source: 'enterprise-knowledge',
    enterprise_category: category,
    category_display_name: categoryInfo?.title || category,
    title,
    created_at: new Date().toISOString(),
    ...additionalMetadata
  };
}

/**
 * 验证企业知识库元数据
 */
export function validateEnterpriseMetadata(metadata: any): metadata is EnterpriseDocumentMetadata {
  return (
    metadata &&
    metadata.source === 'enterprise-knowledge' &&
    typeof metadata.enterprise_category === 'string' &&
    enterpriseCategories.some(c => c.id === metadata.enterprise_category)
  );
}

/**
 * 为搜索请求添加企业知识库筛选条件
 */
export function addEnterpriseFilters(
  request: any,
  category: EnterpriseCategory
): any {
  return {
    ...request,
    filters: {
      ...request.filters,
      'metadata.source': 'enterprise-knowledge',
      'metadata.enterprise_category': category
    }
  };
}

/**
 * 为RAG查询添加企业知识库筛选条件
 */
export function addEnterpriseRAGFilters(
  request: any,
  category: EnterpriseCategory
): any {
  return {
    ...request,
    filters: {
      ...request.filters,
      'metadata.source': 'enterprise-knowledge',
      'metadata.enterprise_category': category
    }
  };
}

/**
 * 统计各分类的文档数量
 */
export function countDocumentsByCategory(documents: any[]): Record<EnterpriseCategory, number> {
  const counts: Record<EnterpriseCategory, number> = {
    enterprise: 0,
    product: 0,
    industry: 0
  };

  documents.forEach(doc => {
    if (validateEnterpriseMetadata(doc.metadata)) {
      counts[doc.metadata.enterprise_category]++;
    }
  });

  return counts;
}

/**
 * 过滤企业知识库文档
 */
export function filterEnterpriseDocuments(
  documents: any[],
  category?: EnterpriseCategory
): any[] {
  return documents.filter(doc => {
    if (!validateEnterpriseMetadata(doc.metadata)) {
      return false;
    }
    
    if (category && doc.metadata.enterprise_category !== category) {
      return false;
    }
    
    return true;
  });
}

/**
 * 获取分类显示名称
 */
export function getCategoryDisplayName(category: EnterpriseCategory): string {
  const categoryInfo = enterpriseCategories.find(c => c.id === category);
  return categoryInfo?.title || category;
}
