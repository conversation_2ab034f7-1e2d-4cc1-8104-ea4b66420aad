/**
 * 话术模板编辑器组件
 * 提供模板的创建和编辑功能
 */

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Save,
  X,
  Plus,
  Trash2,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { toast } from 'sonner';
import templateService from '@/services/templateService';
import { Template, PromptOverrides, TEMPLATE_CATEGORIES, RESPONSE_STYLES } from '@/types/template';

interface TemplateEditorProps {
  template?: Template | null;
  open: boolean;
  onClose: () => void;
  onSave: () => void;
}

const TemplateEditor: React.FC<TemplateEditorProps> = ({
  template,
  open,
  onClose,
  onSave
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'general',
    tags: [] as string[],
    role: {
      id: 'default_role',
      name: '智能助手',
      description: '专业的AI助手角色',
      icon: '🤖',
      category: 'general' as const,
      system_prompt: '你是一个专业、友好的AI助手。请根据用户的问题提供准确、有用的回答。',
      style: 'professional',
      temperature: 0.7,
      max_tokens: 2000
    },
    style: {
      id: 'default_style',
      name: '详细回答',
      description: '提供详细、全面的回答',
      template: '{response}',
      category: 'detailed' as const
    },
    prompt_overrides: {
      query: {
        prompt_template: '请根据以下上下文回答用户的问题。\n\n上下文：{context}\n\n用户问题：{question}\n\n请提供准确、有用的回答。'
      }
    } as PromptOverrides,
    custom_instructions: ''
  });

  const [newTag, setNewTag] = useState('');
  const [validation, setValidation] = useState<any>(null);
  const [saving, setSaving] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        description: template.description,
        category: template.category,
        tags: [...template.tags],
        role: { ...template.role } as any,
        style: { ...template.style } as any,
        prompt_overrides: { ...template.prompt_overrides } as PromptOverrides,
        custom_instructions: template.custom_instructions || ''
      });
    } else {
      // 重置为默认值
      setFormData({
        name: '',
        description: '',
        category: TEMPLATE_CATEGORIES.GENERAL,
        tags: [],
        role: {
          id: 'new_role',
          name: '',
          description: '',
          icon: '🤖',
          category: 'general',
          system_prompt: '',
          style: 'professional',
          temperature: 0.7,
          max_tokens: 500
        },
        style: {
          id: 'new_style',
          name: '',
          description: '',
          template: '',
          category: 'detailed'
        },
        prompt_overrides: {
          query: {
            prompt_template: ''
          }
        } as PromptOverrides,
        custom_instructions: ''
      });
    }
  }, [template, open]);

  // 实时验证
  useEffect(() => {
    if (formData.name || formData.prompt_overrides.query.prompt_template) {
      validateTemplate();
    }
  }, [formData]);

  const validateTemplate = () => {
    const mockTemplate = {
      ...formData,
      id: 'temp',
      version: '1.0.0',
      author: 'User',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      usage_count: 0,
      rating: 5,
      is_active: false,
      is_default: false,
      role: {
        ...formData.role,
        id: 'temp_role'
      },
      style: {
        ...formData.style,
        id: 'temp_style'
      }
    } as Template;

    const result = templateService.validateTemplate(mockTemplate);
    setValidation(result);
  };

  const handleSave = async () => {
    if (!validation?.is_valid) {
      toast.error('请修复验证错误后再保存');
      return;
    }

    setSaving(true);
    try {
      const templateData = {
        ...formData,
        version: template?.version || '1.0.0',
        author: template?.author || 'User',
        rating: template?.rating || 5,
        is_active: template?.is_active || false,
        is_default: template?.is_default || false,
        role: {
          ...formData.role,
          id: template?.role.id || `role_${Date.now()}`
        },
        style: {
          ...formData.style,
          id: template?.style.id || `style_${Date.now()}`
        }
      };

      if (template) {
        await templateService.updateTemplate(template.id, templateData);
        toast.success('模板更新成功');
      } else {
        await templateService.createTemplate(templateData);
        toast.success('模板创建成功');
      }

      onSave();
    } catch (error) {
      console.error('保存模板失败:', error);
      toast.error('保存模板失败');
    } finally {
      setSaving(false);
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const updateField = (path: string, value: any) => {
    setFormData(prev => {
      const newData = { ...prev };
      const keys = path.split('.');
      let current: any = newData;

      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newData;
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose} modal={true}>
      <DialogContent
        className="max-w-4xl max-h-[90vh] overflow-y-auto"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>
            {template ? '编辑模板' : '创建新模板'}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="role">角色设定</TabsTrigger>
            <TabsTrigger value="prompt">提示词</TabsTrigger>
            <TabsTrigger value="validation">验证</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">模板名称 *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => updateField('name', e.target.value)}
                  placeholder="输入模板名称"
                />
              </div>
              <div>
                <Label htmlFor="category">类别 *</Label>
                <Select value={formData.category} onValueChange={(value) => updateField('category', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(TEMPLATE_CATEGORIES).map(cat => (
                      <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">描述 *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => updateField('description', e.target.value)}
                placeholder="描述模板的用途和特点"
                rows={3}
              />
            </div>

            <div>
              <Label>标签</Label>
              <div className="flex gap-2 mb-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="添加标签"
                  onKeyPress={(e) => e.key === 'Enter' && addTag()}
                />
                <Button type="button" onClick={addTag}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {formData.tags.map(tag => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="role" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="role-name">角色名称 *</Label>
                <Input
                  id="role-name"
                  value={formData.role.name}
                  onChange={(e) => updateField('role.name', e.target.value)}
                  placeholder="如：客服代表、销售顾问"
                />
              </div>
              <div>
                <Label htmlFor="role-icon">角色图标</Label>
                <Input
                  id="role-icon"
                  value={formData.role.icon}
                  onChange={(e) => updateField('role.icon', e.target.value)}
                  placeholder="🤖"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="role-description">角色描述</Label>
              <Textarea
                id="role-description"
                value={formData.role.description}
                onChange={(e) => updateField('role.description', e.target.value)}
                placeholder="描述角色的特点和职责"
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="system-prompt">系统提示词 *</Label>
              <Textarea
                id="system-prompt"
                value={formData.role.system_prompt}
                onChange={(e) => updateField('role.system_prompt', e.target.value)}
                placeholder="定义AI的角色、性格和行为方式"
                rows={4}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="temperature">温度 (0-1)</Label>
                <Input
                  id="temperature"
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  value={formData.role.temperature}
                  onChange={(e) => updateField('role.temperature', parseFloat(e.target.value))}
                />
              </div>
              <div>
                <Label htmlFor="max-tokens">最大令牌数</Label>
                <Input
                  id="max-tokens"
                  type="number"
                  min="100"
                  max="2000"
                  value={formData.role.max_tokens}
                  onChange={(e) => updateField('role.max_tokens', parseInt(e.target.value))}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="prompt" className="space-y-4">
            <div>
              <Label htmlFor="prompt-template">查询提示词模板 *</Label>
              <Textarea
                id="prompt-template"
                value={formData.prompt_overrides.query.prompt_template}
                onChange={(e) => updateField('prompt_overrides.query.prompt_template', e.target.value)}
                placeholder="必须包含 {question} 和 {context} 占位符"
                rows={10}
                className="font-mono text-sm"
              />
              <div className="text-xs text-muted-foreground mt-1">
                必需占位符：{'{question}'} - 用户问题，{'{context}'} - 检索到的相关信息
              </div>
            </div>

            <div>
              <Label htmlFor="custom-instructions">自定义指令（可选）</Label>
              <Textarea
                id="custom-instructions"
                value={formData.custom_instructions}
                onChange={(e) => updateField('custom_instructions', e.target.value)}
                placeholder="额外的指令或要求"
                rows={3}
              />
            </div>
          </TabsContent>

          <TabsContent value="validation" className="space-y-4">
            {validation && (
              <div className="space-y-4">
                {/* 验证状态 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {validation.is_valid ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-500" />
                      )}
                      验证结果
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className={validation.is_valid ? 'text-green-600' : 'text-red-600'}>
                      {validation.is_valid ? '模板验证通过，可以保存' : '模板存在错误，请修复后保存'}
                    </p>
                  </CardContent>
                </Card>

                {/* 错误信息 */}
                {validation.errors.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-red-600">
                        <AlertCircle className="h-5 w-5" />
                        错误
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="list-disc list-inside space-y-1">
                        {validation.errors.map((error: string, index: number) => (
                          <li key={index} className="text-red-600">{error}</li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}

                {/* 警告信息 */}
                {validation.warnings.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-yellow-600">
                        <AlertCircle className="h-5 w-5" />
                        警告
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="list-disc list-inside space-y-1">
                        {validation.warnings.map((warning: string, index: number) => (
                          <li key={index} className="text-yellow-600">{warning}</li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}

                {/* 建议信息 */}
                {validation.suggestions.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-blue-600">
                        <Info className="h-5 w-5" />
                        建议
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="list-disc list-inside space-y-1">
                        {validation.suggestions.map((suggestion: string, index: number) => (
                          <li key={index} className="text-blue-600">{suggestion}</li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button
            onClick={handleSave}
            disabled={!validation?.is_valid || saving}
          >
            {saving ? (
              <>保存中...</>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                保存
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TemplateEditor;
