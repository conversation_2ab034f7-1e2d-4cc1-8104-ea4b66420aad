/**
 * 删除用户确认对话框组件
 */
import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, Trash2, AlertTriangle, User, Shield, Check, Mail, Calendar } from 'lucide-react'
import { useAuthStore } from '@/store/auth'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface AdminUser {
  id: number
  username: string
  email: string
  full_name?: string
  phone?: string
  is_active: boolean
  is_superuser: boolean
  created_at: string
  roles: string[]
}

interface UserDependencies {
  user_id: number
  username: string
  email: string
  is_superuser: boolean
  is_active: boolean
  dependencies: {
    role_count: number
    is_superuser: boolean
    can_delete: boolean
  }
}

interface DeleteUserConfirmDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
  user: AdminUser | null
}

export const DeleteUserConfirmDialog: React.FC<DeleteUserConfirmDialogProps> = ({
  open,
  onOpenChange,
  onSuccess,
  user,
}) => {
  const { token, user: currentUser } = useAuthStore()

  // UI状态
  const [isDeleting, setIsDeleting] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [dependencies, setDependencies] = useState<UserDependencies | null>(null)

  // 获取用户依赖关系
  const fetchUserDependencies = async () => {
    if (!token || !user) return

    setIsLoading(true)
    try {
      const response = await fetch(`${API_BASE_URL}/admin/users/${user.id}/dependencies`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        setDependencies(data)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '获取用户信息失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 对话框打开时获取用户依赖关系
  useEffect(() => {
    if (open && user) {
      fetchUserDependencies()
    }
  }, [open, user, token])

  // 检查是否可以删除
  const canDelete = dependencies?.dependencies.can_delete && user?.id !== currentUser?.id

  // 删除用户
  const handleDelete = async () => {
    if (!token || !user || !canDelete) return

    setIsDeleting(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch(`${API_BASE_URL}/admin/users/${user.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const result = await response.json()
        setSuccess(`用户删除成功，同时删除了 ${result.deleted_roles} 个角色关联`)
        onSuccess()
        setTimeout(() => {
          handleClose()
        }, 1500)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '删除用户失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsDeleting(false)
    }
  }

  // 关闭对话框
  const handleClose = () => {
    setError('')
    setSuccess('')
    setDependencies(null)
    onOpenChange(false)
  }

  // 获取用户显示名称的首字母
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (!user) return null

  const displayName = user.full_name || user.username
  const initials = getInitials(displayName)

  return (
    <Dialog open={open} onOpenChange={(open) => open ? null : handleClose()} modal={true}>
      <DialogContent
        className="max-w-2xl"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2 text-destructive">
            <Trash2 className="h-5 w-5" />
            <span>删除用户确认</span>
          </DialogTitle>
          <DialogDescription>
            此操作不可撤销，请仔细确认要删除的用户信息
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* 系统用户警告 */}
          {user.is_superuser && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>超级用户不能删除！</strong>
                <br />
                {user.username} 是超级用户，删除可能导致系统功能异常。
              </AlertDescription>
            </Alert>
          )}

          {/* 自己账户警告 */}
          {user.id === currentUser?.id && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>不能删除自己的账户！</strong>
                <br />
                您不能删除当前登录的账户。
              </AlertDescription>
            </Alert>
          )}

          {/* 用户信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>用户信息</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start space-x-4">
                <Avatar className="h-16 w-16">
                  <AvatarFallback className="text-lg">{initials}</AvatarFallback>
                </Avatar>

                <div className="flex-1 space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">用户名</p>
                      <p className="font-medium">{user.username}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">状态</p>
                      <div className="flex space-x-2">
                        <Badge variant={user.is_active ? "default" : "secondary"}>
                          {user.is_active ? "激活" : "禁用"}
                        </Badge>
                        {user.is_superuser && (
                          <Badge variant="destructive">超级用户</Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground flex items-center">
                        <Mail className="h-3 w-3 mr-1" />
                        邮箱
                      </p>
                      <p className="text-sm">{user.email}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        创建时间
                      </p>
                      <p className="text-sm">{new Date(user.created_at).toLocaleString()}</p>
                    </div>
                  </div>

                  {user.full_name && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">全名</p>
                      <p className="text-sm">{user.full_name}</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 影响范围 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>影响范围</span>
              </CardTitle>
              <CardDescription>
                删除此用户将影响以下内容
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {isLoading ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span>加载用户信息中...</span>
                </div>
              ) : dependencies ? (
                <>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">角色关联</p>
                      <p className="text-sm text-muted-foreground">
                        用户拥有的角色数量
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge variant={dependencies.dependencies.role_count > 0 ? "default" : "secondary"}>
                        {dependencies.dependencies.role_count} 个角色
                      </Badge>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">用户权限</p>
                      <p className="text-sm text-muted-foreground">
                        通过角色获得的权限
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge variant="secondary">
                        {user.roles.length} 个角色权限
                      </Badge>
                    </div>
                  </div>

                  {dependencies.dependencies.role_count > 0 && (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        删除此用户将同时移除 {dependencies.dependencies.role_count} 个角色关联。
                      </AlertDescription>
                    </Alert>
                  )}
                </>
              ) : null}
            </CardContent>
          </Card>

          {/* 用户角色详情 */}
          {user.roles.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">用户角色</CardTitle>
                <CardDescription>
                  此用户拥有的所有角色
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {user.roles.map((role) => (
                    <Badge key={role} variant="outline" className="text-sm">
                      {role}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 错误和成功消息 */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <Check className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isDeleting}
          >
            取消
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting || !canDelete}
          >
            {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            确认删除
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
