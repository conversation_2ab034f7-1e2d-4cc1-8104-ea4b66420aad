"""
FastAPI 主应用文件
提供API服务的入口点，配置CORS、中间件和路由
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.database import init_db
from app.routers import health, tasks, auth, profile, admin, sales_training


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    await init_db()
    yield
    # 关闭时的清理工作（如果需要）


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="ZHT System 后端API服务 - 演示完整的数据库CRUD操作",
    version="1.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan
)

# 配置CORS中间件，允许前端跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)

# 注册路由
app.include_router(health.router, prefix=settings.API_V1_STR, tags=["健康检查"])
app.include_router(tasks.router, prefix=f"{settings.API_V1_STR}/tasks", tags=["任务管理"])
app.include_router(auth.router, prefix=f"{settings.API_V1_STR}/auth", tags=["用户认证"])
app.include_router(profile.router, prefix=f"{settings.API_V1_STR}/auth", tags=["用户资料"])
app.include_router(admin.router, prefix=f"{settings.API_V1_STR}/admin", tags=["管理员"])
app.include_router(sales_training.router, tags=["销冠实战训练"])

# 静态文件服务
import os
upload_dir = "/tmp/uploads"
os.makedirs(upload_dir, exist_ok=True)
app.mount("/uploads", StaticFiles(directory=upload_dir), name="uploads")


@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "ZHT System API 服务",
        "version": "1.0.0",
        "docs_url": "/docs",
        "features": [
            "任务管理CRUD操作",
            "异步SQLAlchemy 2.0",
            "PostgreSQL数据库",
            "Pydantic数据验证",
            "自动API文档"
        ]
    }
