"""
销冠实战训练相关的Pydantic模式
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, validator, ConfigDict


# 客户相关模式
class SalesTrainingCustomerBase(BaseModel):
    """客户基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="客户姓名")
    company: Optional[str] = Field(None, max_length=200, description="公司名称")
    phone: Optional[str] = Field(None, max_length=20, description="联系电话")
    email: Optional[str] = Field(None, max_length=100, description="邮箱地址")
    industry: Optional[str] = Field(None, max_length=100, description="行业分类")
    region: Optional[str] = Field(None, max_length=100, description="地区信息")
    country_code: Optional[str] = Field(None, max_length=10, description="国家代码")

    training_scenario: Optional[str] = Field(None, max_length=100, description="训练场景")
    notes: Optional[str] = Field(None, description="备注信息")

    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('客户姓名不能为空')
        return v.strip()

    @validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('邮箱格式不正确')
        return v.strip() if v else v

    @validator('phone')
    def validate_phone(cls, v):
        if v and not v.replace('-', '').replace(' ', '').replace('+', '').isdigit():
            raise ValueError('电话号码格式不正确')
        return v.strip() if v else v


class SalesTrainingCustomerCreate(SalesTrainingCustomerBase):
    """创建客户模式"""
    pass


class SalesTrainingCustomerUpdate(BaseModel):
    """更新客户模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    company: Optional[str] = Field(None, max_length=200)
    phone: Optional[str] = Field(None, max_length=20)
    email: Optional[str] = Field(None, max_length=100)
    industry: Optional[str] = Field(None, max_length=100)
    region: Optional[str] = Field(None, max_length=100)
    country_code: Optional[str] = Field(None, max_length=10)

    training_scenario: Optional[str] = Field(None, max_length=100)
    notes: Optional[str] = None
    is_active: Optional[bool] = None

    @validator('name')
    def validate_name(cls, v):
        if v is not None and not v.strip():
            raise ValueError('客户姓名不能为空')
        return v.strip() if v else v

    @validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            raise ValueError('邮箱格式不正确')
        return v.strip() if v else v

    @validator('phone')
    def validate_phone(cls, v):
        if v and not v.replace('-', '').replace(' ', '').replace('+', '').isdigit():
            raise ValueError('电话号码格式不正确')
        return v.strip() if v else v


class SalesTrainingCustomer(SalesTrainingCustomerBase):
    """客户响应模式"""
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 国家相关模式
class SalesTrainingCountryBase(BaseModel):
    """国家基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="国家名称")
    code: str = Field(..., min_length=1, max_length=10, description="国家代码")
    flag: Optional[str] = Field(None, max_length=20, description="国旗emoji")

    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('国家名称不能为空')
        return v.strip()

    @validator('code')
    def validate_code(cls, v):
        if not v.strip():
            raise ValueError('国家代码不能为空')
        return v.strip().upper()


class SalesTrainingCountryCreate(SalesTrainingCountryBase):
    """创建国家模式"""
    pass


class SalesTrainingCountryUpdate(BaseModel):
    """更新国家模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    code: Optional[str] = Field(None, min_length=1, max_length=10)
    flag: Optional[str] = Field(None, max_length=20)
    is_active: Optional[bool] = None

    @validator('name')
    def validate_name(cls, v):
        if v is not None and not v.strip():
            raise ValueError('国家名称不能为空')
        return v.strip() if v else v

    @validator('code')
    def validate_code(cls, v):
        if v is not None and not v.strip():
            raise ValueError('国家代码不能为空')
        return v.strip().upper() if v else v


class SalesTrainingCountry(SalesTrainingCountryBase):
    """国家响应模式"""
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 产品相关模式
class SalesTrainingProductBase(BaseModel):
    """产品基础模式"""
    name: str = Field(..., min_length=1, max_length=200, description="产品名称")
    category: str = Field(..., min_length=1, max_length=100, description="产品分类")
    description: Optional[str] = Field(None, description="产品描述")

    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('产品名称不能为空')
        return v.strip()

    @validator('category')
    def validate_category(cls, v):
        if not v.strip():
            raise ValueError('产品分类不能为空')
        return v.strip()


class SalesTrainingProductCreate(SalesTrainingProductBase):
    """创建产品模式"""
    pass


class SalesTrainingProductUpdate(BaseModel):
    """更新产品模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    category: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    is_active: Optional[bool] = None

    @validator('name')
    def validate_name(cls, v):
        if v is not None and not v.strip():
            raise ValueError('产品名称不能为空')
        return v.strip() if v else v

    @validator('category')
    def validate_category(cls, v):
        if v is not None and not v.strip():
            raise ValueError('产品分类不能为空')
        return v.strip() if v else v


class SalesTrainingProduct(SalesTrainingProductBase):
    """产品响应模式"""
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True




# 训练场景相关模式
class SalesTrainingScenarioBase(BaseModel):
    """训练场景基础模式"""
    name: str = Field(..., min_length=1, max_length=100, description="场景名称")
    description: Optional[str] = Field(None, description="场景描述")
    category: str = Field(..., max_length=50, description="场景分类")
    difficulty: str = Field(default='intermediate', description="难度等级")
    duration: Optional[int] = Field(None, description="预计时长(分钟)")
    objectives: Optional[str] = Field(None, description="训练目标(JSON格式)")
    challenges: Optional[str] = Field(None, description="挑战要点(JSON格式)")
    tags: Optional[str] = Field(None, description="标签(JSON格式)")
    icon: Optional[str] = Field(None, max_length=10, description="图标emoji")
    color: Optional[str] = Field(None, max_length=20, description="颜色样式")

    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('场景名称不能为空')
        return v.strip()

    @validator('difficulty')
    def validate_difficulty(cls, v):
        allowed_difficulties = ['beginner', 'intermediate', 'advanced', 'expert']
        if v not in allowed_difficulties:
            raise ValueError(f'难度等级必须是以下之一: {", ".join(allowed_difficulties)}')
        return v


class SalesTrainingScenarioCreate(SalesTrainingScenarioBase):
    """创建训练场景的请求模式"""
    pass


class SalesTrainingScenarioUpdate(BaseModel):
    """更新训练场景的请求模式"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="场景名称")
    description: Optional[str] = Field(None, description="场景描述")
    category: Optional[str] = Field(None, max_length=50, description="场景分类")
    difficulty: Optional[str] = Field(None, description="难度等级")
    duration: Optional[int] = Field(None, description="预计时长(分钟)")
    objectives: Optional[str] = Field(None, description="训练目标(JSON格式)")
    challenges: Optional[str] = Field(None, description="挑战要点(JSON格式)")
    tags: Optional[str] = Field(None, description="标签(JSON格式)")
    icon: Optional[str] = Field(None, max_length=10, description="图标emoji")
    color: Optional[str] = Field(None, max_length=20, description="颜色样式")
    is_active: Optional[bool] = Field(None, description="是否启用")

    @validator('name')
    def validate_name(cls, v):
        if v is not None and not v.strip():
            raise ValueError('场景名称不能为空')
        return v.strip() if v else v

    @validator('difficulty')
    def validate_difficulty(cls, v):
        if v is not None:
            allowed_difficulties = ['beginner', 'intermediate', 'advanced', 'expert']
            if v not in allowed_difficulties:
                raise ValueError(f'难度等级必须是以下之一: {", ".join(allowed_difficulties)}')
        return v


class SalesTrainingScenario(SalesTrainingScenarioBase):
    """训练场景响应模式"""
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(
        from_attributes = True
    )


# 通用响应模式
class SalesTrainingResponse(BaseModel):
    """通用响应模式"""
    success: bool
    message: Optional[str] = None
    data: Optional[dict] = None
