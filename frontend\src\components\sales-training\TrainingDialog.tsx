/**
 * 训练对话弹窗组件
 * 提供完整的销售训练对话界面
 */

import { useState, useRef, useEffect } from 'react'
import {
  X,
  Send,
  User,
  Bot,
  Globe,
  Package,
  Building2,
  Clock,
  Star,
  Lightbulb,
  MessageSquare,
  BarChart3,
  Maximize2,
  Minimize2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { EvaluationPanel } from './EvaluationPanel'
import { LearningTipsPanel } from './LearningTipsPanel'
import type { TrainingCustomer, ChatMessage, ChatSession } from '@/types/salesTraining'

interface TrainingDialogProps {
  customer: TrainingCustomer
  open: boolean
  onClose: () => void
}

export function TrainingDialog({ customer, open, onClose }: TrainingDialogProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showEvaluation, setShowEvaluation] = useState(true)
  const [showLearningTips, setShowLearningTips] = useState(true)
  const [sessionStartTime] = useState(new Date())
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 初始化对话
  useEffect(() => {
    if (open) {
      const welcomeMessage: ChatMessage = {
        id: '1',
        role: 'customer',
        content: `你好！我是来自${customer.country.name}的${customer.name}，我们公司对${customer.product.name}很感兴趣。能详细介绍一下吗？`,
        timestamp: new Date(),
        metadata: {
          type: 'text',
          confidence: 0.9
        }
      }
      setMessages([welcomeMessage])
    }
  }, [open, customer])

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // 发送消息
  const handleSendMessage = () => {
    if (!inputValue.trim()) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue,
      timestamp: new Date(),
      metadata: {
        type: 'text'
      }
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')

    // 模拟客户回复
    setTimeout(() => {
      const customerReplies = [
        '这个价格有点高，能不能再优惠一些？',
        '我们需要考虑一下质量保证的问题。',
        '交货期能保证吗？我们的项目时间很紧。',
        '你们的售后服务怎么样？',
        '能提供一些成功案例吗？',
        '我需要和我的团队讨论一下，稍后给你回复。'
      ]
      
      const reply = customerReplies[Math.floor(Math.random() * customerReplies.length)]
      const customerMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'customer',
        content: reply,
        timestamp: new Date(),
        metadata: {
          type: 'text',
          confidence: 0.8
        }
      }
      
      setMessages(prev => [...prev, customerMessage])
    }, 1000 + Math.random() * 2000)
  }

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // 计算会话时长
  const getSessionDuration = () => {
    const duration = Math.floor((Date.now() - sessionStartTime.getTime()) / 1000)
    const minutes = Math.floor(duration / 60)
    const seconds = duration % 60
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  return (
    <Dialog open={open} onOpenChange={(open) => open ? null : onClose()} modal={true}>
      <DialogContent 
        className={`${isFullscreen ? 'max-w-full h-full' : 'max-w-7xl h-[90vh]'} p-0 overflow-hidden [&>button]:hidden`}
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        {/* 头部工具栏 */}
        <div className={`relative flex items-center justify-between px-4 border-b bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm z-10 ${
          (showEvaluation || showLearningTips) ? 'py-3 min-h-[72px]' : 'py-2 min-h-[60px]'
        }`}
        style={{
          position: 'relative',
          zIndex: 999
        }}>
          <div className="flex items-center space-x-3 flex-1 min-w-0 pr-4">
            <div className="flex items-center space-x-2">
              <div className="relative flex-shrink-0">
                <Avatar className={`ring-1 ring-blue-200 dark:ring-blue-700 ${
                  (showEvaluation || showLearningTips) ? 'w-8 h-8' : 'w-7 h-7'
                }`}>
                  <AvatarFallback className="bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 text-blue-600 dark:text-blue-400 font-medium text-sm">
                    {customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-green-400 rounded-full border border-white dark:border-gray-800"></div>
              </div>
              <div className="min-w-0 flex-1 max-w-md">
                <h3 className={`font-semibold text-gray-900 dark:text-white truncate ${
                  (showEvaluation || showLearningTips) ? 'text-base' : 'text-sm'
                }`}>
                  与 {customer.name} 的销售训练
                </h3>
                <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                  <span className="text-sm flex-shrink-0">{customer.country.flag}</span>
                  <span className="flex-shrink-0 hidden sm:inline">{customer.country.name}</span>
                  <span className="flex-shrink-0 hidden sm:inline">·</span>
                  <span className="truncate hidden sm:inline">{customer.product.name}</span>
                </div>
              </div>
            </div>
            <Badge variant="secondary" className="bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-400 text-xs px-2 py-1 flex-shrink-0 hidden md:flex">
              <Clock className="w-3 h-3 mr-1" />
              {getSessionDuration()}
            </Badge>
          </div>

          {/* 右侧按钮区域 - 固定宽度 */}
          <div className="flex items-center space-x-2 flex-shrink-0" style={{minWidth: '200px', position: 'relative', zIndex: 1000}}>
            {(showEvaluation || showLearningTips) && (
              <div className="flex items-center space-x-2 text-xs hidden lg:flex">
                <div className="flex items-center space-x-1">
                  <Switch
                    id="learning-tips"
                    checked={showLearningTips}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setShowLearningTips(true);
                        setShowEvaluation(false);
                      } else {
                        setShowLearningTips(false);
                      }
                    }}
                    className="scale-75"
                  />
                  <Label htmlFor="learning-tips" className="text-xs font-medium text-gray-600 dark:text-gray-400">提示</Label>
                </div>
                <div className="flex items-center space-x-1">
                  <Switch
                    id="evaluation"
                    checked={showEvaluation}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setShowEvaluation(true);
                        setShowLearningTips(false);
                      } else {
                        setShowEvaluation(false);
                      }
                    }}
                    className="scale-75"
                  />
                  <Label htmlFor="evaluation" className="text-xs font-medium text-gray-600 dark:text-gray-400">评价</Label>
                </div>
                <Separator orientation="vertical" className="h-4" />
              </div>
            )}
            {!(showEvaluation || showLearningTips) && (
              <div className="flex items-center space-x-1 hidden lg:flex">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowLearningTips(true);
                    setShowEvaluation(false);
                  }}
                  className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  提示
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowEvaluation(true);
                    setShowLearningTips(false);
                  }}
                  className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  评价
                </Button>
                <Separator orientation="vertical" className="h-3" />
              </div>
            )}
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
                className={`p-0 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md border border-gray-300 dark:border-gray-600 ${
                  (showEvaluation || showLearningTips) ? 'h-8 w-8' : 'h-7 w-7'
                }`}
                title={isFullscreen ? "退出全屏" : "全屏显示"}
              >
                {isFullscreen ?
                  <Minimize2 className={`${(showEvaluation || showLearningTips) ? 'w-4 h-4' : 'w-3.5 h-3.5'}`} /> :
                  <Maximize2 className={`${(showEvaluation || showLearningTips) ? 'w-4 h-4' : 'w-3.5 h-3.5'}`} />
                }
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={onClose}
                className={`p-0 bg-red-500 hover:bg-red-600 text-white rounded-md shadow-sm font-bold ${
                  (showEvaluation || showLearningTips) ? 'h-8 w-8' : 'h-7 w-7'
                }`}
                title="关闭训练窗口"
              >
                <X className={`${(showEvaluation || showLearningTips) ? 'w-4 h-4 stroke-2' : 'w-3.5 h-3.5 stroke-2'}`} />
              </Button>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex flex-1 overflow-hidden">
          {/* 左侧：客户信息面板 */}
          <div className="w-72 border-r bg-gradient-to-b from-gray-50/80 to-white dark:from-gray-800/80 dark:to-gray-800 p-3 overflow-y-auto">
            <Card className="shadow-sm border-gray-200 dark:border-gray-700">
              <CardHeader className="pb-2 bg-gradient-to-r from-blue-50/50 to-white dark:from-blue-900/10 dark:to-gray-800">
                <CardTitle className="text-base flex items-center space-x-2">
                  <div className="p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-md">
                    <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <span>客户信息</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 p-3">
                <div className="flex items-center space-x-2 p-2 bg-gray-50/50 dark:bg-gray-800/50 rounded-md border border-gray-100 dark:border-gray-700">
                  <Avatar className="w-10 h-10 ring-1 ring-blue-200 dark:ring-blue-800 shadow-sm">
                    <AvatarFallback className="bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 text-blue-600 dark:text-blue-400 font-semibold text-sm">
                      {customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white text-sm">{customer.name}</h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400 font-medium">
                      {customer.background?.position}
                    </p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex items-center space-x-2 p-1.5 hover:bg-blue-50/50 dark:hover:bg-blue-900/10 rounded-md transition-colors duration-200">
                    <div className="p-1 bg-green-100 dark:bg-green-900/30 rounded">
                      <Globe className="w-3 h-3 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-sm animate-float">{customer.country.flag}</span>
                      <span className="text-xs font-medium">{customer.country.name}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 p-1.5 hover:bg-purple-50/50 dark:hover:bg-purple-900/10 rounded-md transition-colors duration-200">
                    <div className="p-1 bg-purple-100 dark:bg-purple-900/30 rounded">
                      <Package className="w-3 h-3 text-purple-600 dark:text-purple-400" />
                    </div>
                    <span className="text-xs font-medium">{customer.product.name}</span>
                  </div>
                  <div className="flex items-center space-x-2 p-1.5 hover:bg-orange-50/50 dark:hover:bg-orange-900/10 rounded-md transition-colors duration-200">
                    <div className="p-1 bg-orange-100 dark:bg-orange-900/30 rounded">
                      <Building2 className="w-3 h-3 text-orange-600 dark:text-orange-400" />
                    </div>
                    <span className="text-xs font-medium">{customer.background?.company}</span>
                  </div>
                </div>

                {customer.background?.preferences && (
                  <>
                    <Separator />
                    <div>
                      <h5 className="font-medium mb-2">客户偏好</h5>
                      <div className="flex flex-wrap gap-1">
                        {customer.background.preferences.map((pref, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {pref}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                <Separator />
                <div>
                  <h5 className="font-medium mb-2">经验背景</h5>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {customer.background?.experience}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 中间：对话窗口 */}
          <div className="flex-1 flex flex-col bg-gradient-to-b from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
            {/* 消息列表 */}
            <ScrollArea className="flex-1 p-6">
              <div className="space-y-4">
                {messages.map((message, index) => (
                  <div
                    key={message.id}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'} opacity-0 animate-fade-in-up`}
                    style={{animationDelay: `${index * 0.1}s`, animationFillMode: 'forwards'}}
                  >
                    <div
                      className={`max-w-[75%] rounded-xl p-4 shadow-md transition-all duration-200 hover:shadow-lg ${
                        message.role === 'user'
                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                          : message.role === 'customer'
                          ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600'
                          : 'bg-gradient-to-r from-yellow-100 to-yellow-200 dark:from-yellow-900/30 dark:to-yellow-800/30 text-yellow-800 dark:text-yellow-200 border border-yellow-300 dark:border-yellow-700'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        {message.role === 'customer' && (
                          <div className="p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                            <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                          </div>
                        )}
                        {message.role === 'system' && (
                          <div className="p-1.5 bg-yellow-100 dark:bg-yellow-900/30 rounded-full">
                            <Bot className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                          </div>
                        )}
                        <div className="flex-1">
                          <p className="text-sm leading-relaxed">{message.content}</p>
                          <p className="text-xs opacity-70 mt-2 flex items-center space-x-1">
                            <Clock className="w-3 h-3" />
                            <span>{message.timestamp.toLocaleTimeString()}</span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* 输入区域 */}
            <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-white dark:bg-gray-800">
              <div className="flex space-x-3">
                <Input
                  placeholder="输入您的回复..."
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1 h-12 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim()}
                  className="h-12 px-6 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Send className="w-4 h-4 mr-2" />
                  发送
                </Button>
              </div>
            </div>
          </div>

          {/* 右侧：评价和学习面板 */}
          {(showEvaluation || showLearningTips) && (
            <div className="w-72 border-l bg-gray-50/50 dark:bg-gray-800/50 overflow-y-auto">
              <div className="p-3 space-y-3">
                {showEvaluation && (
                  <EvaluationPanel messages={messages} customer={customer} />
                )}

                {showLearningTips && (
                  <LearningTipsPanel messages={messages} customer={customer} />
                )}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
