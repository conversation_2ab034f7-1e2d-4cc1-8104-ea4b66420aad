"""
健康检查API路由
提供系统健康状态检查
"""
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from app.core.database import get_db
from datetime import datetime

router = APIRouter()


@router.get("/health", summary="健康检查")
async def health_check(db: AsyncSession = Depends(get_db)):
    """
    系统健康检查
    
    检查API服务和数据库连接状态
    """
    try:
        # 测试数据库连接
        await db.execute(text("SELECT 1"))
        db_status = "healthy"
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"
    
    return {
        "status": "healthy" if db_status == "healthy" else "unhealthy",
        "timestamp": datetime.now().isoformat(),
        "service": "ZHT System API",
        "version": "1.0.0",
        "database": db_status
    }
