/**
 * 企业知识库上传对话框
 * 支持文件上传和文本创作功能
 */
import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Upload,
  FileText,
  Image,
  File,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  Plus
} from 'lucide-react';
import { useUploadDocument } from '@/hooks/useMorphik';
import { createUploadMetadata, type EnterpriseCategory } from '@/utils/enterpriseKnowledge';
import { toast } from 'sonner';

// 支持的文件类型
const SUPPORTED_FILE_TYPES = {
  'application/pdf': { icon: FileText, label: 'PDF', color: 'bg-red-100 text-red-800' },
  'application/msword': { icon: FileText, label: 'DOC', color: 'bg-blue-100 text-blue-800' },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { icon: FileText, label: 'DOCX', color: 'bg-blue-100 text-blue-800' },
  'text/plain': { icon: FileText, label: 'TXT', color: 'bg-gray-100 text-gray-800' },
  'text/markdown': { icon: FileText, label: 'MD', color: 'bg-gray-100 text-gray-800' },
  'image/jpeg': { icon: Image, label: 'JPG', color: 'bg-green-100 text-green-800' },
  'image/png': { icon: Image, label: 'PNG', color: 'bg-green-100 text-green-800' },
};

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

interface UploadFile {
  file: File;
  id: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  documentId?: string;
}

interface EnterpriseUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  category: EnterpriseCategory;
  onUploadComplete?: (documentIds: string[]) => void;
  defaultTab?: 'file' | 'text';
}

export function EnterpriseUploadDialog({
  open,
  onOpenChange,
  category,
  onUploadComplete,
  defaultTab = 'file'
}: EnterpriseUploadDialogProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [textContent, setTextContent] = useState('');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [tags, setTags] = useState('');
  const [activeTab, setActiveTab] = useState<'file' | 'text'>(defaultTab);

  const uploadDocument = useUploadDocument();

  // 当 defaultTab 变化时更新 activeTab
  useEffect(() => {
    setActiveTab(defaultTab);
  }, [defaultTab]);

  // 文档状态轮询
  useEffect(() => {
    const processingFiles = uploadFiles.filter(f => f.status === 'processing' && f.documentId);

    if (processingFiles.length === 0) {
      return;
    }

    const pollInterval = setInterval(async () => {
      try {
        // 检查每个处理中文档的状态
        const statusChecks = processingFiles.map(async (file) => {
          if (!file.documentId) return null;

          try {
            const response = await fetch(`http://localhost:8000/documents/${file.documentId}`);
            if (!response.ok) throw new Error('Failed to fetch document status');

            const doc = await response.json();
            const status = doc.system_metadata?.status || 'completed';

            return {
              fileId: file.id,
              documentId: file.documentId,
              status: status
            };
          } catch (error) {
            console.error(`Failed to check status for document ${file.documentId}:`, error);
            return null;
          }
        });

        const results = await Promise.all(statusChecks);

        // 更新文件状态
        setUploadFiles(prev => prev.map(f => {
          const result = results.find(r => r && r.fileId === f.id);
          if (result && result.status !== 'processing') {
            return {
              ...f,
              status: result.status === 'completed' ? 'completed' : 'error',
              error: result.status === 'failed' ? '处理失败' : undefined
            };
          }
          return f;
        }));

      } catch (error) {
        console.error('Error polling document status:', error);
      }
    }, 3000); // 每3秒检查一次

    return () => clearInterval(pollInterval);
  }, [uploadFiles]);

  // 文件拖拽处理
  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    console.log('onDrop called:', { acceptedFiles, rejectedFiles });

    // 处理被拒绝的文件
    rejectedFiles.forEach(({ file, errors }) => {
      console.warn(`File ${file.name} rejected:`, errors);
      toast.error(`文件 ${file.name} 不支持: ${errors.map((e: any) => e.message).join(', ')}`);
    });

    // 添加接受的文件到上传列表
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      file,
      id: `${file.name}-${Date.now()}-${Math.random()}`,
      status: 'pending',
      progress: 0,
    }));

    console.log('Adding new files:', newFiles);
    setUploadFiles(prev => {
      const updated = [...prev, ...newFiles];
      console.log('Updated uploadFiles:', updated);
      return updated;
    });
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: Object.keys(SUPPORTED_FILE_TYPES).reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize: MAX_FILE_SIZE,
    multiple: true,
    disabled: uploadFiles.some(f => f.status === 'uploading'),
  });

  // 移除文件
  const removeFile = (fileId: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // 获取文件图标和样式
  const getFileInfo = (file: File) => {
    const fileType = SUPPORTED_FILE_TYPES[file.type as keyof typeof SUPPORTED_FILE_TYPES];
    return fileType || { icon: File, label: 'FILE', color: 'bg-gray-100 text-gray-800' };
  };



  // 上传文本内容
  const uploadText = async () => {
    if (!textContent.trim()) {
      toast.error('请输入文本内容');
      return;
    }

    try {
      // 创建企业知识库元数据
      const metadata = createUploadMetadata(category, title || '文本文档', {
        description,
        tags: tags ? tags.split(',').map(t => t.trim()) : [],
        contentType: 'text/plain',
      });

      const response = await uploadDocument.mutateAsync({
        content: textContent,
        title: title || '文本文档',
        metadata,
      });

      toast.success('文本创作成功');

      if (onUploadComplete) {
        onUploadComplete([response.document_id]);
      }

      // 重置表单
      setTextContent('');
      setTitle('');
      setDescription('');
      setTags('');
      onOpenChange(false);
    } catch (error: any) {
      toast.error(`文本创作失败: ${error.message}`);
    }
  };

  // 批量上传文件
  const handleBatchUpload = async () => {
    const pendingFiles = uploadFiles.filter(f => f.status === 'pending');
    if (pendingFiles.length === 0) {
      toast.error('没有待上传的文件');
      return;
    }

    try {
      const documentIds: string[] = [];

      for (const uploadFile of pendingFiles) {
        // 创建企业知识库元数据
        const metadata = createUploadMetadata(category, title || uploadFile.file.name, {
          description,
          tags: tags ? tags.split(',').map(t => t.trim()) : [],
          filename: uploadFile.file.name,
          fileSize: uploadFile.file.size,
          fileType: uploadFile.file.type,
        });

        const response = await uploadDocument.mutateAsync({
          file: uploadFile.file,
          metadata,
        });

        documentIds.push(response.document_id);
      }

      toast.success(`成功上传 ${documentIds.length} 个文件`);

      if (onUploadComplete) {
        onUploadComplete(documentIds);
      }

      // 重置表单
      setUploadFiles([]);
      setTitle('');
      setDescription('');
      setTags('');
      onOpenChange(false);
    } catch (error: any) {
      toast.error(`批量上传失败: ${error.message}`);
    }
  };

  // 重置对话框
  const resetDialog = () => {
    setUploadFiles([]);
    setTextContent('');
    setTitle('');
    setDescription('');
    setTags('');
    setActiveTab(defaultTab);
  };

  // 关闭对话框时重置
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetDialog();
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange} modal={true}>
      <DialogContent
        className="sm:max-w-3xl max-h-[85vh] flex flex-col"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader className="space-y-4 pb-6 border-b border-gray-100 flex-shrink-0">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
              <FileText className="h-5 w-5 text-white" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold text-gray-900">企业知识库 - 添加内容</DialogTitle>
              <DialogDescription className="text-gray-600 mt-1">
                上传文件或创作文本内容到{category === 'enterprise' ? '企业知识' :
                                        category === 'product' ? '产品知识' : '产业知识'}分类
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto py-6 px-6 min-h-0">
          <div className="space-y-6">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'file' | 'text')}>
              <TabsList className="grid w-full grid-cols-2 bg-gray-100 p-1 rounded-xl">
                <TabsTrigger
                  value="file"
                  className="flex items-center gap-2 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all"
                >
                  <Upload className="h-4 w-4" />
                  上传文件
                </TabsTrigger>
                <TabsTrigger
                  value="text"
                  className="flex items-center gap-2 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all"
                >
                  <Plus className="h-4 w-4" />
                  创作文本
                </TabsTrigger>
              </TabsList>

            {/* 通用元数据字段 */}
            <div className="bg-gradient-to-br from-gray-50 to-gray-100/50 rounded-xl p-5 space-y-4 border border-gray-200">
              <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                基本信息
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title" className="text-sm font-medium text-gray-700">标题</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="输入标题（可选）"
                    className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tags" className="text-sm font-medium text-gray-700">标签</Label>
                  <Input
                    id="tags"
                    value={tags}
                    onChange={(e) => setTags(e.target.value)}
                    placeholder="用逗号分隔，如：重要,机密"
                    className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium text-gray-700">描述</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="输入描述信息（可选）"
                  rows={2}
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 resize-none"
                />
              </div>
            </div>

            <TabsContent value="file" className="space-y-6 mt-6">
              {/* 文件列表 - 移到上传区域前面 */}
              {uploadFiles.length > 0 && (
                <div className="bg-green-50 border-2 border-green-300 rounded-xl shadow-sm mb-4">
                  <div className="p-5">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <h4 className="font-semibold text-gray-900">待上传文件</h4>
                      <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                        {uploadFiles.length}
                      </Badge>
                    </div>
                    <div className="space-y-3">
                      {uploadFiles.map((uploadFile) => {
                        const fileInfo = getFileInfo(uploadFile.file);
                        const IconComponent = fileInfo.icon;

                        return (
                          <div key={uploadFile.id} className="flex items-center gap-4 p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
                            <div className="flex-shrink-0 w-12 h-12 bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-center">
                              <IconComponent className="h-6 w-6 text-gray-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-3 mb-2">
                                <p className="font-medium text-gray-900 truncate">{uploadFile.file.name}</p>
                                <Badge className={`${fileInfo.color} border-0`}>{fileInfo.label}</Badge>
                              </div>
                              <p className="text-sm text-gray-500">
                                {(uploadFile.file.size / 1024 / 1024).toFixed(2)} MB • 状态: {uploadFile.status}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              {uploadFile.status === 'pending' && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeFile(uploadFile.id)}
                                  className="w-8 h-8 p-0 hover:bg-red-100 hover:text-red-600 text-gray-400 hover:text-red-500"
                                  title="删除文件"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              )}
                              {uploadFile.status === 'uploading' && (
                                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                  <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                                </div>
                              )}
                              {uploadFile.status === 'processing' && (
                                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                  <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                                </div>
                              )}
                              {uploadFile.status === 'completed' && (
                                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                  <CheckCircle className="h-5 w-5 text-green-600" />
                                </div>
                              )}
                              {uploadFile.status === 'error' && (
                                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                  <AlertCircle className="h-5 w-5 text-red-600" />
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}

              {/* 文件上传区域 */}
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
                <div className="p-6">
                  <div
                    {...getRootProps()}
                    className={`border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200 ${
                      isDragActive
                        ? 'border-blue-400 bg-blue-50 scale-[1.02]'
                        : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'
                    }`}
                  >
                    <input {...getInputProps()} />
                    <div className="flex flex-col items-center">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-3 transition-colors ${
                        isDragActive ? 'bg-blue-100' : 'bg-gray-100'
                      }`}>
                        <Upload className={`h-6 w-6 transition-colors ${
                          isDragActive ? 'text-blue-600' : 'text-gray-500'
                        }`} />
                      </div>
                      <p className="text-base font-semibold text-gray-900 mb-2">
                        {isDragActive ? '释放文件以上传' : '拖拽文件到此处或点击选择'}
                      </p>
                      <p className="text-sm text-gray-500 mb-3">
                        支持 PDF, Word, TXT, MD, JPG, PNG 格式，最大 50MB
                      </p>
                      <div className="flex flex-wrap gap-2 justify-center">
                        {Object.entries(SUPPORTED_FILE_TYPES).slice(0, 5).map(([type, info]) => (
                          <Badge key={type} variant="secondary" className="text-xs bg-gray-100 text-gray-600">
                            {info.label}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>




          </TabsContent>

            <TabsContent value="text" className="space-y-6 mt-6">
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
                <div className="p-5">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <Label htmlFor="content" className="font-semibold text-gray-900">文本内容</Label>
                  </div>
                  <Textarea
                    id="content"
                    value={textContent}
                    onChange={(e) => setTextContent(e.target.value)}
                    placeholder="输入您要创作的文本内容..."
                    rows={8}
                    className="resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500 w-full"
                  />
                  <div className="flex justify-between items-center mt-3 text-sm text-gray-500">
                    <span>支持 Markdown 格式</span>
                    <span>{textContent.length} 字符</span>
                  </div>
                </div>
              </div>
            </TabsContent>
            </Tabs>
          </div>
        </div>

        <DialogFooter className="flex flex-row justify-end gap-3 pt-6 border-t border-gray-100 bg-gray-50/50 flex-shrink-0">
          <Button
            variant="outline"
            onClick={() => handleOpenChange(false)}
            className="px-6 py-2.5 font-medium border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors"
          >
            取消
          </Button>
          {activeTab === 'file' ? (
            <Button
              onClick={handleBatchUpload}
              disabled={uploadFiles.filter(f => f.status === 'pending').length === 0 || uploadDocument.isPending}
              className="px-6 py-2.5 font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploadDocument.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              <Upload className="h-4 w-4 mr-2" />
              上传文件 ({uploadFiles.filter(f => f.status === 'pending').length})
            </Button>
          ) : (
            <Button
              onClick={uploadText}
              disabled={!textContent.trim() || uploadDocument.isPending}
              className="px-6 py-2.5 font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {uploadDocument.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              <Plus className="h-4 w-4 mr-2" />
              创作文档
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
