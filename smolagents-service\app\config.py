"""
配置管理模块
管理 SmoLAgents 微服务的所有配置项
"""

import os
from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置类"""

    # 服务基本配置
    service_name: str = Field(default="smolagents-service", env="SERVICE_NAME")
    service_version: str = Field(default="1.0.0", env="SERVICE_VERSION")
    service_host: str = Field(default="0.0.0.0", env="SERVICE_HOST")
    service_port: int = Field(default=8002, env="SERVICE_PORT")

    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")

    # 模型选择配置
    model_provider: str = Field(default="ollama", env="MODEL_PROVIDER")  # "ollama" 或 "qwen"

    # Ollama 配置
    ollama_base_url: str = Field(default="http://host.docker.internal:11434", env="OLLAMA_BASE_URL")
    ollama_model: str = Field(default="Qwen3-8B-M:latest", env="OLLAMA_MODEL")
    ollama_timeout: int = Field(default=300, env="OLLAMA_TIMEOUT")  # 增加到5分钟

    # Qwen 在线模型配置
    qwen_model_id: str = Field(default="qwen-plus", env="QWEN_MODEL_ID")
    qwen_api_key: str = Field(default="sk-a281ffc3039c497f96ba5b62ce6baa3f", env="QWEN_API_KEY")
    qwen_api_base: str = Field(default="https://dashscope.aliyuncs.com/compatible-mode/v1", env="QWEN_API_BASE")
    qwen_temperature: float = Field(default=0.1, env="QWEN_TEMPERATURE")
    qwen_max_tokens: int = Field(default=4000, env="QWEN_MAX_TOKENS")

    # Redis 配置
    redis_host: str = Field(default="zht_morphik_redis_0624", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_db: int = Field(default=1, env="REDIS_DB")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_timeout: int = Field(default=10, env="REDIS_TIMEOUT")

    # 网络搜索配置
    enable_web_search: bool = Field(default=True, env="ENABLE_WEB_SEARCH")
    max_search_results: int = Field(default=10, env="MAX_SEARCH_RESULTS")
    search_timeout: int = Field(default=30, env="SEARCH_TIMEOUT")
    user_agent: str = Field(
        default="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        env="USER_AGENT"
    )

    # 市场分析性能优化配置
    market_analysis_max_steps: int = Field(default=12, env="MARKET_ANALYSIS_MAX_STEPS", description="市场分析最大步骤数")
    strategy_max_steps: int = Field(default=6, env="STRATEGY_MAX_STEPS", description="单个策略最大步骤数")
    search_max_steps: int = Field(default=4, env="SEARCH_MAX_STEPS", description="单次搜索最大步骤数")
    agent_default_max_steps: int = Field(default=8, env="AGENT_DEFAULT_MAX_STEPS", description="代理默认最大步骤数")

    # 超时控制配置
    single_search_timeout: int = Field(default=120, env="SINGLE_SEARCH_TIMEOUT")  # 单个搜索2分钟
    strategy_execution_timeout: int = Field(default=180, env="STRATEGY_EXECUTION_TIMEOUT")  # 单个策略3分钟
    total_analysis_timeout: int = Field(default=900, env="TOTAL_ANALYSIS_TIMEOUT")  # 总分析15分钟

    # 缓存配置
    enable_search_cache: bool = Field(default=True, env="ENABLE_SEARCH_CACHE")
    search_cache_ttl: int = Field(default=3600, env="SEARCH_CACHE_TTL")  # 1小时

    # 代理配置
    http_proxy: Optional[str] = Field(default=None, env="HTTP_PROXY")
    https_proxy: Optional[str] = Field(default=None, env="HTTPS_PROXY")
    no_proxy: Optional[str] = Field(default="localhost,127.0.0.1", env="NO_PROXY")

    # 安全配置
    secret_key: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")

    # 性能配置
    max_workers: int = Field(default=4, env="MAX_WORKERS")
    worker_timeout: int = Field(default=300, env="WORKER_TIMEOUT")
    max_requests_per_minute: int = Field(default=60, env="MAX_REQUESTS_PER_MINUTE")

    # 开发模式
    debug: bool = Field(default=False, env="DEBUG")
    dev_mode: bool = Field(default=False, env="DEV_MODE")

    # HuggingFace 配置
    hf_endpoint: str = Field(default="https://hf-mirror.com", env="HF_ENDPOINT")
    hf_token: Optional[str] = Field(default=None, env="HF_TOKEN")

    # 缓存配置
    cache_ttl: int = Field(default=3600, env="CACHE_TTL")
    cache_max_size: int = Field(default=1000, env="CACHE_MAX_SIZE")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    @property
    def redis_url(self) -> str:
        """构建 Redis 连接 URL"""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"

    @property
    def proxy_config(self) -> Optional[dict]:
        """获取代理配置"""
        if self.http_proxy or self.https_proxy:
            return {
                "http": self.http_proxy,
                "https": self.https_proxy,
            }
        return None


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
