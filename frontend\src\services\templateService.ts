/**
 * 话术模板管理服务
 * 提供模板的增删改查、导入导出、激活管理等功能
 */

import {
  Template,
  TemplateFilter,
  TemplateValidation,
  TemplateExport,
  TemplateStats,
  RoleConfig,
  StyleConfig,
  TEMPLATE_CATEGORIES,
  RESPONSE_STYLES
} from '@/types/template';

class TemplateService {
  private readonly STORAGE_KEY = 'zht_templates';
  private readonly STATS_KEY = 'zht_template_stats';
  private readonly VERSION_KEY = 'zht_template_version';
  private readonly ACTIVE_TEMPLATE_KEY = 'zht_active_template';
  private readonly CURRENT_VERSION = '1.0.0';

  // 内存缓存，确保状态一致性
  private templatesCache: Template[] | null = null;
  private activeTemplateId: string | null = null;

  // 获取所有模板
  async getTemplates(filter?: TemplateFilter): Promise<Template[]> {
    try {
      const templates = this.loadTemplatesFromStorage();
      console.log('获取模板列表，总数:', templates.length);

      if (!filter) {
        return templates;
      }

      return templates.filter(template => {
        // 类别过滤
        if (filter.category && template.category !== filter.category) {
          return false;
        }

        // 标签过滤
        if (filter.tags && filter.tags.length > 0) {
          const hasMatchingTag = filter.tags.some(tag =>
            template.tags.includes(tag)
          );
          if (!hasMatchingTag) return false;
        }

        // 作者过滤
        if (filter.author && template.author !== filter.author) {
          return false;
        }

        // 激活状态过滤
        if (filter.is_active !== undefined && template.is_active !== filter.is_active) {
          return false;
        }

        // 评分过滤
        if (filter.rating_min && template.rating < filter.rating_min) {
          return false;
        }

        // 搜索查询过滤
        if (filter.search_query) {
          const query = filter.search_query.toLowerCase();
          const searchableText = [
            template.name,
            template.description,
            template.role.name,
            template.style.name,
            ...template.tags
          ].join(' ').toLowerCase();

          if (!searchableText.includes(query)) {
            return false;
          }
        }

        return true;
      });
    } catch (error) {
      console.error('获取模板失败:', error);
      return [];
    }
  }

  // 获取单个模板
  async getTemplate(id: string): Promise<Template | null> {
    try {
      const templates = this.loadTemplatesFromStorage();
      return templates.find(t => t.id === id) || null;
    } catch (error) {
      console.error('获取模板失败:', error);
      return null;
    }
  }

  // 创建模板
  async createTemplate(templateData: Omit<Template, 'id' | 'created_at' | 'updated_at' | 'usage_count'>): Promise<Template> {
    try {
      console.log('开始创建模板:', templateData);
      const templates = this.loadTemplatesFromStorage();
      console.log('当前模板数量:', templates.length);

      // 为缺失的字段提供默认值
      const newTemplate: Template = {
        name: templateData.name || '未命名模板',
        description: templateData.description || '暂无描述',
        version: templateData.version || '1.0.0',
        author: templateData.author || 'User',
        category: templateData.category || 'general',
        tags: templateData.tags || [],
        rating: templateData.rating || 0,
        is_active: templateData.is_active || false,
        is_default: templateData.is_default || false,
        role: {
          id: templateData.role?.id || `role_${Date.now()}`,
          name: templateData.role?.name || '默认角色',
          description: templateData.role?.description || '默认角色描述',
          icon: templateData.role?.icon || '🤖',
          category: templateData.role?.category || 'general',
          system_prompt: templateData.role?.system_prompt || '你是一个有用的AI助手。',
          style: templateData.role?.style || 'professional',
          temperature: templateData.role?.temperature || 0.7,
          max_tokens: templateData.role?.max_tokens || 2000
        },
        style: {
          id: templateData.style?.id || `style_${Date.now()}`,
          name: templateData.style?.name || '默认风格',
          description: templateData.style?.description || '默认回答风格',
          template: templateData.style?.template || '{response}',
          category: templateData.style?.category || 'detailed'
        },
        prompt_overrides: {
          query: {
            prompt_template: templateData.prompt_overrides?.query?.prompt_template ||
              '请根据以下上下文回答用户的问题。\n\n上下文：{context}\n\n用户问题：{question}\n\n请提供准确、有用的回答。'
          },
          ...templateData.prompt_overrides
        },
        user_context_schema: templateData.user_context_schema,
        custom_instructions: templateData.custom_instructions,
        id: this.generateId(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        usage_count: 0
      };

      console.log('新模板数据:', newTemplate);

      // 验证模板
      const validation = this.validateTemplate(newTemplate);
      console.log('模板验证结果:', validation);
      if (!validation.is_valid) {
        throw new Error(`模板验证失败: ${validation.errors.join(', ')}`);
      }

      templates.push(newTemplate);
      console.log('添加后模板数量:', templates.length);

      this.saveTemplatesToStorage(templates);
      console.log('模板保存完成');

      return newTemplate;
    } catch (error) {
      console.error('创建模板失败:', error);
      throw error;
    }
  }

  // 更新模板
  async updateTemplate(id: string, updates: Partial<Template>): Promise<Template> {
    try {
      const templates = this.loadTemplatesFromStorage();
      const index = templates.findIndex(t => t.id === id);

      if (index === -1) {
        throw new Error('模板不存在');
      }

      const updatedTemplate: Template = {
        ...templates[index],
        ...updates,
        id, // 确保ID不被修改
        updated_at: new Date().toISOString()
      };

      // 验证更新后的模板
      const validation = this.validateTemplate(updatedTemplate);
      if (!validation.is_valid) {
        throw new Error(`模板验证失败: ${validation.errors.join(', ')}`);
      }

      templates[index] = updatedTemplate;
      this.saveTemplatesToStorage(templates);

      return updatedTemplate;
    } catch (error) {
      console.error('更新模板失败:', error);
      throw error;
    }
  }

  // 删除模板
  async deleteTemplate(id: string): Promise<boolean> {
    try {
      const templates = this.loadTemplatesFromStorage();
      const index = templates.findIndex(t => t.id === id);

      if (index === -1) {
        throw new Error('模板不存在');
      }

      // 如果删除的是激活模板，需要清除激活状态
      const activeTemplate = this.getActiveTemplate();
      if (activeTemplate && activeTemplate.id === id) {
        this.deactivateTemplate();
      }

      templates.splice(index, 1);
      this.saveTemplatesToStorage(templates);

      return true;
    } catch (error) {
      console.error('删除模板失败:', error);
      return false;
    }
  }

  // 激活模板
  async activateTemplate(id: string): Promise<boolean> {
    try {
      const templates = this.loadTemplatesFromStorage();
      const template = templates.find(t => t.id === id);

      if (!template) {
        throw new Error('模板不存在');
      }

      // 取消其他模板的激活状态
      templates.forEach(t => {
        t.is_active = t.id === id;
      });

      this.saveTemplatesToStorage(templates);

      // 保存激活的模板到单独的存储
      localStorage.setItem(this.ACTIVE_TEMPLATE_KEY, JSON.stringify(template));

      return true;
    } catch (error) {
      console.error('激活模板失败:', error);
      return false;
    }
  }

  // 取消激活
  deactivateTemplate(): void {
    try {
      const templates = this.loadTemplatesFromStorage();
      templates.forEach(t => {
        t.is_active = false;
      });
      this.saveTemplatesToStorage(templates);
      localStorage.removeItem(this.ACTIVE_TEMPLATE_KEY);
    } catch (error) {
      console.error('取消激活失败:', error);
    }
  }

  // 获取当前激活的模板
  getActiveTemplate(): Template | null {
    try {
      // 优先从主存储中获取激活模板，确保状态一致性
      const templates = this.loadTemplatesFromStorage();
      const activeTemplate = templates.find(t => t.is_active);

      if (activeTemplate) {
        // 同步更新单独存储，确保一致性
        localStorage.setItem(this.ACTIVE_TEMPLATE_KEY, JSON.stringify(activeTemplate));
        return activeTemplate;
      }

      // 如果主存储中没有激活模板，检查单独存储
      const activeTemplateData = localStorage.getItem(this.ACTIVE_TEMPLATE_KEY);
      if (activeTemplateData) {
        const storedTemplate = JSON.parse(activeTemplateData);
        // 验证该模板是否仍然存在于主存储中
        const templateExists = templates.find(t => t.id === storedTemplate.id);
        if (templateExists) {
          // 修复主存储中的激活状态
          templateExists.is_active = true;
          this.saveTemplatesToStorage(templates);
          return templateExists;
        } else {
          // 模板已不存在，清除单独存储
          localStorage.removeItem(this.ACTIVE_TEMPLATE_KEY);
        }
      }

      return null;
    } catch (error) {
      console.error('获取激活模板失败:', error);
      return null;
    }
  }

  // 刷新激活的模板（从存储中重新加载最新版本）
  refreshActiveTemplate(): boolean {
    try {
      const activeTemplate = this.getActiveTemplate();
      if (!activeTemplate) {
        return false;
      }

      const templates = this.loadTemplatesFromStorage();
      const updatedTemplate = templates.find(t => t.id === activeTemplate.id);

      if (!updatedTemplate) {
        console.warn('激活的模板在存储中不存在，取消激活');
        this.deactivateTemplate();
        return false;
      }

      // 更新激活模板的存储
      localStorage.setItem(this.ACTIVE_TEMPLATE_KEY, JSON.stringify(updatedTemplate));
      return true;
    } catch (error) {
      console.error('刷新激活模板失败:', error);
      return false;
    }
  }

  // 强制更新默认模板
  forceUpdateDefaultTemplates(): void {
    try {
      const existingTemplates = this.loadTemplatesFromStorage();
      const defaultTemplates = this.getDefaultTemplates();
      let needsUpdate = false;

      // 更新现有的默认模板
      existingTemplates.forEach(existing => {
        if (existing.is_default) {
          const defaultTemplate = defaultTemplates.find(dt => dt.id === existing.id);
          if (defaultTemplate) {
            // 更新模板内容，保留用户的激活状态和使用统计
            const preservedData = {
              is_active: existing.is_active,
              usage_count: existing.usage_count,
              rating: existing.rating
            };
            Object.assign(existing, defaultTemplate, preservedData);
            needsUpdate = true;
          }
        }
      });

      // 添加新的默认模板
      defaultTemplates.forEach(defaultTemplate => {
        if (!existingTemplates.find(et => et.id === defaultTemplate.id)) {
          existingTemplates.push(defaultTemplate);
          needsUpdate = true;
        }
      });

      if (needsUpdate) {
        this.saveTemplatesToStorage(existingTemplates);
        // 如果有激活的默认模板，需要刷新激活模板
        this.refreshActiveTemplate();
        console.log('默认模板已更新');
      }
    } catch (error) {
      console.error('强制更新默认模板失败:', error);
    }
  }

  // 验证模板
  validateTemplate(template: Template): TemplateValidation {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // 必填字段验证
    if (!template.name?.trim()) {
      errors.push('模板名称不能为空');
    }

    if (!template.description?.trim()) {
      warnings.push('建议填写模板描述');
    }

    if (!template.role?.system_prompt?.trim()) {
      warnings.push('建议填写角色系统提示词');
    }

    if (!template.prompt_overrides?.query?.prompt_template?.trim()) {
      warnings.push('建议填写查询提示词模板');
    }

    // 提示词模板占位符验证
    const queryTemplate = template.prompt_overrides?.query?.prompt_template || '';
    if (!queryTemplate.includes('{question}')) {
      errors.push('查询提示词模板必须包含 {question} 占位符');
    }

    if (!queryTemplate.includes('{context}')) {
      warnings.push('建议在查询提示词模板中包含 {context} 占位符');
    }

    // 长度验证
    if (template.name.length > 100) {
      warnings.push('模板名称过长，建议控制在100字符以内');
    }

    if (queryTemplate.length > 2000) {
      warnings.push('提示词模板过长，可能影响性能');
    }

    // 建议
    if (!template.tags || template.tags.length === 0) {
      suggestions.push('建议添加标签以便分类管理');
    }

    if (template.role.temperature === undefined) {
      suggestions.push('建议设置温度参数以控制回答的创造性');
    }

    return {
      is_valid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  // 导出模板
  async exportTemplates(templateIds?: string[]): Promise<TemplateExport> {
    try {
      const allTemplates = this.loadTemplatesFromStorage();
      const templatesToExport = templateIds
        ? allTemplates.filter(t => templateIds.includes(t.id))
        : allTemplates;

      const categories = [...new Set(templatesToExport.map(t => t.category))];
      const tags = [...new Set(templatesToExport.flatMap(t => t.tags))];

      return {
        version: '1.0.0',
        export_date: new Date().toISOString(),
        templates: templatesToExport,
        metadata: {
          total_count: templatesToExport.length,
          categories,
          tags
        }
      };
    } catch (error) {
      console.error('导出模板失败:', error);
      throw error;
    }
  }

  // 导入模板
  async importTemplates(exportData: TemplateExport, options?: { overwrite?: boolean }): Promise<{ success: number; failed: number; errors: string[] }> {
    try {
      const existingTemplates = this.loadTemplatesFromStorage();
      const results = { success: 0, failed: 0, errors: [] as string[] };

      for (const template of exportData.templates) {
        try {
          // 检查是否已存在
          const existingIndex = existingTemplates.findIndex(t => t.id === template.id);

          if (existingIndex !== -1 && !options?.overwrite) {
            results.errors.push(`模板 "${template.name}" 已存在，跳过导入`);
            results.failed++;
            continue;
          }

          // 验证模板
          const validation = this.validateTemplate(template);
          if (!validation.is_valid) {
            results.errors.push(`模板 "${template.name}" 验证失败: ${validation.errors.join(', ')}`);
            results.failed++;
            continue;
          }

          // 更新时间戳
          const importedTemplate: Template = {
            ...template,
            updated_at: new Date().toISOString()
          };

          if (existingIndex !== -1) {
            existingTemplates[existingIndex] = importedTemplate;
          } else {
            existingTemplates.push(importedTemplate);
          }

          results.success++;
        } catch (error) {
          results.errors.push(`导入模板 "${template.name}" 失败: ${error}`);
          results.failed++;
        }
      }

      this.saveTemplatesToStorage(existingTemplates);
      return results;
    } catch (error) {
      console.error('导入模板失败:', error);
      throw error;
    }
  }

  // 私有方法：从存储加载模板
  private loadTemplatesFromStorage(): Template[] {
    try {
      // 检查版本，如果版本不匹配则强制更新
      const storedVersion = localStorage.getItem(this.VERSION_KEY);
      if (storedVersion !== this.CURRENT_VERSION) {
        console.log('模板版本不匹配，强制更新默认模板');
        const defaultTemplates = this.getDefaultTemplates();
        this.saveTemplatesToStorage(defaultTemplates);
        localStorage.setItem(this.VERSION_KEY, this.CURRENT_VERSION);
        return defaultTemplates;
      }

      const data = localStorage.getItem(this.STORAGE_KEY);
      if (!data) {
        const defaultTemplates = this.getDefaultTemplates();
        this.saveTemplatesToStorage(defaultTemplates);
        localStorage.setItem(this.VERSION_KEY, this.CURRENT_VERSION);
        return defaultTemplates;
      }
      return JSON.parse(data);
    } catch (error) {
      console.error('加载模板失败:', error);
      const defaultTemplates = this.getDefaultTemplates();
      this.saveTemplatesToStorage(defaultTemplates);
      localStorage.setItem(this.VERSION_KEY, this.CURRENT_VERSION);
      return defaultTemplates;
    }
  }

  // 私有方法：保存模板到存储
  private saveTemplatesToStorage(templates: Template[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(templates));
    } catch (error) {
      console.error('保存模板失败:', error);
      throw error;
    }
  }

  // 私有方法：生成ID
  private generateId(): string {
    return `template_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // 获取模板使用统计
  async getTemplateStats(id: string): Promise<TemplateStats | null> {
    try {
      const statsData = localStorage.getItem(this.STATS_KEY);
      const stats = statsData ? JSON.parse(statsData) : {};
      return stats[id] || null;
    } catch (error) {
      console.error('获取模板统计失败:', error);
      return null;
    }
  }

  // 更新模板使用统计
  async updateTemplateStats(id: string, success: boolean, responseTime?: number): Promise<void> {
    try {
      const statsData = localStorage.getItem(this.STATS_KEY);
      const allStats = statsData ? JSON.parse(statsData) : {};

      const currentStats = allStats[id] || {
        template_id: id,
        usage_count: 0,
        success_rate: 1,
        average_rating: 5,
        last_used: new Date().toISOString(),
        performance_metrics: {
          response_time: 0,
          user_satisfaction: 5,
          error_rate: 0
        }
      };

      // 更新统计数据
      currentStats.usage_count++;
      currentStats.last_used = new Date().toISOString();

      if (responseTime) {
        currentStats.performance_metrics.response_time =
          (currentStats.performance_metrics.response_time + responseTime) / 2;
      }

      // 更新成功率
      const totalAttempts = currentStats.usage_count;
      const successCount = Math.round(currentStats.success_rate * (totalAttempts - 1)) + (success ? 1 : 0);
      currentStats.success_rate = successCount / totalAttempts;

      // 更新错误率
      currentStats.performance_metrics.error_rate = 1 - currentStats.success_rate;

      allStats[id] = currentStats;
      localStorage.setItem(this.STATS_KEY, JSON.stringify(allStats));

      // 同时更新模板的使用次数
      const templates = this.loadTemplatesFromStorage();
      const templateIndex = templates.findIndex(t => t.id === id);
      if (templateIndex !== -1) {
        templates[templateIndex].usage_count = currentStats.usage_count;
        this.saveTemplatesToStorage(templates);
      }
    } catch (error) {
      console.error('更新模板统计失败:', error);
    }
  }

  // 私有方法：获取默认模板
  private getDefaultTemplates(): Template[] {
    const now = new Date().toISOString();

    return [
      {
        id: 'default_customer_service',
        name: '专业客服助手',
        description: '友好、耐心、专业的客服代表模板，适用于客户咨询和问题解决',
        version: '1.0.0',
        author: 'ZHT System',
        created_at: now,
        updated_at: now,
        category: TEMPLATE_CATEGORIES.CUSTOMER_SERVICE,
        tags: ['客服', '专业', '友好', '问题解决'],
        role: {
          id: 'customer_service_role',
          name: '客服代表',
          description: '专业的客服代表',
          icon: '🎧',
          category: 'customer_service',
          system_prompt: '你是一位专业的客服代表，具有友好、耐心、专业的服务态度。',
          style: 'professional',
          temperature: 0.7,
          max_tokens: 500
        },
        style: {
          id: 'detailed_style',
          name: '详细说明',
          description: '提供详细的解释和步骤',
          template: '请详细回答客户问题，提供具体的解决方案',
          category: 'detailed'
        },
        prompt_overrides: {
          query: {
            prompt_template: `请基于以下相关信息回答客户问题，并以专业客服代表的服务标准提供帮助：

相关信息：
{context}

客户问题：{question}

回答要求：
1. 首先基于上述相关信息准确回答客户问题
2. 如果相关信息中没有答案，请明确说明并提供可能的解决方向
3. 以"您好！"开始回答
4. 使用友好、耐心、专业的服务态度
5. 使用礼貌和正式的语言
6. 优先提供解决方案而不是解释问题
7. 在回答结束时询问"还有其他问题需要帮助吗？"

请回答：`
          }
        },
        usage_count: 0,
        rating: 5,
        is_active: false,
        is_default: true
      },
      {
        id: 'default_sales_assistant',
        name: '销售顾问助手',
        description: '积极主动的销售助手模板，善于产品推荐和客户需求分析',
        version: '1.0.0',
        author: 'ZHT System',
        created_at: now,
        updated_at: now,
        category: TEMPLATE_CATEGORIES.SALES,
        tags: ['销售', '推荐', '积极', '产品'],
        role: {
          id: 'sales_role',
          name: '销售顾问',
          description: '积极主动的销售顾问',
          icon: '💼',
          category: 'sales',
          system_prompt: '你是一位积极主动的销售顾问，善于发现客户需求并提供针对性建议。',
          style: 'enthusiastic',
          temperature: 0.8,
          max_tokens: 800
        },
        style: {
          id: 'persuasive_style',
          name: '说服式回答',
          description: '强调产品优势和价值',
          template: '重点突出产品优势，提供有说服力的建议',
          category: 'comparison'
        },
        prompt_overrides: {
          query: {
            prompt_template: `你是一位专业的销售顾问。请根据以下信息回答客户问题：

相关信息：
{context}

客户问题：{question}

回答指导：
1. 首先仔细阅读客户问题，理解客户的真实需求
2. 基于提供的相关信息准确回答问题
3. 如果相关信息中包含产品或服务信息，可以适当强调其优势和价值
4. 如果问题与销售无关（如技术问题、故事情节等），请直接基于相关信息回答，不要强行推销
5. 保持专业、友好的语调
6. 只有在问题确实与产品或服务相关时，才在回答结束时询问是否需要了解更多信息

请回答：`
          }
        },
        usage_count: 0,
        rating: 5,
        is_active: false,
        is_default: true
      },
      {
        id: 'default_smart_assistant',
        name: '智能助手',
        description: '智能判断问题类型，提供最合适的回答方式',
        version: '1.0.0',
        author: 'ZHT System',
        created_at: now,
        updated_at: now,
        category: 'general',
        tags: ['智能', '通用', '自适应', '专业'],
        role: {
          id: 'smart_role',
          name: '智能助手',
          description: '能够智能判断问题类型的助手',
          icon: '🤖',
          category: 'general',
          system_prompt: '你是一位智能助手，能够根据问题内容提供最合适的回答方式。',
          style: 'adaptive',
          temperature: 0.7,
          max_tokens: 800
        },
        style: {
          id: 'adaptive_style',
          name: '自适应回答',
          description: '根据问题类型自动调整回答风格',
          template: '智能分析问题类型，提供最合适的回答',
          category: 'detailed'
        },
        prompt_overrides: {
          query: {
            prompt_template: `请基于以下信息回答用户问题：

相关信息：
{context}

用户问题：{question}

回答要求：
1. 仔细分析用户问题的类型和意图
2. 基于提供的相关信息准确回答问题
3. 如果相关信息不足以回答问题，请诚实说明
4. 根据问题性质选择合适的回答风格：
   - 技术问题：提供详细的技术解答
   - 产品咨询：强调产品优势和价值
   - 故事情节：基于文档内容回答
   - 一般问题：提供准确、有用的信息
5. 保持专业、友好的语调

请回答：`
          }
        },
        usage_count: 0,
        rating: 5,
        is_active: false,
        is_default: true
      },
      {
        id: 'default_technical_support',
        name: '技术支持专家',
        description: '专业的技术支持工程师模板，提供详细的技术解决方案',
        version: '1.0.0',
        author: 'ZHT System',
        created_at: now,
        updated_at: now,
        category: TEMPLATE_CATEGORIES.TECHNICAL_SUPPORT,
        tags: ['技术', '支持', '专业', '解决方案'],
        role: {
          id: 'technical_role',
          name: '技术支持工程师',
          description: '资深的技术支持工程师',
          icon: '🔧',
          category: 'technical',
          system_prompt: '你是一位资深的技术支持工程师，提供准确、详细、专业的技术解答。',
          style: 'technical',
          temperature: 0.5,
          max_tokens: 1000
        },
        style: {
          id: 'troubleshooting_style',
          name: '故障排除',
          description: '提供分步骤的故障排除指南',
          template: '提供详细的故障排除步骤和多种解决方案',
          category: 'troubleshooting'
        },
        prompt_overrides: {
          query: {
            prompt_template: `请基于以下技术文档回答用户的技术问题，并以资深技术支持工程师的专业标准提供帮助：

相关技术文档：
{context}

技术问题：{question}

回答要求：
1. 首先基于上述技术文档准确回答技术问题
2. 如果技术文档中没有相关信息，请明确说明并提供一般性的技术建议
3. 提供准确、详细、专业的技术解答
4. 提供具体的操作步骤和解决方案
5. 使用专业但易懂的技术术语
6. 尽可能提供多种解决方案供选择
7. 在回答结束时询问"这个解决方案是否解决了您的问题？"

请回答：`
          }
        },
        usage_count: 0,
        rating: 5,
        is_active: false,
        is_default: true
      }
    ];
  }
}

export const templateService = new TemplateService();
export default templateService;
