"""
知识库管理API路由
演示如何在新功能中使用权限控制
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.core.permissions import require_permissions, Permissions
from app.models.user import User

router = APIRouter()


@router.get("/knowledge-bases", summary="获取知识库列表")
async def get_knowledge_bases(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_permissions([Permissions.KNOWLEDGE_READ]))
):
    """
    获取知识库列表
    
    需要权限：knowledge:read
    """
    # 业务逻辑实现
    return {"message": "获取知识库列表", "user_id": current_user.id}


@router.post("/knowledge-bases", summary="创建知识库")
async def create_knowledge_base(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_permissions([Permissions.KNOWLEDGE_CREATE]))
):
    """
    创建新的知识库
    
    需要权限：knowledge:create
    """
    # 业务逻辑实现
    return {"message": "创建知识库成功", "user_id": current_user.id}


@router.put("/knowledge-bases/{kb_id}", summary="更新知识库")
async def update_knowledge_base(
    *,
    kb_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_permissions([Permissions.KNOWLEDGE_UPDATE]))
):
    """
    更新知识库
    
    需要权限：knowledge:update
    """
    # 业务逻辑实现
    return {"message": f"更新知识库 {kb_id} 成功", "user_id": current_user.id}


@router.delete("/knowledge-bases/{kb_id}", summary="删除知识库")
async def delete_knowledge_base(
    *,
    kb_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_permissions([Permissions.KNOWLEDGE_DELETE]))
):
    """
    删除知识库
    
    需要权限：knowledge:delete
    """
    # 业务逻辑实现
    return {"message": f"删除知识库 {kb_id} 成功", "user_id": current_user.id}


@router.post("/knowledge-bases/{kb_id}/export", summary="导出知识库")
async def export_knowledge_base(
    *,
    kb_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_permissions([Permissions.KNOWLEDGE_EXPORT]))
):
    """
    导出知识库
    
    需要权限：knowledge:export
    """
    # 业务逻辑实现
    return {"message": f"导出知识库 {kb_id} 成功", "user_id": current_user.id}


# 组合权限示例：需要多个权限
@router.post("/knowledge-bases/{kb_id}/advanced-operation", summary="高级操作")
async def advanced_operation(
    *,
    kb_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(require_permissions([
        Permissions.KNOWLEDGE_READ,
        Permissions.KNOWLEDGE_UPDATE,
        Permissions.AI_CONFIG
    ]))
):
    """
    高级操作：需要多个权限
    
    需要权限：knowledge:read, knowledge:update, ai:config
    """
    # 业务逻辑实现
    return {"message": f"执行高级操作 {kb_id} 成功", "user_id": current_user.id}
