#
# clean_code_db.ps1
#
# Description: Cleans VS Code databases by removing entries containing "augment"
# This script finds the appropriate database files based on the operating system,
# creates backups, and then removes specific records from the SQLite databases.

# Set strict mode for better error handling
Set-StrictMode -Version Latest
$ErrorActionPreference = "Stop"

# Check PowerShell version compatibility
if ($PSVersionTable.PSVersion.Major -lt 5) {
    Write-Host "Note: Running on PowerShell version $($PSVersionTable.PSVersion)" -ForegroundColor Yellow
    Write-Host "Some features may be limited, but the script will attempt to continue." -ForegroundColor Yellow
}

# Log functions using Write-Host with colors
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if sqlite3 is installed
function Test-Dependencies {
    try {
        $null = Get-Command sqlite3 -ErrorAction Stop
        return $true
    } catch {
        Write-Error "sqlite3 is not installed. Please install it and try again."
        Write-Info "You can install sqlite3 using:"
        Write-Info "  - Chocolatey: choco install sqlite"
        Write-Info "  - Scoop: scoop install sqlite"
        Write-Info "  - Download from: https://www.sqlite.org/download.html"
        return $false
    }
}

# Get database paths based on operating system
function Get-DatabasePaths {
    $dbPaths = @()
    
    # Windows paths for VS Code
    $appDataPath = $env:APPDATA
    if (-not $appDataPath) {
        Write-Error "APPDATA environment variable not found"
        return @()
    }
    
    $vsCodeDbPath = Join-Path $appDataPath "Code\User\globalStorage\state.vscdb"
    if (Test-Path $vsCodeDbPath) {
        $dbPaths += $vsCodeDbPath
    }
    
    # Also check for VS Code Insiders
    $vsCodeInsidersDbPath = Join-Path $appDataPath "Code - Insiders\User\globalStorage\state.vscdb"
    if (Test-Path $vsCodeInsidersDbPath) {
        $dbPaths += $vsCodeInsidersDbPath
    }
    
    # Check for Cursor (VS Code fork)
    $cursorDbPath = Join-Path $appDataPath "Cursor\User\globalStorage\state.vscdb"
    if (Test-Path $cursorDbPath) {
        $dbPaths += $cursorDbPath
    }
    
    if ($dbPaths.Count -eq 0) {
        Write-Warning "No database files found"
        return @()
    }
    
    return $dbPaths
}

# Clean a single database
function Clear-Database {
    param(
        [string]$DbPath
    )
    
    $appName = "Unknown"
    if ($DbPath -like "*Code - Insiders*") {
        $appName = "VS Code Insiders"
    } elseif ($DbPath -like "*Code*") {
        $appName = "VS Code"
    } elseif ($DbPath -like "*Cursor*") {
        $appName = "Cursor"
    }
    
    Write-Info "Processing $appName database at: $DbPath"
    
    # Create backup
    $backupPath = "$DbPath" + "_backup"
    try {
        Copy-Item $DbPath $backupPath -Force
        Write-Success "Created backup at: $backupPath"
    } catch {
        Write-Error "Failed to create backup: $($_.Exception.Message)"
        return $false
    }
    
    # Execute SQLite command to delete records
    try {
        $sqlCommand = "DELETE FROM ItemTable WHERE key LIKE '%augment%';"
        $result = & sqlite3 $DbPath $sqlCommand
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Cleaned $appName database"
            return $true
        } else {
            Write-Error "Failed to clean $appName database (Exit code: $LASTEXITCODE)"
            return $false
        }
    } catch {
        Write-Error "Failed to clean $appName database: $($_.Exception.Message)"
        return $false
    }
}

# Main function
function Start-DatabaseCleanup {
    Write-Info "Starting database cleanup process"
    
    # Check dependencies
    if (-not (Test-Dependencies)) {
        exit 1
    }
    
    # Get database paths
    Write-Info "Searching for database files..."
    $dbPaths = Get-DatabasePaths
    
    if ($dbPaths.Count -eq 0) {
        Write-Error "No database files found to process"
        exit 1
    }
    
    Write-Success "Found $($dbPaths.Count) database file(s)"
    
    # Process each database
    $successCount = 0
    foreach ($dbPath in $dbPaths) {
        if (Clear-Database -DbPath $dbPath) {
            $successCount++
        }
    }
    
    # Summary
    if ($successCount -eq $dbPaths.Count) {
        Write-Success "All databases processed successfully"
    } else {
        Write-Warning "$successCount out of $($dbPaths.Count) databases processed successfully"
    }
    
    Write-Info "Database cleanup process completed"
}

# Execute main function
Start-DatabaseCleanup
