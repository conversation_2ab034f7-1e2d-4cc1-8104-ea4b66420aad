/**
 * 销售训练设置对话框组件
 * 管理国家、产品、职位选项的增删功能
 */

import { useState, useEffect } from 'react'
import {
  Plus,
  Trash2,
  Globe,
  Package,
  Settings,
  X,
  Loader2,
  AlertTriangle,
  Target,
  Zap,
  Shuffle
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'

import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { useSalesTrainingData, useSalesTrainingActions } from '@/store/salesTraining'
// import { ErrorAlert } from '@/components/ui/error-alert'


interface SettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SettingsDialog({
  open,
  onOpenChange
}: SettingsDialogProps) {
  // 使用数据管理Hook
  const {
    countries,
    products,
    scenarios,
    isLoading,
    isInitialLoading,
    error: dataError,
    clearError,
    refreshData
  } = useSalesTrainingData()

  // 使用操作方法
  const {
    addCountry,
    deleteCountry,
    addProduct,
    deleteProduct,
    addScenario,
    updateScenario,
    deleteScenario,
    refreshScenarios
  } = useSalesTrainingActions()

  // 确保在对话框打开时数据已加载并刷新
  useEffect(() => {
    if (open) {
      refreshData()
    }
  }, [open, refreshData])

  const [activeTab, setActiveTab] = useState<'countries' | 'products' | 'scenarios'>('countries')
  const [newCountryName, setNewCountryName] = useState('')
  const [newProductName, setNewProductName] = useState('')
  const [newProductCategory, setNewProductCategory] = useState('')

  const [newScenarioName, setNewScenarioName] = useState('')
  const [newScenarioDescription, setNewScenarioDescription] = useState('')
  const [newScenarioCategory, setNewScenarioCategory] = useState('')
  const [error, setError] = useState('')
  const [deleteError, setDeleteError] = useState<string>('')
  const [deleteConfirm, setDeleteConfirm] = useState<{
    isOpen: boolean
    type: 'country' | 'product' | 'scenario'
    id: string
    name: string
  }>({
    isOpen: false,
    type: 'country',
    id: '',
    name: ''
  })

  // 合并错误状态
  const displayError = error || dataError

  // 添加国家
  const handleAddCountry = async () => {
    if (!newCountryName.trim()) {
      setError('请输入国家名称')
      return
    }

    // 检查是否已存在相同名称的国家
    if (countries.some(c => c.name === newCountryName.trim())) {
      setError('该国家已存在')
      return
    }

    // 生成国家代码（前两个字符）
    const countryCode = newCountryName.trim().toUpperCase().slice(0, 2)

    const success = await addCountry({
      name: newCountryName.trim(),
      code: countryCode,
      flag: '🌍'
    })

    if (success) {
      setNewCountryName('')
      setError('')
    }
  }

  // 打开删除确认对话框
  const openDeleteConfirm = (type: 'country' | 'product' | 'scenario', id: string, name: string) => {
    setDeleteConfirm({
      isOpen: true,
      type,
      id,
      name
    })
  }

  // 关闭删除确认对话框
  const closeDeleteConfirm = () => {
    setDeleteConfirm({
      isOpen: false,
      type: 'country',
      id: '',
      name: ''
    })
    setDeleteError('')
  }

  // 确认删除
  const confirmDelete = async () => {
    const { type, id } = deleteConfirm
    setDeleteError('')

    try {
      if (type === 'country') {
        await deleteCountry(parseInt(id))
      } else if (type === 'product') {
        await deleteProduct(parseInt(id))
      } else if (type === 'scenario') {
        await deleteScenario(parseInt(id))
      }

      closeDeleteConfirm()
      toast.success('删除成功')
    } catch (error: any) {
      // 处理删除保护错误
      if (error?.status === 400 || error?.statusCode === 400) {
        // 从SalesTrainingError中获取错误信息
        const errorMessage = error.message || '删除失败'

        // 对于训练场景，关闭删除确认对话框并在页面顶部显示错误
        if (type === 'scenario') {
          closeDeleteConfirm()
          setError(errorMessage)
        } else {
          // 对于国家和产品，在删除确认对话框中显示错误
          setDeleteError(errorMessage)
        }
      } else {
        const errorMessage = error?.message || '删除失败'

        // 对于训练场景，关闭删除确认对话框并在页面顶部显示错误
        if (type === 'scenario') {
          closeDeleteConfirm()
          setError(errorMessage)
        } else {
          // 对于国家和产品，在删除确认对话框中显示错误
          setDeleteError(errorMessage)
        }
      }
    }
  }

  // 删除国家（保留原函数名以兼容现有代码）
  const handleDeleteCountry = (countryId: number) => {
    const country = countries.find(c => c.id === countryId)
    if (country) {
      openDeleteConfirm('country', countryId.toString(), country.name)
    }
  }

  // 添加产品
  const handleAddProduct = async () => {
    if (!newProductName.trim() || !newProductCategory.trim()) {
      setError('请输入产品名称和分类')
      return
    }

    if (products.some(p => p.name === newProductName.trim())) {
      setError('该产品已存在')
      return
    }

    const success = await addProduct({
      name: newProductName.trim(),
      category: newProductCategory.trim(),
      description: `${newProductName.trim()}产品线`
    })

    if (success) {
      setNewProductName('')
      setNewProductCategory('')
      setError('')
    }
  }

  // 删除产品
  const handleDeleteProduct = (productId: number) => {
    const product = products.find(p => p.id === productId)
    if (product) {
      openDeleteConfirm('product', productId.toString(), product.name)
    }
  }



  // 添加训练场景
  const handleAddScenario = async () => {
    if (!newScenarioName.trim() || !newScenarioCategory.trim()) {
      setError('请输入场景名称和分类')
      return
    }

    if (scenarios.some(s => s.name === newScenarioName.trim())) {
      setError('该训练场景已存在')
      return
    }

    const success = await addScenario({
      name: newScenarioName.trim(),
      description: newScenarioDescription.trim() || undefined,
      category: newScenarioCategory.trim(),
      difficulty: 'intermediate',
      objectives: JSON.stringify(['提升销售技能', '增强沟通能力']),
      challenges: JSON.stringify(['客户异议处理', '沟通技巧']),
      tags: JSON.stringify([newScenarioCategory.trim()]),
      icon: '🎯',
      color: 'bg-blue-500'
    })

    if (success) {
      setNewScenarioName('')
      setNewScenarioDescription('')
      setNewScenarioCategory('')
      setError('')
    }
  }

  // 删除训练场景
  const handleDeleteScenario = (scenarioId: number) => {
    const scenario = scenarios.find(s => s.id === scenarioId)
    if (scenario) {
      openDeleteConfirm('scenario', scenarioId.toString(), scenario.name)
    }
  }

  // 随机生成训练场景
  const handleRandomScenario = () => {
    // 场景名称模板
    const scenarioTypes = [
      '价格敏感型客户谈判', '技术导向型客户沟通', '决策者关系建立',
      '新客户开发', '老客户维护', '紧急需求响应', '跨文化商务沟通',
      '产品演示推介', '合同条款协商', '售后服务处理', '竞争对手分析',
      '客户异议处理', '长期合作建立', '项目招标应对', '客户投诉解决'
    ]

    // 场景分类
    const categories = [
      '销售谈判', '客户关系', '产品推广', '服务支持',
      '商务沟通', '项目管理', '市场开拓', '客户维护'
    ]

    // 场景描述模板
    const descriptionTemplates = [
      '专注于提升销售人员的专业沟通技巧和客户关系管理能力',
      '通过模拟真实商务场景，提高销售团队的应变能力和谈判技巧',
      '针对不同类型客户的特点，训练个性化的销售策略和沟通方式',
      '结合实际业务场景，提升销售人员的专业素养和服务水平',
      '通过角色扮演和情景模拟，增强销售团队的实战经验',
      '培养销售人员的客户洞察力和需求分析能力',
      '提升团队在复杂商务环境下的沟通协调能力'
    ]

    // 随机选择
    const randomScenarioType = scenarioTypes[Math.floor(Math.random() * scenarioTypes.length)]
    const randomCategory = categories[Math.floor(Math.random() * categories.length)]
    const randomDescription = descriptionTemplates[Math.floor(Math.random() * descriptionTemplates.length)]

    // 填充表单
    setNewScenarioName(randomScenarioType)
    setNewScenarioDescription(randomDescription)
    setNewScenarioCategory(randomCategory)

    toast.success(`已随机生成场景: ${randomScenarioType}`, {
      description: '表单已自动填充，您可以修改后添加'
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      {/* <DialogContent className="max-w-5xl max-h-[85vh] overflow-hidden"> */}
        <DialogContent
          className="max-w-5xl max-h-[85vh] overflow-hidden"
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >

        <DialogHeader className="pb-6">
          <DialogTitle className="text-lg font-medium flex items-center space-x-2 text-gray-800 dark:text-gray-200">
            <div className="p-1.5 bg-gray-100 dark:bg-gray-800 rounded-lg transition-colors duration-200">
              <Settings className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </div>
            <span>管理选项设置</span>
          </DialogTitle>
        </DialogHeader>

        {displayError && (
          <Alert variant="destructive" className="mb-4 relative">
            <AlertDescription className="pr-8">{displayError}</AlertDescription>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setError('')
                clearError()
              }}
              className="absolute top-2 right-2 h-6 w-6 p-0 text-red-400 hover:text-red-600 hover:bg-red-100 dark:hover:bg-red-900/40"
            >
              <X className="w-3 h-3" />
            </Button>
          </Alert>
        )}

        <div className="flex flex-col space-y-6 overflow-y-auto max-h-[calc(85vh-120px)] pr-2">
          {isInitialLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex flex-col items-center space-y-4">
                <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
                <p className="text-sm text-gray-500 dark:text-gray-400">正在加载数据...</p>
              </div>
            </div>
          ) : (
            <>
          {/* 标签页导航 */}
          <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg border border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setActiveTab('countries')}
              className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 rounded-md transition-all duration-200 ${
                activeTab === 'countries'
                  ? 'bg-white dark:bg-gray-700 shadow-sm text-gray-900 dark:text-gray-100 font-medium border border-gray-200 dark:border-gray-600'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-white/60 dark:hover:bg-gray-700/60'
              }`}
            >
              <Globe className="w-4 h-4" />
              <span>国家</span>
            </button>
            <button
              onClick={() => setActiveTab('products')}
              className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 rounded-md transition-all duration-200 ${
                activeTab === 'products'
                  ? 'bg-white dark:bg-gray-700 shadow-sm text-gray-900 dark:text-gray-100 font-medium border border-gray-200 dark:border-gray-600'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-white/60 dark:hover:bg-gray-700/60'
              }`}
            >
              <Package className="w-4 h-4" />
              <span>产品</span>
            </button>

            <button
              onClick={() => setActiveTab('scenarios')}
              className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2.5 rounded-md transition-all duration-200 ${
                activeTab === 'scenarios'
                  ? 'bg-white dark:bg-gray-700 shadow-sm text-gray-900 dark:text-gray-100 font-medium border border-gray-200 dark:border-gray-600'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-white/60 dark:hover:bg-gray-700/60'
              }`}
            >
              <Target className="w-4 h-4" />
              <span>训练场景</span>
            </button>
          </div>
          {/* 标签页内容 */}
          <div className="bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-800 dark:to-gray-900/50 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
            {activeTab === 'countries' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>国家选项管理</span>
                  </h3>
                  <Badge variant="secondary" className="text-xs">
                    {countries.length} 个国家
                  </Badge>
                </div>

                {/* 添加国家表单 */}
                <div className="bg-gradient-to-r from-gray-50 to-blue-50/30 dark:from-gray-900/50 dark:to-blue-900/10 rounded-xl p-4 border border-gray-100 dark:border-gray-800">
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block flex items-center space-x-2">
                    <Plus className="w-3 h-3 text-blue-500" />
                    <span>添加新国家</span>
                  </Label>
                  <div className="flex space-x-3">
                    <Input
                      placeholder="输入国家名称"
                      value={newCountryName}
                      onChange={(e) => setNewCountryName(e.target.value)}
                      className="flex-1 transition-all duration-200 focus:scale-[1.02] focus:shadow-md"
                      onKeyDown={(e) => e.key === 'Enter' && handleAddCountry()}
                    />
                    <Button
                      onClick={handleAddCountry}
                      disabled={isLoading}
                      className="px-4 transition-all duration-200 hover:scale-105 hover:shadow-md"
                    >
                      {isLoading ? (
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <Plus className="w-4 h-4 mr-2 transition-transform duration-200 group-hover:rotate-90" />
                      )}
                      添加
                    </Button>
                  </div>
                </div>

                {/* 国家列表 */}
                <div className="space-y-3">
                  {countries.map((country) => (
                    <div
                      key={country.id}
                      className="flex items-center justify-between p-4 bg-white dark:bg-gray-800/50 rounded-xl border border-gray-100 dark:border-gray-700/50 hover:border-gray-200 dark:hover:border-gray-600 hover:shadow-sm transition-all duration-200"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                          <Globe className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <span className="font-medium text-gray-800 dark:text-gray-200">{country.name}</span>
                          <div className="text-xs text-gray-500 dark:text-gray-400 font-mono">{country.code}</div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteCountry(country.id)}
                        disabled={isLoading}
                        className="text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                      >
                        {isLoading ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <Trash2 className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'products' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center space-x-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>产品选项管理</span>
                  </h3>
                  <Badge variant="secondary" className="text-xs">
                    {products.length} 个产品
                  </Badge>
                </div>

                {/* 添加产品表单 */}
                <div className="bg-gradient-to-r from-gray-50 to-purple-50/30 dark:from-gray-900/50 dark:to-purple-900/10 rounded-xl p-4 border border-gray-100 dark:border-gray-800">
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block flex items-center space-x-2">
                    <Plus className="w-3 h-3 text-purple-500" />
                    <span>添加新产品</span>
                  </Label>
                  <div className="space-y-3">
                    <Input
                      placeholder="输入产品名称"
                      value={newProductName}
                      onChange={(e) => setNewProductName(e.target.value)}
                      className="transition-all duration-200 focus:scale-[1.02] focus:shadow-md"
                    />
                    <div className="flex space-x-3">
                      <Input
                        placeholder="输入产品分类"
                        value={newProductCategory}
                        onChange={(e) => setNewProductCategory(e.target.value)}
                        className="flex-1 transition-all duration-200 focus:scale-[1.02] focus:shadow-md"
                      />
                      <Button
                        onClick={handleAddProduct}
                        disabled={isLoading}
                        className="px-4 transition-all duration-200 hover:scale-105 hover:shadow-md"
                      >
                        {isLoading ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Plus className="w-4 h-4 mr-2 transition-transform duration-200 group-hover:rotate-90" />
                        )}
                        添加
                      </Button>
                    </div>
                  </div>
                </div>

                {/* 产品列表 */}
                <div className="space-y-3">
                  {products.map((product) => (
                    <div
                      key={product.id}
                      className="flex items-center justify-between p-4 bg-white dark:bg-gray-800/50 rounded-xl border border-gray-100 dark:border-gray-700/50 hover:border-gray-200 dark:hover:border-gray-600 hover:shadow-sm transition-all duration-200"
                    >
                      <div className="flex-1">
                        <div className="font-medium text-gray-800 dark:text-gray-200 flex items-center space-x-2">
                          <Package className="w-4 h-4 text-purple-500" />
                          <span>{product.name}</span>
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="secondary" className="text-xs">
                            {product.category}
                          </Badge>
                          {product.description && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">{product.description}</span>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteProduct(product.id)}
                        disabled={isLoading}
                        className="text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                      >
                        {isLoading ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <Trash2 className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}



            {activeTab === 'scenarios' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>训练场景管理</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleRandomScenario}
                      className="ml-2 text-green-600 hover:text-green-700 hover:bg-green-50 dark:text-green-400 dark:hover:text-green-300 dark:hover:bg-green-900/20 transition-colors duration-200"
                      title="随机生成训练场景"
                    >
                      <Shuffle className="w-4 h-4" />
                    </Button>
                  </h3>
                  <Badge variant="secondary" className="text-xs">
                    {scenarios.length} 个场景
                  </Badge>
                </div>

                {/* 添加训练场景表单 */}
                <div className="bg-gradient-to-r from-gray-50 to-green-50/30 dark:from-gray-900/50 dark:to-green-900/10 rounded-xl p-4 border border-gray-100 dark:border-gray-800">
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block flex items-center space-x-2">
                    <Plus className="w-3 h-3 text-green-500" />
                    <span>添加新训练场景</span>
                  </Label>
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <Input
                        placeholder="输入场景名称"
                        value={newScenarioName}
                        onChange={(e) => setNewScenarioName(e.target.value)}
                        className="transition-all duration-200 focus:scale-[1.02] focus:shadow-md"
                      />
                      <Input
                        placeholder="输入场景分类"
                        value={newScenarioCategory}
                        onChange={(e) => setNewScenarioCategory(e.target.value)}
                        className="transition-all duration-200 focus:scale-[1.02] focus:shadow-md"
                      />
                    </div>
                    <Textarea
                      placeholder="输入场景描述（可选）"
                      value={newScenarioDescription}
                      onChange={(e) => setNewScenarioDescription(e.target.value)}
                      className="transition-all duration-200 focus:scale-[1.02] focus:shadow-md resize-none"
                      rows={2}
                    />
                    <div className="flex justify-end">
                      <Button
                        onClick={handleAddScenario}
                        disabled={isLoading}
                        className="px-4 transition-all duration-200 hover:scale-105 hover:shadow-md"
                      >
                        {isLoading ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Plus className="w-4 h-4 mr-2 transition-transform duration-200 group-hover:rotate-90" />
                        )}
                        添加场景
                      </Button>
                    </div>
                  </div>
                </div>

                {/* 训练场景列表 */}
                <div className="space-y-3">
                  {scenarios.map((scenario) => (
                    <div
                      key={scenario.id}
                      className="flex items-center justify-between p-4 bg-white dark:bg-gray-800/50 rounded-xl border border-gray-100 dark:border-gray-700/50 hover:border-gray-200 dark:hover:border-gray-600 hover:shadow-sm transition-all duration-200"
                    >
                      <div className="flex-1">
                        <div className="font-medium text-gray-800 dark:text-gray-200 flex items-center space-x-2">
                          <span className="text-lg">{scenario.icon || '🎯'}</span>
                          <span>{scenario.name}</span>
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="secondary" className="text-xs">
                            {scenario.category}
                          </Badge>
                          {scenario.description && (
                            <span className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-xs">
                              {scenario.description}
                            </span>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteScenario(scenario.id)}
                        disabled={isLoading}
                        className="text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                      >
                        {isLoading ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <Trash2 className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
            </>
          )}
        </div>

        {/* 关闭按钮 */}
        <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700 mt-6">
          <Button
            onClick={() => onOpenChange(false)}
            variant="outline"
            className="px-6 transition-all duration-200 hover:scale-105 hover:shadow-md hover:border-gray-300 dark:hover:border-gray-500"
          >
            关闭
          </Button>
        </div>
      </DialogContent>

      {/* 确认删除对话框 */}
      <Dialog open={deleteConfirm.isOpen} onOpenChange={(open) => open ? null : closeDeleteConfirm()} modal={true}>
        <DialogContent
          className="max-w-md"
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2 text-red-600 dark:text-red-400">
              <AlertTriangle className="w-5 h-5" />
              <span>确认删除</span>
            </DialogTitle>
          </DialogHeader>

          <div className="py-4">
            <p className="text-gray-700 dark:text-gray-300">
              确定要删除 <span className="font-semibold text-red-600 dark:text-red-400">"{deleteConfirm.name}"</span> 吗？
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              此操作无法撤销。
            </p>

            {/* {deleteError && (
              <div className="mt-4">
                <ErrorAlert
                  message={deleteError}
                  onClose={() => setDeleteError('')}
                />
              </div>
            )} */}
          </div>

          <DialogFooter className="flex space-x-2">
            <Button
              variant="outline"
              onClick={closeDeleteConfirm}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  删除中...
                </>
              ) : (
                '确认删除'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Dialog>
  )
}
