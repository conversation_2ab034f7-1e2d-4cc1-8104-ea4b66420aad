/**
 * Morphik 连接测试组件
 * 用于验证 Morphik Core API 的连接状态和基础功能
 */
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, XCircle, Activity, Database, Brain } from 'lucide-react';
import { useSystemHealth, useDocumentStats, useTestConnection } from '@/hooks/useMorphik';
import morphikApi from '@/services/morphikApi';

function MorphikConnectionTest() {
  const { data: health, isLoading: healthLoading, error: healthError } = useSystemHealth();
  const { data: stats, isLoading: statsLoading, error: statsError } = useDocumentStats();
  const { mutate: testConnection, isPending: testingConnection } = useTestConnection();

  const handleDirectApiTest = async () => {
    try {
      console.log('Testing direct API call...');
      const response = await morphikApi.testConnection();
      console.log('Direct API test result:', response);
    } catch (error) {
      console.error('Direct API test failed:', error);
    }
  };

  const getStatusColor = (status: string | boolean) => {
    if (typeof status === 'boolean') {
      return status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
    }
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800';
      case 'unhealthy':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string | boolean) => {
    if (typeof status === 'boolean') {
      return status ? <CheckCircle className="h-4 w-4" /> : <XCircle className="h-4 w-4" />;
    }
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4" />;
      case 'degraded':
        return <Activity className="h-4 w-4" />;
      case 'unhealthy':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* 连接测试按钮 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            Morphik Core 连接测试
          </CardTitle>
          <CardDescription>
            测试与 Morphik Core API 服务的连接状态
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Button
              onClick={() => testConnection()}
              disabled={testingConnection}
              className="w-full"
            >
              {testingConnection && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {testingConnection ? '测试连接中...' : '测试连接'}
            </Button>
            <Button
              onClick={handleDirectApiTest}
              variant="outline"
              className="w-full"
            >
              直接API测试
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 系统健康状态 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-primary" />
            系统健康状态
          </CardTitle>
          <CardDescription>
            Morphik Core 各服务组件的运行状态
          </CardDescription>
        </CardHeader>
        <CardContent>
          {healthLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">加载中...</span>
            </div>
          ) : healthError ? (
            <div className="text-center py-8">
              <XCircle className="h-12 w-12 text-red-500 mx-auto mb-2" />
              <p className="text-red-600">无法获取系统状态</p>
              <p className="text-sm text-muted-foreground mt-1">
                {healthError.message}
              </p>
            </div>
          ) : health ? (
            <div className="space-y-4">
              {/* 总体状态 */}
              <div className="flex items-center justify-between">
                <span className="font-medium">总体状态</span>
                <Badge className={getStatusColor(health.status)}>
                  {getStatusIcon(health.status)}
                  <span className="ml-1 capitalize">{health.status}</span>
                </Badge>
              </div>

              {/* 各服务状态 */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">服务状态</h4>
                <div className="grid grid-cols-2 gap-2">
                  {Object.entries(health.services).map(([service, status]) => (
                    <div key={service} className="flex items-center justify-between p-2 rounded-md bg-muted/50">
                      <span className="text-sm capitalize">{service.replace('_', ' ')}</span>
                      <Badge className={getStatusColor(status)} variant="outline">
                        {getStatusIcon(status)}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              {/* 系统指标 */}
              {health.metrics && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">系统指标</h4>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="p-2 rounded-md bg-muted/50">
                      <div className="text-xs text-muted-foreground">响应时间</div>
                      <div className="text-sm font-medium">{health.metrics.response_time}ms</div>
                    </div>
                    <div className="p-2 rounded-md bg-muted/50">
                      <div className="text-xs text-muted-foreground">内存使用</div>
                      <div className="text-sm font-medium">{health.metrics.memory_usage}%</div>
                    </div>
                    <div className="p-2 rounded-md bg-muted/50">
                      <div className="text-xs text-muted-foreground">CPU 使用</div>
                      <div className="text-sm font-medium">{health.metrics.cpu_usage}%</div>
                    </div>
                    <div className="p-2 rounded-md bg-muted/50">
                      <div className="text-xs text-muted-foreground">磁盘使用</div>
                      <div className="text-sm font-medium">{health.metrics.disk_usage}%</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : null}
        </CardContent>
      </Card>

      {/* 文档统计 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5 text-primary" />
            文档统计
          </CardTitle>
          <CardDescription>
            当前知识库中的文档数量和状态分布
          </CardDescription>
        </CardHeader>
        <CardContent>
          {statsLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">加载中...</span>
            </div>
          ) : statsError ? (
            <div className="text-center py-8">
              <XCircle className="h-12 w-12 text-red-500 mx-auto mb-2" />
              <p className="text-red-600">无法获取文档统计</p>
              <p className="text-sm text-muted-foreground mt-1">
                {statsError.message}
              </p>
            </div>
          ) : stats ? (
            <div className="space-y-4">
              {/* 总体统计 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 rounded-md bg-muted/50">
                  <div className="text-2xl font-bold text-primary">{stats.total_documents}</div>
                  <div className="text-sm text-muted-foreground">总文档数</div>
                </div>
                <div className="text-center p-4 rounded-md bg-muted/50">
                  <div className="text-2xl font-bold text-primary">{stats.total_chunks}</div>
                  <div className="text-sm text-muted-foreground">文档块数</div>
                </div>
              </div>

              {/* 状态分布 */}
              {stats.by_status && Object.keys(stats.by_status).length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">状态分布</h4>
                  <div className="space-y-1">
                    {Object.entries(stats.by_status).map(([status, count]) => (
                      <div key={status} className="flex items-center justify-between">
                        <span className="text-sm capitalize">{status.replace('_', ' ')}</span>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 类型分布 */}
              {stats.by_type && Object.keys(stats.by_type).length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">文件类型分布</h4>
                  <div className="space-y-1">
                    {Object.entries(stats.by_type).map(([type, count]) => (
                      <div key={type} className="flex items-center justify-between">
                        <span className="text-sm uppercase">{type}</span>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 其他指标 */}
              <div className="grid grid-cols-2 gap-2">
                <div className="p-2 rounded-md bg-muted/50">
                  <div className="text-xs text-muted-foreground">处理队列</div>
                  <div className="text-sm font-medium">{stats.processing_queue}</div>
                </div>
                <div className="p-2 rounded-md bg-muted/50">
                  <div className="text-xs text-muted-foreground">失败文档</div>
                  <div className="text-sm font-medium">{stats.failed_documents}</div>
                </div>
              </div>
            </div>
          ) : null}
        </CardContent>
      </Card>
    </div>
  );
}

export default MorphikConnectionTest;
