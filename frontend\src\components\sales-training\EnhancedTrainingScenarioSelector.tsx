/**
 * 增强版训练场景选择器
 * 提供多样化的训练场景选择功能
 */

import { useState, useEffect } from 'react'
import {
  Target,
  Users,
  Building2,
  Zap,
  Globe,
  Clock,
  Star,
  Trophy,
  ChevronRight,
  Filter,
  Search,
  Sparkles,
  TrendingUp,
  Brain,
  Heart,
  Shield,
  X
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import type { TrainingScenario, TrainingCustomer } from '@/types/salesTraining'

interface EnhancedTrainingScenarioSelectorProps {
  customer: TrainingCustomer
  onScenarioSelect: (scenario: TrainingScenario) => void
  onClose: () => void
}

// 模拟训练场景数据
const mockScenarios: TrainingScenario[] = [
  {
    id: '1',
    name: '价格敏感型客户谈判',
    description: '面对对价格极其敏感的客户，如何在保持利润的同时达成交易',
    industry: '制造业',
    customerType: 'procurement_manager',
    difficulty: 'intermediate',
    duration: 15,
    objectives: ['掌握价值销售技巧', '学会处理价格异议', '建立长期合作关系'],
    challenges: ['客户预算有限', '竞争对手报价更低', '需要快速决策'],
    tags: ['价格谈判', '价值销售', '异议处理'],
    icon: '💰',
    color: 'bg-yellow-500'
  },
  {
    id: '2',
    name: '技术导向型客户沟通',
    description: '与注重技术细节的客户进行深度技术交流和产品演示',
    industry: '科技',
    customerType: 'technical_expert',
    difficulty: 'advanced',
    duration: 25,
    objectives: ['掌握技术销售技巧', '建立技术可信度', '处理技术异议'],
    challenges: ['客户技术要求高', '需要深度技术知识', '竞品技术对比'],
    tags: ['技术销售', '产品演示', '技术异议'],
    icon: '🔧',
    color: 'bg-blue-500'
  },
  {
    id: '3',
    name: '跨文化商务谈判',
    description: '与不同文化背景的国际客户进行商务谈判',
    industry: '国际贸易',
    customerType: 'decision_maker',
    difficulty: 'expert',
    duration: 30,
    objectives: ['掌握跨文化沟通技巧', '理解文化差异', '建立国际信任'],
    challenges: ['文化差异大', '沟通方式不同', '商务礼仪要求'],
    tags: ['跨文化', '国际贸易', '商务礼仪'],
    icon: '🌍',
    color: 'bg-green-500'
  },
  {
    id: '4',
    name: '紧急项目销售',
    description: '客户有紧急项目需求，需要快速响应和决策',
    industry: '服务业',
    customerType: 'end_user',
    difficulty: 'intermediate',
    duration: 20,
    objectives: ['快速需求分析', '紧急方案制定', '快速决策促成'],
    challenges: ['时间压力大', '需求不明确', '竞争激烈'],
    tags: ['紧急项目', '快速响应', '时间管理'],
    icon: '⚡',
    color: 'bg-red-500'
  },
  {
    id: '5',
    name: '长期合作伙伴关系建立',
    description: '与潜在长期合作伙伴建立深度合作关系',
    industry: '金融',
    customerType: 'decision_maker',
    difficulty: 'advanced',
    duration: 35,
    objectives: ['建立信任关系', '制定合作框架', '长期价值规划'],
    challenges: ['关系建立周期长', '多方利益平衡', '合作模式复杂'],
    tags: ['合作伙伴', '关系建立', '长期规划'],
    icon: '🤝',
    color: 'bg-purple-500'
  }
]

const industryIcons = {
  '制造业': Building2,
  '科技': Zap,
  '国际贸易': Globe,
  '服务业': Users,
  '金融': TrendingUp,
  '医疗': Heart,
  '教育': Brain,
  '安全': Shield
}

const difficultyColors = {
  'beginner': 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400',
  'intermediate': 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400',
  'advanced': 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400',
  'expert': 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400'
}

const difficultyLabels = {
  'beginner': '初级',
  'intermediate': '中级',
  'advanced': '高级',
  'expert': '专家'
}

const customerTypeLabels = {
  'decision_maker': '决策者',
  'technical_expert': '技术专家',
  'procurement_manager': '采购经理',
  'end_user': '终端用户'
}

export function EnhancedTrainingScenarioSelector({ 
  customer, 
  onScenarioSelect, 
  onClose 
}: EnhancedTrainingScenarioSelectorProps) {
  const [scenarios] = useState<TrainingScenario[]>(mockScenarios)
  const [filteredScenarios, setFilteredScenarios] = useState<TrainingScenario[]>(mockScenarios)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedIndustry, setSelectedIndustry] = useState<string>('all')
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all')
  const [selectedCustomerType, setSelectedCustomerType] = useState<string>('all')

  // 获取所有行业
  const industries = Array.from(new Set(scenarios.map(s => s.industry)))

  // 过滤场景
  useEffect(() => {
    let filtered = scenarios

    if (searchTerm) {
      filtered = filtered.filter(scenario =>
        scenario.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        scenario.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        scenario.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    if (selectedIndustry !== 'all') {
      filtered = filtered.filter(scenario => scenario.industry === selectedIndustry)
    }

    if (selectedDifficulty !== 'all') {
      filtered = filtered.filter(scenario => scenario.difficulty === selectedDifficulty)
    }

    if (selectedCustomerType !== 'all') {
      filtered = filtered.filter(scenario => scenario.customerType === selectedCustomerType)
    }

    setFilteredScenarios(filtered)
  }, [searchTerm, selectedIndustry, selectedDifficulty, selectedCustomerType, scenarios])

  const handleScenarioSelect = (scenario: TrainingScenario) => {
    onScenarioSelect(scenario)
  }

  const getRecommendedScenarios = () => {
    // 基于客户信息推荐场景
    return scenarios.filter(scenario => 
      scenario.industry === customer.product.category ||
      scenario.tags.some(tag => customer.background?.preferences?.includes(tag))
    ).slice(0, 3)
  }

  return (
    <div className="space-y-6">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">选择训练场景</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            为 {customer.name} 选择合适的销售训练场景
          </p>
        </div>
        <Button 
          variant="destructive" 
          size="sm" 
          onClick={onClose} 
          className="h-8 w-8 p-0"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="搜索场景..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="行业" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有行业</SelectItem>
              {industries.map(industry => (
                <SelectItem key={industry} value={industry}>{industry}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="难度" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有难度</SelectItem>
              <SelectItem value="beginner">初级</SelectItem>
              <SelectItem value="intermediate">中级</SelectItem>
              <SelectItem value="advanced">高级</SelectItem>
              <SelectItem value="expert">专家</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">全部场景</TabsTrigger>
          <TabsTrigger value="recommended">推荐场景</TabsTrigger>
          <TabsTrigger value="popular">热门场景</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredScenarios.map((scenario, index) => (
              <ScenarioCard
                key={scenario.id}
                scenario={scenario}
                onSelect={handleScenarioSelect}
                index={index}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recommended" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {getRecommendedScenarios().map((scenario, index) => (
              <ScenarioCard
                key={scenario.id}
                scenario={scenario}
                onSelect={handleScenarioSelect}
                index={index}
                isRecommended
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="popular" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {scenarios.slice(0, 4).map((scenario, index) => (
              <ScenarioCard
                key={scenario.id}
                scenario={scenario}
                onSelect={handleScenarioSelect}
                index={index}
                isPopular
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

interface ScenarioCardProps {
  scenario: TrainingScenario
  onSelect: (scenario: TrainingScenario) => void
  index: number
  isRecommended?: boolean
  isPopular?: boolean
}

function ScenarioCard({ scenario, onSelect, index, isRecommended, isPopular }: ScenarioCardProps) {
  const IndustryIcon = industryIcons[scenario.industry as keyof typeof industryIcons] || Building2

  return (
    <Card 
      className="group cursor-pointer hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-gray-200 dark:border-gray-700 opacity-0 animate-fade-in-up"
      style={{animationDelay: `${index * 0.1}s`, animationFillMode: 'forwards'}}
      onClick={() => onSelect(scenario)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-12 h-12 rounded-lg ${scenario.color} flex items-center justify-center text-white text-xl shadow-md`}>
              {scenario.icon}
            </div>
            <div className="flex-1">
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                {scenario.name}
                {isRecommended && (
                  <Badge className="ml-2 bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400">
                    <Sparkles className="w-3 h-3 mr-1" />
                    推荐
                  </Badge>
                )}
                {isPopular && (
                  <Badge className="ml-2 bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400">
                    <Trophy className="w-3 h-3 mr-1" />
                    热门
                  </Badge>
                )}
              </CardTitle>
              <div className="flex items-center space-x-2 mt-1">
                <IndustryIcon className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">{scenario.industry}</span>
                <span className="text-gray-400">·</span>
                <Badge className={difficultyColors[scenario.difficulty]}>
                  {difficultyLabels[scenario.difficulty]}
                </Badge>
              </div>
            </div>
          </div>
          <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <CardDescription className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
          {scenario.description}
        </CardDescription>
        
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-1 text-gray-500">
            <Clock className="w-4 h-4" />
            <span>{scenario.duration} 分钟</span>
          </div>
          <div className="flex items-center space-x-1 text-gray-500">
            <Users className="w-4 h-4" />
            <span>{customerTypeLabels[scenario.customerType]}</span>
          </div>
        </div>

        <div className="flex flex-wrap gap-1">
          {scenario.tags.slice(0, 3).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {scenario.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{scenario.tags.length - 3}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
