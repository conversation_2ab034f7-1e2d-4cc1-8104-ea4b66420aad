"""
日志配置模块
配置应用的日志系统
"""

import logging
import sys
from typing import Optional
from loguru import logger


def setup_logger(log_level: str = "INFO", log_format: str = "text"):
    """
    设置日志配置
    
    Args:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: 日志格式 (text, json)
    """
    
    # 移除默认的 loguru 处理器
    logger.remove()
    
    # 设置日志级别
    level = log_level.upper()
    
    # 使用简化的文本格式日志，避免 JSON 格式问题
    log_format_str = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{module}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        format=log_format_str,
        level=level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 添加文件处理器
    logger.add(
        "logs/smolagents-service.log",
        format=log_format_str,
        level=level,
        rotation="100 MB",
        retention="30 days",
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # 添加错误日志文件处理器
    logger.add(
        "logs/smolagents-service-error.log",
        format=log_format_str,
        level="ERROR",
        rotation="50 MB",
        retention="30 days",
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # 配置标准库日志
    class InterceptHandler(logging.Handler):
        """拦截标准库日志并重定向到 loguru"""
        
        def emit(self, record):
            # 获取对应的 loguru 级别
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = record.levelno
            
            # 查找调用者
            frame, depth = logging.currentframe(), 2
            while frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1
            
            logger.opt(depth=depth, exception=record.exc_info).log(
                level, record.getMessage()
            )
    
    # 设置标准库日志处理器
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    # 设置第三方库日志级别
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("fastapi").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("aioredis").setLevel(logging.WARNING)
    
    logger.info(f"日志系统初始化完成 - 级别: {level}, 格式: {log_format}")


def get_logger(name: Optional[str] = None):
    """
    获取日志器实例
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    if name:
        return logger.bind(module=name)
    return logger
