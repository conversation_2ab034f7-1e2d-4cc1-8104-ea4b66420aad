/**
 * 销售训练对话框组件 - 重新构建版本
 * 专注于简洁清晰的设计和可靠的功能
 */

import { useState, useRef, useEffect } from 'react'
import {
  Send,
  User,
  Globe,
  Package,
  MessageSquare
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import type { TrainingCustomer, ChatMessage } from '@/types/salesTraining'

interface NewTrainingDialogProps {
  customer: TrainingCustomer
  open: boolean
  onClose: () => void
}

export function TrainingDialog({ customer, open, onClose }: NewTrainingDialogProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [inputValue, setInputValue] = useState('')
  const [messageCount, setMessageCount] = useState(0)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 初始化对话
  useEffect(() => {
    if (open) {
      const welcomeMessage: ChatMessage = {
        id: '1',
        role: 'customer',
        content: `你好！我是来自${customer.country.name}的${customer.name}，我们公司对${customer.product.name}很感兴趣。能详细介绍一下产品的特点和优势吗？`,
        timestamp: new Date(),
        metadata: {
          type: 'text',
          confidence: 0.9
        }
      }
      setMessages([welcomeMessage])
      setMessageCount(0)
    }
  }, [open, customer])

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSendMessage = () => {
    if (!inputValue.trim()) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue,
      timestamp: new Date(),
      metadata: {
        type: 'text',
        confidence: 1.0
      }
    }

    setMessages(prev => [...prev, userMessage])
    setMessageCount(prev => prev + 1)
    setInputValue('')

    // 模拟AI客户回复
    setTimeout(() => {
      const responses = [
        `谢谢您的详细介绍。我对${customer.product.name}的技术规格很感兴趣，能详细说说技术参数吗？`,
        `听起来很不错，但是价格方面如何呢？我们需要考虑成本效益。`,
        `我们公司在${customer.country.name}的市场环境比较特殊，这个产品能适应我们的需求吗？`,
        `售后服务方面是怎样的？我们需要技术支持和培训。`,
        `交付周期大概需要多长时间？我们项目时间比较紧。`,
        `能否提供一些成功案例？特别是在我们这个行业的。`
      ]

      const customerReply: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'customer',
        content: responses[Math.floor(Math.random() * responses.length)],
        timestamp: new Date(),
        metadata: {
          type: 'text',
          confidence: 0.85
        }
      }

      setMessages(prev => [...prev, customerReply])
    }, 1000 + Math.random() * 2000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }



  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()} modal={true}>
      <DialogContent 
        className="max-w-6xl h-[90vh] p-0 flex flex-col"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        {/* 头部工具栏 */}
        <div className="flex items-center px-6 py-4 border-b bg-white dark:bg-gray-800 flex-shrink-0">
          <div className="flex items-center space-x-4">
            <Avatar className="w-10 h-10">
              <AvatarFallback className="bg-blue-500 text-white">
                {customer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                与 {customer.name} 的销售训练
              </h2>
              <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <span>{customer.country.flag} {customer.country.name}</span>
                <span>·</span>
                <span>{customer.product.name}</span>
              </div>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex flex-1 min-h-0">
          {/* 左侧：客户信息面板 */}
          <div className="w-80 border-r bg-gray-50 dark:bg-gray-800 p-4 overflow-y-auto flex-shrink-0">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center">
                  <User className="w-4 h-4 mr-2 text-blue-500" />
                  客户信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Globe className="w-4 h-4 text-green-500" />
                    <span className="text-sm">
                      {customer.country.flag} {customer.country.name}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Package className="w-4 h-4 text-purple-500" />
                    <span className="text-sm">{customer.product.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-orange-500" />
                    <span className="text-sm">
                      {customer.background?.position || '采购经理'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="mt-4">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">训练要点</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• 主动了解客户需求和痛点</li>
                  <li>• 突出产品核心价值和优势</li>
                  <li>• 建立信任关系</li>
                  <li>• 处理异议和疑虑</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* 右侧：对话区域 */}
          <div className="flex-1 flex flex-col bg-gray-50 dark:bg-gray-900 min-h-0">
            {/* 消息列表 */}
            <div className="flex-1 overflow-y-auto min-h-0">
              {messages.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>开始您的销售训练对话</p>
                  </div>
                </div>
              ) : (
                <div className="p-4 space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[70%] rounded-lg px-4 py-3 shadow-sm ${
                          message.role === 'user'
                            ? 'bg-blue-500 text-white'
                            : 'bg-white border border-gray-200 text-gray-900'
                        }`}
                      >
                        <p className="text-sm leading-relaxed">{message.content}</p>
                        <p className={`text-xs mt-2 ${
                          message.role === 'user' ? 'text-blue-100' : 'text-gray-500'
                        }`}>
                          {message.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>

            {/* 输入区域 */}
            <div className="border-t bg-white dark:bg-gray-800 p-4">
              <div className="flex space-x-3">
                <Input
                  placeholder="输入您的回复..."
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim()}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4"
                >
                  <Send className="w-4 h-4 mr-2" />
                  发送
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 