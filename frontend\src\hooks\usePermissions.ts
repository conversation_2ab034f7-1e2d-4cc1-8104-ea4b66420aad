/**
 * 权限管理Hook
 * 提供前端权限检查功能
 */
import { useState, useEffect } from 'react'
import { useAuthStore } from '@/store/auth'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface UserPermissions {
  permissions: string[]
  roles: string[]
}

export const usePermissions = () => {
  const { user, token } = useAuthStore()
  const [userPermissions, setUserPermissions] = useState<UserPermissions>({
    permissions: [],
    roles: []
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 获取用户权限
  const fetchUserPermissions = async () => {
    if (!user || !token) {
      setLoading(false)
      return
    }

    try {
      const response = await fetch(`${API_BASE_URL}/admin/users/${user.id}/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        setUserPermissions({
          permissions: data.permissions.map((p: any) => p.name),
          roles: data.roles.map((r: any) => r.name)
        })
      } else {
        setError('获取用户权限失败')
      }
    } catch (err) {
      setError('网络错误')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUserPermissions()
  }, [user, token])

  // 检查是否有指定权限
  const hasPermission = (permission: string): boolean => {
    if (!user) return false
    if (user.is_superuser) return true
    return userPermissions.permissions.includes(permission)
  }

  // 检查是否有指定角色
  const hasRole = (role: string): boolean => {
    if (!user) return false
    if (user.is_superuser) return true
    return userPermissions.roles.includes(role)
  }

  // 检查是否有任一权限
  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!user) return false
    if (user.is_superuser) return true
    return permissions.some(permission => userPermissions.permissions.includes(permission))
  }

  // 检查是否有所有权限
  const hasAllPermissions = (permissions: string[]): boolean => {
    if (!user) return false
    if (user.is_superuser) return true
    return permissions.every(permission => userPermissions.permissions.includes(permission))
  }

  // 检查是否有任一角色
  const hasAnyRole = (roles: string[]): boolean => {
    if (!user) return false
    if (user.is_superuser) return true
    return roles.some(role => userPermissions.roles.includes(role))
  }

  // 检查菜单访问权限
  const hasMenuAccess = (menuPermission: string): boolean => {
    if (!user) return false
    if (user.is_superuser) return true
    return userPermissions.permissions.includes(menuPermission)
  }

  // 检查管理员权限
  const hasAdminAccess = (): boolean => {
    if (!user) return false
    if (user.is_superuser) return true
    return hasRole('admin') || hasPermission('admin:access')
  }

  return {
    userPermissions,
    loading,
    error,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    hasAnyRole,
    hasMenuAccess,
    hasAdminAccess,
    refetch: fetchUserPermissions
  }
}

// 基于菜单的权限常量
export const MENU_PERMISSIONS = {
  // 主要功能
  DASHBOARD_ACCESS: 'dashboard:access',
  TASKS_ACCESS: 'tasks:access',

  // 贸易教官
  TRADE_COACH_ACCESS: 'trade_coach:access',
  COMPANY_KNOWLEDGE_ACCESS: 'company_knowledge:access',
  TRADE_KNOWLEDGE_ACCESS: 'trade_knowledge:access',
  MARKET_ANALYSIS_ACCESS: 'market_analysis:access',
  GROUP_ANALYSIS_ACCESS: 'group_analysis:access',
  SALES_TRAINING_ACCESS: 'sales_training:access',
  ENHANCED_TRAINING_ACCESS: 'enhanced_training:access',
  LEARNING_PLAN_ACCESS: 'learning_plan:access',

  // AI工具
  AI_KNOWLEDGE_ACCESS: 'ai_knowledge:access',
  AI_CHAT_ACCESS: 'ai_chat:access',
  TEMPLATES_ACCESS: 'templates:access',

  // 系统功能
  ADMIN_ACCESS: 'admin:access',

  // 管理员功能
  USER_MANAGE: 'user:manage',
  ROLE_MANAGE: 'role:manage',
  PERMISSION_MANAGE: 'permission:manage',
} as const

// 兼容旧版本的权限常量
export const PERMISSIONS = {
  // 用户管理权限
  USER_READ: 'user:manage',
  USER_CREATE: 'user:manage',
  USER_UPDATE: 'user:manage',
  USER_DELETE: 'user:manage',

  // 角色管理权限
  ROLE_READ: 'role:manage',
  ROLE_CREATE: 'role:manage',
  ROLE_UPDATE: 'role:manage',
  ROLE_DELETE: 'role:manage',

  // 权限管理权限
  PERMISSION_READ: 'permission:manage',
  PERMISSION_CREATE: 'permission:manage',
  PERMISSION_UPDATE: 'permission:manage',
  PERMISSION_DELETE: 'permission:manage',
} as const

// 角色常量
export const ROLES = {
  ADMIN: 'admin',
} as const
