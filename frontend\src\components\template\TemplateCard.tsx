/**
 * 话术模板卡片组件
 * 显示模板的基本信息和操作按钮
 */

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Play,
  Pause,
  Edit,
  Eye,
  Trash2,
  Star,
  Clock,
  User,
  Tag
} from 'lucide-react';
import { Template } from '@/types/template';

interface TemplateCardProps {
  template: Template;
  isActive: boolean;
  onActivate: () => void;
  onDeactivate?: () => void;
  onEdit: () => void;
  onPreview: () => void;
  onDelete: () => void;
}

const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  isActive,
  onActivate,
  onDeactivate,
  onEdit,
  onPreview,
  onDelete
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'customer_service': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      'sales': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      'technical_support': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      'medical': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      'legal': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      'general': 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    };
    return colors[category] || colors.general;
  };

  const getRoleIcon = (roleCategory: string) => {
    const icons: Record<string, string> = {
      'customer_service': '🎧',
      'sales': '💼',
      'technical': '🔧',
      'medical': '⚕️',
      'legal': '⚖️',
      'general': '🤖'
    };
    return icons[roleCategory] || icons.general;
  };

  return (
    <Card className={`relative transition-all duration-200 hover:shadow-md ${
      isActive
        ? 'ring-2 ring-green-500 border-green-200 bg-green-50 dark:bg-green-950 dark:border-green-800'
        : 'hover:border-primary/50'
    }`}>
      {/* 激活状态指示器 */}
      {isActive && (
        <div className="absolute top-2 right-2">
          <Badge className="bg-green-500 text-white">
            <Play className="h-3 w-3 mr-1" />
            激活中
          </Badge>
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2">
              <span className="text-xl">{getRoleIcon(template.role.category)}</span>
              {template.name}
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
              {template.description}
            </p>
          </div>
        </div>

        {/* 类别和标签 */}
        <div className="flex flex-wrap gap-2 mt-3">
          <Badge className={getCategoryColor(template.category)}>
            {template.category}
          </Badge>
          {template.tags.slice(0, 2).map(tag => (
            <Badge key={tag} variant="outline" className="text-xs">
              <Tag className="h-3 w-3 mr-1" />
              {tag}
            </Badge>
          ))}
          {template.tags.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{template.tags.length - 2}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* 角色和风格信息 */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center gap-2 text-sm">
            <User className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{template.role.name}</span>
            <span className="text-muted-foreground">·</span>
            <span className="text-muted-foreground">{template.style.name}</span>
          </div>

          {/* 统计信息 */}
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {formatDate(template.updated_at)}
            </div>
            <div className="flex items-center gap-1">
              <Star className="h-3 w-3" />
              {template.rating.toFixed(1)}
            </div>
            <div>
              使用 {template.usage_count} 次
            </div>
          </div>
        </div>

        {/* 作者和版本信息 */}
        <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
          <span>作者: {template.author}</span>
          <span>v{template.version}</span>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant={isActive ? "outline" : "default"}
            onClick={isActive ? onDeactivate : onActivate}
            className="flex-1"
          >
            {isActive ? (
              <>
                <Pause className="h-4 w-4 mr-1" />
                取消激活
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-1" />
                激活
              </>
            )}
          </Button>

          <Button size="sm" variant="outline" onClick={onPreview}>
            <Eye className="h-4 w-4" />
          </Button>

          <Button size="sm" variant="outline" onClick={onEdit}>
            <Edit className="h-4 w-4" />
          </Button>

          {!template.is_default && (
            <Button
              size="sm"
              variant="outline"
              onClick={onDelete}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* 默认模板标识 */}
        {template.is_default && (
          <div className="mt-2 text-xs text-muted-foreground flex items-center gap-1">
            <Star className="h-3 w-3" />
            系统默认模板
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TemplateCard;
