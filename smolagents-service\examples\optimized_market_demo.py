#!/usr/bin/env python3
"""
优化版智能市场分析演示系统
解决了原版本的超时和稳定性问题

优化特性：
- 减少搜索策略数量（从7个减少到4个）
- 优化超时设置和错误处理
- 增加重试机制
- 更好的资源管理
"""

import asyncio
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
import httpx
from dataclasses import dataclass


@dataclass
class SearchQuery:
    """搜索查询结构"""
    query: str
    category: str
    priority: int
    description: str


@dataclass
class AnalysisContext:
    """分析上下文"""
    product: str
    region: str
    time_period: str
    analysis_type: str


class OptimizedMarketAnalysis:
    """优化版智能市场分析系统"""
    
    def __init__(self, base_url: str = "http://localhost:8002"):
        self.base_url = base_url
        self.session: Optional[httpx.AsyncClient] = None
        
    async def __aenter__(self):
        self.session = httpx.AsyncClient(timeout=120.0)  # 优化超时时间
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.aclose()
    
    def parse_search_term(self, search_term: str) -> AnalysisContext:
        """智能解析搜索词"""
        print(f"🔍 解析搜索词: {search_term}")
        
        # 产品识别
        product_match = re.search(r'([^0-9\s]+?)(?=\s*20\d{2}|\s*市场|\s*销售|\s*分析)', search_term)
        product = product_match.group(1).strip() if product_match else "未知产品"
        
        # 地区识别
        region_keywords = {
            '亚洲': ['亚洲', 'Asia', 'Asian'],
            '中国': ['中国', 'China', 'Chinese'],
            '美国': ['美国', 'USA', 'US', 'America'],
            '欧洲': ['欧洲', 'Europe', 'European'],
            '全球': ['全球', 'global', 'worldwide', 'international']
        }
        
        region = "全球"
        for region_name, keywords in region_keywords.items():
            if any(keyword in search_term for keyword in keywords):
                region = region_name
                break
        
        # 时间识别
        time_match = re.search(r'20\d{2}', search_term)
        time_period = time_match.group(0) if time_match else "2024"
        
        # 分析类型识别
        analysis_keywords = {
            '销售分析': ['销售', '销量', 'sales', 'revenue'],
            '市场趋势': ['趋势', '发展', 'trend', 'growth'],
            '竞争分析': ['竞争', '对手', 'competition', 'competitor'],
            '综合分析': ['分析', 'analysis', '研究']
        }
        
        analysis_type = "综合分析"
        for analysis_name, keywords in analysis_keywords.items():
            if any(keyword in search_term for keyword in keywords):
                analysis_type = analysis_name
                break
        
        context = AnalysisContext(product, region, time_period, analysis_type)
        
        print(f"✅ 解析结果:")
        print(f"   产品: {context.product}")
        print(f"   地区: {context.region}")
        print(f"   时间: {context.time_period}")
        print(f"   分析类型: {context.analysis_type}")
        print()
        
        return context
    
    def generate_search_queries(self, context: AnalysisContext) -> List[SearchQuery]:
        """生成优化的搜索查询（4个核心策略）"""
        print("🎯 生成优化搜索策略...")
        
        queries = []
        
        # 1. 市场规模和基础信息
        queries.append(SearchQuery(
            query=f"{context.product} {context.region} market size {context.time_period} industry report",
            category="市场规模",
            priority=1,
            description="获取市场规模和基础行业数据"
        ))
        
        # 2. 销售趋势和增长预测
        queries.append(SearchQuery(
            query=f"{context.product} sales trends {context.region} {context.time_period} growth forecast 2025",
            category="销售趋势",
            priority=1,
            description="获取销售数据和增长趋势"
        ))
        
        # 3. 竞争格局和市场份额
        queries.append(SearchQuery(
            query=f"{context.product} {context.region} competitors market share top companies {context.time_period}",
            category="竞争分析",
            priority=1,
            description="分析竞争格局和主要参与者"
        ))
        
        # 4. 消费者需求和未来展望
        queries.append(SearchQuery(
            query=f"{context.product} consumer demand {context.region} {context.time_period} 2025 outlook trends",
            category="需求预测",
            priority=2,
            description="分析消费者需求和未来预测"
        ))
        
        print(f"✅ 生成了 {len(queries)} 个优化搜索策略:")
        for i, query in enumerate(queries, 1):
            print(f"   {i}. [{query.category}] {query.description}")
        print()
        
        return queries
    
    async def execute_search_with_retry(self, query: SearchQuery, max_retries: int = 2) -> Optional[Dict[str, Any]]:
        """执行搜索（带重试机制）"""
        for attempt in range(max_retries + 1):
            try:
                search_prompt = f"""
请使用网络搜索工具查找以下信息：

搜索查询: {query.query}
搜索目的: {query.description}

请执行网络搜索，并提供以下格式的结果：
1. 找到的关键信息和数据
2. 相关的数字、统计数据或趋势
3. 重要的市场洞察

请确保搜索真实的网络数据，保持结果简洁。
"""
                
                data = {
                    "query": search_prompt,
                    "max_steps": 8,  # 进一步减少步骤数
                    "use_cache": False,
                    "enable_web_search": True,
                    "timeout": 90  # 减少单次搜索超时时间
                }
                
                response = await self.session.post(f"{self.base_url}/api/v1/agent/query", json=data)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        return {
                            'query': query.query,
                            'result': result.get('result'),
                            'priority': query.priority,
                            'description': query.description
                        }
                    else:
                        if attempt < max_retries:
                            print(f"      ⚠️  尝试 {attempt + 1} 失败，重试中...")
                            await asyncio.sleep(2)
                            continue
                        else:
                            print(f"      ❌ 搜索失败: {result.get('error', '未知错误')}")
                            return None
                else:
                    if attempt < max_retries:
                        print(f"      ⚠️  HTTP {response.status_code}，重试中...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        print(f"      ❌ 请求失败: HTTP {response.status_code}")
                        return None
                        
            except asyncio.TimeoutError:
                if attempt < max_retries:
                    print(f"      ⏰ 超时，重试中...")
                    await asyncio.sleep(3)
                    continue
                else:
                    print(f"      ❌ 搜索超时")
                    return None
            except Exception as e:
                if attempt < max_retries:
                    print(f"      ⚠️  异常，重试中: {e}")
                    await asyncio.sleep(2)
                    continue
                else:
                    print(f"      ❌ 搜索异常: {e}")
                    return None
        
        return None
    
    async def execute_intelligent_search(self, queries: List[SearchQuery]) -> Dict[str, Any]:
        """执行优化的智能搜索"""
        print("🌐 开始执行优化搜索...")
        
        search_results = {}
        successful_searches = 0
        
        for i, query in enumerate(queries, 1):
            print(f"   [{i}/{len(queries)}] 搜索: {query.category}")
            
            result = await self.execute_search_with_retry(query)
            
            if result:
                search_results[query.category] = result
                successful_searches += 1
                print(f"      ✅ 成功获取数据")
            
            # 添加延迟避免过于频繁的请求
            if i < len(queries):
                await asyncio.sleep(3)
        
        print(f"✅ 搜索完成，成功获取 {successful_searches}/{len(queries)} 个数据源")
        print()
        
        return search_results
    
    async def generate_analysis_report(self, context: AnalysisContext, search_results: Dict[str, Any]) -> str:
        """生成分析报告（简化版）"""
        print("📊 生成市场分析报告...")
        
        if not search_results:
            return "无法生成分析报告：未获取到有效的搜索数据"
        
        # 构建简化的分析提示词
        data_summary = ""
        for category, data in search_results.items():
            result_summary = str(data['result'])[:400] if data['result'] else "无数据"
            data_summary += f"{category}: {result_summary}\n\n"
        
        analysis_prompt = f"""
基于以下真实搜索数据，为"{context.product}"在{context.region}市场生成专业分析报告：

搜索数据摘要：
{data_summary[:1500]}

请生成包含以下结构的报告：

# {context.product} - {context.region}市场分析报告

## 1. 市场现状
- 市场规模和增长情况
- 主要参与者

## 2. 关键趋势
- 销售趋势
- 发展方向

## 3. 竞争分析
- 主要竞争对手
- 市场份额

## 4. 未来预测
- 2025年展望
- 增长驱动因素

## 5. 结论建议
- 核心洞察
- 战略建议

要求：基于真实数据，逻辑清晰，适合商业决策，控制在600字以内。
请直接生成分析报告。
"""
        
        try:
            print(f"   提示词长度: {len(analysis_prompt)} 字符")
            
            data = {
                "query": analysis_prompt,
                "max_steps": 8,
                "use_cache": False,
                "enable_web_search": False,
                "timeout": 90
            }
            
            response = await self.session.post(f"{self.base_url}/api/v1/agent/query", json=data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    analysis_report = result.get('result', '')
                    if analysis_report:
                        print("✅ 分析报告生成完成")
                        return analysis_report
                    else:
                        return "分析报告生成失败: 返回结果为空"
                else:
                    error_msg = result.get('error', '未知错误')
                    return f"分析报告生成失败: {error_msg}"
            else:
                return f"分析报告生成失败: HTTP {response.status_code}"
                
        except Exception as e:
            return f"分析报告生成失败: {str(e)}"

    async def run_analysis(self, search_term: str) -> Dict[str, Any]:
        """运行完整的优化分析流程"""
        start_time = datetime.now()
        print("🚀 启动优化版市场分析系统")
        print("=" * 60)
        print(f"搜索词: {search_term}")
        print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        print()

        try:
            # 1. 解析搜索词
            context = self.parse_search_term(search_term)

            # 2. 生成搜索策略
            queries = self.generate_search_queries(context)

            # 3. 执行智能搜索
            search_results = await self.execute_intelligent_search(queries)

            if not search_results:
                return {
                    'success': False,
                    'error': '未能获取有效的市场数据，请检查网络连接或稍后重试',
                    'context': context.__dict__
                }

            # 4. 生成分析报告
            analysis_report = await self.generate_analysis_report(context, search_results)

            # 计算执行时间
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            print("=" * 60)
            print("🎉 优化分析完成!")
            print(f"执行时间: {execution_time:.2f} 秒")
            print(f"数据源数量: {len(search_results)}")
            print("=" * 60)

            return {
                'success': True,
                'context': context.__dict__,
                'search_results': search_results,
                'analysis_report': analysis_report,
                'execution_time': execution_time,
                'data_sources_count': len(search_results),
                'timestamp': end_time.isoformat()
            }

        except Exception as e:
            error_msg = f"分析执行失败: {e}"
            print(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'context': context.__dict__ if 'context' in locals() else None
            }


async def main():
    """主演示函数"""
    search_term = "沙滩包2024亚洲市场销售情况分析及2025市场增长预期"

    print("🌟 优化版智能市场分析演示")
    print("解决了超时和稳定性问题")
    print("=" * 60)
    print()

    async with OptimizedMarketAnalysis() as analyzer:
        result = await analyzer.run_analysis(search_term)

        if result['success']:
            print("\n" + "=" * 60)
            print("📊 市场分析报告")
            print("=" * 60)
            print(result['analysis_report'])
            print("\n" + "=" * 60)
            print("📈 分析统计")
            print(f"数据源数量: {result['data_sources_count']}")
            print(f"执行时间: {result['execution_time']:.2f} 秒")
            print(f"完成时间: {result['timestamp']}")
        else:
            print(f"\n❌ 分析失败: {result['error']}")


if __name__ == "__main__":
    asyncio.run(main())
