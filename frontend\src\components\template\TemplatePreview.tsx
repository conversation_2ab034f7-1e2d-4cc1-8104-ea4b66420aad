/**
 * 话术模板预览组件
 * 显示模板的详细信息和示例对话
 */

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Eye,
  MessageSquare,
  User,
  Settings,
  Tag,
  Clock,
  Star,
  Play,
  Loader2
} from 'lucide-react';
import { Template } from '@/types/template';
import morphikApi from '@/services/morphikApi';
import { toast } from 'sonner';

interface TemplatePreviewProps {
  template: Template;
  open: boolean;
  onClose: () => void;
}

const TemplatePreview: React.FC<TemplatePreviewProps> = ({
  template,
  open,
  onClose
}) => {
  const [testQuery, setTestQuery] = useState('');
  const [testResponse, setTestResponse] = useState('');
  const [testing, setTesting] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleTestTemplate = async () => {
    if (!testQuery.trim()) {
      toast.error('请输入测试问题');
      return;
    }

    setTesting(true);
    try {
      const response = await morphikApi.ragQuery({
        query: testQuery,
        prompt_overrides: template.prompt_overrides,
        max_chunks: 3,
        temperature: template.role.temperature || 0.7,
        max_tokens: template.role.max_tokens || 500
      });

      setTestResponse(response.completion);
      toast.success('测试完成');
    } catch (error) {
      console.error('测试模板失败:', error);
      toast.error('测试失败，请检查Morphik服务连接');
      setTestResponse('测试失败，请检查Morphik服务连接或稍后重试。');
    } finally {
      setTesting(false);
    }
  };

  const getExampleQueries = () => {
    const examples: Record<string, string[]> = {
      'customer_service': [
        '我的订单什么时候能到？',
        '如何申请退款？',
        '产品质量有问题怎么办？'
      ],
      'sales': [
        '这个产品有什么优势？',
        '价格能优惠吗？',
        '有类似的产品推荐吗？'
      ],
      'technical_support': [
        '系统登录不了怎么办？',
        '如何重置密码？',
        '软件安装失败的解决方法'
      ],
      'general': [
        '请介绍一下你的功能',
        '你能帮我做什么？',
        '如何使用这个系统？'
      ]
    };

    return examples[template.category] || examples.general;
  };

  return (
    <Dialog open={open} onOpenChange={onClose} modal={true}>
      <DialogContent
        className="max-w-4xl max-h-[90vh] overflow-y-auto"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            模板预览 - {template.name}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="role">角色设定</TabsTrigger>
            <TabsTrigger value="prompt">提示词</TabsTrigger>
            <TabsTrigger value="test">测试</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* 基本信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <span className="text-2xl">{template.role.icon}</span>
                  {template.name}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">{template.description}</p>

                <div className="flex flex-wrap gap-2">
                  <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                    {template.category}
                  </Badge>
                  {template.tags.map(tag => (
                    <Badge key={tag} variant="outline">
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>作者: {template.author}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Settings className="h-4 w-4 text-muted-foreground" />
                    <span>版本: v{template.version}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>更新: {formatDate(template.updated_at)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-muted-foreground" />
                    <span>评分: {template.rating.toFixed(1)} ({template.usage_count} 次使用)</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 角色和风格概览 */}
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">角色信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div>
                      <span className="font-medium">名称:</span> {template.role.name}
                    </div>
                    <div>
                      <span className="font-medium">描述:</span> {template.role.description}
                    </div>
                    <div>
                      <span className="font-medium">风格:</span> {template.role.style}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">回答风格</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div>
                      <span className="font-medium">名称:</span> {template.style.name}
                    </div>
                    <div>
                      <span className="font-medium">描述:</span> {template.style.description}
                    </div>
                    <div>
                      <span className="font-medium">类别:</span> {template.style.category}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="role" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>角色设定详情</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium">角色名称:</span> {template.role.name}
                  </div>
                  <div>
                    <span className="font-medium">角色图标:</span> {template.role.icon}
                  </div>
                  <div>
                    <span className="font-medium">角色类别:</span> {template.role.category}
                  </div>
                  <div>
                    <span className="font-medium">回答风格:</span> {template.role.style}
                  </div>
                  <div>
                    <span className="font-medium">温度参数:</span> {template.role.temperature}
                  </div>
                  <div>
                    <span className="font-medium">最大令牌:</span> {template.role.max_tokens}
                  </div>
                </div>

                <div>
                  <span className="font-medium">角色描述:</span>
                  <p className="mt-1 text-muted-foreground">{template.role.description}</p>
                </div>

                <div>
                  <span className="font-medium">系统提示词:</span>
                  <pre className="mt-1 p-3 bg-muted rounded-md text-sm whitespace-pre-wrap">
                    {template.role.system_prompt}
                  </pre>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="prompt" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>提示词模板</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <span className="font-medium">查询提示词模板:</span>
                    <pre className="mt-1 p-3 bg-muted rounded-md text-sm whitespace-pre-wrap">
                      {template.prompt_overrides.query?.prompt_template}
                    </pre>
                  </div>

                  {template.custom_instructions && (
                    <div>
                      <span className="font-medium">自定义指令:</span>
                      <pre className="mt-1 p-3 bg-muted rounded-md text-sm whitespace-pre-wrap">
                        {template.custom_instructions}
                      </pre>
                    </div>
                  )}

                  <div className="text-sm text-muted-foreground">
                    <p><strong>占位符说明:</strong></p>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li><code>{'{question}'}</code> - 用户的问题</li>
                      <li><code>{'{context}'}</code> - 从知识库检索到的相关信息</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="test" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  模板测试
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">测试问题</label>
                  <div className="flex gap-2">
                    <Input
                      value={testQuery}
                      onChange={(e) => setTestQuery(e.target.value)}
                      placeholder="输入测试问题..."
                      onKeyPress={(e) => e.key === 'Enter' && !testing && handleTestTemplate()}
                    />
                    <Button onClick={handleTestTemplate} disabled={testing || !testQuery.trim()}>
                      {testing ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Play className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">示例问题</label>
                  <div className="flex flex-wrap gap-2">
                    {getExampleQueries().map((example, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => setTestQuery(example)}
                      >
                        {example}
                      </Button>
                    ))}
                  </div>
                </div>

                {testResponse && (
                  <div>
                    <label className="text-sm font-medium mb-2 block">AI回答</label>
                    <div className="p-3 bg-muted rounded-md">
                      <pre className="whitespace-pre-wrap text-sm">{testResponse}</pre>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end pt-4 border-t">
          <Button onClick={onClose}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TemplatePreview;
