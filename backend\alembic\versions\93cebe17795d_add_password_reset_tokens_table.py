"""Add password reset tokens table

Revision ID: 93cebe17795d
Revises: 93b39bac2186
Create Date: 2025-07-04 07:25:28.415157

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '93cebe17795d'
down_revision = '93b39bac2186'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
