/**
 * 话术模板集成服务
 * 负责将激活的模板应用到Morphik API调用中
 */

import templateService from './templateService';
// import templateAnalytics from './templateAnalytics';
import { Template, PromptOverrides } from '@/types/template';
import { RAGQueryRequest } from '@/types/morphik';

class TemplateIntegrationService {
  /**
   * 获取当前激活的模板配置
   */
  getActiveTemplateConfig(): {
    template: Template | null;
    promptOverrides: PromptOverrides | null;
    temperature?: number;
    maxTokens?: number;
  } {
    // 直接获取激活模板，新的getActiveTemplate已经包含状态同步逻辑
    const activeTemplate = templateService.getActiveTemplate();

    if (!activeTemplate) {
      return {
        template: null,
        promptOverrides: null
      };
    }

    // 调试日志
    console.log('🎭 当前激活模板:', activeTemplate.name);
    console.log('📝 模板prompt_template:', activeTemplate.prompt_overrides?.query?.prompt_template?.substring(0, 100) + '...');

    return {
      template: activeTemplate,
      promptOverrides: activeTemplate.prompt_overrides,
      temperature: activeTemplate.role.temperature,
      maxTokens: activeTemplate.role.max_tokens
    };
  }

  /**
   * 将模板配置应用到RAG查询请求中
   */
  applyTemplateToRAGQuery(request: RAGQueryRequest): RAGQueryRequest {
    const config = this.getActiveTemplateConfig();

    if (!config.template || !config.promptOverrides) {
      return request;
    }

    // 创建增强的请求
    const enhancedRequest: RAGQueryRequest = {
      ...request,
      prompt_overrides: config.promptOverrides,
    };

    // 应用模板的温度和最大令牌数设置（如果请求中没有指定）
    if (config.temperature !== undefined && !request.temperature) {
      enhancedRequest.temperature = config.temperature;
    }

    if (config.maxTokens !== undefined && !request.max_tokens) {
      enhancedRequest.max_tokens = config.maxTokens;
    }

    return enhancedRequest;
  }

  /**
   * 检查是否有激活的模板
   */
  hasActiveTemplate(): boolean {
    return templateService.getActiveTemplate() !== null;
  }

  /**
   * 获取激活模板的基本信息
   */
  getActiveTemplateInfo(): {
    name: string;
    category: string;
    roleName: string;
    styleDescription: string;
  } | null {
    const template = templateService.getActiveTemplate();

    if (!template) {
      return null;
    }

    return {
      name: template.name,
      category: template.category,
      roleName: template.role.name,
      styleDescription: template.style.description
    };
  }

  /**
   * 记录模板使用统计
   */
  async recordTemplateUsage(success: boolean, responseTime?: number, userQuery?: string): Promise<void> {
    const template = templateService.getActiveTemplate();

    if (!template) {
      return;
    }

    try {
      // 更新模板服务的统计
      await templateService.updateTemplateStats(template.id, success, responseTime);

      // 记录到分析服务
      // templateAnalytics.recordUsage(template.id, success, responseTime, userQuery);
    } catch (error) {
      console.error('记录模板使用统计失败:', error);
    }
  }

  /**
   * 为特定场景应用模板
   */
  applyTemplateForContext(
    request: RAGQueryRequest,
    context: 'knowledge-base' | 'chat' | 'general'
  ): RAGQueryRequest {
    const config = this.getActiveTemplateConfig();

    if (!config.template || !config.promptOverrides) {
      return request;
    }

    // 根据上下文调整模板应用
    let enhancedRequest = this.applyTemplateToRAGQuery(request);

    // 为不同场景添加特定的上下文信息
    if (config.promptOverrides.query?.prompt_template) {
      let contextualPrompt = config.promptOverrides.query.prompt_template;

      // 根据使用场景添加额外的上下文指令
      switch (context) {
        case 'knowledge-base':
          contextualPrompt = `${contextualPrompt}\n\n注意：这是在知识库系统中的查询，请基于检索到的文档内容进行回答。`;
          break;
        case 'chat':
          contextualPrompt = `${contextualPrompt}\n\n注意：这是在对话系统中的交互，请保持对话的连贯性和友好性。`;
          break;
        case 'general':
          // 保持原始模板不变
          break;
      }

      enhancedRequest = {
        ...enhancedRequest,
        prompt_overrides: {
          ...enhancedRequest.prompt_overrides,
          query: {
            ...enhancedRequest.prompt_overrides?.query,
            prompt_template: contextualPrompt
          }
        }
      };
    }

    return enhancedRequest;
  }

  /**
   * 验证模板是否适用于当前查询
   */
  validateTemplateForQuery(query: string): {
    isValid: boolean;
    warnings: string[];
    suggestions: string[];
  } {
    const template = templateService.getActiveTemplate();
    const warnings: string[] = [];
    const suggestions: string[] = [];

    if (!template) {
      return {
        isValid: true,
        warnings: [],
        suggestions: []
      };
    }

    // 检查查询长度
    if (query.length > 500) {
      warnings.push('查询内容较长，可能影响模板效果');
    }

    // 检查模板类别与查询内容的匹配度
    const queryLower = query.toLowerCase();
    const categoryKeywords: Record<string, string[]> = {
      'customer_service': ['问题', '帮助', '支持', '服务', '投诉', '退款'],
      'sales': ['购买', '价格', '产品', '推荐', '优惠', '销售'],
      'technical_support': ['错误', '故障', '安装', '配置', '技术', '修复'],
      'medical': ['症状', '治疗', '药物', '健康', '医疗', '诊断'],
      'legal': ['法律', '合同', '权利', '义务', '法规', '条款']
    };

    const templateCategory = template.category;
    const keywords = categoryKeywords[templateCategory];

    if (keywords && !keywords.some(keyword => queryLower.includes(keyword))) {
      suggestions.push(`当前查询可能不太适合"${template.name}"模板，考虑使用更通用的模板`);
    }

    return {
      isValid: true,
      warnings,
      suggestions
    };
  }

  /**
   * 获取模板使用提示
   */
  getTemplateUsageHint(): string | null {
    const template = templateService.getActiveTemplate();

    if (!template) {
      return null;
    }

    return `当前使用"${template.name}"模板 (${template.role.name} - ${template.style.name})`;
  }

  /**
   * 为模板预览生成示例查询
   */
  generateSampleQuery(template: Template): string {
    const sampleQueries: Record<string, string[]> = {
      'customer_service': [
        '我的订单什么时候能到？',
        '如何申请退款？',
        '产品质量有问题怎么办？'
      ],
      'sales': [
        '这个产品有什么优势？',
        '价格能优惠吗？',
        '有类似的产品推荐吗？'
      ],
      'technical_support': [
        '系统登录不了怎么办？',
        '如何重置密码？',
        '软件安装失败的解决方法'
      ],
      'medical': [
        '这种症状需要注意什么？',
        '有什么预防措施？',
        '什么情况下需要就医？'
      ],
      'legal': [
        '这个合同条款是什么意思？',
        '我的权利有哪些？',
        '如何维护自己的合法权益？'
      ],
      'general': [
        '请介绍一下相关信息',
        '能详细说明一下吗？',
        '有什么需要注意的？'
      ]
    };

    const queries = sampleQueries[template.category] || sampleQueries.general;
    return queries[Math.floor(Math.random() * queries.length)];
  }
}

export const templateIntegration = new TemplateIntegrationService();
export default templateIntegration;
