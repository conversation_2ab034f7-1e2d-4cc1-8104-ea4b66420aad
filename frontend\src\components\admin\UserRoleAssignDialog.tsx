/**
 * 用户角色分配对话框组件
 */
import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, Shield, Users, ArrowRight, Check } from 'lucide-react'
import { useAuthStore } from '@/store/auth'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface Permission {
  id: number
  name: string
  description: string
  category: string
}

interface Role {
  id: number
  name: string
  description: string
  is_active: boolean
  created_at: string
  permissions: Permission[]
}

interface AdminUser {
  id: number
  username: string
  email: string
  full_name?: string
  is_active: boolean
  is_superuser: boolean
  created_at: string
  roles: string[]
}

interface UserRoleAssignDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: AdminUser | null
  onSuccess: () => void
}

export const UserRoleAssignDialog: React.FC<UserRoleAssignDialogProps> = ({
  open,
  onOpenChange,
  user,
  onSuccess,
}) => {
  const { token } = useAuthStore()
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [allRoles, setAllRoles] = useState<Role[]>([])
  const [userCurrentRoles, setUserCurrentRoles] = useState<Role[]>([])
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([])

  // 获取所有角色
  const fetchAllRoles = async () => {
    if (!token) return

    try {
      const response = await fetch(`${API_BASE_URL}/admin/roles`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data: Role[] = await response.json()
        setAllRoles(data.filter(role => role.is_active)) // 只显示激活的角色
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '获取角色列表失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    }
  }

  // 获取用户当前角色
  const fetchUserRoles = async () => {
    if (!token || !user) return

    try {
      const response = await fetch(`${API_BASE_URL}/admin/users/${user.id}/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        setUserCurrentRoles(data.roles || [])
        setSelectedRoleIds(data.roles?.map((role: Role) => role.id) || [])
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '获取用户角色失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    }
  }

  // 加载数据
  const loadData = async () => {
    setIsLoading(true)
    setError('')
    await Promise.all([fetchAllRoles(), fetchUserRoles()])
    setIsLoading(false)
  }

  useEffect(() => {
    if (open && user) {
      loadData()
    }
  }, [open, user, token])

  // 处理角色选择
  const handleRoleToggle = (roleId: number, checked: boolean) => {
    setSelectedRoleIds(prev => {
      if (checked) {
        return [...prev, roleId]
      } else {
        return prev.filter(id => id !== roleId)
      }
    })
  }

  // 保存角色分配
  const handleSave = async () => {
    if (!token || !user) return

    setIsSaving(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch(`${API_BASE_URL}/admin/users/${user.id}/roles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          role_ids: selectedRoleIds,
        }),
      })

      if (response.ok) {
        setSuccess('角色分配成功')
        onSuccess()
        setTimeout(() => {
          handleClose()
        }, 1500)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '角色分配失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsSaving(false)
    }
  }

  const handleClose = () => {
    setError('')
    setSuccess('')
    setSelectedRoleIds([])
    setAllRoles([])
    setUserCurrentRoles([])
    onOpenChange(false)
  }

  // 获取选中角色的所有权限（去重）
  const getSelectedPermissions = () => {
    const selectedRoles = allRoles.filter(role => selectedRoleIds.includes(role.id))
    const allPermissions = selectedRoles.flatMap(role => role.permissions)
    
    // 去重
    const uniquePermissions = allPermissions.filter((permission, index, self) =>
      index === self.findIndex(p => p.id === permission.id)
    )

    // 按分类分组
    return uniquePermissions.reduce((groups, permission) => {
      const category = permission.category || '其他'
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push(permission)
      return groups
    }, {} as Record<string, Permission[]>)
  }

  // 检查角色变化
  const hasChanges = () => {
    const currentRoleIds = userCurrentRoles.map(role => role.id).sort()
    const newRoleIds = [...selectedRoleIds].sort()
    return JSON.stringify(currentRoleIds) !== JSON.stringify(newRoleIds)
  }

  if (!user) {
    return null
  }

  const groupedPermissions = getSelectedPermissions()
  const totalPermissions = Object.values(groupedPermissions).flat().length

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>管理用户角色</span>
          </DialogTitle>
          <DialogDescription>
            为用户 "{user.username}" 分配角色和权限
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <Check className="h-4 w-4" />
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">加载角色数据...</span>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 左侧：角色选择 */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">可用角色</CardTitle>
                  <CardDescription>
                    选择要分配给用户的角色
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {allRoles.map((role) => {
                    const isSelected = selectedRoleIds.includes(role.id)
                    const wasOriginallySelected = userCurrentRoles.some(ur => ur.id === role.id)
                    
                    return (
                      <div key={role.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                        <Checkbox
                          id={`role-${role.id}`}
                          checked={isSelected}
                          onCheckedChange={(checked) => handleRoleToggle(role.id, checked as boolean)}
                          disabled={isSaving}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <label
                              htmlFor={`role-${role.id}`}
                              className="font-medium cursor-pointer"
                            >
                              {role.name}
                            </label>
                            {wasOriginallySelected && (
                              <Badge variant="outline" className="text-xs">
                                当前
                              </Badge>
                            )}
                            {isSelected && !wasOriginallySelected && (
                              <Badge variant="default" className="text-xs">
                                新增
                              </Badge>
                            )}
                            {!isSelected && wasOriginallySelected && (
                              <Badge variant="destructive" className="text-xs">
                                移除
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {role.description}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {role.permissions.length} 个权限
                          </p>
                        </div>
                      </div>
                    )
                  })}
                </CardContent>
              </Card>

              {/* 右侧：权限预览 */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center space-x-2">
                    <ArrowRight className="h-4 w-4" />
                    <span>权限预览</span>
                  </CardTitle>
                  <CardDescription>
                    选中角色将获得的权限 ({totalPermissions} 个)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {totalPermissions > 0 ? (
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {Object.entries(groupedPermissions).map(([category, permissions]) => (
                        <div key={category}>
                          <h4 className="font-medium text-sm mb-2 text-muted-foreground">
                            {category} ({permissions.length})
                          </h4>
                          <div className="space-y-1">
                            {permissions.map((permission) => (
                              <div key={permission.id} className="text-xs p-2 bg-muted rounded">
                                <div className="font-mono text-xs mb-1">
                                  {permission.name}
                                </div>
                                <div className="text-muted-foreground">
                                  {permission.description}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Shield className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>请选择角色以查看权限</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isSaving}>
            取消
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={isSaving || !hasChanges()}
          >
            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            保存角色分配
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
