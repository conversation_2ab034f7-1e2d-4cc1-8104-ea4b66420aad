/**
 * 删除角色确认对话框组件
 */
import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, Trash2, AlertTriangle, Users, Shield, Check } from 'lucide-react'
import { useAuthStore } from '@/store/auth'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface Permission {
  id: number
  name: string
  description: string
  category: string
}

interface Role {
  id: number
  name: string
  description: string
  is_active: boolean
  created_at: string
  permissions: Permission[]
}

interface DeleteRoleConfirmDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
  role: Role | null
}

export const DeleteRoleConfirmDialog: React.FC<DeleteRoleConfirmDialogProps> = ({
  open,
  onOpenChange,
  onSuccess,
  role,
}) => {
  const { token } = useAuthStore()

  // UI状态
  const [isDeleting, setIsDeleting] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [userCount, setUserCount] = useState(0)

  // 获取使用该角色的用户数量
  const fetchRoleUsage = async () => {
    if (!token || !role) return

    setIsLoading(true)
    try {
      // 这里可以调用API获取角色使用情况
      // 暂时模拟数据
      const response = await fetch(`${API_BASE_URL}/admin/users`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const users = await response.json()
        // 计算使用该角色的用户数量
        const count = users.filter((user: any) =>
          user.roles?.some((userRole: any) => userRole.id === role.id)
        ).length
        setUserCount(count)
      }
    } catch (err) {
      console.error('获取角色使用情况失败:', err)
    } finally {
      setIsLoading(false)
    }
  }

  // 对话框打开时获取角色使用情况
  useEffect(() => {
    if (open && role) {
      fetchRoleUsage()
    }
  }, [open, role, token])

  // 检查是否为系统内置角色
  const isSystemRole = role && ['superadmin', 'admin', 'user'].includes(role.name)

  // 删除角色
  const handleDelete = async () => {
    if (!token || !role) return

    setIsDeleting(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch(`${API_BASE_URL}/admin/roles/${role.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        setSuccess('角色删除成功')
        onSuccess()
        setTimeout(() => {
          handleClose()
        }, 1500)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '删除角色失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsDeleting(false)
    }
  }

  // 关闭对话框
  const handleClose = () => {
    setError('')
    setSuccess('')
    setUserCount(0)
    onOpenChange(false)
  }

  // 按分类分组权限
  const groupedPermissions = role?.permissions.reduce((groups, permission) => {
    const category = permission.category || '其他'
    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(permission)
    return groups
  }, {} as Record<string, Permission[]>) || {}

  if (!role) return null

  return (
    <Dialog open={open} onOpenChange={(open) => open ? null : handleClose()} modal={true}>
      <DialogContent
        className="max-w-2xl"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2 text-destructive">
            <Trash2 className="h-5 w-5" />
            <span>删除角色确认</span>
          </DialogTitle>
          <DialogDescription>
            此操作不可撤销，请仔细确认要删除的角色信息
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* 系统角色警告 */}
          {isSystemRole && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>系统内置角色不能删除！</strong>
                <br />
                {role.name} 是系统内置角色，删除可能导致系统功能异常。
              </AlertDescription>
            </Alert>
          )}

          {/* 角色信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>角色信息</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">角色名称</p>
                  <p className="font-medium">{role.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">状态</p>
                  <Badge variant={role.is_active ? "default" : "secondary"}>
                    {role.is_active ? "激活" : "禁用"}
                  </Badge>
                </div>
              </div>

              {role.description && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">描述</p>
                  <p className="text-sm">{role.description}</p>
                </div>
              )}

              <div>
                <p className="text-sm font-medium text-muted-foreground">创建时间</p>
                <p className="text-sm">{new Date(role.created_at).toLocaleString()}</p>
              </div>
            </CardContent>
          </Card>

          {/* 影响范围 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>影响范围</span>
              </CardTitle>
              <CardDescription>
                删除此角色将影响以下内容
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">关联用户</p>
                  <p className="text-sm text-muted-foreground">
                    拥有此角色的用户数量
                  </p>
                </div>
                <div className="text-right">
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Badge variant={userCount > 0 ? "destructive" : "secondary"}>
                      {userCount} 个用户
                    </Badge>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">角色权限</p>
                  <p className="text-sm text-muted-foreground">
                    此角色拥有的权限数量
                  </p>
                </div>
                <div className="text-right">
                  <Badge variant="secondary">
                    {role.permissions.length} 个权限
                  </Badge>
                </div>
              </div>

              {userCount > 0 && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>警告：</strong>删除此角色将移除 {userCount} 个用户的相关权限，
                    这可能影响他们对系统功能的访问。
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* 权限详情 */}
          {role.permissions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">权限详情</CardTitle>
                <CardDescription>
                  此角色拥有的所有权限
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(groupedPermissions).map(([category, permissions]) => (
                    <div key={category}>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">
                        {category} ({permissions.length})
                      </h4>
                      <div className="flex flex-wrap gap-2 ml-2">
                        {permissions.map((permission) => (
                          <Badge key={permission.id} variant="outline" className="text-xs">
                            {permission.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 错误和成功消息 */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <Check className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isDeleting}
          >
            取消
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting || isSystemRole || userCount > 0}
          >
            {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            确认删除
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
