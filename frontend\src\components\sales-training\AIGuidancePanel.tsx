/**
 * AI智能指导面板 - 基于真实AI分析
 * 根据客户信息和聊天上下文动态生成指导内容
 */

import { useState, useEffect, useMemo } from 'react'
import {
  Brain,
  MessageCircle,
  Download,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Lightbulb,
  Target,
  Users,
  Globe,
  Star,
  TrendingUp,
  FileText,
  Loader2,
  Eye,
  ThumbsUp,
  ThumbsDown,
  Copy,
  BookOpen,
  Sparkles,
  ArrowRight,
  Clock,
  Award
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import type { ChatMessage, TrainingCustomer } from '@/types/salesTraining'
import { useAIGuidance } from '@/hooks/useAIGuidance'

interface AIGuidancePanelProps {
  messages: ChatMessage[]
  customer: TrainingCustomer
  isSessionActive?: boolean
  // 新增props（参考智能评分面板）
  overallAnalysis?: OverallAnalysis | null
  replyAnalyses?: ReplyAnalysis[]
  isAnalyzing?: boolean
  error?: string | null
  onStartAnalysis?: () => void
}

// 回复评价类型
interface ReplyAnalysis {
  messageIndex: number
  userMessage: string
  strengths: string[]
  weaknesses: string[]
  score: number
  suggestedReply: string
  reasoning: string
}

// 整体分析类型
interface OverallAnalysis {
  conversationFlow: string
  communicationStyle: string
  needsIdentification: string
  relationshipBuilding: string
  culturalAdaptation: string
  improvementAreas: string[]
  nextSteps: string[]
}

export function AIGuidancePanel({ 
  messages, 
  customer, 
  isSessionActive = false,
  overallAnalysis: propOverallAnalysis,
  replyAnalyses: propReplyAnalyses,
  isAnalyzing: propIsAnalyzing,
  error: propError,
  onStartAnalysis
}: AIGuidancePanelProps) {
  const [activeTab, setActiveTab] = useState<'analysis' | 'replies' | 'export'>('analysis')
  const [isExporting, setIsExporting] = useState(false)
  const [exportSuccess, setExportSuccess] = useState(false)
  
  // 如果传入了props，使用props；否则使用hook（向后兼容）
  const {
    overallAnalysis: hookOverallAnalysis,
    replyAnalyses: hookReplyAnalyses,
    isAnalyzing: hookIsAnalyzing,
    error: hookError,
    exportGuidance
  } = useAIGuidance(messages, customer)

  // 使用传入的props或hook的值
  const overallAnalysis = propOverallAnalysis !== undefined ? propOverallAnalysis : hookOverallAnalysis
  const replyAnalyses = propReplyAnalyses !== undefined ? propReplyAnalyses : hookReplyAnalyses
  const isAnalyzing = propIsAnalyzing !== undefined ? propIsAnalyzing : hookIsAnalyzing
  const error = propError !== undefined ? propError : hookError

  // 计算用户消息
  const userMessages = useMemo(() => 
    messages.filter(m => m.role === 'user'), [messages]
  )

  // 判断是否可以进行分析（参考智能评分逻辑）
  const canAnalyze = useMemo(() => {
    return !isSessionActive && // 会话必须停止
           userMessages.length > 0 && // 至少有一轮用户回复
           messages.length >= 2 // 至少有2条消息
  }, [isSessionActive, userMessages.length, messages.length])

  // 获取分数颜色
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50 border-green-200'
    if (score >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  // 复制内容到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // 可以添加toast提示
    } catch (err) {
      console.error('复制失败:', err)
    }
  }

  return (
    <div className="space-y-3 h-full flex flex-col p-3 pb-4">
      {/* 头部状态卡片 */}
      <Card className="bg-gradient-to-r from-purple-50 via-white to-indigo-50 dark:from-gray-800 dark:via-gray-800 dark:to-gray-700 border-purple-200/50 dark:border-gray-600 shadow-sm rounded-lg">
        <CardContent className="p-3">
          <div className="flex items-center justify-between gap-3">
            <div className="flex items-center space-x-2 min-w-0 flex-1">
              <div className="p-1.5 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg flex-shrink-0">
                <Brain className="w-4 h-4 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-sm text-gray-900 dark:text-white truncate">AI智能指导</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  基于{customer.name}的背景和对话分析
                </p>
              </div>
            </div>

          </div>
        </CardContent>
      </Card>

      {/* 主要内容区域 */}
      <Card className="flex-1 flex flex-col overflow-hidden shadow-sm rounded-lg min-h-0">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex flex-col h-full">
          <div className="flex-shrink-0 p-2 border-b border-gray-200 dark:border-gray-700">
            <TabsList className="grid w-full grid-cols-3 bg-gray-100 dark:bg-gray-800 h-8">
              <TabsTrigger value="analysis" className="text-xs px-2">
                <TrendingUp className="w-3 h-3 mr-1" />
                <span className="hidden sm:inline">整体分析</span>
                <span className="sm:hidden">分析</span>
              </TabsTrigger>
              <TabsTrigger value="replies" className="text-xs px-2">
                <MessageCircle className="w-3 h-3 mr-1" />
                <span className="hidden sm:inline">回复评价</span>
                <span className="sm:hidden">评价</span>
              </TabsTrigger>
              <TabsTrigger value="export" className="text-xs px-2">
                <Download className="w-3 h-3 mr-1" />
                <span className="hidden sm:inline">导出报告</span>
                <span className="sm:hidden">导出</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 overflow-y-auto">
            {/* 整体分析标签页 */}
            <TabsContent value="analysis" className="mt-0 p-2">
              {overallAnalysis ? (
                <ScrollArea className="h-full">
                  <div className="space-y-3">
                    {/* 对话流程分析 */}
                    <Card className="border border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/10">
                      <CardHeader className="pb-1 px-3 pt-3">
                        <CardTitle className="text-sm font-medium flex items-center text-blue-700 dark:text-blue-300">
                          <MessageCircle className="w-4 h-4 mr-2" />
                          对话流程分析
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 pb-3">
                        <p className="text-sm text-blue-800 dark:text-blue-200 leading-relaxed">
                          {overallAnalysis.conversationFlow}
                        </p>
                      </CardContent>
                    </Card>

                    {/* 沟通风格评价 */}
                    <Card className="border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/10">
                      <CardHeader className="pb-1 px-3 pt-3">
                        <CardTitle className="text-sm font-medium flex items-center text-green-700 dark:text-green-300">
                          <Users className="w-4 h-4 mr-2" />
                          沟通风格评价
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 pb-3">
                        <p className="text-sm text-green-800 dark:text-green-200 leading-relaxed">
                          {overallAnalysis.communicationStyle}
                        </p>
                      </CardContent>
                    </Card>

                    {/* 需求识别能力 */}
                    <Card className="border border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/10">
                      <CardHeader className="pb-1 px-3 pt-3">
                        <CardTitle className="text-sm font-medium flex items-center text-yellow-700 dark:text-yellow-300">
                          <Target className="w-4 h-4 mr-2" />
                          需求识别能力
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 pb-3">
                        <p className="text-sm text-yellow-800 dark:text-yellow-200 leading-relaxed">
                          {overallAnalysis.needsIdentification}
                        </p>
                      </CardContent>
                    </Card>

                    {/* 关系建立 */}
                    <Card className="border border-purple-200 dark:border-purple-800 bg-purple-50 dark:bg-purple-900/10">
                      <CardHeader className="pb-1 px-3 pt-3">
                        <CardTitle className="text-sm font-medium flex items-center text-purple-700 dark:text-purple-300">
                          <Star className="w-4 h-4 mr-2" />
                          关系建立
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 pb-3">
                        <p className="text-sm text-purple-800 dark:text-purple-200 leading-relaxed">
                          {overallAnalysis.relationshipBuilding}
                        </p>
                      </CardContent>
                    </Card>

                    {/* 跨文化适应 */}
                    <Card className="border border-indigo-200 dark:border-indigo-800 bg-indigo-50 dark:bg-indigo-900/10">
                      <CardHeader className="pb-1 px-3 pt-3">
                        <CardTitle className="text-sm font-medium flex items-center text-indigo-700 dark:text-indigo-300">
                          <Globe className="w-4 h-4 mr-2" />
                          跨文化适应
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 pb-3">
                        <p className="text-sm text-indigo-800 dark:text-indigo-200 leading-relaxed">
                          {overallAnalysis.culturalAdaptation}
                        </p>
                      </CardContent>
                    </Card>

                    {/* 改进建议 */}
                    <Card className="border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-900/10">
                      <CardHeader className="pb-1 px-3 pt-3">
                        <CardTitle className="text-sm font-medium flex items-center text-orange-700 dark:text-orange-300">
                          <Lightbulb className="w-4 h-4 mr-2" />
                          改进建议
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 pb-3">
                        <ul className="space-y-2">
                          {overallAnalysis.improvementAreas.map((area, index) => (
                            <li key={index} className="text-sm text-orange-800 dark:text-orange-200 flex items-start">
                              <ArrowRight className="w-3 h-3 mr-2 mt-0.5 flex-shrink-0" />
                              {area}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>

                    {/* 下步行动 */}
                    <Card className="border border-gray-200 dark:border-gray-700">
                      <CardHeader className="pb-1 px-3 pt-3">
                        <CardTitle className="text-sm font-medium flex items-center text-gray-700 dark:text-gray-300">
                          <Clock className="w-4 h-4 mr-2" />
                          下步行动建议
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-3 pb-3">
                        <ul className="space-y-2">
                          {overallAnalysis.nextSteps.map((step, index) => (
                            <li key={index} className="text-sm text-gray-700 dark:text-gray-300 flex items-start">
                              <CheckCircle className="w-3 h-3 mr-2 mt-0.5 flex-shrink-0 text-green-500" />
                              {step}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  </div>
                </ScrollArea>
              ) : (
                <div className="text-center py-6">
                  {isAnalyzing ? (
                    <div className="space-y-3">
                      <div className="w-10 h-10 mx-auto relative">
                        <div className="absolute inset-0 rounded-full border-4 border-purple-100 dark:border-purple-900/30"></div>
                        <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-purple-500 animate-spin"></div>
                        <Brain className="w-5 h-5 text-purple-500 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">AI正在分析对话内容...</p>
                    </div>
                  ) : userMessages.length === 0 ? (
                    <div className="space-y-3">
                      <BookOpen className="w-10 h-10 text-gray-400 mx-auto" />
                      <h3 className="font-medium text-gray-900 dark:text-white">开始对话获取指导</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        至少需要1轮对话才能进行指导
                      </p>
                    </div>
                  ) : isSessionActive ? (
                    <div className="space-y-3">
                      <Clock className="w-10 h-10 text-blue-400 mx-auto" />
                      <h3 className="font-medium text-gray-900 dark:text-white">训练进行中</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        请先停止训练，然后开始AI指导分析
                      </p>
                    </div>
                  ) : error ? (
                    <div className="space-y-3">
                      <AlertTriangle className="w-10 h-10 text-red-400 mx-auto" />
                      <h3 className="font-medium text-red-900 dark:text-red-100">分析失败</h3>
                      <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
                      {canAnalyze && onStartAnalysis && (
                        <Button size="sm" onClick={onStartAnalysis} variant="outline">
                          <RefreshCw className="w-3 h-3 mr-1" />
                          重试
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <Sparkles className="w-10 h-10 text-gray-400 mx-auto" />
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {canAnalyze ? '点击开始AI指导分析' : '停止训练后可进行分析'}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {canAnalyze 
                          ? `基于${userMessages.length}轮对话进行专业指导分析`
                          : '需要停止训练会话才能开始分析'
                        }
                      </p>
                      {canAnalyze && onStartAnalysis && (
                        <Button size="sm" onClick={onStartAnalysis}>
                          <Brain className="w-3 h-3 mr-1" />
                          开始分析
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            {/* 回复评价标签页 */}
            <TabsContent value="replies" className="mt-0 p-2">
              {replyAnalyses.length > 0 ? (
                <ScrollArea className="h-full">
                  <div className="space-y-3">
                    {replyAnalyses.map((analysis, index) => (
                      <Card key={index} className="border border-gray-200 dark:border-gray-700">
                        <CardHeader className="pb-1 px-3 pt-3">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-sm font-medium flex items-center">
                              <MessageCircle className="w-4 h-4 mr-2 text-blue-500" />
                              第 {analysis.messageIndex + 1} 轮回复
                            </CardTitle>
                            <Badge className={`text-xs border ${getScoreColor(analysis.score)}`}>
                              {analysis.score}分
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-2 px-3 pb-3">
                          {/* 原始回复 */}
                          <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">您的回复:</span>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => copyToClipboard(analysis.userMessage)}
                                className="h-6 text-xs px-1"
                              >
                                <Copy className="w-3 h-3" />
                              </Button>
                            </div>
                            <p className="text-sm text-gray-700 dark:text-gray-300">
                              {analysis.userMessage}
                            </p>
                          </div>

                          {/* 优点 */}
                          {analysis.strengths.length > 0 && (
                            <div>
                              <h4 className="text-xs font-medium text-green-700 dark:text-green-300 mb-2 flex items-center">
                                <ThumbsUp className="w-3 h-3 mr-1" />
                                优点
                              </h4>
                              <ul className="space-y-1">
                                {analysis.strengths.map((strength, idx) => (
                                  <li key={idx} className="text-xs text-green-600 dark:text-green-400 flex items-start">
                                    <CheckCircle className="w-3 h-3 mr-1 mt-0.5 flex-shrink-0" />
                                    {strength}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {/* 缺点 */}
                          {analysis.weaknesses.length > 0 && (
                            <div>
                              <h4 className="text-xs font-medium text-red-700 dark:text-red-300 mb-2 flex items-center">
                                <ThumbsDown className="w-3 h-3 mr-1" />
                                待改进
                              </h4>
                              <ul className="space-y-1">
                                {analysis.weaknesses.map((weakness, idx) => (
                                  <li key={idx} className="text-xs text-red-600 dark:text-red-400 flex items-start">
                                    <AlertTriangle className="w-3 h-3 mr-1 mt-0.5 flex-shrink-0" />
                                    {weakness}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {/* 建议回复 */}
                          <div>
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="text-xs font-medium text-blue-700 dark:text-blue-300 flex items-center">
                                <Lightbulb className="w-3 h-3 mr-1" />
                                建议回复
                              </h4>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => copyToClipboard(analysis.suggestedReply)}
                                className="h-6 text-xs px-1"
                              >
                                <Copy className="w-3 h-3" />
                              </Button>
                            </div>
                            <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                              <p className="text-sm text-blue-800 dark:text-blue-200">
                                {analysis.suggestedReply}
                              </p>
                            </div>
                          </div>

                          {/* 评价理由 */}
                          <div>
                            <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">评价理由</h4>
                            <p className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed">
                              {analysis.reasoning}
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              ) : (
                <div className="text-center py-6">
                  <MessageCircle className="w-10 h-10 text-gray-400 mx-auto mb-3" />
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                    {isSessionActive ? '训练进行中' : '暂无回复分析'}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isSessionActive 
                      ? '请先停止训练，然后开始AI指导分析'
                      : canAnalyze 
                        ? '点击开始分析按钮获取回复评价'
                        : '完成对话并停止训练后，AI将分析您的每一轮回复'
                    }
                  </p>
                  {canAnalyze && onStartAnalysis && (
                    <Button size="sm" onClick={onStartAnalysis} className="mt-3">
                      <Brain className="w-3 h-3 mr-1" />
                      开始分析
                    </Button>
                  )}
                </div>
              )}
            </TabsContent>

            {/* 导出报告标签页 */}
            <TabsContent value="export" className="mt-0 p-2">
              <div className="space-y-3">
                <Card className="border border-gray-200 dark:border-gray-700">
                  <CardHeader className="px-3 pt-3 pb-2">
                    <CardTitle className="text-sm font-medium flex items-center">
                      <FileText className="w-4 h-4 mr-2 text-blue-500" />
                      指导报告导出
                    </CardTitle>
                    <CardDescription className="text-xs">
                      导出完整的AI指导分析报告，包含整体分析和回复评价
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3 px-3 pb-3">
                    {/* 报告统计 */}
                    <div className="grid grid-cols-2 gap-2">
                      <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-center">
                        <div className="text-base font-bold text-blue-600 dark:text-blue-400">
                          {userMessages.length}
                        </div>
                        <p className="text-xs text-gray-600 dark:text-gray-400">分析轮次</p>
                      </div>
                      <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg text-center">
                        <div className="text-base font-bold text-green-600 dark:text-green-400">
                          {replyAnalyses.length > 0 
                            ? Math.round(replyAnalyses.reduce((sum, r) => sum + r.score, 0) / replyAnalyses.length)
                            : 0
                          }
                        </div>
                        <p className="text-xs text-gray-600 dark:text-gray-400">平均得分</p>
                      </div>
                    </div>

                    {/* 导出选项 */}
                    <div className="space-y-3">
                      <Button
                        onClick={async () => {
                          setIsExporting(true)
                          setExportSuccess(false)
                          
                          try {
                            // 调试日志
                            console.log('导出数据检查:', {
                              hasOverallAnalysis: !!overallAnalysis,
                              replyAnalysesLength: replyAnalyses.length,
                              propOverallAnalysis: !!propOverallAnalysis,
                              propReplyAnalysesLength: propReplyAnalyses?.length || 0,
                              hookOverallAnalysis: !!hookOverallAnalysis,
                              hookReplyAnalysesLength: hookReplyAnalyses.length
                            })

                            // 使用当前组件的数据进行导出，而不是hook内部的数据
                            if (!overallAnalysis && replyAnalyses.length === 0) {
                              throw new Error('没有可导出的分析数据')
                            }

                            // 动态导入导出工具
                            const { exportToTXT } = await import('@/utils/documentExport')

                            const exportData = {
                              customer: {
                                name: customer.name,
                                company: customer.background?.company || '未知公司',
                                country: customer.country.name,
                                industry: '通用行业',
                                product: customer.product.name
                              },
                              overallAnalysis,
                              replyAnalyses,
                              generatedAt: new Date().toISOString()
                            }

                            exportToTXT(exportData)
                            setExportSuccess(true)
                            
                            // 3秒后隐藏成功提示
                            setTimeout(() => {
                              setExportSuccess(false)
                            }, 3000)
                            
                          } catch (error) {
                            console.error('导出失败:', error)
                            alert('导出失败: ' + (error as Error).message)
                          } finally {
                            setIsExporting(false)
                          }
                        }}
                        disabled={(!overallAnalysis && replyAnalyses.length === 0) || isExporting}
                        className="w-full justify-center bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200"
                        variant="outline"
                      >
                        {isExporting ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            正在导出...
                          </>
                        ) : exportSuccess ? (
                          <>
                            <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                            导出成功！
                          </>
                        ) : (
                          <>
                            <Download className="w-4 h-4 mr-2" />
                            下载指导报告
                          </>
                        )}
                      </Button>
                    </div>

                    {/* 成功提示 */}
                    {exportSuccess && (
                      <Alert className="border-green-200 bg-green-50 dark:bg-green-900/20">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <AlertDescription className="text-sm text-green-700 dark:text-green-300">
                          报告已成功下载到您的下载文件夹！
                        </AlertDescription>
                      </Alert>
                    )}

                    {(!overallAnalysis && replyAnalyses.length === 0) && (
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription className="text-sm">
                          请先进行对话分析后再导出报告
                        </AlertDescription>
                      </Alert>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </Card>
    </div>
  )
}