/**
 * 个人资料页面
 */
import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { ProfileForm } from '@/components/profile/ProfileForm'
import { AvatarUpload } from '@/components/profile/AvatarUpload'
import { PasswordChange } from '@/components/profile/PasswordChange'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuthStore } from '@/store/auth'

const API_BASE_URL = 'http://localhost:8001/api/v1'

const ProfilePage: React.FC = () => {
  const { token, updateUser } = useAuthStore()
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null)

  // 获取当前头像URL
  const fetchCurrentAvatar = async () => {
    if (!token) return

    try {
      const response = await fetch(`${API_BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        setAvatarUrl(data.avatar_url)
      }
    } catch (err) {
      console.error('获取头像失败:', err)
    }
  }

  useEffect(() => {
    fetchCurrentAvatar()
  }, [token])

  const handleAvatarUpdate = (newAvatarUrl: string | null) => {
    setAvatarUrl(newAvatarUrl)
    // 更新全局用户状态
    updateUser({ avatar_url: newAvatarUrl })
  }

  return (
    <ProtectedRoute>
      <div className="container mx-auto py-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold">个人资料</h1>
          <p className="text-muted-foreground">
            管理您的个人信息、头像和账户设置
          </p>
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="profile">基本信息</TabsTrigger>
            <TabsTrigger value="avatar">头像设置</TabsTrigger>
            <TabsTrigger value="password">密码安全</TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            <ProfileForm />
          </TabsContent>

          <TabsContent value="avatar" className="space-y-6">
            <AvatarUpload 
              currentAvatarUrl={avatarUrl}
              onAvatarUpdate={handleAvatarUpdate}
            />
          </TabsContent>

          <TabsContent value="password" className="space-y-6">
            <PasswordChange />
          </TabsContent>
        </Tabs>
      </div>
    </ProtectedRoute>
  )
}

export default ProfilePage
