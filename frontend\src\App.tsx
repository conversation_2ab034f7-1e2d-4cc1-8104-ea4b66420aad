import { Routes, Route } from 'react-router-dom'
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar'
import { AppSidebar } from '@/components/app-sidebar'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { UserMenu } from '@/components/auth/UserMenu'


import HomePage from '@/pages/HomePage'
import KnowledgeBasePage from '@/pages/KnowledgeBasePage'
// import ThemeDemoPage from '@/pages/ThemeDemoPage'
import ChatPage from '@/pages/ChatPage'
import TemplateManagementPage from '@/pages/TemplateManagementPage'
import PermissionDebugPage from '@/pages/PermissionDebugPage'
import LoginPage from '@/pages/LoginPage'
import RegisterPage from '@/pages/RegisterPage'
import ForgotPasswordPage from '@/pages/ForgotPasswordPage'
import ResetPasswordPage from '@/pages/ResetPasswordPage'
import ProfilePage from '@/pages/ProfilePage'
import AdminPage from '@/pages/AdminPage'
import TradeCoachPage from '@/pages/TradeCoachPage'
import EnterpriseKnowledgeBasePage from '@/pages/EnterpriseKnowledgeBasePage'
import MarketAnalysisPage from '@/pages/MarketAnalysisPage'
import GroupAnalysisPage from '@/pages/GroupAnalysisPage'
import TestPage from '@/pages/TestPage'
import SalesTrainingPage from '@/pages/SalesTrainingPage'
import EnhancedTrainingDemoPage from '@/pages/EnhancedTrainingDemoPage'

import { TaskManagement } from './pages/TaskManagement'

// 简单的页面组件

// 贸易教官页面组件
// function CompanyKnowledgePage() {
//   return (
//     <div className="flex-1 space-y-4 p-8 pt-6">
//       <h2 className="text-3xl font-bold tracking-tight">企业知识库</h2>
//       <p className="text-muted-foreground">管理公司内部知识和资料</p>
//     </div>
//   )
// }

function TradeKnowledgePage() {
  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <h2 className="text-3xl font-bold tracking-tight">外贸知识库</h2>
      <p className="text-muted-foreground">外贸专业知识和经验分享</p>
    </div>
  )
}














function LearningPlanPage() {
  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <h2 className="text-3xl font-bold tracking-tight">个性化学习方案</h2>
      <p className="text-muted-foreground">定制化的学习计划和方案</p>
    </div>
  )
}




// 话术模板管理页面现在使用独立组件

// function CustomerInsightsPage() {
//   return (
//     <div className="flex-1 space-y-4 p-8 pt-6">
//       <h2 className="text-3xl font-bold tracking-tight">客户洞察</h2>
//       <p className="text-muted-foreground">深度分析客户行为和偏好</p>
//     </div>
//   )
// }







function App() {
  return (
    <Routes>
      {/* 公共路由 - 不需要认证 */}
      <Route path="/login" element={<LoginPage />} />
      <Route path="/register" element={<RegisterPage />} />
      <Route path="/forgot-password" element={<ForgotPasswordPage />} />
      <Route path="/reset-password" element={<ResetPasswordPage />} />

      {/* 受保护的路由 - 需要认证 */}
      <Route path="/*" element={
        <ProtectedRoute>
          <SidebarProvider>
            <AppSidebar />
            <SidebarInset>
              <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
                <div className="flex items-center gap-2 px-4">
                  <SidebarTrigger className="-ml-1" />
                  <div className="h-4 w-px bg-sidebar-border" />
                  <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
                    <span>智汇兔AI外贸收割机</span>
                  </nav>
                </div>
                <div className="ml-auto px-4">
                  <UserMenu />
                </div>
              </header>
              <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
                <Routes>
                  <Route path="/" element={<HomePage />} />

                  {/* 贸易教官路由 */}
                  <Route path="/trade-coach" element={<TradeCoachPage />} />
                  <Route path="/trade-coach/company-knowledge" element={<EnterpriseKnowledgeBasePage/>} />
                  <Route path="/trade-coach/trade-knowledge" element={<TradeKnowledgePage />} />
                  <Route path="/trade-coach/market-analysis" element={<MarketAnalysisPage />} />
                  <Route path="/trade-coach/group-analysis" element={<GroupAnalysisPage />} />
                  <Route path="/test" element={<TestPage />} />
                  <Route path="/sales-training" element={<SalesTrainingPage />} />
                  <Route path="/enhanced-training-demo" element={<EnhancedTrainingDemoPage />} />
                  <Route path="/trade-coach/learning-plan" element={<LearningPlanPage />} />

                  <Route path="/enterprise-knowledge-base" element={<EnterpriseKnowledgeBasePage />} />
                  <Route path="/knowledge-base" element={<KnowledgeBasePage />} />
                  <Route path="/market-analysis" element={<TemplateManagementPage />} />
                  <Route path="/chat" element={<ChatPage />} />

                  <Route path="/tasks" element={<TaskManagement />} />
                  <Route path="/profile" element={<ProfilePage />} />
                  <Route path="/admin" element={<AdminPage />} />
                  <Route path="/permission-debug" element={<PermissionDebugPage />} />
                </Routes>
              </div>
            </SidebarInset>
          </SidebarProvider>
        </ProtectedRoute>
      } />
    </Routes>
  )
}

export default App
