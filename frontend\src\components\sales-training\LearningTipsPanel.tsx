/**
 * AI学习提示和指导面板 - 增强版
 * 提供智能学习建议、实时指导和个性化学习路径
 */

import { useState, useEffect, useMemo } from 'react'
import {
  Lightbulb,
  BookOpen,
  Target,
  Star,
  Brain,
  MessageCircle,
  Users,
  Globe,
  Zap,
  Award,
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Play,
  Pause,
  RotateCcw,
  Eye,
  ThumbsUp,
  AlertCircle,
  Sparkles,
  Compass,
  Shield,
  Heart,
  Clock,
  Layers,
  PlusCircle,
  ChevronRight,
  BookMarked,
  Gamepad2,
  Trophy,
  Megaphone
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import type { ChatMessage, TrainingCustomer, LearningTip, KnowledgeCard } from '@/types/salesTraining'

interface LearningTipsPanelProps {
  messages: ChatMessage[]
  customer: TrainingCustomer
}

// 学习技巧分类
const tipCategories = [
  {
    id: 'communication',
    name: '沟通技巧',
    icon: MessageCircle,
    color: 'text-blue-600',
    bgColor: 'bg-blue-500',
    lightBg: 'bg-blue-50',
    borderColor: 'border-blue-200',
    description: '提升沟通表达和倾听技能'
  },
  {
    id: 'negotiation',
    name: '谈判技巧',
    icon: Zap,
    color: 'text-purple-600',
    bgColor: 'bg-purple-500',
    lightBg: 'bg-purple-50',
    borderColor: 'border-purple-200',
    description: '掌握谈判策略和成交技巧'
  },
  {
    id: 'cultural',
    name: '跨文化',
    icon: Globe,
    color: 'text-green-600',
    bgColor: 'bg-green-500',
    lightBg: 'bg-green-50',
    borderColor: 'border-green-200',
    description: '了解不同文化的商务礼仪'
  },
  {
    id: 'product',
    name: '产品知识',
    icon: Star,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-500',
    lightBg: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    description: '深化产品特性和价值理解'
  },
  {
    id: 'psychology',
    name: '销售心理学',
    icon: Brain,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-500',
    lightBg: 'bg-indigo-50',
    borderColor: 'border-indigo-200',
    description: '理解客户心理和购买决策'
  }
]

// 智能建议类型
interface SmartSuggestion {
  id: string
  type: 'technique' | 'response' | 'strategy' | 'caution'
  category: string
  title: string
  content: string
  priority: 'high' | 'medium' | 'low'
  applicable: boolean
  reasoning: string
  example?: string
  relatedTips?: string[]
  timestamp: Date
}

// 学习进度追踪
interface LearningProgress {
  category: string
  completedTips: number
  totalTips: number
  masteryLevel: number
  recentActivity: Date
}

export function LearningTipsPanel({ messages, customer }: LearningTipsPanelProps) {
  const [activeTab, setActiveTab] = useState<'suggestions' | 'tips' | 'progress'>('suggestions')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [smartSuggestions, setSmartSuggestions] = useState<SmartSuggestion[]>([])
  const [learningProgress, setLearningProgress] = useState<LearningProgress[]>([])
  const [currentTips, setCurrentTips] = useState<LearningTip[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [lastAnalyzedMessageCount, setLastAnalyzedMessageCount] = useState(0)

  // 计算用户消息
  const userMessages = useMemo(() => 
    messages.filter(m => m.role === 'user'), [messages]
  )

  // 模拟智能建议生成
  useEffect(() => {
    const messageCount = userMessages.length

    if (messageCount <= lastAnalyzedMessageCount) {
      return
    }

    setIsAnalyzing(true)

    const analysisTimer = setTimeout(() => {
      // 生成智能建议
      const newSuggestions = generateSmartSuggestions(userMessages, customer)
      setSmartSuggestions(prev => [...prev.slice(-10), ...newSuggestions])

      // 生成相关学习提示
      const newTips = generateContextualTips(userMessages, customer)
      setCurrentTips(newTips)

      // 更新学习进度
      updateLearningProgress()

      setLastAnalyzedMessageCount(messageCount)
      setIsAnalyzing(false)
    }, 1000)

    return () => clearTimeout(analysisTimer)
  }, [userMessages.length, lastAnalyzedMessageCount, userMessages, customer])

  // 生成智能建议
  const generateSmartSuggestions = (messages: ChatMessage[], customer: TrainingCustomer): SmartSuggestion[] => {
    const suggestions: SmartSuggestion[] = []
    
    if (messages.length === 0) {
      suggestions.push({
        id: `suggestion_${Date.now()}_1`,
        type: 'strategy',
        category: 'communication',
        title: '开场建议',
        content: `与来自${customer.country.name}的客户交流时，建议先了解其文化背景和商务习惯`,
        priority: 'high',
        applicable: true,
        reasoning: '良好的开场是成功销售的基础',
        example: `"很高兴与您交流，我了解${customer.country.name}在${customer.product.category}领域有很高的标准..."`,
        timestamp: new Date()
      })
      return suggestions
    }

    const lastMessage = messages[messages.length - 1]?.content || ''

    // 基于最后一条消息分析
    if (lastMessage.length < 20) {
      suggestions.push({
        id: `suggestion_${Date.now()}_2`,
        type: 'technique',
        category: 'communication',
        title: '详细回复建议',
        content: '您的回复较为简短，建议提供更详细的信息来建立信任',
        priority: 'medium',
        applicable: true,
        reasoning: '详细的回复能展现专业性和对客户的重视',
        example: '详细说明产品特性、优势和适用场景',
        timestamp: new Date()
      })
    }

    if (!lastMessage.includes('需求') && !lastMessage.includes('要求')) {
      suggestions.push({
        id: `suggestion_${Date.now()}_3`,
        type: 'strategy',
        category: 'communication',
        title: '需求挖掘',
        content: '建议主动了解客户的具体需求和期望',
        priority: 'high',
        applicable: true,
        reasoning: '了解需求是提供精准解决方案的前提',
        example: '"请问您在选择这类产品时，最关注哪些方面？"',
        timestamp: new Date()
      })
    }

    // 根据客户背景生成建议
    if (customer.country.name !== '中国') {
      suggestions.push({
        id: `suggestion_${Date.now()}_4`,
        type: 'caution',
        category: 'cultural',
        title: '跨文化沟通',
        content: `注意${customer.country.name}的商务文化差异，调整沟通风格`,
        priority: 'medium',
        applicable: true,
        reasoning: '跨文化敏感度对国际销售至关重要',
        timestamp: new Date()
      })
    }

    return suggestions
  }

  // 生成上下文相关提示
  const generateContextualTips = (messages: ChatMessage[], customer: TrainingCustomer): LearningTip[] => {
    const tips: LearningTip[] = [
      {
        id: 'tip_1',
        title: '建立信任的三要素',
        content: '专业知识、诚信态度、个人魅力是建立客户信任的三大要素。在对话中要展现您的专业性，保持诚实透明，同时展现个人的亲和力。',
        category: 'communication',
        priority: 'high',
        trigger: {
          keywords: ['信任', '专业', '合作'],
          timing: 'during'
        }
      },
      {
        id: 'tip_2',
        title: '需求挖掘的SPIN技巧',
        content: 'SPIN销售法：情况问题(Situation)、问题问题(Problem)、暗示问题(Implication)、需求效益问题(Need-payoff)。逐步引导客户认识到需求的重要性。',
        category: 'negotiation',
        priority: 'high',
        trigger: {
          keywords: ['需求', '问题', '解决'],
          timing: 'during'
        }
      },
      {
        id: 'tip_3',
        title: `${customer.country.name}商务文化`,
        content: getCountryCultureTip(customer.country.name),
        category: 'cultural',
        priority: 'medium',
        trigger: {
          context: 'cultural_adaptation',
          timing: 'before'
        }
      },
      {
        id: 'tip_4',
        title: '产品价值呈现',
        content: `在介绍${customer.product.name}时，重点强调其如何解决客户的具体问题，而不仅仅是产品功能。将特性转化为客户利益。`,
        category: 'product',
        priority: 'high',
        trigger: {
          keywords: ['产品', '功能', '特点'],
          timing: 'during'
        }
      },
      {
        id: 'tip_5',
        title: '异议处理框架',
        content: '处理客户异议的AIDA框架：认同(Acknowledge)、调查(Investigate)、展示(Demonstrate)、行动(Action)。先认同客户的关切，然后深入了解，提供证据，最后引导下一步行动。',
        category: 'negotiation',
        priority: 'high',
        trigger: {
          keywords: ['但是', '不过', '担心', '问题'],
          timing: 'during'
        }
      }
    ]

    // 根据对话内容筛选相关提示
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1]?.content.toLowerCase() || ''
      return tips.filter(tip => {
        if (tip.trigger?.keywords) {
          return tip.trigger.keywords.some(keyword => lastMessage.includes(keyword))
        }
        return true
      })
    }

    return tips.slice(0, 3)
  }

  // 获取国家文化提示
  const getCountryCultureTip = (countryName: string): string => {
    const cultureTips: Record<string, string> = {
      '美国': '美国商务文化注重直接沟通、时间效率和个人成就。建议开门见山，突出ROI和竞争优势。',
      '日本': '日本商务文化重视礼貌、等级秩序和长期关系。建议展现尊重，注重细节，强调质量和可靠性。',
      '德国': '德国商务文化崇尚精确、守时和技术专业性。建议提供详细技术资料，强调质量和工程标准。',
      '英国': '英国商务文化注重礼貌、谦逊和传统。建议保持礼貌距离，展现专业素养和历史传承。',
      '法国': '法国商务文化重视优雅、智慧和创新。建议展现产品的设计美感和技术创新。'
    }
    return cultureTips[countryName] || '了解目标市场的商务文化，调整沟通风格以符合当地习惯。'
  }

  // 更新学习进度
  const updateLearningProgress = () => {
    const progress: LearningProgress[] = tipCategories.map(category => ({
      category: category.id,
      completedTips: Math.floor(Math.random() * 8) + 2,
      totalTips: 10,
      masteryLevel: Math.floor(Math.random() * 40) + 60,
      recentActivity: new Date()
    }))
    setLearningProgress(progress)
  }

  // 获取建议类型图标
  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'technique': return <Lightbulb className="w-4 h-4 text-yellow-500" />
      case 'response': return <MessageCircle className="w-4 h-4 text-blue-500" />
      case 'strategy': return <Target className="w-4 h-4 text-green-500" />
      case 'caution': return <AlertCircle className="w-4 h-4 text-orange-500" />
      default: return <Eye className="w-4 h-4 text-gray-500" />
    }
  }

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-200 text-red-600 bg-red-50'
      case 'medium': return 'border-yellow-200 text-yellow-600 bg-yellow-50'
      case 'low': return 'border-gray-200 text-gray-600 bg-gray-50'
      default: return 'border-gray-200 text-gray-600 bg-gray-50'
    }
  }

  return (
    <div className="space-y-4 h-full flex flex-col px-3">
      {/* 头部状态卡片 */}
      <Card className="bg-gradient-to-r from-purple-50 via-white to-indigo-50 dark:from-gray-800 dark:via-gray-800 dark:to-gray-700 border-purple-200/50 dark:border-gray-600 max-w-xs w-full">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg">
                <Brain className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 dark:text-white">AI智能指导</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">个性化学习建议</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {isAnalyzing && <RotateCcw className="w-4 h-4 text-purple-500 animate-spin" />}
              <Badge className="bg-purple-100 text-purple-700 border-purple-200 dark:bg-purple-900/30 dark:text-purple-400">
                {smartSuggestions.length} 条建议
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 标签页 */}
      <Card className="flex-1 flex flex-col overflow-hidden">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'suggestions' | 'tips' | 'progress')} className="flex flex-col h-full">
          <div className="flex-shrink-0 p-3 border-b border-gray-200 dark:border-gray-700">
            <TabsList className="grid w-full grid-cols-3 bg-gray-100 dark:bg-gray-800">
              <TabsTrigger value="suggestions" className="text-xs">
                <Sparkles className="w-3 h-3 mr-1" />
                建议
              </TabsTrigger>
              <TabsTrigger value="tips" className="text-xs">
                <BookOpen className="w-3 h-3 mr-1" />
                技巧
              </TabsTrigger>
              <TabsTrigger value="progress" className="text-xs">
                <TrendingUp className="w-3 h-3 mr-1" />
                进度
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="flex-1 overflow-y-auto">
            {/* 智能建议标签页 */}
            <TabsContent value="suggestions" className="mt-0 p-3">
              <ScrollArea className="h-full">
                {smartSuggestions.length > 0 ? (
                  <div className="space-y-3">
                    {smartSuggestions.slice(-6).reverse().map((suggestion) => (
                      <Card key={suggestion.id} className="border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0 mt-0.5">
                              {getSuggestionIcon(suggestion.type)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                                  {suggestion.title}
                                </h4>
                                <Badge variant="outline" className={`text-xs ${getPriorityColor(suggestion.priority)}`}>
                                  {suggestion.priority}
                                </Badge>
                              </div>
                              <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                                {suggestion.content}
                              </p>
                              {suggestion.example && (
                                <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded-md mb-2">
                                  <p className="text-xs text-gray-700 dark:text-gray-300 italic">
                                    示例: {suggestion.example}
                                  </p>
                                </div>
                              )}
                              <div className="flex items-center justify-between">
                                <span className="text-xs text-gray-500">
                                  {suggestion.reasoning}
                                </span>
                                <Button size="sm" variant="ghost" className="h-6 text-xs">
                                  <Play className="w-3 h-3 mr-1" />
                                  应用
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Compass className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">开始对话获取建议</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      AI将根据您的对话内容提供个性化建议
                    </p>
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            {/* 学习技巧标签页 */}
            <TabsContent value="tips" className="mt-0 p-3">
              <div className="space-y-3">
                {/* 分类筛选 */}
                <div className="flex flex-wrap gap-2">
                  <Button
                    size="sm"
                    variant={selectedCategory === 'all' ? 'default' : 'outline'}
                    onClick={() => setSelectedCategory('all')}
                    className="h-8 text-xs"
                  >
                    全部
                  </Button>
                  {tipCategories.map((category) => (
                    <Button
                      key={category.id}
                      size="sm"
                      variant={selectedCategory === category.id ? 'default' : 'outline'}
                      onClick={() => setSelectedCategory(category.id)}
                      className="h-8 text-xs"
                    >
                      <category.icon className="w-3 h-3 mr-1" />
                      {category.name}
                    </Button>
                  ))}
                </div>

                <ScrollArea className="h-80">
                  <div className="space-y-3">
                    {currentTips
                      .filter(tip => selectedCategory === 'all' || tip.category === selectedCategory)
                      .map((tip) => {
                        const category = tipCategories.find(cat => cat.id === tip.category)
                        return (
                          <Card key={tip.id} className={`border ${category?.borderColor} dark:border-gray-700`}>
                            <CardContent className="p-4">
                              <div className="flex items-start space-x-3">
                                {category && (
                                  <div className={`p-2 ${category.bgColor} rounded-lg flex-shrink-0`}>
                                    <category.icon className="w-4 h-4 text-white" />
                                  </div>
                                )}
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center justify-between mb-2">
                                    <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                                      {tip.title}
                                    </h4>
                                    <Badge variant="outline" className={`text-xs ${getPriorityColor(tip.priority)}`}>
                                      {tip.priority}
                                    </Badge>
                                  </div>
                                  <p className="text-xs text-gray-600 dark:text-gray-400 leading-relaxed">
                                    {tip.content}
                                  </p>
                                  <div className="flex items-center justify-between mt-3">
                                    <span className="text-xs text-gray-500">
                                      {category?.name}
                                    </span>
                                    <div className="flex space-x-1">
                                      <Button size="sm" variant="ghost" className="h-6 text-xs">
                                        <BookMarked className="w-3 h-3 mr-1" />
                                        收藏
                                      </Button>
                                      <Button size="sm" variant="ghost" className="h-6 text-xs">
                                        <ThumbsUp className="w-3 h-3" />
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )
                      })}
                  </div>
                </ScrollArea>
              </div>
            </TabsContent>

            {/* 学习进度标签页 */}
            <TabsContent value="progress" className="mt-0 p-3">
              <div className="space-y-4">
                {/* 整体进度 */}
                <Card className="border border-gray-200 dark:border-gray-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center">
                      <Trophy className="w-4 h-4 mr-2 text-yellow-500" />
                      学习统计
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-3 text-center">
                      <div className="p-2 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg">
                        <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                          {currentTips.length}
                        </div>
                        <p className="text-xs text-gray-600 dark:text-gray-400">活跃技巧</p>
                      </div>
                      <div className="p-2 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg">
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">
                          {learningProgress.reduce((sum, p) => sum + p.completedTips, 0)}
                        </div>
                        <p className="text-xs text-gray-600 dark:text-gray-400">已掌握</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 分类进度 */}
                <ScrollArea className="h-64">
                  <div className="space-y-3">
                    {tipCategories.map((category) => {
                      const progress = learningProgress.find(p => p.category === category.id)
                      const progressPercent = progress ? (progress.completedTips / progress.totalTips) * 100 : 0
                      const masteryLevel = progress?.masteryLevel || 0

                      return (
                        <Card key={category.id} className={`border ${category.borderColor} dark:border-gray-700`}>
                          <CardContent className="p-3">
                            <div className="flex items-center space-x-3">
                              <div className={`p-2 ${category.bgColor} rounded-lg flex-shrink-0`}>
                                <category.icon className="w-4 h-4 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                                    {category.name}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {progress?.completedTips || 0}/{progress?.totalTips || 0}
                                  </span>
                                </div>
                                <Progress value={progressPercent} className="h-1.5 mb-1" />
                                <div className="flex items-center justify-between">
                                  <span className="text-xs text-gray-500">掌握度: {masteryLevel}%</span>
                                  <Badge variant="outline" className="text-xs">
                                    {masteryLevel >= 80 ? '精通' : masteryLevel >= 60 ? '熟练' : '学习中'}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </ScrollArea>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </Card>
    </div>
  )
}
