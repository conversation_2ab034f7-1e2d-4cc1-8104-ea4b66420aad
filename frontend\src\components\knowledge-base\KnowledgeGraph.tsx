/**
 * 知识图谱可视化组件
 * 使用React Flow展示文档间的关联关系和知识网络
 */
import React, { useState, useCallback, useMemo } from 'react';
import {
  ReactFlow,
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  ConnectionMode,
  Panel,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Network,
  FileText,
  Tag,
  Loader2,
  RefreshCw,
  Maximize
} from 'lucide-react';
import { useKnowledgeGraph, useGraphVisualization, useGraphs } from '@/hooks/useMorphik';
import { useDocuments } from '@/hooks/useDocuments';

// 文档节点组件
function DocumentNode({ data }: { data: any }) {
  return (
    <div className="px-4 py-2 shadow-md rounded-md bg-white border-2 border-blue-200 min-w-[150px]">
      <div className="flex items-center gap-2">
        <FileText className="h-4 w-4 text-blue-600" />
        <div className="font-bold text-sm">{data.label}</div>
      </div>
      <div className="text-xs text-gray-500 mt-1">
        {data.type} • {data.status}
      </div>
      {data.category && (
        <Badge variant="outline" className="mt-1 text-xs">
          {data.category}
        </Badge>
      )}
    </div>
  );
}

// 分类节点组件
function CategoryNode({ data }: { data: any }) {
  return (
    <div className="px-3 py-2 shadow-md rounded-md bg-green-50 border-2 border-green-200 min-w-[120px]">
      <div className="flex items-center gap-2">
        <Tag className="h-4 w-4 text-green-600" />
        <div className="font-bold text-sm">{data.label}</div>
      </div>
      <div className="text-xs text-gray-500 mt-1">
        {data.count} 个文档
      </div>
    </div>
  );
}

// 标签节点组件
function TagNode({ data }: { data: any }) {
  return (
    <div className="px-3 py-2 shadow-md rounded-md bg-purple-50 border-2 border-purple-200 min-w-[100px]">
      <div className="flex items-center gap-2">
        <Tag className="h-3 w-3 text-purple-600" />
        <div className="font-medium text-xs">{data.label}</div>
      </div>
    </div>
  );
}

// 自定义节点类型
const nodeTypes = {
  document: DocumentNode,
  category: CategoryNode,
  tag: TagNode,
};

interface KnowledgeGraphProps {
  onNodeSelect?: (node: Node) => void;
  height?: string;
  showMiniMap?: boolean;
  showControls?: boolean;
  selectedGraph?: string;
  useRealData?: boolean;
}

function KnowledgeGraph({
  onNodeSelect,
  height = "600px",
  showMiniMap = true,
  showControls = true,
  selectedGraph,
  useRealData = true
}: KnowledgeGraphProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'documents' | 'categories' | 'tags'>('all');
  const [isFullscreen, setIsFullscreen] = useState(false);

  const { data: documentsData, isLoading: documentsLoading } = useDocuments({ limit: 100 });
  const { data: graphs } = useGraphs();

  const { data: graphVisualization, isLoading: graphVisualizationLoading } = useGraphVisualization(
    selectedGraph || (graphs && graphs.length > 0 ? graphs[0].name : ''),
    useRealData && !!(selectedGraph || (graphs && graphs.length > 0))
  );

  // 调试信息
  React.useEffect(() => {
    console.log('🔍 KnowledgeGraph Debug:');
    console.log('  - selectedGraph:', selectedGraph);
    console.log('  - selectedGraph type:', typeof selectedGraph);
    console.log('  - selectedGraph length:', selectedGraph?.length);
    console.log('  - graphs:', graphs);
    console.log('  - useRealData:', useRealData);
    console.log('  - graphVisualization:', graphVisualization);
    console.log('  - graphVisualizationLoading:', graphVisualizationLoading);
  }, [selectedGraph, graphs, useRealData, graphVisualization, graphVisualizationLoading]);
  const { isLoading: graphLoading } = useKnowledgeGraph();

  // 生成真实图谱数据
  const generateRealGraphData = useCallback(() => {
    console.log('🔄 generateRealGraphData called');
    console.log('  - graphVisualization:', graphVisualization);
    console.log('  - graphVisualization.nodes:', graphVisualization?.nodes);
    console.log('  - graphVisualization.links:', graphVisualization?.links);

    if (!graphVisualization) {
      console.log('❌ No graphVisualization data, returning');
      return;
    }

    const newNodes: Node[] = [];
    const newEdges: Edge[] = [];

    // 处理真实图谱节点
    graphVisualization.nodes.forEach((node) => {
      const nodeType = node.type.toLowerCase();
      let nodeColor = '#3b82f6'; // 默认蓝色

      switch (nodeType) {
        case 'person':
          nodeColor = '#f59e0b'; // 橙色
          break;
        case 'organization':
          nodeColor = '#10b981'; // 绿色
          break;
        case 'location':
          nodeColor = '#8b5cf6'; // 紫色
          break;
        case 'concept':
          nodeColor = '#ef4444'; // 红色
          break;
        default:
          nodeColor = '#6b7280'; // 灰色
      }

      newNodes.push({
        id: node.id,
        type: 'document', // 使用现有的节点类型
        position: {
          x: Math.random() * 800,
          y: Math.random() * 600
        },
        data: {
          label: node.label,
          type: node.type,
          status: 'completed',
          properties: node.properties,
          color: nodeColor,
        },
      });
    });

    // 处理真实图谱边
    graphVisualization.links.forEach((link, index) => {
      const sourceId = typeof link.source === 'string' ? link.source : (link.source as any)?.id || link.source;
      const targetId = typeof link.target === 'string' ? link.target : (link.target as any)?.id || link.target;

      newEdges.push({
        id: `edge-${index}`,
        source: sourceId,
        target: targetId,
        type: 'smoothstep',
        style: { stroke: '#6b7280', strokeWidth: 2 },
        label: link.label || link.type,
        animated: false,
      });
    });

    console.log('✅ Setting nodes and edges:');
    console.log('  - newNodes count:', newNodes.length);
    console.log('  - newEdges count:', newEdges.length);
    console.log('  - newNodes:', newNodes);

    setNodes(newNodes);
    setEdges(newEdges);
  }, [graphVisualization, setNodes, setEdges]);

  // 生成模拟图谱数据
  const generateMockGraphData = useCallback(() => {
    if (!documentsData?.items) return;

    const documents = documentsData.items;
    const newNodes: Node[] = [];
    const newEdges: Edge[] = [];

    // 收集所有分类和标签
    const categories = new Map<string, number>();
    const tags = new Map<string, number>();

    documents.forEach(doc => {
      if (doc.metadata?.category) {
        categories.set(doc.metadata.category, (categories.get(doc.metadata.category) || 0) + 1);
      }
      if (doc.metadata?.tags) {
        doc.metadata.tags.forEach(tag => {
          tags.set(tag, (tags.get(tag) || 0) + 1);
        });
      }
    });

    // 创建分类节点
    let yOffset = 0;
    categories.forEach((count, category) => {
      newNodes.push({
        id: `category-${category}`,
        type: 'category',
        position: { x: -200, y: yOffset },
        data: {
          label: category,
          count,
          type: 'category'
        },
      });
      yOffset += 100;
    });

    // 创建标签节点
    let tagXOffset = 400;
    let tagYOffset = 0;
    tags.forEach((count, tag) => {
      newNodes.push({
        id: `tag-${tag}`,
        type: 'tag',
        position: { x: tagXOffset, y: tagYOffset },
        data: {
          label: tag,
          count,
          type: 'tag'
        },
      });
      tagYOffset += 80;
      if (tagYOffset > 400) {
        tagYOffset = 0;
        tagXOffset += 150;
      }
    });

    // 创建文档节点
    let docXOffset = 0;
    let docYOffset = 0;
    documents.forEach((doc) => {
      const nodeId = `doc-${doc.external_id}`;

      newNodes.push({
        id: nodeId,
        type: 'document',
        position: { x: docXOffset, y: docYOffset },
        data: {
          label: doc.filename || doc.metadata?.title || '未命名文档',
          type: doc.content_type || 'unknown',
          status: doc.system_metadata?.status || 'completed',
          category: doc.metadata?.category,
          document: doc,
        },
      });

      // 连接到分类
      if (doc.metadata?.category) {
        newEdges.push({
          id: `${nodeId}-category-${doc.metadata.category}`,
          source: nodeId,
          target: `category-${doc.metadata.category}`,
          type: 'smoothstep',
          style: { stroke: '#10b981', strokeWidth: 2 },
          label: '属于',
        });
      }

      // 连接到标签
      if (doc.metadata?.tags) {
        doc.metadata.tags.forEach(tag => {
          newEdges.push({
            id: `${nodeId}-tag-${tag}`,
            source: nodeId,
            target: `tag-${tag}`,
            type: 'smoothstep',
            style: { stroke: '#8b5cf6', strokeWidth: 1 },
            label: '标记',
          });
        });
      }

      // 布局文档节点
      docXOffset += 200;
      if (docXOffset > 600) {
        docXOffset = 0;
        docYOffset += 120;
      }
    });

    setNodes(newNodes);
    setEdges(newEdges);
  }, [documentsData, setNodes, setEdges]);

  // 当数据加载完成时生成图谱
  React.useEffect(() => {
    if (useRealData && graphVisualization) {
      generateRealGraphData();
    } else if (!useRealData && documentsData?.items && documentsData.items.length > 0) {
      generateMockGraphData();
    }
  }, [useRealData, graphVisualization, documentsData, generateRealGraphData, generateMockGraphData]);

  // 过滤节点
  const filteredNodes = useMemo(() => {
    if (selectedFilter === 'all') return nodes;

    return nodes.filter(node => {
      switch (selectedFilter) {
        case 'documents':
          return node.type === 'document';
        case 'categories':
          return node.type === 'category';
        case 'tags':
          return node.type === 'tag';
        default:
          return true;
      }
    });
  }, [nodes, selectedFilter]);

  // 过滤边
  const filteredEdges = useMemo(() => {
    if (selectedFilter === 'all') return edges;

    const nodeIds = new Set(filteredNodes.map(node => node.id));
    return edges.filter(edge =>
      nodeIds.has(edge.source) && nodeIds.has(edge.target)
    );
  }, [edges, filteredNodes]);

  // 节点点击处理
  const onNodeClick = useCallback((_event: React.MouseEvent, node: Node) => {
    console.log('Node clicked:', node);
    onNodeSelect?.(node);
  }, [onNodeSelect]);

  // 连接处理
  const onConnect = useCallback(
    (params: any) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // 重新布局
  const handleRelayout = () => {
    if (useRealData && graphVisualization) {
      generateRealGraphData();
    } else {
      generateMockGraphData();
    }
  };

  // 全屏切换
  const handleFullscreen = () => {
    const element = document.querySelector('.knowledge-graph-container');
    if (!isFullscreen) {
      if (element?.requestFullscreen) {
        element.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  const isLoading = useRealData
    ? (graphVisualizationLoading)
    : (documentsLoading || graphLoading);

  // 临时禁用早期返回，用于调试
  if (false && isLoading) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">构建知识图谱中...</h3>
          <p className="text-muted-foreground">正在分析文档关系和知识结构</p>
        </CardContent>
      </Card>
    );
  }

  // 检查是否有数据
  const hasData = useRealData
    ? (graphVisualization && (graphVisualization.nodes.length > 0 || graphVisualization.links.length > 0))
    : (documentsData?.items && documentsData.items.length > 0);

  // 添加详细的调试信息
  console.log('🔍 KnowledgeGraph 数据检查:');
  console.log('  - selectedGraph:', selectedGraph);
  console.log('  - useRealData:', useRealData);
  console.log('  - graphVisualization:', graphVisualization);
  console.log('  - graphVisualization?.nodes?.length:', graphVisualization?.nodes?.length);
  console.log('  - graphVisualization?.links?.length:', graphVisualization?.links?.length);
  console.log('  - documentsData?.items?.length:', documentsData?.items?.length);
  console.log('  - hasData:', hasData);
  console.log('  - isLoading:', isLoading);

  if (!hasData) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Network className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">暂无知识图谱</h3>
          <p className="text-muted-foreground mb-4">
            {useRealData
              ? `图谱 "${selectedGraph}" 暂无数据，可能原因：\n1. 图谱还在处理中\n2. 没有关联文档\n3. 文档处理失败`
              : '上传一些文档后，系统将自动构建知识图谱'
            }
          </p>
          <div className="space-y-2">
            <Button variant="outline" onClick={() => window.location.reload()}>
              刷新重试
            </Button>
            <div className="text-xs text-muted-foreground mt-2">
              调试信息: 节点={graphVisualization?.nodes?.length || 0}, 链接={graphVisualization?.links?.length || 0}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4 knowledge-graph-container">
      {/* 控制面板 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Network className="h-5 w-5" />
              <CardTitle>知识图谱</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {filteredNodes.length} 个节点
              </Badge>
              <Badge variant="outline">
                {filteredEdges.length} 个连接
              </Badge>
            </div>
          </div>
          <CardDescription>
            可视化展示文档间的关联关系和知识结构
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">筛选:</span>
              {(['all', 'documents', 'categories', 'tags'] as const).map((filter) => (
                <Button
                  key={filter}
                  variant={selectedFilter === filter ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedFilter(filter)}
                >
                  {filter === 'all' && '全部'}
                  {filter === 'documents' && '文档'}
                  {filter === 'categories' && '分类'}
                  {filter === 'tags' && '标签'}
                </Button>
              ))}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRelayout}
                title="重新布局图谱"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleFullscreen}
                title={isFullscreen ? "退出全屏" : "全屏显示"}
              >
                <Maximize className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 图谱可视化 */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">
              {selectedGraph ? `图谱: ${selectedGraph}` : '暂无知识图谱'}
            </CardTitle>
            <div className="flex items-center gap-2">
              {selectedGraph && (
                <Badge variant="outline">
                  {graphVisualizationLoading ? '加载中...' : '已加载'}
                </Badge>
              )}
            </div>
          </div>
          {selectedGraph && (
            <CardDescription>
              节点: {filteredNodes.length} | 连接: {filteredEdges.length}
            </CardDescription>
          )}
        </CardHeader>
        <CardContent className="p-0">
          <div
            style={{
              height: isFullscreen ? '80vh' : height,
              width: '100%'
            }}
            className="relative"
          >


            {/* 强制显示图谱内容，忽略selectedGraph检查 */}
            {false ? (
              <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                <Network className="h-16 w-16 mb-4" />
                <h3 className="text-lg font-semibold mb-2">请选择知识图谱</h3>
                <p className="text-center">
                  从左侧面板选择一个图谱来查看可视化内容
                </p>
              </div>
            ) : isLoading ? (
              <div className="flex flex-col items-center justify-center h-full">
                <Loader2 className="h-8 w-8 animate-spin mb-4" />
                <p className="text-muted-foreground">正在加载图谱数据...</p>
              </div>
            ) : false && filteredNodes.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                <Network className="h-16 w-16 mb-4" />
                <h3 className="text-lg font-semibold mb-2">图谱暂无数据</h3>
                <p className="text-center mb-4">
                  该图谱还没有包含任何文档或实体。<br />
                  请上传文档或更新图谱以生成可视化内容。
                </p>
                <Button variant="outline" onClick={() => window.location.reload()}>
                  刷新图谱
                </Button>
              </div>
            ) : (
              <ReactFlow
              nodes={filteredNodes}
              edges={filteredEdges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={onNodeClick}
              nodeTypes={nodeTypes}
              connectionMode={ConnectionMode.Loose}
              fitView
              fitViewOptions={{ padding: 0.2 }}
            >
              {showControls && <Controls />}
              {showMiniMap && (
                <MiniMap
                  nodeColor={(node) => {
                    switch (node.type) {
                      case 'document': return '#3b82f6';
                      case 'category': return '#10b981';
                      case 'tag': return '#8b5cf6';
                      default: return '#6b7280';
                    }
                  }}
                  nodeStrokeWidth={3}
                  zoomable
                  pannable
                />
              )}
              <Background
                variant={BackgroundVariant.Dots}
                gap={12}
                size={1}
              />

              {/* 图例 */}
              <Panel position="top-left">
                <div className="bg-white p-3 rounded-lg shadow-md border">
                  <h4 className="font-medium text-sm mb-2">图例</h4>
                  <div className="space-y-1 text-xs">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-blue-500 rounded"></div>
                      <span>文档</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded"></div>
                      <span>分类</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-purple-500 rounded"></div>
                      <span>标签</span>
                    </div>
                  </div>
                </div>
              </Panel>
            </ReactFlow>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default KnowledgeGraph;
