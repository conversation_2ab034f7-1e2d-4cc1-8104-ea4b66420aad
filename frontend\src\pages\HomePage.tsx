import {
  <PERSON>,
  MessageSquare,
  <PERSON>rollText,
  BarChart3,
  FileText,
  <PERSON>,
  ArrowRight
} from "lucide-react"

export default function HomePage() {
  return (
    <div className="flex-1 p-6 bg-gradient-to-br from-blue-50/30 via-white to-indigo-50/20 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 min-h-screen">
      {/* 页面头部 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-sm text-blue-600 dark:text-blue-400 font-medium mb-2">
              {new Date().toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
              })}
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">工作台</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-1">智汇兔AI外贸收割机 - 您的智能外贸助手</p>
          </div>
          <div className="hidden md:flex items-center space-x-3">
            <div className="bg-white dark:bg-gray-800 rounded-lg px-3 py-2 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="text-xs text-gray-500 dark:text-gray-400">当前时间</div>
              <div className="text-sm font-semibold text-gray-900 dark:text-white">
                {new Date().toLocaleTimeString('zh-CN', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要功能区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
        {/* AI工具卡片 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200/50 dark:border-gray-700/50 hover:shadow-md transition-all duration-200 group">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-md group-hover:scale-105 transition-transform duration-200">
              <Brain className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">智能知识库</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">文档管理与智能检索</p>
            </div>
          </div>
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
            上传文档，构建知识图谱，实现智能问答和语义搜索
          </p>
          <button
            onClick={() => window.location.href = '/knowledge-base'}
            className="w-full bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 py-2 px-4 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <span>立即使用</span>
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>

        {/* 智能对话卡片 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200/50 dark:border-gray-700/50 hover:shadow-md transition-all duration-200 group">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-md group-hover:scale-105 transition-transform duration-200">
              <MessageSquare className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">智能对话</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">AI助手多轮对话</p>
            </div>
          </div>
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
            与AI助手进行智能对话，支持多轮对话和会话管理
          </p>
          <button
            onClick={() => window.location.href = '/chat'}
            className="w-full bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 py-2 px-4 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <span>开始对话</span>
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>

        {/* 话术模板卡片 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200/50 dark:border-gray-700/50 hover:shadow-md transition-all duration-200 group">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md group-hover:scale-105 transition-transform duration-200">
              <ScrollText className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">话术模板</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">外贸话术管理</p>
            </div>
          </div>
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
            管理外贸话术模板，提升沟通效率和专业度
          </p>
          <button
            onClick={() => window.location.href = '/market-analysis'}
            className="w-full bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 py-2 px-4 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <span>管理模板</span>
            <ArrowRight className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 快速统计区域 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200/50 dark:border-gray-700/50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className="text-sm text-gray-600 dark:text-gray-400">文档总数</div>
              <div className="text-xl font-bold text-gray-900 dark:text-white">128</div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200/50 dark:border-gray-700/50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className="text-sm text-gray-600 dark:text-gray-400">对话次数</div>
              <div className="text-xl font-bold text-gray-900 dark:text-white">456</div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200/50 dark:border-gray-700/50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
              <ScrollText className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className="text-sm text-gray-600 dark:text-gray-400">话术模板</div>
              <div className="text-xl font-bold text-gray-900 dark:text-white">32</div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200/50 dark:border-gray-700/50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-white" />
            </div>
            <div>
              <div className="text-sm text-gray-600 dark:text-gray-400">知识图谱</div>
              <div className="text-xl font-bold text-gray-900 dark:text-white">8</div>
            </div>
          </div>
        </div>
      </div>

      {/* 最近活动区域 */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200/50 dark:border-gray-700/50">
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center">
            <Clock className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">最近活动</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">系统使用记录</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
              <Brain className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900 dark:text-white">上传了新文档</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">2小时前</div>
            </div>
          </div>

          <div className="flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
              <MessageSquare className="w-4 h-4 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900 dark:text-white">进行了AI对话</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">5小时前</div>
            </div>
          </div>

          <div className="flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
            <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
              <ScrollText className="w-4 h-4 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900 dark:text-white">更新了话术模板</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">1天前</div>
            </div>
          </div>
        </div>
      </div>

    </div>
  )
}
