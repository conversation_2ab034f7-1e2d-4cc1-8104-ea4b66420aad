/**
 * 性能监控组件
 * 监控知识库系统的性能指标和用户体验
 */
import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Activity,
  Clock,
  Zap,
  Database,
  Wifi,
  AlertTriangle,
  CheckCircle,
  TrendingUp
} from 'lucide-react';

interface PerformanceMetrics {
  // 页面性能
  pageLoadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  firstInputDelay: number;

  // API性能
  apiResponseTimes: {
    search: number;
    upload: number;
    chat: number;
    documents: number;
  };

  // 资源使用
  memoryUsage: number;
  bundleSize: number;
  networkRequests: number;

  // 用户体验
  interactionToNextPaint: number;
  timeToInteractive: number;
}

interface PerformanceMonitorProps {
  showDetails?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function PerformanceMonitor({
  showDetails = false,
  autoRefresh = true,
  refreshInterval = 30000
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 收集性能指标
  const collectMetrics = async (): Promise<PerformanceMetrics> => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');

    // 页面性能指标
    const pageLoadTime = navigation.loadEventEnd - (navigation as any).navigationStart;
    const firstContentfulPaint = paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0;

    // Web Vitals (模拟值，实际应该使用web-vitals库)
    const largestContentfulPaint = firstContentfulPaint + Math.random() * 1000;
    const cumulativeLayoutShift = Math.random() * 0.1;
    const firstInputDelay = Math.random() * 100;
    const interactionToNextPaint = Math.random() * 200;
    const timeToInteractive = pageLoadTime * 0.8;

    // API性能指标 (模拟值，实际应该从API调用中收集)
    const apiResponseTimes = {
      search: 150 + Math.random() * 300,
      upload: 500 + Math.random() * 1000,
      chat: 800 + Math.random() * 1200,
      documents: 200 + Math.random() * 400,
    };

    // 资源使用指标
    const memoryUsage = (performance as any).memory?.usedJSHeapSize || 0;
    const bundleSize = 2.5; // MB (估算值)
    const networkRequests = performance.getEntriesByType('resource').length;

    return {
      pageLoadTime,
      firstContentfulPaint,
      largestContentfulPaint,
      cumulativeLayoutShift,
      firstInputDelay,
      apiResponseTimes,
      memoryUsage,
      bundleSize,
      networkRequests,
      interactionToNextPaint,
      timeToInteractive,
    };
  };

  // 初始化和定期更新指标
  useEffect(() => {
    const updateMetrics = async () => {
      setIsLoading(true);
      try {
        const newMetrics = await collectMetrics();
        setMetrics(newMetrics);
      } catch (error) {
        console.error('Failed to collect performance metrics:', error);
      } finally {
        setIsLoading(false);
      }
    };

    updateMetrics();

    if (autoRefresh) {
      const interval = setInterval(updateMetrics, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  // 性能评分
  const getPerformanceScore = (metrics: PerformanceMetrics): number => {
    let score = 100;

    // 页面加载时间评分
    if (metrics.pageLoadTime > 3000) score -= 20;
    else if (metrics.pageLoadTime > 2000) score -= 10;

    // LCP评分
    if (metrics.largestContentfulPaint > 2500) score -= 15;
    else if (metrics.largestContentfulPaint > 1500) score -= 8;

    // CLS评分
    if (metrics.cumulativeLayoutShift > 0.1) score -= 15;
    else if (metrics.cumulativeLayoutShift > 0.05) score -= 8;

    // FID评分
    if (metrics.firstInputDelay > 100) score -= 15;
    else if (metrics.firstInputDelay > 50) score -= 8;

    // API响应时间评分
    const avgApiTime = Object.values(metrics.apiResponseTimes).reduce((a, b) => a + b, 0) / 4;
    if (avgApiTime > 1000) score -= 15;
    else if (avgApiTime > 500) score -= 8;

    return Math.max(0, Math.round(score));
  };

  // 获取性能等级
  const getPerformanceGrade = (score: number): { grade: string; color: string; icon: React.ReactNode } => {
    if (score >= 90) return { grade: 'A', color: 'text-green-600', icon: <CheckCircle className="h-4 w-4" /> };
    if (score >= 80) return { grade: 'B', color: 'text-blue-600', icon: <TrendingUp className="h-4 w-4" /> };
    if (score >= 70) return { grade: 'C', color: 'text-yellow-600', icon: <Activity className="h-4 w-4" /> };
    if (score >= 60) return { grade: 'D', color: 'text-orange-600', icon: <AlertTriangle className="h-4 w-4" /> };
    return { grade: 'F', color: 'text-red-600', icon: <AlertTriangle className="h-4 w-4" /> };
  };

  // 格式化时间
  const formatTime = (ms: number): string => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  // 格式化内存
  const formatMemory = (bytes: number): string => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)}MB`;
  };

  if (isLoading || !metrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4 animate-pulse" />
            <span className="text-sm">收集性能数据中...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const score = getPerformanceScore(metrics);
  const grade = getPerformanceGrade(score);

  return (
    <div className="space-y-4">
      {/* 性能总览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            性能监控
          </CardTitle>
          <CardDescription>
            实时监控知识库系统的性能指标和用户体验
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className={`flex items-center gap-2 ${grade.color}`}>
                {grade.icon}
                <span className="text-2xl font-bold">{grade.grade}</span>
              </div>
              <div>
                <div className="text-lg font-semibold">{score}/100</div>
                <div className="text-xs text-muted-foreground">性能评分</div>
              </div>
            </div>
            <Progress value={score} className="w-32" />
          </div>

          {/* 核心指标 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <Clock className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
              <div className="text-sm font-medium">{formatTime(metrics.pageLoadTime)}</div>
              <div className="text-xs text-muted-foreground">页面加载</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <Zap className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
              <div className="text-sm font-medium">{formatTime(metrics.firstContentfulPaint)}</div>
              <div className="text-xs text-muted-foreground">首次绘制</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <Database className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
              <div className="text-sm font-medium">{formatMemory(metrics.memoryUsage)}</div>
              <div className="text-xs text-muted-foreground">内存使用</div>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <Wifi className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
              <div className="text-sm font-medium">{metrics.networkRequests}</div>
              <div className="text-xs text-muted-foreground">网络请求</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 详细指标 */}
      {showDetails && (
        <Card>
          <CardHeader>
            <CardTitle>详细性能指标</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Web Vitals */}
            <div>
              <h4 className="font-medium mb-2">Web Vitals</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <div className="flex justify-between items-center p-2 bg-muted/30 rounded">
                  <span className="text-sm">LCP</span>
                  <Badge variant={metrics.largestContentfulPaint < 2500 ? 'default' : 'destructive'}>
                    {formatTime(metrics.largestContentfulPaint)}
                  </Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-muted/30 rounded">
                  <span className="text-sm">CLS</span>
                  <Badge variant={metrics.cumulativeLayoutShift < 0.1 ? 'default' : 'destructive'}>
                    {metrics.cumulativeLayoutShift.toFixed(3)}
                  </Badge>
                </div>
                <div className="flex justify-between items-center p-2 bg-muted/30 rounded">
                  <span className="text-sm">FID</span>
                  <Badge variant={metrics.firstInputDelay < 100 ? 'default' : 'destructive'}>
                    {formatTime(metrics.firstInputDelay)}
                  </Badge>
                </div>
              </div>
            </div>

            {/* API性能 */}
            <div>
              <h4 className="font-medium mb-2">API响应时间</h4>
              <div className="space-y-2">
                {Object.entries(metrics.apiResponseTimes).map(([api, time]) => (
                  <div key={api} className="flex justify-between items-center">
                    <span className="text-sm capitalize">{api}</span>
                    <Badge variant={time < 500 ? 'default' : time < 1000 ? 'secondary' : 'destructive'}>
                      {formatTime(time)}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>

            {/* 资源信息 */}
            <div>
              <h4 className="font-medium mb-2">资源使用</h4>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex justify-between items-center p-2 bg-muted/30 rounded">
                  <span className="text-sm">Bundle大小</span>
                  <span className="text-sm font-medium">{metrics.bundleSize}MB</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-muted/30 rounded">
                  <span className="text-sm">交互延迟</span>
                  <span className="text-sm font-medium">{formatTime(metrics.interactionToNextPaint)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default PerformanceMonitor;
