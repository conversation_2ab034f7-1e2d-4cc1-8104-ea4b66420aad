FROM postgres:16-alpine

# Configure Alpine mirrors for faster downloads in China
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    echo "https://mirrors.tuna.tsinghua.edu.cn/alpine/v$(cat /etc/alpine-release | cut -d'.' -f1,2)/main" > /etc/apk/repositories && \
    echo "https://mirrors.tuna.tsinghua.edu.cn/alpine/v$(cat /etc/alpine-release | cut -d'.' -f1,2)/community" >> /etc/apk/repositories && \
    apk update

# Install build dependencies with optimized package selection
RUN apk add --no-cache \
    git \
    build-base \
    clang19 \
    llvm19 \
    postgresql-dev

# Clone and build pgvector (try multiple sources for reliability)
RUN (git clone --branch v0.8.0 --depth 1 https://github.com/pgvector/pgvector.git || \
     git clone --branch v0.8.0 --depth 1 https://gitee.com/mirrors/pgvector.git || \
     git clone --branch v0.8.0 --depth 1 https://hub.fastgit.xyz/pgvector/pgvector.git) \
    && cd pgvector \
    && make OPTFLAGS="" \
    && make install

# Cleanup
RUN apk del git build-base clang19 llvm19 postgresql-dev \
    && rm -rf /pgvector

# Copy data dump
COPY dump.sql /tmp/dump.sql
