/**
 * 销冠实战训练数据管理Hook
 * 统一管理国家、产品、职位数据的获取、更新、删除操作
 */

import { useState, useEffect, useCallback } from 'react'
import { toast } from 'sonner'
import type { Country, Product } from '@/types/salesTraining'
import salesTrainingService, { SalesTrainingError } from '@/services/salesTrainingService'

interface UseSalesTrainingDataReturn {
  // 数据状态
  countries: Country[]
  products: Product[]

  // 加载状态
  isLoading: boolean
  isInitialLoading: boolean
  error: string | null

  // 国家操作
  addCountry: (data: Omit<Country, 'id'>) => Promise<boolean>
  updateCountry: (id: number, data: Partial<Omit<Country, 'id'>>) => Promise<boolean>
  deleteCountry: (id: number) => Promise<boolean>

  // 产品操作
  addProduct: (data: Omit<Product, 'id'>) => Promise<boolean>
  updateProduct: (id: number, data: Partial<Omit<Product, 'id'>>) => Promise<boolean>
  deleteProduct: (id: number) => Promise<boolean>

  // 工具方法
  refreshData: () => Promise<void>
  clearError: () => void
}

export function useSalesTrainingData(): UseSalesTrainingDataReturn {
  // 数据状态
  const [countries, setCountries] = useState<Country[]>([])
  const [products, setProducts] = useState<Product[]>([])

  // 加载状态
  const [isLoading, setIsLoading] = useState(false)
  const [isInitialLoading, setIsInitialLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 错误处理
  const handleError = useCallback((error: unknown, defaultMessage: string) => {
    const message = error instanceof SalesTrainingError
      ? error.message
      : error instanceof Error
        ? error.message
        : defaultMessage

    setError(message)
    toast.error(message)
    console.error('Sales Training Data Error:', error)
  }, [])

  // 清除错误
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // 刷新所有数据
  const refreshData = useCallback(async () => {
    try {
      setIsLoading(true)
      clearError()

      const data = await salesTrainingService.batch.getAllData()

      setCountries(data.countries)
      setProducts(data.products)
    } catch (error) {
      handleError(error, '获取数据失败')
    } finally {
      setIsLoading(false)
      setIsInitialLoading(false)
    }
  }, [handleError, clearError])

  // 初始化数据
  useEffect(() => {
    refreshData()
  }, [refreshData])

  // 国家操作
  const addCountry = useCallback(async (data: Omit<Country, 'id'>): Promise<boolean> => {
    try {
      setIsLoading(true)
      clearError()

      const newCountry = await salesTrainingService.country.create(data)
      setCountries(prev => [...prev, newCountry])

      toast.success(`国家 "${newCountry.name}" 已添加`)
      return true
    } catch (error) {
      handleError(error, '添加国家失败')
      return false
    } finally {
      setIsLoading(false)
    }
  }, [handleError, clearError])

  const updateCountry = useCallback(async (id: number, data: Partial<Omit<Country, 'id'>>): Promise<boolean> => {
    try {
      setIsLoading(true)
      clearError()

      const updatedCountry = await salesTrainingService.country.update(id, data)
      setCountries(prev => prev.map(country =>
        country.id === id ? updatedCountry : country
      ))

      toast.success(`国家 "${updatedCountry.name}" 已更新`)
      return true
    } catch (error) {
      handleError(error, '更新国家失败')
      return false
    } finally {
      setIsLoading(false)
    }
  }, [handleError, clearError])

  const deleteCountry = useCallback(async (id: number): Promise<boolean> => {
    try {
      setIsLoading(true)
      clearError()

      const country = countries.find(c => c.id === id)
      if (!country) {
        throw new Error('国家不存在')
      }

      await salesTrainingService.country.delete(id)
      setCountries(prev => prev.filter(c => c.id !== id))

      toast.success(`国家 "${country.name}" 已删除`)
      return true
    } catch (error: any) {
      // 重新抛出错误，让上层组件处理
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [countries, clearError])

  // 产品操作
  const addProduct = useCallback(async (data: Omit<Product, 'id'>): Promise<boolean> => {
    try {
      setIsLoading(true)
      clearError()

      const newProduct = await salesTrainingService.product.create(data)
      setProducts(prev => [...prev, newProduct])

      toast.success(`产品 "${newProduct.name}" 已添加`)
      return true
    } catch (error) {
      handleError(error, '添加产品失败')
      return false
    } finally {
      setIsLoading(false)
    }
  }, [handleError, clearError])

  const updateProduct = useCallback(async (id: number, data: Partial<Omit<Product, 'id'>>): Promise<boolean> => {
    try {
      setIsLoading(true)
      clearError()

      const updatedProduct = await salesTrainingService.product.update(id, data)
      setProducts(prev => prev.map(product =>
        product.id === id ? updatedProduct : product
      ))

      toast.success(`产品 "${updatedProduct.name}" 已更新`)
      return true
    } catch (error) {
      handleError(error, '更新产品失败')
      return false
    } finally {
      setIsLoading(false)
    }
  }, [handleError, clearError])

  const deleteProduct = useCallback(async (id: number): Promise<boolean> => {
    try {
      setIsLoading(true)
      clearError()

      const product = products.find(p => p.id === id)
      if (!product) {
        throw new Error('产品不存在')
      }

      await salesTrainingService.product.delete(id)
      setProducts(prev => prev.filter(p => p.id !== id))

      toast.success(`产品 "${product.name}" 已删除`)
      return true
    } catch (error: any) {
      // 重新抛出错误，让上层组件处理
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [products, clearError])



  return {
    // 数据状态
    countries,
    products,

    // 加载状态
    isLoading,
    isInitialLoading,
    error,

    // 国家操作
    addCountry,
    updateCountry,
    deleteCountry,

    // 产品操作
    addProduct,
    updateProduct,
    deleteProduct,

    // 工具方法
    refreshData,
    clearError,
  }
}
