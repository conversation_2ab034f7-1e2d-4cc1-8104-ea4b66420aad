@tailwind base;
@tailwind components;
@tailwind utilities;

/* 销冠实战训练页面动画 */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

@keyframes typing-dots {
  0%, 20% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes score-increase {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
    color: #10b981;
  }
  100% {
    transform: scale(1);
  }
}

@keyframes achievement-unlock {
  0% {
    transform: scale(0.8) rotate(-10deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.1) rotate(5deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fade-in-left 0.5s ease-out;
}

.animate-fade-in-right {
  animation: fade-in-right 0.5s ease-out;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-typing-dots {
  animation: typing-dots 1.5s ease-in-out infinite;
}

.animate-score-increase {
  animation: score-increase 0.6s ease-out;
}

.animate-achievement-unlock {
  animation: achievement-unlock 0.8s ease-out;
}

/* 知识库主题样式 */
.knowledge-base-theme-root {
  --kb-transition: all 0.2s ease-in-out;
  --kb-border-radius: 0.5rem;
  --kb-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

.knowledge-base-container {
  transition: var(--kb-transition);
}

.knowledge-base-card {
  transition: var(--kb-transition);
  border-radius: var(--kb-border-radius);
  box-shadow: var(--kb-shadow);
}

.knowledge-base-card:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.compact-mode .knowledge-base-card {
  padding: var(--knowledge-base-spacing, 0.5rem);
}

.compact-mode .text-sm {
  font-size: var(--knowledge-base-text-sm, 0.75rem);
}

.compact-mode .text-base {
  font-size: var(--knowledge-base-text-base, 0.875rem);
}

/* 知识库专用动画 */
@keyframes kb-fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes kb-slide-in {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

.kb-fade-in {
  animation: kb-fade-in 0.3s ease-out;
}

.kb-slide-in {
  animation: kb-slide-in 0.3s ease-out;
}

/* 企业知识库专用样式 */
.enterprise-knowledge-container {
  transition: var(--kb-transition);
}

.enterprise-knowledge-card {
  transition: var(--kb-transition);
  border-radius: var(--kb-border-radius);
  box-shadow: var(--kb-shadow);
}

.enterprise-knowledge-card:hover {
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  transform: translateY(-1px);
}

.document-list-item {
  transition: all 0.2s ease-in-out;
}

.document-list-item:hover {
  background-color: hsl(var(--accent) / 0.5);
}

/* 文件类型图标样式 */
.file-type-icon {
  transition: transform 0.2s ease-in-out;
}

.file-type-icon:hover {
  transform: scale(1.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .knowledge-base-container {
    padding: 1rem;
  }

  .enterprise-knowledge-container {
    padding: 1rem;
  }

  .grid-cols-6 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  /* 移动端文档卡片优化 */
  .document-grid-mobile {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

@media (max-width: 640px) {
  .enterprise-knowledge-card {
    margin: 0.5rem 0;
  }

  .document-list-item {
    padding: 0.75rem;
  }
}

/* 增强版销冠实战训练样式 */
.enhanced-training-container {
  transition: all 0.3s ease-in-out;
}

.training-scenario-card {
  transition: all 0.3s ease-in-out;
  border-radius: 0.75rem;
  overflow: hidden;
}

.training-scenario-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.smart-chat-message {
  transition: all 0.2s ease-in-out;
}

.smart-chat-message:hover {
  transform: translateX(2px);
}

.evaluation-dimension-card {
  transition: all 0.3s ease-in-out;
  border-radius: 0.5rem;
}

.evaluation-dimension-card:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
}

.achievement-badge {
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}

.achievement-badge:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

.achievement-badge.unlocked {
  animation: achievement-unlock 0.8s ease-out;
}

.quick-reply-button {
  transition: all 0.2s ease-in-out;
  border-radius: 0.5rem;
}

.quick-reply-button:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  transform: translateX(4px);
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.typing-dot {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #9ca3af;
  border-radius: 50%;
  animation: typing-dots 1.5s ease-in-out infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

.score-progress-bar {
  transition: all 0.5s ease-in-out;
  border-radius: 0.25rem;
  overflow: hidden;
}

.score-progress-fill {
  transition: width 0.8s ease-in-out;
  background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
  background-size: 200% 100%;
  animation: shimmer 2s ease-in-out infinite;
}

.message-status-indicator {
  transition: all 0.2s ease-in-out;
}

.voice-input-button {
  transition: all 0.3s ease-in-out;
}

.voice-input-button.recording {
  animation: pulse-glow 1s ease-in-out infinite;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.scenario-difficulty-badge {
  position: relative;
  overflow: hidden;
}

.scenario-difficulty-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease-in-out;
}

.scenario-difficulty-badge:hover::before {
  left: 100%;
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .enhanced-training-container {
    padding: 1rem;
  }

  .training-scenario-card {
    margin-bottom: 1rem;
  }
}

@media (max-width: 768px) {
  .smart-chat-message {
    max-width: 90%;
  }

  .evaluation-dimension-card {
    padding: 0.75rem;
  }

  .achievement-badge {
    transform: scale(0.9);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Sidebar colors - Blue theme */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 221.2 83.2% 53.3%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 222.2 84% 4.9%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 221.2 83.2% 53.3%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Sidebar colors for dark mode - Blue theme */
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Markdown内容样式优化 */
.markdown-content {
  line-height: 1.7;
}

.markdown-content h1 {
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
}

.markdown-content h2 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #374151;
}

.dark .markdown-content h2 {
  color: #d1d5db;
}

.markdown-content h3 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: #4b5563;
}

.dark .markdown-content h3 {
  color: #9ca3af;
}

.markdown-content table {
  border-collapse: collapse;
  margin: 1.5rem 0;
  width: 100%;
}

.markdown-content blockquote {
  border-left: 4px solid #3b82f6;
  background-color: #eff6ff;
  padding: 1rem;
  margin: 1.5rem 0;
  border-radius: 0 0.5rem 0.5rem 0;
}

.dark .markdown-content blockquote {
  background-color: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

.markdown-content ul,
.markdown-content ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.markdown-content li {
  margin: 0.25rem 0;
}

.markdown-content code {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
}

.markdown-content p {
  margin: 1rem 0;
}

.markdown-content strong {
  font-weight: 600;
  color: #1f2937;
}

.dark .markdown-content strong {
  color: #f9fafb;
}

/* 增强训练对话框优化样式 */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* 对话框动画优化 */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out forwards;
}

/* 滚动条样式优化 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.8);
}

/* 对话框响应式优化 */
@media (max-width: 768px) {
  .dialog-mobile-padding {
    padding: 0.5rem;
  }
  
  .dialog-mobile-text {
    font-size: 0.875rem;
  }
}
