/**
 * 话术模板分析仪表板组件
 * 显示模板使用统计、性能分析和优化建议
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  TrendingUp,
  Users,
  Clock,
  Star,
  AlertTriangle,
  CheckCircle,
  Target,
  Download,
  RefreshCw
} from 'lucide-react';
import { templateService } from '@/services/templateService';
// import templateAnalytics from '@/services/templateAnalytics';
import { Template } from '@/types/template';

interface AnalyticsDashboardProps {
  className?: string;
}

const TemplateAnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  className = ''
}) => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const templateList = await templateService.getTemplates();
      setTemplates(templateList);
      if (templateList.length > 0 && !selectedTemplate) {
        setSelectedTemplate(templateList[0]);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExportData = () => {
    // const data = templateAnalytics.exportAnalyticsData();
    const data = JSON.stringify({ message: 'Analytics data export not implemented yet' });
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `template_analytics_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderOverviewTab = () => {
    // const allStats = templateAnalytics.getAllTemplateStats();
    // const rankings = templateAnalytics.getTemplateRanking();
    const allStats = {};
    const rankings = [];

    const totalUsage = 0; // Object.values(allStats).reduce((sum, stats) => sum + stats.usageCount, 0);
    const averageRating = 0; // Object.values(allStats).reduce((sum, stats) => sum + stats.userRating, 0) / Object.keys(allStats).length || 0;
    const totalErrors = 0; // Object.values(allStats).reduce((sum, stats) => sum + stats.errorCount, 0);
    const averageSuccessRate = 0; // Object.values(allStats).reduce((sum, stats) => sum + stats.successRate, 0) / Object.keys(allStats).length || 0;

    return (
      <div className="space-y-6">
        {/* 总体统计 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总使用次数</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalUsage}</div>
              <p className="text-xs text-muted-foreground">
                跨所有模板的总使用次数
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均评分</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{averageRating.toFixed(1)}</div>
              <p className="text-xs text-muted-foreground">
                用户满意度评分
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">成功率</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{(averageSuccessRate * 100).toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                平均执行成功率
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">错误总数</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalErrors}</div>
              <p className="text-xs text-muted-foreground">
                需要关注的错误数量
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 模板排行榜 */}
        <Card>
          <CardHeader>
            <CardTitle>模板排行榜</CardTitle>
            <CardDescription>基于使用频率、质量和用户满意度的综合排名</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {rankings.slice(0, 10).map((ranking) => {
                const template = templates.find(t => t.id === ranking.templateId);
                if (!template) return null;

                return (
                  <div key={ranking.templateId} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">#{ranking.rank}</Badge>
                      <div>
                        <div className="font-medium">{template.name}</div>
                        <div className="text-sm text-muted-foreground">{template.category}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{ranking.score.toFixed(1)}</div>
                      <div className="text-sm text-muted-foreground">综合分数</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderTemplateDetailsTab = () => {
    if (!selectedTemplate) {
      return <div className="text-center text-muted-foreground">请选择一个模板查看详细分析</div>;
    }

    // const stats = templateAnalytics.getTemplateStats(selectedTemplate.id);
    // const performance = templateAnalytics.analyzeTemplatePerformance(selectedTemplate);
    // const suggestions = templateAnalytics.generateOptimizationSuggestions(selectedTemplate);
    const stats = { usageCount: 0, successRate: 0, averageResponseTime: 0, userRating: 0, errorCount: 0 };
    const performance = {
      score: 0,
      strengths: [],
      weaknesses: [],
      responseQuality: 0,
      userSatisfaction: 0,
      engagementScore: 0,
      consistencyScore: 0
    };
    const suggestions = [];

    return (
      <div className="space-y-6">
        {/* 模板选择器 */}
        <Card>
          <CardHeader>
            <CardTitle>选择模板</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
              {templates.map((template) => (
                <Button
                  key={template.id}
                  variant={selectedTemplate?.id === template.id ? "default" : "outline"}
                  onClick={() => setSelectedTemplate(template)}
                  className="justify-start"
                >
                  {template.name}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {stats ? (
          <>
            {/* 性能指标 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>使用统计</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>使用次数:</span>
                    <span className="font-medium">{stats.usageCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>成功率:</span>
                    <span className="font-medium">{(stats.successRate * 100).toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>平均响应时间:</span>
                    <span className="font-medium">{(stats.averageResponseTime / 1000).toFixed(1)}s</span>
                  </div>
                  <div className="flex justify-between">
                    <span>用户评分:</span>
                    <span className="font-medium">{stats.userRating.toFixed(1)}/5</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>性能分析</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>响应质量</span>
                      <span>{performance.responseQuality.toFixed(1)}</span>
                    </div>
                    <Progress value={performance.responseQuality} />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>用户满意度</span>
                      <span>{performance.userSatisfaction.toFixed(1)}</span>
                    </div>
                    <Progress value={performance.userSatisfaction} />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>参与度</span>
                      <span>{performance.engagementScore.toFixed(1)}</span>
                    </div>
                    <Progress value={performance.engagementScore} />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>一致性</span>
                      <span>{performance.consistencyScore.toFixed(1)}</span>
                    </div>
                    <Progress value={performance.consistencyScore} />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 优化建议 */}
            <Card>
              <CardHeader>
                <CardTitle>优化建议</CardTitle>
                <CardDescription>基于使用数据生成的改进建议</CardDescription>
              </CardHeader>
              <CardContent>
                {suggestions.length > 0 ? (
                  <div className="space-y-4">
                    {suggestions.map((suggestion, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Badge variant={
                              suggestion.priority === 'high' ? 'destructive' :
                              suggestion.priority === 'medium' ? 'default' : 'secondary'
                            }>
                              {suggestion.priority === 'high' ? '高优先级' :
                               suggestion.priority === 'medium' ? '中优先级' : '低优先级'}
                            </Badge>
                            <span className="font-medium">{suggestion.title}</span>
                          </div>
                          <Target className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{suggestion.description}</p>
                        <p className="text-sm mb-2">{suggestion.suggestion}</p>
                        <p className="text-sm text-green-600">预期改进: {suggestion.expectedImprovement}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground">
                    <CheckCircle className="h-12 w-12 mx-auto mb-2 text-green-500" />
                    <p>该模板表现良好，暂无优化建议</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </>
        ) : (
          <Card>
            <CardContent className="text-center py-8">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">该模板尚未有使用数据</p>
              <p className="text-sm text-muted-foreground mt-2">激活并使用模板后将显示详细分析</p>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        <span>加载分析数据...</span>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">模板分析仪表板</h2>
          <p className="text-muted-foreground">监控模板性能并获取优化建议</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新数据
          </Button>
          <Button variant="outline" onClick={handleExportData}>
            <Download className="h-4 w-4 mr-2" />
            导出数据
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="details">详细分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          {renderOverviewTab()}
        </TabsContent>

        <TabsContent value="details">
          {renderTemplateDetailsTab()}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TemplateAnalyticsDashboard;
