#!/usr/bin/env python3
"""
SmoLAgents 微服务基本使用示例
演示如何使用 SmoLAgents 微服务的各种功能
"""

import asyncio
import json
import httpx
import subprocess
import sys
from typing import Dict, Any


class SmoLAgentsClient:
    """SmoLAgents 客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8002"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = httpx.AsyncClient(timeout=120.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.aclose()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        response = await self.session.get(f"{self.base_url}/api/v1/health")
        response.raise_for_status()
        return response.json()
    
    async def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        response = await self.session.get(f"{self.base_url}/api/v1/info")
        response.raise_for_status()
        return response.json()
    
    async def query_agent(
        self, 
        query: str, 
        max_steps: int = 10,
        use_cache: bool = True,
        enable_web_search: bool = True,
        timeout: int = 60
    ) -> Dict[str, Any]:
        """查询智能代理"""
        data = {
            "query": query,
            "max_steps": max_steps,
            "use_cache": use_cache,
            "enable_web_search": enable_web_search,
            "timeout": timeout
        }
        
        response = await self.session.post(f"{self.base_url}/api/v1/agent/query", json=data)
        response.raise_for_status()
        return response.json()
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        response = await self.session.get(f"{self.base_url}/api/v1/agent/task/{task_id}")
        response.raise_for_status()
        return response.json()
    
    async def execute_code(self, code: str, language: str = "python", timeout: int = 30) -> Dict[str, Any]:
        """执行代码"""
        data = {
            "code": code,
            "language": language,
            "timeout": timeout
        }
        
        response = await self.session.post(f"{self.base_url}/api/v1/agent/execute", json=data)
        response.raise_for_status()
        return response.json()
    
    async def web_search(self, query: str, max_results: int = 10, timeout: int = 30) -> Dict[str, Any]:
        """网络搜索"""
        data = {
            "query": query,
            "max_results": max_results,
            "timeout": timeout
        }
        
        response = await self.session.post(f"{self.base_url}/api/v1/agent/search", json=data)
        response.raise_for_status()
        return response.json()


async def demo_basic_queries():
    """演示基本查询功能"""
    print("🤖 演示基本查询功能")
    print("-" * 50)

    async with SmoLAgentsClient() as client:
        # 数学计算
        print("1. 数学计算")
        try:
            result = await client.query_agent("计算 123 * 456 的结果")
            if result is not None:
                print(f"查询: 计算 123 * 456 的结果")
                result_text = result.get('result', 'N/A')
                if result_text and result_text != 'N/A':
                    print(f"结果: {str(result_text)[:200]}...")
                else:
                    print(f"结果: 无结果返回")
                print(f"成功: {result.get('success', False)}")
            else:
                print("❌ 查询返回空结果")
        except Exception as e:
            print(f"❌ 查询失败: {e}")
        print()

        # Python 编程
        print("2. Python 编程")
        try:
            result = await client.query_agent("用Python写一个函数计算阶乘")
            if result is not None:
                print(f"查询: 用Python写一个函数计算阶乘")
                result_text = result.get('result', 'N/A')
                if result_text and result_text != 'N/A':
                    print(f"结果: {str(result_text)[:200]}...")
                else:
                    print(f"结果: 无结果返回")
                print(f"成功: {result.get('success', False)}")
            else:
                print("❌ 查询返回空结果")
        except Exception as e:
            print(f"❌ 查询失败: {e}")
        print()

        # 数据分析
        print("3. 数据分析")
        try:
            result = await client.query_agent("创建一个包含10个随机数的列表，并计算平均值")
            if result is not None:
                print(f"查询: 创建一个包含10个随机数的列表，并计算平均值")
                result_text = result.get('result', 'N/A')
                if result_text and result_text != 'N/A':
                    print(f"结果: {str(result_text)[:200]}...")
                else:
                    print(f"结果: 无结果返回")
                print(f"成功: {result.get('success', False)}")
            else:
                print("❌ 查询返回空结果")
        except Exception as e:
            print(f"❌ 查询失败: {e}")
        print()


async def demo_code_execution():
    """演示代码执行功能"""
    print("💻 演示代码执行功能")
    print("-" * 50)
    
    async with SmoLAgentsClient() as client:
        # 简单计算
        code1 = """
import math

def calculate_circle_area(radius):
    return math.pi * radius ** 2

radius = 5
area = calculate_circle_area(radius)
print(f"半径为 {radius} 的圆的面积是: {area:.2f}")
"""
        
        print("1. 圆面积计算")
        try:
            result = await client.execute_code(code1)
            if result is not None:
                print(f"代码执行成功: {result.get('success', False)}")
                output = result.get('output', 'N/A')
                if output and output != 'N/A':
                    print(f"输出: {output}")
                else:
                    print("输出: 无输出结果")
            else:
                print("❌ 代码执行返回空结果")
        except Exception as e:
            print(f"❌ 代码执行失败: {e}")
        print()

        # 数据处理
        code2 = """
import json

data = [
    {"name": "Alice", "age": 25, "city": "Beijing"},
    {"name": "Bob", "age": 30, "city": "Shanghai"},
    {"name": "Charlie", "age": 35, "city": "Guangzhou"}
]

# 计算平均年龄
avg_age = sum(person["age"] for person in data) / len(data)
print(f"平均年龄: {avg_age:.1f}")

# 按城市分组
cities = {}
for person in data:
    city = person["city"]
    if city not in cities:
        cities[city] = []
    cities[city].append(person["name"])

print("按城市分组:")
for city, names in cities.items():
    print(f"  {city}: {', '.join(names)}")
"""

        print("2. 数据处理")
        try:
            result = await client.execute_code(code2)
            if result is not None:
                print(f"代码执行成功: {result.get('success', False)}")
                output = result.get('output', 'N/A')
                if output and output != 'N/A':
                    print(f"输出: {output}")
                else:
                    print("输出: 无输出结果")
            else:
                print("❌ 代码执行返回空结果")
        except Exception as e:
            print(f"❌ 代码执行失败: {e}")
        print()


async def demo_advanced_features():
    """演示高级功能"""
    print("🚀 演示高级功能")
    print("-" * 50)
    
    async with SmoLAgentsClient() as client:
        # 复杂问题解决
        print("1. 复杂问题解决")
        query = """
        我有一个包含学生成绩的数据集，需要：
        1. 计算每个学生的总分和平均分
        2. 找出成绩最高和最低的学生
        3. 生成成绩分布统计
        
        数据格式：[{"name": "张三", "math": 85, "english": 92, "science": 78}, ...]
        请用Python实现完整的分析过程。
        """
        
        try:
            result = await client.query_agent(query, max_steps=15)
            if result is not None:
                print(f"查询: 复杂数据分析任务")
                result_text = result.get('result', 'N/A')
                if result_text and result_text != 'N/A':
                    print(f"结果: {str(result_text)[:300]}...")
                else:
                    print("结果: 无结果返回")
                print(f"成功: {result.get('success', False)}")
                print(f"执行步骤: {result.get('steps_taken', 'N/A')}")
            else:
                print("❌ 复杂查询返回空结果")
        except Exception as e:
            print(f"❌ 复杂查询失败: {e}")
        print()

        # 算法实现
        print("2. 算法实现")
        query = """
        实现一个快速排序算法，包括：
        1. 递归版本的快速排序函数
        2. 测试用例验证正确性
        3. 性能分析和时间复杂度说明
        """

        try:
            result = await client.query_agent(query, max_steps=10)
            if result is not None:
                print(f"查询: 快速排序算法实现")
                result_text = result.get('result', 'N/A')
                if result_text and result_text != 'N/A':
                    print(f"结果: {str(result_text)[:300]}...")
                else:
                    print("结果: 无结果返回")
                print(f"成功: {result.get('success', False)}")
            else:
                print("❌ 算法实现查询返回空结果")
        except Exception as e:
            print(f"❌ 算法实现查询失败: {e}")
        print()


async def demo_service_monitoring():
    """演示服务监控功能"""
    print("📊 演示服务监控功能")
    print("-" * 50)

    async with SmoLAgentsClient() as client:
        # 健康检查
        print("1. 健康检查")
        try:
            health = await client.health_check()
            if health is not None:
                print(f"服务状态: {health.get('service', 'unknown')}")
                print(f"模型状态: {health.get('model', 'unknown')}")
                print(f"工具状态: {health.get('tools', 'unknown')}")
                print(f"Redis状态: {health.get('redis', 'unknown')}")
            else:
                print("❌ 健康检查返回空结果")
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
        print()

        # 服务信息
        print("2. 服务信息")
        try:
            info = await client.get_service_info()
            if info is not None:
                print(f"服务名称: {info.get('name', 'N/A')}")
                print(f"版本: {info.get('version', 'N/A')}")
                print(f"状态: {info.get('status', 'N/A')}")
                features = info.get('features', [])
                if features:
                    print(f"功能: {', '.join(features)}")
                else:
                    print("功能: 无功能信息")
            else:
                print("❌ 服务信息返回空结果")
        except Exception as e:
            print(f"❌ 获取服务信息失败: {e}")
        print()


async def diagnose_service():
    """诊断服务状态"""
    print("🔍 SmoLAgents 服务诊断")
    print("=" * 50)

    # 1. 检查Docker容器状态
    print("1. 检查Docker容器状态...")
    try:
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=smolagents", "--format", "table {{.Names}}\t{{.Status}}\t{{.Ports}}"],
            capture_output=True, text=True, timeout=10
        )
        if result.returncode == 0:
            print(result.stdout)
        else:
            print("❌ 无法获取Docker容器状态")
    except Exception as e:
        print(f"❌ Docker命令执行失败: {e}")

    # 2. 检查端口占用
    print("\n2. 检查端口8002占用情况...")
    try:
        result = subprocess.run(
            ["netstat", "-an"], capture_output=True, text=True, timeout=10
        )
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            port_8002_lines = [line for line in lines if ':8002' in line]
            if port_8002_lines:
                for line in port_8002_lines:
                    print(f"  {line.strip()}")
            else:
                print("❌ 端口8002未被占用")
        else:
            print("❌ 无法检查端口状态")
    except Exception as e:
        print(f"❌ 端口检查失败: {e}")

    # 3. 尝试连接服务
    print("\n3. 尝试连接服务...")
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get("http://localhost:8002/api/v1/health")
            if response.status_code == 200:
                print("✅ 服务连接成功")
                print(f"响应: {response.json()}")
            else:
                print(f"❌ 服务响应异常: {response.status_code}")
    except httpx.ConnectError:
        print("❌ 无法连接到服务 (连接被拒绝)")
    except httpx.TimeoutException:
        print("❌ 连接超时")
    except Exception as e:
        print(f"❌ 连接失败: {e}")

    print("\n" + "=" * 50)
    print("🔧 建议的修复步骤:")
    print("1. 启动服务: docker-compose up -d zht_smolagents_0624")
    print("2. 查看日志: docker logs zht_smolagents_0624")
    print("3. 重启服务: docker-compose restart zht_smolagents_0624")
    print("4. 检查配置: docker-compose config")


async def main():
    """主演示函数"""
    print("🎯 SmoLAgents 微服务使用示例")
    print("=" * 60)
    print()

    try:
        # 检查服务可用性
        print("🔍 正在检查服务连接...")
        async with SmoLAgentsClient() as client:
            health_result = await client.health_check()
            print("✅ 服务连接成功")
            print(f"📊 健康检查结果: {health_result}")
        print()

        # 运行演示
        print("🚀 开始运行演示...")

        try:
            await demo_service_monitoring()
        except Exception as e:
            print(f"❌ 服务监控演示失败: {e}")
            print("继续执行其他演示...")

        try:
            await demo_basic_queries()
            return 
        
        except Exception as e:
            print(f"❌ 基本查询演示失败: {e}")
            print("继续执行其他演示...")
            

        try:
            await demo_code_execution()
        except Exception as e:
            print(f"❌ 代码执行演示失败: {e}")
            print("继续执行其他演示...")

        try:
            await demo_advanced_features()
        except Exception as e:
            print(f"❌ 高级功能演示失败: {e}")

        print("🎉 演示完成！")

    except httpx.ConnectError as e:
        print("❌ 无法连接到 SmoLAgents 服务")
        print(f"连接错误详情: {e}")
        print()
        print("� 正在运行服务诊断...")
        await diagnose_service()
    except httpx.HTTPStatusError as e:
        print(f"❌ HTTP 错误: {e.response.status_code}")
        print(f"响应内容: {e.response.text}")
        print(f"请求URL: {e.request.url}")
    except httpx.TimeoutException as e:
        print(f"❌ 请求超时: {e}")
        print("服务可能正在启动中，请稍后重试")
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
