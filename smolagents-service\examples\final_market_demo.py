#!/usr/bin/env python3
"""
最终版智能市场分析演示系统
基于 SmoLAgents 网络搜索 + 直接模型分析
避免 CodeAgent 的复杂代码执行问题
"""

import asyncio
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
import httpx
from dataclasses import dataclass


@dataclass
class AnalysisContext:
    """分析上下文"""
    product: str
    region: str
    time_period: str
    analysis_type: str


class FinalMarketAnalysis:
    """最终版智能市场分析系统"""
    
    def __init__(self, base_url: str = "http://localhost:8002"):
        self.base_url = base_url
        self.session: Optional[httpx.AsyncClient] = None
        
    async def __aenter__(self):
        self.session = httpx.AsyncClient(timeout=120.0)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.aclose()
    
    def parse_search_term(self, search_term: str) -> AnalysisContext:
        """智能解析搜索词"""
        print(f"🔍 解析搜索词: {search_term}")
        
        # 简化的解析逻辑
        product_match = re.search(r'([^0-9\s]+?)(?=\s*20\d{2}|\s*市场)', search_term)
        product = product_match.group(1).strip() if product_match else "未知产品"
        
        region = "全球"
        if "亚洲" in search_term: region = "亚洲"
        elif "中国" in search_term: region = "中国"
        elif "美国" in search_term: region = "美国"
        elif "欧洲" in search_term: region = "欧洲"
        
        time_match = re.search(r'20\d{2}', search_term)
        time_period = time_match.group(0) if time_match else "2024"
        
        analysis_type = "综合分析"
        if "销售" in search_term: analysis_type = "销售分析"
        elif "竞争" in search_term: analysis_type = "竞争分析"
        elif "趋势" in search_term: analysis_type = "趋势分析"
        
        context = AnalysisContext(product, region, time_period, analysis_type)
        
        print(f"✅ 解析结果: {context.product} | {context.region} | {context.time_period} | {context.analysis_type}")
        return context
    
    async def search_market_data(self, context: AnalysisContext) -> List[str]:
        """搜索市场数据"""
        print("🌐 开始搜索市场数据...")
        
        search_queries = [
            f"{context.product} {context.region} market size {context.time_period}",
            f"{context.product} sales trends {context.region} {context.time_period}",
            f"{context.product} market forecast 2025 {context.region}"
        ]
        
        search_results = []
        
        for i, query in enumerate(search_queries, 1):
            print(f"   [{i}/{len(search_queries)}] 搜索: {query}")
            
            try:
                # 使用简单的搜索提示词
                search_prompt = f"""
请使用网络搜索工具查找关于"{query}"的市场信息。

请提供简洁的搜索结果，重点关注：
- 市场数据和统计信息
- 关键趋势和洞察
- 相关的数字和事实

保持结果简洁明了。
"""
                
                data = {
                    "query": search_prompt,
                    "max_steps": 8,
                    "use_cache": False,
                    "enable_web_search": True,
                    "timeout": 90
                }
                
                response = await self.session.post(f"{self.base_url}/api/v1/agent/query", json=data)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        search_result = result.get('result', '')
                        if search_result:
                            search_results.append(search_result)
                            print(f"      ✅ 成功")
                        else:
                            print(f"      ⚠️  结果为空")
                    else:
                        print(f"      ❌ 失败: {result.get('error', '未知错误')}")
                else:
                    print(f"      ❌ HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"      ❌ 异常: {e}")
            
            # 添加延迟
            if i < len(search_queries):
                await asyncio.sleep(2)
        
        print(f"✅ 搜索完成，获取 {len(search_results)} 个有效结果")
        return search_results
    
    async def generate_simple_analysis(self, context: AnalysisContext, search_data: List[str]) -> str:
        """生成简单分析报告（避免 CodeAgent 复杂性）"""
        print("📊 生成市场分析报告...")
        
        # 合并搜索数据
        combined_data = "\n\n".join(search_data[:3])  # 限制数据长度
        
        # 构建简单的分析提示词
        analysis_prompt = f"""
请基于以下搜索数据，为"{context.product}"在{context.region}市场生成一份简洁的市场分析报告：

搜索数据：
{combined_data[:2000]}

请生成一份包含以下内容的报告：

# {context.product} - {context.region}市场分析报告

## 1. 市场现状
- 市场规模和增长情况
- 主要参与者

## 2. 关键趋势
- 增长趋势
- 发展方向

## 3. 未来预测
- 2025年展望
- 增长驱动因素

## 4. 结论建议
- 核心洞察
- 战略建议

要求：
- 基于提供的真实数据
- 保持简洁明了
- 适合商业决策参考
- 控制在500字以内

请直接生成分析报告，不需要代码。
"""
        
        try:
            print(f"   提示词长度: {len(analysis_prompt)} 字符")
            
            data = {
                "query": analysis_prompt,
                "max_steps": 5,  # 减少步骤数
                "use_cache": False,
                "enable_web_search": False,  # 不需要额外搜索
                "timeout": 60  # 减少超时时间
            }
            
            response = await self.session.post(f"{self.base_url}/api/v1/agent/query", json=data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    analysis_report = result.get('result', '')
                    if analysis_report:
                        print("✅ 分析报告生成完成")
                        return analysis_report
                    else:
                        return "分析报告生成失败: 返回结果为空"
                else:
                    error_msg = result.get('error', '未知错误')
                    return f"分析报告生成失败: {error_msg}"
            else:
                return f"分析报告生成失败: HTTP {response.status_code}"
                
        except Exception as e:
            return f"分析报告生成失败: {str(e)}"
    
    async def run_analysis(self, search_term: str) -> Dict[str, Any]:
        """运行完整分析流程"""
        start_time = datetime.now()
        print("🚀 启动最终版市场分析系统")
        print("=" * 60)
        print(f"搜索词: {search_term}")
        print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        print()
        
        try:
            # 1. 解析搜索词
            context = self.parse_search_term(search_term)
            
            # 2. 搜索市场数据
            search_results = await self.search_market_data(context)
            
            if not search_results:
                return {
                    'success': False,
                    'error': '未能获取有效的市场数据',
                    'context': context.__dict__
                }
            
            # 3. 生成分析报告
            analysis_report = await self.generate_simple_analysis(context, search_results)
            
            # 计算执行时间
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            print("=" * 60)
            print("🎉 分析完成!")
            print(f"执行时间: {execution_time:.2f} 秒")
            print(f"数据源数量: {len(search_results)}")
            print("=" * 60)
            
            return {
                'success': True,
                'context': context.__dict__,
                'search_results': search_results,
                'analysis_report': analysis_report,
                'execution_time': execution_time,
                'data_sources_count': len(search_results),
                'timestamp': end_time.isoformat()
            }
            
        except Exception as e:
            error_msg = f"分析执行失败: {e}"
            print(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'context': context.__dict__ if 'context' in locals() else None
            }


async def main():
    """主演示函数"""
    search_term = "沙滩包2024亚洲市场销售情况分析及2025市场增长预期"
    
    print("🌟 最终版智能市场分析演示")
    print("基于 SmoLAgents 网络搜索 + 直接模型分析")
    print("=" * 60)
    print()
    
    async with FinalMarketAnalysis() as analyzer:
        result = await analyzer.run_analysis(search_term)
        
        if result['success']:
            print("\n" + "=" * 60)
            print("📊 市场分析报告")
            print("=" * 60)
            print(result['analysis_report'])
            print("\n" + "=" * 60)
            print("📈 分析统计")
            print(f"数据源数量: {result['data_sources_count']}")
            print(f"执行时间: {result['execution_time']:.2f} 秒")
            print(f"完成时间: {result['timestamp']}")
        else:
            print(f"\n❌ 分析失败: {result['error']}")


if __name__ == "__main__":
    asyncio.run(main())
