/**
 * 用户权限查看对话框组件
 */
import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, Shield, Search, User, Mail, Calendar, Settings } from 'lucide-react'
import { useAuthStore } from '@/store/auth'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface Permission {
  id: number
  name: string
  description: string
  category: string
  created_at: string
}

interface Role {
  id: number
  name: string
  description: string
  is_active: boolean
  created_at: string
  permissions: Permission[]
}

interface UserPermissionData {
  user_id: number
  roles: Role[]
  permissions: Permission[]
}

interface AdminUser {
  id: number
  username: string
  email: string
  full_name?: string
  phone?: string
  is_active: boolean
  is_superuser: boolean
  created_at: string
  roles: string[]
}

interface UserPermissionsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: AdminUser | null
  onManageRoles?: (user: AdminUser) => void
}

export const UserPermissionsDialog: React.FC<UserPermissionsDialogProps> = ({
  open,
  onOpenChange,
  user,
  onManageRoles,
}) => {
  const { token } = useAuthStore()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [permissionData, setPermissionData] = useState<UserPermissionData | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  // 获取用户权限数据
  const fetchUserPermissions = async () => {
    if (!token || !user) return

    setIsLoading(true)
    setError('')

    try {
      const response = await fetch(`${API_BASE_URL}/admin/users/${user.id}/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data: UserPermissionData = await response.json()
        setPermissionData(data)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '获取用户权限失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (open && user) {
      fetchUserPermissions()
    }
  }, [open, user, token])

  // 获取用户姓名首字母
  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // 按分类分组权限
  const groupPermissionsByCategory = (permissions: Permission[]) => {
    const filtered = permissions.filter(permission =>
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.category.toLowerCase().includes(searchTerm.toLowerCase())
    )

    return filtered.reduce((groups, permission) => {
      const category = permission.category || '其他'
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push(permission)
      return groups
    }, {} as Record<string, Permission[]>)
  }

  const handleClose = () => {
    setSearchTerm('')
    setError('')
    setPermissionData(null)
    onOpenChange(false)
  }

  const handleManageRoles = () => {
    if (user && onManageRoles) {
      onManageRoles(user)
      handleClose()
    }
  }

  if (!user) {
    return null
  }

  const displayName = user.full_name || user.username
  const initials = getInitials(displayName)
  const groupedPermissions = permissionData ? groupPermissionsByCategory(permissionData.permissions) : {}

  return (
    <Dialog open={open} onOpenChange={handleClose} modal={true}>
      <DialogContent
        className="sm:max-w-[800px] max-h-[80vh] overflow-hidden flex flex-col"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>用户权限详情</span>
          </DialogTitle>
          <DialogDescription>
            查看用户 "{user.username}" 的角色和权限信息
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">加载权限数据...</span>
            </div>
          ) : permissionData ? (
            <>
              {/* 用户基本信息 */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">用户信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start space-x-4">
                    <Avatar className="h-16 w-16">
                      <AvatarFallback className="text-lg">{initials}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-semibold">{displayName}</h3>
                        <Badge variant={user.is_active ? "default" : "secondary"}>
                          {user.is_active ? "正常" : "禁用"}
                        </Badge>
                        {user.is_superuser && (
                          <Badge variant="destructive">
                            <Shield className="h-3 w-3 mr-1" />
                            超级用户
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <User className="h-4 w-4" />
                          <span>{user.username}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Mail className="h-4 w-4" />
                          <span>{user.email}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>注册于 {new Date(user.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 用户角色 */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">分配的角色</CardTitle>
                    {onManageRoles && (
                      <Button variant="outline" size="sm" onClick={handleManageRoles}>
                        <Settings className="h-4 w-4 mr-1" />
                        管理角色
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {permissionData.roles.length > 0 ? (
                    <div className="space-y-3">
                      {permissionData.roles.map((role) => (
                        <div key={role.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <div className="flex items-center space-x-2">
                              <Badge variant={role.is_active ? "default" : "secondary"}>
                                {role.name}
                              </Badge>
                              {!role.is_active && (
                                <span className="text-xs text-muted-foreground">(已禁用)</span>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mt-1">
                              {role.description}
                            </p>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {role.permissions.length} 个权限
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      未分配任何角色
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 权限搜索 */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索权限..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* 用户权限 */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">拥有的权限</CardTitle>
                  <CardDescription>
                    通过角色获得的所有权限 ({permissionData.permissions.length} 个)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {Object.keys(groupedPermissions).length > 0 ? (
                    <div className="space-y-4">
                      {Object.entries(groupedPermissions).map(([category, permissions]) => (
                        <div key={category}>
                          <h4 className="font-medium text-sm mb-2 text-muted-foreground">
                            {category} ({permissions.length})
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {permissions.map((permission) => (
                              <div key={permission.id} className="p-2 border rounded text-sm">
                                <div className="font-mono text-xs bg-muted px-1 py-0.5 rounded mb-1">
                                  {permission.name}
                                </div>
                                <div className="text-muted-foreground text-xs">
                                  {permission.description}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : searchTerm ? (
                    <div className="text-center py-4 text-muted-foreground">
                      未找到匹配的权限
                    </div>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      用户暂无任何权限
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          ) : null}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
