"""
RBAC权限控制Pydantic模式
定义角色、权限相关API请求和响应的数据结构
"""
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List
from datetime import datetime


# 权限相关Schema
class PermissionBase(BaseModel):
    """权限基础模式"""
    name: str = Field(..., max_length=100, description="权限名称")
    description: Optional[str] = Field(None, description="权限描述")
    category: Optional[str] = Field(None, max_length=50, description="权限分类")


class PermissionCreate(PermissionBase):
    """创建权限的请求模式"""
    pass


class PermissionUpdate(BaseModel):
    """更新权限的请求模式"""
    name: Optional[str] = Field(None, max_length=100, description="权限名称")
    description: Optional[str] = Field(None, description="权限描述")
    category: Optional[str] = Field(None, max_length=50, description="权限分类")


class Permission(PermissionBase):
    """权限响应模式"""
    id: int = Field(..., description="权限ID")
    created_at: datetime = Field(..., description="创建时间")
    
    model_config = ConfigDict(from_attributes=True)


# 角色相关Schema
class RoleBase(BaseModel):
    """角色基础模式"""
    name: str = Field(..., max_length=50, description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    is_active: bool = Field(default=True, description="是否激活")


class RoleCreate(RoleBase):
    """创建角色的请求模式"""
    permission_ids: Optional[List[int]] = Field(default=[], description="权限ID列表")


class RoleUpdate(BaseModel):
    """更新角色的请求模式"""
    name: Optional[str] = Field(None, max_length=50, description="角色名称")
    description: Optional[str] = Field(None, description="角色描述")
    is_active: Optional[bool] = Field(None, description="是否激活")
    permission_ids: Optional[List[int]] = Field(None, description="权限ID列表")


class Role(RoleBase):
    """角色响应模式"""
    id: int = Field(..., description="角色ID")
    created_at: datetime = Field(..., description="创建时间")
    permissions: List[Permission] = Field(default=[], description="角色权限列表")
    
    model_config = ConfigDict(from_attributes=True)


# 用户角色相关Schema
class UserRoleAssign(BaseModel):
    """分配用户角色的请求模式"""
    role_ids: List[int] = Field(..., description="角色ID列表")


class UserRoleResponse(BaseModel):
    """用户角色响应模式"""
    id: int = Field(..., description="关联ID")
    user_id: int = Field(..., description="用户ID")
    role_id: int = Field(..., description="角色ID")
    role_name: str = Field(..., description="角色名称")
    assigned_at: datetime = Field(..., description="分配时间")
    assigned_by: Optional[int] = Field(None, description="分配者ID")


class UserPermissionResponse(BaseModel):
    """用户权限响应模式"""
    user_id: int = Field(..., description="用户ID")
    roles: List[Role] = Field(..., description="用户角色列表")
    permissions: List[Permission] = Field(..., description="用户权限列表")


# 角色权限相关Schema
class RolePermissionAssign(BaseModel):
    """分配角色权限的请求模式"""
    permission_ids: List[int] = Field(..., description="权限ID列表")


# 管理员用户列表Schema
class AdminUserResponse(BaseModel):
    """管理员用户列表响应模式"""
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: str = Field(..., description="邮箱")
    full_name: Optional[str] = Field(None, description="全名")
    is_active: bool = Field(..., description="是否激活")
    is_superuser: bool = Field(..., description="是否超级用户")
    created_at: datetime = Field(..., description="创建时间")
    roles: List[str] = Field(default=[], description="角色名称列表")
    
    model_config = ConfigDict(from_attributes=True)


# 权限检查响应Schema
class PermissionCheckResponse(BaseModel):
    """权限检查响应模式"""
    has_permission: bool = Field(..., description="是否有权限")
    user_id: int = Field(..., description="用户ID")
    permission_name: str = Field(..., description="权限名称")
    roles: List[str] = Field(..., description="用户角色列表")
