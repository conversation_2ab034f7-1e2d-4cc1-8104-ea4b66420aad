"""
密码重置令牌数据模型
定义密码重置令牌的数据库表结构
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class PasswordResetToken(Base):
    """
    密码重置令牌数据模型
    用于存储密码重置的临时令牌
    """
    __tablename__ = "password_reset_tokens"
    
    # 主键ID
    id = Column(Integer, primary_key=True, index=True, comment="令牌ID")
    
    # 用户ID（外键）
    user_id = Column(
        Integer, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False, 
        index=True,
        comment="用户ID"
    )
    
    # 重置令牌
    token = Column(
        String(255), 
        unique=True, 
        nullable=False, 
        index=True,
        comment="重置令牌"
    )
    
    # 过期时间
    expires_at = Column(
        DateTime(timezone=True), 
        nullable=False,
        comment="过期时间"
    )
    
    # 是否已使用
    used = Column(Boolean, default=False, comment="是否已使用")
    
    # 创建时间
    created_at = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        comment="创建时间"
    )
    
    # 关联用户
    user = relationship("User", back_populates="password_reset_tokens")
    
    def __repr__(self):
        return f"<PasswordResetToken(id={self.id}, user_id={self.user_id}, used={self.used})>"
