"""
API 数据模型定义
定义请求和响应的 Pydantic 模型
"""

from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any, Union

from pydantic import BaseModel, Field, validator


def get_default_max_steps_for_agent() -> int:
    """获取AgentRequest的默认max_steps值"""
    try:
        from app.core.config_validator import get_task_config, TaskType
        config = get_task_config(TaskType.AGENT_DEFAULT)
        return config.max_steps
    except ImportError:
        # 如果导入失败，使用硬编码默认值
        return 8


def get_default_max_steps_for_market_analysis() -> int:
    """获取MarketAnalysisRequest的默认max_steps值"""
    try:
        from app.core.config_validator import get_task_config, TaskType
        config = get_task_config(TaskType.MARKET_ANALYSIS)
        return config.max_steps
    except ImportError:
        # 如果导入失败，使用硬编码默认值
        return 12


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    NOT_FOUND = "not_found"


class AgentRequest(BaseModel):
    """智能代理请求模型"""
    query: str = Field(..., description="用户查询内容", min_length=1, max_length=10000)
    task_id: Optional[str] = Field(None, description="任务ID，用于跟踪任务状态")
    max_steps: Optional[int] = Field(default_factory=get_default_max_steps_for_agent, description="最大执行步骤数", ge=1, le=50)
    use_cache: bool = Field(True, description="是否使用缓存")
    enable_web_search: bool = Field(True, description="是否启用网络搜索")
    timeout: Optional[int] = Field(120, description="超时时间（秒）", ge=10, le=1800)  # 最大30分钟
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")
    
    @validator('query')
    def validate_query(cls, v):
        """验证查询内容"""
        if not v.strip():
            raise ValueError('查询内容不能为空')
        return v.strip()

    @validator('max_steps')
    def validate_max_steps(cls, v):
        """验证最大步骤数的合理性"""
        if v is None:
            return v

        # 根据不同场景建议不同的步骤数
        if v > 20:
            import warnings
            warnings.warn(
                f"max_steps={v} 可能导致执行时间过长。"
                f"建议: 简单查询使用3-5步，复杂分析使用8-12步。"
            )

        return v

    @validator('timeout')
    def validate_timeout(cls, v, values):
        """验证超时时间与步骤数的匹配性"""
        if v is None:
            return v

        max_steps = values.get('max_steps', 8)

        # 估算最小需要时间：步骤数 * 每步平均时间
        estimated_min_time = max_steps * 3  # 每步约3秒

        if v < estimated_min_time:
            import warnings
            warnings.warn(
                f"timeout={v}秒 可能不足以完成 {max_steps} 步执行。"
                f"建议至少设置 {estimated_min_time}秒。"
            )

        return v


class AgentResponse(BaseModel):
    """智能代理响应模型"""
    success: bool = Field(..., description="请求是否成功")
    result: Optional[Any] = Field(None, description="代理执行结果")
    error: Optional[str] = Field(None, description="错误信息")
    task_id: Optional[str] = Field(None, description="任务ID")
    cached: bool = Field(False, description="结果是否来自缓存")
    execution_time: Optional[float] = Field(None, description="执行时间（秒）")
    steps_taken: Optional[int] = Field(None, description="实际执行步骤数")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")


class AgentStreamResponse(BaseModel):
    """智能代理流式响应模型"""
    type: str = Field(..., description="响应类型: step, result, error")
    content: str = Field(..., description="响应内容")
    task_id: Optional[str] = Field(None, description="任务ID")
    step_number: Optional[int] = Field(None, description="当前步骤号")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


class TaskRequest(BaseModel):
    """任务请求模型"""
    task_id: str = Field(..., description="任务ID")


class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    result: Optional[str] = Field(None, description="任务结果")
    error: Optional[str] = Field(None, description="错误信息")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    execution_time: Optional[float] = Field(None, description="执行时间（秒）")
    progress: Optional[float] = Field(None, description="进度百分比", ge=0, le=100)


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    service: str = Field(..., description="服务状态")
    model: str = Field(..., description="模型状态")
    tools: str = Field(..., description="工具状态")
    redis: str = Field(..., description="Redis状态")
    timestamp: datetime = Field(..., description="检查时间")
    version: Optional[str] = Field(None, description="服务版本")
    uptime: Optional[float] = Field(None, description="运行时间（秒）")
    error: Optional[str] = Field(None, description="错误信息")


class ServiceInfo(BaseModel):
    """服务信息模型"""
    name: str = Field(..., description="服务名称")
    version: str = Field(..., description="服务版本")
    description: str = Field(..., description="服务描述")
    status: str = Field(..., description="服务状态")
    endpoints: List[str] = Field(..., description="可用端点")
    features: List[str] = Field(..., description="支持的功能")


class WebSearchRequest(BaseModel):
    """网络搜索请求模型"""
    query: str = Field(..., description="搜索查询", min_length=1, max_length=1000)
    max_results: Optional[int] = Field(10, description="最大结果数", ge=1, le=50)
    timeout: Optional[int] = Field(30, description="超时时间（秒）", ge=5, le=120)
    language: Optional[str] = Field("zh", description="搜索语言")


class WebSearchResult(BaseModel):
    """网络搜索结果模型"""
    title: str = Field(..., description="标题")
    url: str = Field(..., description="链接")
    snippet: str = Field(..., description="摘要")
    relevance_score: Optional[float] = Field(None, description="相关性评分", ge=0, le=1)


class WebSearchResponse(BaseModel):
    """网络搜索响应模型"""
    success: bool = Field(..., description="搜索是否成功")
    results: List[WebSearchResult] = Field(default_factory=list, description="搜索结果")
    total_results: int = Field(0, description="总结果数")
    search_time: Optional[float] = Field(None, description="搜索耗时（秒）")
    error: Optional[str] = Field(None, description="错误信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="搜索时间")


class CodeExecutionRequest(BaseModel):
    """代码执行请求模型"""
    code: str = Field(..., description="要执行的代码", min_length=1, max_length=50000)
    language: str = Field("python", description="编程语言")
    timeout: Optional[int] = Field(30, description="超时时间（秒）", ge=5, le=300)
    context: Optional[Dict[str, Any]] = Field(None, description="执行上下文")


class CodeExecutionResponse(BaseModel):
    """代码执行响应模型"""
    success: bool = Field(..., description="执行是否成功")
    output: Optional[str] = Field(None, description="执行输出")
    error: Optional[str] = Field(None, description="错误信息")
    execution_time: Optional[float] = Field(None, description="执行时间（秒）")
    timestamp: datetime = Field(default_factory=datetime.now, description="执行时间戳")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(False, description="请求是否成功")
    error: str = Field(..., description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    detail: Optional[str] = Field(None, description="详细错误信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间戳")


class MetricsResponse(BaseModel):
    """指标响应模型"""
    total_requests: int = Field(0, description="总请求数")
    successful_requests: int = Field(0, description="成功请求数")
    failed_requests: int = Field(0, description="失败请求数")
    average_response_time: float = Field(0.0, description="平均响应时间（秒）")
    active_tasks: int = Field(0, description="活跃任务数")
    cache_hit_rate: float = Field(0.0, description="缓存命中率", ge=0, le=1)
    uptime: float = Field(0.0, description="运行时间（秒）")
    timestamp: datetime = Field(default_factory=datetime.now, description="指标时间戳")


class StrategyGenerationRequest(BaseModel):
    """策略生成请求模型"""
    product: str = Field(..., description="产品名称", min_length=1, max_length=200)
    query: str = Field(..., description="查询关键词", min_length=1, max_length=500)
    full_topic: Optional[str] = Field(None, description="完整分析主题（产品+关键词组合）", max_length=700)
    analysis_type: Optional[str] = Field("market_analysis", description="分析类型")
    strategy_count: Optional[int] = Field(4, description="策略数量", ge=3, le=10)
    region: Optional[str] = Field("global", description="分析地区")
    time_period: Optional[str] = Field(None, description="时间周期")
    business_focus: Optional[List[str]] = Field(default_factory=list, description="商业分析重点领域")
    language: Optional[str] = Field("zh-CN", description="输出语言，默认中文")

    @validator('product')
    def validate_product(cls, v):
        """验证产品名称"""
        if not v.strip():
            raise ValueError('产品名称不能为空')
        return v.strip()

    @validator('query')
    def validate_query(cls, v):
        """验证查询内容"""
        if not v.strip():
            raise ValueError('查询内容不能为空')
        return v.strip()


class SearchStrategy(BaseModel):
    """搜索策略模型"""
    id: str = Field(..., description="策略ID")
    query: str = Field(..., description="搜索查询")
    category: str = Field(..., description="策略分类")
    priority: int = Field(..., description="优先级", ge=1, le=5)
    description: str = Field(..., description="策略描述")


class StrategyGenerationResponse(BaseModel):
    """策略生成响应模型"""
    success: bool = Field(..., description="生成是否成功")
    strategies: List[SearchStrategy] = Field(default_factory=list, description="生成的策略列表")
    total_count: int = Field(0, description="策略总数")
    generation_time: Optional[float] = Field(None, description="生成耗时（秒）")
    error: Optional[str] = Field(None, description="错误信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="生成时间戳")


class MarketAnalysisRequest(BaseModel):
    """市场分析执行请求模型"""
    product: str = Field(..., description="产品名称", min_length=1, max_length=200)
    query: str = Field(..., description="分析查询", min_length=1, max_length=500)
    strategies: Optional[List[SearchStrategy]] = Field(default_factory=list, description="搜索策略列表（可选，为空时自动生成）")
    analysis_type: str = Field("market_analysis", description="分析类型")
    region: str = Field("global", description="分析地区")
    time_period: Optional[str] = Field(None, description="时间周期")
    strategy_count: Optional[int] = Field(4, description="策略数量（当strategies为空时生成的策略数量）", ge=1, le=10)
    max_steps: Optional[int] = Field(default_factory=get_default_max_steps_for_market_analysis, description="最大执行步骤数", ge=3, le=50)
    timeout: Optional[int] = Field(300, description="超时时间（秒）", ge=60, le=1800)  # 最大30分钟

    @validator('product')
    def validate_product(cls, v):
        """验证产品名称"""
        if not v.strip():
            raise ValueError('产品名称不能为空')
        return v.strip()

    @validator('query')
    def validate_query(cls, v):
        """验证查询内容"""
        if not v.strip():
            raise ValueError('查询内容不能为空')
        return v.strip()

    @validator('max_steps')
    def validate_max_steps(cls, v, values):
        """验证最大步骤数的合理性"""
        if v is None:
            return v

        # 根据策略数量调整步骤数建议
        strategy_count = values.get('strategy_count', 4)

        # 建议的步骤数范围
        if strategy_count <= 4:
            recommended_max = 12
        elif strategy_count <= 7:
            recommended_max = 10  # 7个策略时降低步骤数以控制总时间
        else:
            recommended_max = 8   # 更多策略时进一步降低

        # 如果步骤数过高，给出警告（但不强制修改）
        if v > recommended_max * 1.5:  # 超过建议值50%时警告
            import warnings
            warnings.warn(
                f"max_steps={v} 可能导致执行时间过长。"
                f"建议使用 {recommended_max} 步以获得最佳性能。"
                f"当前策略数量: {strategy_count}"
            )

        return v

    @validator('timeout')
    def validate_timeout(cls, v, values):
        """验证超时时间的合理性"""
        if v is None:
            return v

        max_steps = values.get('max_steps', 12)
        strategy_count = values.get('strategy_count', 4)

        # 估算最小需要时间：策略数量 * 每步平均时间
        estimated_min_time = strategy_count * max_steps * 2  # 每步约2秒

        if v < estimated_min_time:
            import warnings
            warnings.warn(
                f"timeout={v}秒 可能不足以完成分析。"
                f"建议至少设置 {estimated_min_time}秒。"
                f"当前配置: {strategy_count}个策略 × {max_steps}步"
            )

        return v


class MarketDataSource(BaseModel):
    """市场数据源模型"""
    title: str = Field(..., description="数据源标题")
    url: str = Field(..., description="数据源URL")
    content: str = Field(..., description="数据内容")
    data_type: str = Field(..., description="数据类型/分类")
    relevance_score: Optional[float] = Field(None, description="相关性评分")
    timestamp: datetime = Field(default_factory=datetime.now, description="获取时间戳")


class MarketAnalysisResponse(BaseModel):
    """市场分析执行响应模型"""
    success: bool = Field(..., description="分析是否成功")
    analysis_report: Optional[str] = Field(None, description="分析报告内容")
    data_sources: Optional[List[MarketDataSource]] = Field(None, description="使用的数据源列表")
    strategies_executed: int = Field(0, description="执行的策略数量")
    total_data_points: int = Field(0, description="收集的数据点总数")
    execution_time: Optional[float] = Field(None, description="执行耗时（秒）")
    generated_strategies: Optional[List[SearchStrategy]] = Field(None, description="生成的搜索策略列表")
    error: Optional[str] = Field(None, description="错误信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="分析时间戳")


# 响应模型联合类型
ResponseModel = Union[
    AgentResponse,
    TaskResponse,
    HealthResponse,
    WebSearchResponse,
    CodeExecutionResponse,
    ErrorResponse,
    MetricsResponse,
    StrategyGenerationResponse,
    MarketAnalysisResponse
]
