"""Remove experience_level field from sales_training_customers

Revision ID: 007_remove_experience_level
Revises: 006_add_sales_training_tables
Create Date: 2025-08-02 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '007_remove_experience_level'
down_revision = '006_add_sales_training_tables'
branch_labels = None
depends_on = None


def upgrade():
    """Remove experience_level column from sales_training_customers table"""
    # Drop the experience_level column
    op.drop_column('sales_training_customers', 'experience_level')


def downgrade():
    """Add back experience_level column to sales_training_customers table"""
    # Add back the experience_level column
    op.add_column('sales_training_customers', 
                  sa.Column('experience_level', sa.String(50), comment="工作经验等级"))
