/**
 * Ollama API 服务
 * 直接调用本地Ollama服务，支持流式响应和模型管理
 */

import {
  OllamaModel,
  OllamaModelsResponse,
  OllamaChatRequest,
  OllamaChatResponse,
  OllamaChatMessage,
  OllamaApiConfig,
  OllamaApiError,
  StreamingResponse,
} from '@/types/ollama';

// 默认配置
const DEFAULT_CONFIG: OllamaApiConfig = {
  baseUrl: 'http://localhost:11434',
  timeout: 30000,
  defaultModel: 'qwen3:8b',
  defaultOptions: {
    temperature: 0.7,
    top_p: 0.9,
    top_k: 40,
    repeat_penalty: 1.1,
    num_ctx: 4096,
  },
};

class OllamaApiService {
  private config: OllamaApiConfig;

  constructor(config?: Partial<OllamaApiConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * 获取可用模型列表
   */
  async getModels(): Promise<OllamaModel[]> {
    try {
      const response = await fetch(`${this.config.baseUrl}/api/tags`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors',
        signal: AbortSignal.timeout(this.config.timeout),
      });

      if (!response.ok) {
        throw new OllamaApiError(
          'FETCH_MODELS_FAILED',
          `获取模型列表失败: ${response.status} ${response.statusText}`
        );
      }

      const data: OllamaModelsResponse = await response.json();
      return data.models || [];
    } catch (error) {
      if (error instanceof OllamaApiError) {
        throw error;
      }

      // 提供更详细的错误信息
      let errorMessage = '网络错误';
      if (error instanceof TypeError && error.message.includes('fetch')) {
        errorMessage = '无法连接到Ollama服务，请确保Ollama正在运行并监听端口11434';
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      throw new OllamaApiError(
        'NETWORK_ERROR',
        errorMessage,
        error
      );
    }
  }

  /**
   * 发送聊天消息（非流式）
   */
  async chat(request: OllamaChatRequest): Promise<OllamaChatResponse> {
    try {
      const response = await fetch(`${this.config.baseUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...request,
          stream: false,
          options: {
            ...this.config.defaultOptions,
            ...request.options,
          },
        }),
        signal: AbortSignal.timeout(this.config.timeout),
      });

      if (!response.ok) {
        throw new OllamaApiError(
          'CHAT_REQUEST_FAILED',
          `聊天请求失败: ${response.status} ${response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof OllamaApiError) {
        throw error;
      }
      throw new OllamaApiError(
        'NETWORK_ERROR',
        `网络错误: ${error instanceof Error ? error.message : '未知错误'}`,
        error
      );
    }
  }

  /**
   * 发送聊天消息（流式响应）
   */
  async *chatStream(request: OllamaChatRequest): AsyncGenerator<StreamingResponse> {
    try {
      const response = await fetch(`${this.config.baseUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...request,
          stream: true,
          options: {
            ...this.config.defaultOptions,
            ...request.options,
          },
        }),
      });

      if (!response.ok) {
        throw new OllamaApiError(
          'CHAT_STREAM_FAILED',
          `流式聊天请求失败: ${response.status} ${response.statusText}`
        );
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new OllamaApiError('STREAM_ERROR', '无法获取响应流');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim()) {
              try {
                const data: OllamaChatResponse = JSON.parse(line);
                yield {
                  content: data.message?.content || '',
                  done: data.done,
                };

                if (data.done) {
                  return;
                }
              } catch (parseError) {
                console.warn('解析流式响应失败:', parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      if (error instanceof OllamaApiError) {
        throw error;
      }
      yield {
        content: '',
        done: true,
        error: `流式响应错误: ${error instanceof Error ? error.message : '未知错误'}`,
      };
    }
  }

  /**
   * 检查Ollama服务是否可用
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.baseUrl}/api/tags`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000),
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 构建聊天消息
   */
  buildChatMessage(role: 'user' | 'assistant', content: string): OllamaChatMessage {
    return { role, content };
  }

  /**
   * 构建聊天请求
   */
  buildChatRequest(
    model: string,
    messages: OllamaChatMessage[],
    options?: OllamaChatRequest['options']
  ): OllamaChatRequest {
    return {
      model,
      messages,
      options: {
        ...this.config.defaultOptions,
        ...options,
      },
    };
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<OllamaApiConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  getConfig(): OllamaApiConfig {
    return { ...this.config };
  }
}

// 创建默认实例
export const ollamaApi = new OllamaApiService();

// 导出服务类
export { OllamaApiService };
export default ollamaApi;
