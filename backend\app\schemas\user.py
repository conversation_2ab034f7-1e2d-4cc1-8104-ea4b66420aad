"""
用户管理Pydantic模式
定义用户相关API请求和响应的数据结构
"""
from pydantic import BaseModel, Field, EmailStr, ConfigDict
from typing import Optional
from datetime import datetime


class UserBase(BaseModel):
    """用户基础模式"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    phone: Optional[str] = Field(None, max_length=20, description="电话号码")
    is_active: bool = Field(default=True, description="是否激活")


class UserCreate(UserBase):
    """创建用户的请求模式"""
    password: str = Field(..., min_length=6, max_length=50, description="密码")


class UserUpdate(BaseModel):
    """更新用户的请求模式"""
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    phone: Optional[str] = Field(None, max_length=20, description="电话号码")
    avatar_url: Optional[str] = Field(None, max_length=255, description="头像URL")
    is_active: Optional[bool] = Field(None, description="是否激活")


class UserPasswordUpdate(BaseModel):
    """更新密码的请求模式"""
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=6, max_length=50, description="新密码")


class User(UserBase):
    """用户响应模式"""
    id: int = Field(..., description="用户ID")
    avatar_url: Optional[str] = Field(None, description="头像URL")
    is_superuser: bool = Field(..., description="是否超级用户")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    model_config = ConfigDict(from_attributes=True)


class UserLogin(BaseModel):
    """用户登录请求模式"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")


class Token(BaseModel):
    """JWT Token响应模式"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")


class TokenData(BaseModel):
    """Token数据模式"""
    username: Optional[str] = None
    user_id: Optional[int] = None


class UserLoginResponse(BaseModel):
    """用户登录响应模式"""
    user: User = Field(..., description="用户信息")
    token: Token = Field(..., description="认证令牌")
