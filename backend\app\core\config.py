"""
应用配置文件
使用Pydantic Settings管理环境变量和应用配置
"""
from pydantic_settings import BaseSettings
from typing import List


class Settings(BaseSettings):
    """应用设置类，自动从环境变量加载配置"""

    # API设置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "ZHT System API"

    # 数据库设置
    DATABASE_URL: str = "postgresql+asyncpg://zht_user:zht_password@zht_db:5432/zht_db"

    # JWT设置 - 支持两种变量名以保持兼容性
    JWT_SECRET: str = "your-super-secret-jwt-key-change-in-production"
    JWT_SECRET_KEY: str = "your-super-secret-jwt-key-change-in-production"  # 统一变量名
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRE_MINUTES: int = 30

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 如果设置了 JWT_SECRET_KEY，则使用它作为 JWT_SECRET
        if hasattr(self, 'JWT_SECRET_KEY') and self.JWT_SECRET_KEY != "your-super-secret-jwt-key-change-in-production":
            self.JWT_SECRET = self.JWT_SECRET_KEY

    # CORS设置
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:5173"]

    # 开发设置
    DEBUG: bool = True
    DEV_MODE: bool = False

    class Config:
        """Pydantic配置类"""
        env_file = ".env"
        case_sensitive = True


# 创建全局设置实例
settings = Settings()
