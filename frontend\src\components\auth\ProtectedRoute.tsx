/**
 * 受保护的路由组件
 * 用于保护需要认证的页面
 */
import React, { useEffect } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '@/store/auth'
import { Loader2 } from 'lucide-react'

// 开发模式检测
const isDevMode = import.meta.env.VITE_DEV_MODE === 'true'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAuth?: boolean // 是否需要认证，默认为true
  requireSuperuser?: boolean // 是否需要超级用户权限
  redirectTo?: string // 重定向路径，默认为/login
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requireSuperuser = false,
  redirectTo = '/login',
}) => {
  const location = useLocation()
  const { isAuthenticated, user, isLoading, refreshToken, initDevMode } = useAuthStore()

  useEffect(() => {
    // 开发模式下自动初始化认证状态
    if (isDevMode && !isAuthenticated) {
      initDevMode()
      return
    }

    // 如果已认证但token可能过期，尝试刷新
    if (isAuthenticated && user && !isDevMode) {
      refreshToken()
    }
  }, [isAuthenticated, user, refreshToken, initDevMode])

  // 如果不需要认证，直接渲染子组件
  if (!requireAuth) {
    return <>{children}</>
  }

  // 开发模式下直接渲染子组件（跳过认证检查）
  if (isDevMode) {
    return <>{children}</>
  }

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-sm text-muted-foreground">正在验证身份...</p>
        </div>
      </div>
    )
  }

  // 如果未认证，重定向到登录页面
  if (!isAuthenticated || !user) {
    return (
      <Navigate 
        to={redirectTo} 
        state={{ from: location }} 
        replace 
      />
    )
  }

  // 如果需要超级用户权限但用户不是超级用户
  if (requireSuperuser && !user.is_superuser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold text-red-600">权限不足</h1>
          <p className="text-muted-foreground">
            您没有访问此页面的权限。请联系管理员。
          </p>
        </div>
      </div>
    )
  }

  // 如果用户账户被禁用
  if (!user.is_active) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold text-red-600">账户已禁用</h1>
          <p className="text-muted-foreground">
            您的账户已被禁用。请联系管理员。
          </p>
        </div>
      </div>
    )
  }

  // 所有检查通过，渲染子组件
  return <>{children}</>
}

/**
 * 公共路由组件
 * 用于只有未认证用户才能访问的页面（如登录、注册页面）
 */
export const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuthStore()

  // 如果已认证，重定向到首页
  if (isAuthenticated) {
    return <Navigate to="/" replace />
  }

  return <>{children}</>
}
