"""Add users table for authentication

Revision ID: 93b39bac2186
Revises: 
Create Date: 2025-07-04 07:03:19.144598

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '93b39bac2186'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # 首先删除有外键约束的表
    try:
        op.drop_index('ix_user_sessions_id', table_name='user_sessions')
        op.drop_index('ix_user_sessions_session_token', table_name='user_sessions')
        op.drop_index('ix_user_sessions_user_id', table_name='user_sessions')
        op.drop_table('user_sessions')
    except:
        pass

    try:
        op.drop_table('role_permissions')
    except:
        pass

    try:
        op.drop_table('user_roles')
    except:
        pass

    try:
        op.drop_index('ix_password_reset_tokens_id', table_name='password_reset_tokens')
        op.drop_index('ix_password_reset_tokens_token', table_name='password_reset_tokens')
        op.drop_index('ix_password_reset_tokens_user_id', table_name='password_reset_tokens')
        op.drop_table('password_reset_tokens')
    except:
        pass

    try:
        op.drop_index('ix_permissions_category', table_name='permissions')
        op.drop_index('ix_permissions_id', table_name='permissions')
        op.drop_index('ix_permissions_name', table_name='permissions')
        op.drop_table('permissions')
    except:
        pass

    try:
        op.drop_index('ix_roles_id', table_name='roles')
        op.drop_index('ix_roles_name', table_name='roles')
        op.drop_table('roles')
    except:
        pass
    # 先添加可空的hashed_password字段
    op.add_column('users', sa.Column('hashed_password', sa.String(length=255), nullable=True, comment='密码哈希'))

    # 为现有用户设置默认密码哈希（如果有的话）
    # 这里使用一个临时的默认密码哈希，实际使用时需要用户重新设置密码
    op.execute("UPDATE users SET hashed_password = '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtO5S7ZOvG' WHERE hashed_password IS NULL")

    # 现在将字段设置为非空
    op.alter_column('users', 'hashed_password', nullable=False)
    op.alter_column('users', 'id',
               existing_type=sa.INTEGER(),
               comment='用户ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('users', 'username',
               existing_type=sa.VARCHAR(length=50),
               comment='用户名',
               existing_nullable=False)
    op.alter_column('users', 'email',
               existing_type=sa.VARCHAR(length=100),
               comment='邮箱地址',
               existing_nullable=False)
    op.alter_column('users', 'full_name',
               existing_type=sa.VARCHAR(length=100),
               comment='全名',
               existing_nullable=True)
    op.alter_column('users', 'avatar_url',
               existing_type=sa.VARCHAR(length=255),
               comment='头像URL',
               existing_nullable=True)
    op.alter_column('users', 'phone',
               existing_type=sa.VARCHAR(length=20),
               comment='电话号码',
               existing_nullable=True)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               comment='是否激活')
    op.alter_column('users', 'is_superuser',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               comment='是否超级用户')
    op.alter_column('users', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               comment='创建时间',
               existing_server_default=sa.text('now()'))
    op.alter_column('users', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               comment='更新时间',
               existing_server_default=sa.text('now()'))
    op.drop_column('users', 'country')
    op.drop_column('users', 'theme')
    op.drop_column('users', 'language')
    op.drop_column('users', 'address')
    op.drop_column('users', 'bio')
    op.drop_column('users', 'password_hash')
    op.drop_column('users', 'city')
    op.drop_column('users', 'last_login')
    op.drop_column('users', 'timezone')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('timezone', sa.VARCHAR(length=50), autoincrement=False, nullable=False))
    op.add_column('users', sa.Column('last_login', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('city', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('password_hash', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.add_column('users', sa.Column('bio', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('address', sa.VARCHAR(length=200), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('language', sa.VARCHAR(length=10), autoincrement=False, nullable=False))
    op.add_column('users', sa.Column('theme', sa.VARCHAR(length=20), autoincrement=False, nullable=False))
    op.add_column('users', sa.Column('country', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.alter_column('users', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               comment=None,
               existing_comment='更新时间',
               existing_server_default=sa.text('now()'))
    op.alter_column('users', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               comment=None,
               existing_comment='创建时间',
               existing_server_default=sa.text('now()'))
    op.alter_column('users', 'is_superuser',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               comment=None,
               existing_comment='是否超级用户')
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               comment=None,
               existing_comment='是否激活')
    op.alter_column('users', 'phone',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='电话号码',
               existing_nullable=True)
    op.alter_column('users', 'avatar_url',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='头像URL',
               existing_nullable=True)
    op.alter_column('users', 'full_name',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='全名',
               existing_nullable=True)
    op.alter_column('users', 'email',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='邮箱地址',
               existing_nullable=False)
    op.alter_column('users', 'username',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='用户名',
               existing_nullable=False)
    op.alter_column('users', 'id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='用户ID',
               existing_nullable=False,
               autoincrement=True)
    op.drop_column('users', 'hashed_password')
    op.create_table('user_roles',
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('role_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('assigned_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('assigned_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['assigned_by'], ['users.id'], name='user_roles_assigned_by_fkey'),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], name='user_roles_role_id_fkey'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='user_roles_user_id_fkey'),
    sa.PrimaryKeyConstraint('user_id', 'role_id', name='user_roles_pkey')
    )
    op.create_table('roles',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('roles_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('display_name', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('is_system', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('created_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], name='roles_created_by_fkey'),
    sa.PrimaryKeyConstraint('id', name='roles_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index('ix_roles_name', 'roles', ['name'], unique=False)
    op.create_index('ix_roles_id', 'roles', ['id'], unique=False)
    op.create_table('password_reset_tokens',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('token', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('expires_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('used', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='password_reset_tokens_pkey')
    )
    op.create_index('ix_password_reset_tokens_user_id', 'password_reset_tokens', ['user_id'], unique=False)
    op.create_index('ix_password_reset_tokens_token', 'password_reset_tokens', ['token'], unique=False)
    op.create_index('ix_password_reset_tokens_id', 'password_reset_tokens', ['id'], unique=False)
    op.create_table('role_permissions',
    sa.Column('role_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('permission_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('granted_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('granted_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['granted_by'], ['users.id'], name='role_permissions_granted_by_fkey'),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], name='role_permissions_permission_id_fkey'),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], name='role_permissions_role_id_fkey'),
    sa.PrimaryKeyConstraint('role_id', 'permission_id', name='role_permissions_pkey')
    )
    op.create_table('permissions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('display_name', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('category', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('level', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('is_system', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('created_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], name='permissions_created_by_fkey'),
    sa.PrimaryKeyConstraint('id', name='permissions_pkey')
    )
    op.create_index('ix_permissions_name', 'permissions', ['name'], unique=False)
    op.create_index('ix_permissions_id', 'permissions', ['id'], unique=False)
    op.create_index('ix_permissions_category', 'permissions', ['category'], unique=False)
    op.create_table('user_sessions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('session_token', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('cached_permissions', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('ip_address', sa.VARCHAR(length=45), autoincrement=False, nullable=True),
    sa.Column('user_agent', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('last_accessed', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('expires_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='user_sessions_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='user_sessions_pkey')
    )
    op.create_index('ix_user_sessions_user_id', 'user_sessions', ['user_id'], unique=False)
    op.create_index('ix_user_sessions_session_token', 'user_sessions', ['session_token'], unique=False)
    op.create_index('ix_user_sessions_id', 'user_sessions', ['id'], unique=False)
    # ### end Alembic commands ###
