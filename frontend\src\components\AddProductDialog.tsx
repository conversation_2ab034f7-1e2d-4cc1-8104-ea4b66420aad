import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'

interface AddProductDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onAddProduct: (label: string, iconName: string) => Promise<{ success: boolean; error?: string; value?: string }>
  onProductAdded?: (productValue: string) => void // 新增：产品添加成功后的回调
}

export const AddProductDialog: React.FC<AddProductDialogProps> = ({
  open,
  onOpenChange,
  onAddProduct,
  onProductAdded
}) => {
  const [formData, setFormData] = useState({
    label: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  // 重置表单
  const resetForm = () => {
    setFormData({ label: '' })
    setError('')
    setIsSubmitting(false)
  }

  // 处理对话框关闭
  const handleOpenChange = (newOpen: boolean) => {
    if (!isSubmitting) {
      onOpenChange(newOpen)
      if (!newOpen) {
        resetForm()
      }
    }
  }

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 表单验证
    if (!formData.label.trim()) {
      setError('请输入产品名称')
      return
    }

    setIsSubmitting(true)
    setError('')

    try {
      // 使用Package作为默认图标
      const result = await onAddProduct(formData.label.trim(), 'Package')

      if (result.success) {
        // 成功后关闭对话框并重置表单
        toast.success(`产品选项 "${formData.label}" 已成功添加`)

        // 如果有回调函数且返回了产品value，则自动选择该产品
        if (onProductAdded && result.value) {
          onProductAdded(result.value)
        }

        onOpenChange(false)
        resetForm()
      } else {
        setError(result.error || '添加产品选项失败')
        toast.error(result.error || '添加产品选项失败')
      }
    } catch (err) {
      setError('添加过程中发生错误')
      console.error('添加产品选项失败:', err)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange} modal={true}>
      <DialogContent
        className="sm:max-w-[425px]"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>添加新产品选项</DialogTitle>
          <DialogDescription>
            添加一个新的产品类型到选择列表中。
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 产品名称输入 */}
          <div className="space-y-2">
            <Label htmlFor="product-name">产品名称</Label>
            <Input
              id="product-name"
              placeholder="例如：手提包"
              value={formData.label}
              onChange={(e) => setFormData(prev => ({ ...prev, label: e.target.value }))}
              disabled={isSubmitting}
              className="w-full"
            />
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={isSubmitting}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  添加中...
                </>
              ) : (
                '添加产品'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default AddProductDialog
