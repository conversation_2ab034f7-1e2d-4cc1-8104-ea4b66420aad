"""
权限控制装饰器和工具函数
提供基于角色和权限的访问控制
"""
from functools import wraps
from typing import List, Optional, Callable, Any
from fastapi import HTTPException, status, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.models.user import User
from app.crud.rbac import user_role_crud


class PermissionChecker:
    """权限检查器类"""

    def __init__(self, required_permissions: List[str] = None, required_roles: List[str] = None):
        """
        初始化权限检查器

        Args:
            required_permissions: 需要的权限列表
            required_roles: 需要的角色列表
        """
        self.required_permissions = required_permissions or []
        self.required_roles = required_roles or []

    async def __call__(
        self,
        current_user: User = Depends(get_current_active_user),
        db: AsyncSession = Depends(get_db)
    ) -> User:
        """
        检查用户权限

        Args:
            current_user: 当前用户
            db: 数据库会话

        Returns:
            User: 通过权限检查的用户

        Raises:
            HTTPException: 权限不足时抛出异常
        """
        # 超级用户拥有所有权限
        if current_user.is_superuser:
            return current_user

        # 检查角色权限
        if self.required_roles:
            user_roles = await user_role_crud.get_user_roles(db, current_user.id)
            user_role_names = [ur.role.name for ur in user_roles if ur.role.is_active]

            has_required_role = any(role in user_role_names for role in self.required_roles)
            if not has_required_role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"需要以下角色之一: {', '.join(self.required_roles)}"
                )

        # 检查具体权限
        if self.required_permissions:
            for permission in self.required_permissions:
                has_permission = await user_role_crud.check_permission(
                    db, current_user.id, permission
                )
                if not has_permission:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"缺少权限: {permission}"
                    )

        return current_user


def require_permissions(permissions: List[str]):
    """
    权限装饰器 - 要求指定权限

    Args:
        permissions: 需要的权限列表

    Returns:
        装饰器函数
    """
    return PermissionChecker(required_permissions=permissions)


def require_roles(roles: List[str]):
    """
    角色装饰器 - 要求指定角色

    Args:
        roles: 需要的角色列表

    Returns:
        装饰器函数
    """
    return PermissionChecker(required_roles=roles)


def require_admin():
    """
    管理员装饰器 - 要求管理员角色

    Returns:
        装饰器函数
    """
    return PermissionChecker(required_roles=["admin"])


# 基于菜单的权限常量
class MenuPermissions:
    """菜单权限常量"""
    # 主要功能
    DASHBOARD_ACCESS = "dashboard:access"
    TASKS_ACCESS = "tasks:access"

    # 贸易教官
    TRADE_COACH_ACCESS = "trade_coach:access"
    COMPANY_KNOWLEDGE_ACCESS = "company_knowledge:access"
    TRADE_KNOWLEDGE_ACCESS = "trade_knowledge:access"
    MARKET_ANALYSIS_ACCESS = "market_analysis:access"
    GROUP_ANALYSIS_ACCESS = "group_analysis:access"
    SALES_TRAINING_ACCESS = "sales_training:access"
    ENHANCED_TRAINING_ACCESS = "enhanced_training:access"
    LEARNING_PLAN_ACCESS = "learning_plan:access"

    # AI工具
    AI_KNOWLEDGE_ACCESS = "ai_knowledge:access"
    AI_CHAT_ACCESS = "ai_chat:access"
    TEMPLATES_ACCESS = "templates:access"

    # 系统功能
    ADMIN_ACCESS = "admin:access"

    # 管理员功能
    USER_MANAGE = "user:manage"
    ROLE_MANAGE = "role:manage"
    PERMISSION_MANAGE = "permission:manage"


def require_superuser():
    """
    超级用户装饰器 - 要求超级用户权限

    Returns:
        装饰器函数
    """
    async def check_superuser(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要超级用户权限"
            )
        return current_user

    return check_superuser


# 权限常量类
class Permissions:
    """权限常量类"""

    # 用户管理权限
    USER_READ = "user:read"
    USER_CREATE = "user:create"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"

    # 角色管理权限
    ROLE_READ = "role:read"
    ROLE_CREATE = "role:create"
    ROLE_UPDATE = "role:update"
    ROLE_DELETE = "role:delete"

    # 权限管理权限
    PERMISSION_READ = "permission:read"
    PERMISSION_CREATE = "permission:create"
    PERMISSION_UPDATE = "permission:update"
    PERMISSION_DELETE = "permission:delete"

    # 系统管理权限
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"

    # 内容管理权限
    CONTENT_READ = "content:read"
    CONTENT_CREATE = "content:create"
    CONTENT_UPDATE = "content:update"
    CONTENT_DELETE = "content:delete"

    # 管理员功能（兼容旧版本）
    USER_MANAGE = "user:manage"
    ROLE_MANAGE = "role:manage"
    PERMISSION_MANAGE = "permission:manage"

    # 知识库管理权限（新增示例）
    KNOWLEDGE_READ = "knowledge:read"
    KNOWLEDGE_CREATE = "knowledge:create"
    KNOWLEDGE_UPDATE = "knowledge:update"
    KNOWLEDGE_DELETE = "knowledge:delete"
    KNOWLEDGE_EXPORT = "knowledge:export"

    # AI服务权限（新增示例）
    AI_CHAT = "ai:chat"
    AI_EMBEDDING = "ai:embedding"
    AI_RERANK = "ai:rerank"
    AI_CONFIG = "ai:config"


# 预定义的角色常量
class Roles:
    """角色常量类"""

    ADMIN = "admin"                # 管理员


# 权限工具函数
async def get_user_permissions(db: AsyncSession, user_id: int) -> List[str]:
    """
    获取用户的所有权限名称列表

    Args:
        db: 数据库会话
        user_id: 用户ID

    Returns:
        权限名称列表
    """
    permissions = await user_role_crud.get_user_permissions(db, user_id)
    return [p.name for p in permissions]


async def get_user_roles(db: AsyncSession, user_id: int) -> List[str]:
    """
    获取用户的所有角色名称列表

    Args:
        db: 数据库会话
        user_id: 用户ID

    Returns:
        角色名称列表
    """
    user_roles = await user_role_crud.get_user_roles(db, user_id)
    return [ur.role.name for ur in user_roles if ur.role.is_active]


async def check_user_permission(db: AsyncSession, user_id: int, permission: str) -> bool:
    """
    检查用户是否有指定权限

    Args:
        db: 数据库会话
        user_id: 用户ID
        permission: 权限名称

    Returns:
        是否有权限
    """
    return await user_role_crud.check_permission(db, user_id, permission)
