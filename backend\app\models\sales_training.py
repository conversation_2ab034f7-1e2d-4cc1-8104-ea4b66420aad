"""
销冠实战训练相关数据模型
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Index
from sqlalchemy.sql import func
from app.core.database import Base


class SalesTrainingCustomer(Base):
    """销冠实战训练客户数据模型"""
    __tablename__ = "sales_training_customers"

    id = Column(Integer, primary_key=True, index=True, comment="客户ID")
    name = Column(String(100), nullable=False, comment="客户姓名")
    company = Column(String(200), comment="公司名称")
    phone = Column(String(20), comment="联系电话")
    email = Column(String(100), comment="邮箱地址")
    industry = Column(String(100), comment="行业分类")
    region = Column(String(100), comment="地区信息")
    country_code = Column(String(10), comment="国家代码")

    training_scenario = Column(String(100), comment="训练场景")  # 新增训练场景字段
    notes = Column(Text, comment="备注信息")
    is_active = Column(Boolean, nullable=False, default=True, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 创建索引
    __table_args__ = (
        Index('idx_customer_name', 'name'),
        Index('idx_customer_company', 'company'),
        Index('idx_customer_industry', 'industry'),
        Index('idx_customer_region', 'region'),
        Index('idx_customer_active', 'is_active'),
    )


class SalesTrainingCountry(Base):
    """销售训练国家选项表"""
    __tablename__ = "sales_training_countries"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), nullable=False, unique=True, comment="国家名称")
    code = Column(String(10), nullable=False, unique=True, comment="国家代码")
    flag = Column(String(20), nullable=True, comment="国旗emoji")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引
    __table_args__ = (
        Index('idx_country_name', 'name'),
        Index('idx_country_code', 'code'),
        Index('idx_country_active', 'is_active'),
    )

    def __repr__(self):
        return f"<SalesTrainingCountry(id={self.id}, name='{self.name}', code='{self.code}')>"


class SalesTrainingProduct(Base):
    """销售训练产品选项表"""
    __tablename__ = "sales_training_products"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(200), nullable=False, comment="产品名称")
    category = Column(String(100), nullable=False, comment="产品分类")
    description = Column(Text, nullable=True, comment="产品描述")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引和约束
    __table_args__ = (
        Index('idx_product_name', 'name'),
        Index('idx_product_category', 'category'),
        Index('idx_product_active', 'is_active'),
        Index('uk_product_name_category', 'name', 'category', unique=True),
    )

    def __repr__(self):
        return f"<SalesTrainingProduct(id={self.id}, name='{self.name}', category='{self.category}')>"



class SalesTrainingScenario(Base):
    """销售训练场景表"""
    __tablename__ = "sales_training_scenarios"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    name = Column(String(100), nullable=False, unique=True, comment="场景名称")
    description = Column(Text, comment="场景描述")
    category = Column(String(50), nullable=False, comment="场景分类")
    difficulty = Column(String(20), nullable=False, default='intermediate', comment="难度等级")  # beginner, intermediate, advanced, expert
    duration = Column(Integer, comment="预计时长(分钟)")
    objectives = Column(Text, comment="训练目标(JSON格式)")
    challenges = Column(Text, comment="挑战要点(JSON格式)")
    tags = Column(Text, comment="标签(JSON格式)")
    icon = Column(String(10), comment="图标emoji")
    color = Column(String(20), comment="颜色样式")
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 索引和约束
    __table_args__ = (
        Index('idx_scenario_name', 'name'),
        Index('idx_scenario_category', 'category'),
        Index('idx_scenario_difficulty', 'difficulty'),
        Index('idx_scenario_active', 'is_active'),
    )

    def __repr__(self):
        return f"<SalesTrainingScenario(id={self.id}, name='{self.name}', category='{self.category}')>"
