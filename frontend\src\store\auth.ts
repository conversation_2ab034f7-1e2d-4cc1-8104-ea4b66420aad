/**
 * 用户认证状态管理
 * 使用Zustand管理用户登录状态、token等
 */
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// 用户信息类型
export interface User {
  id: number
  username: string
  email: string
  full_name?: string
  phone?: string
  avatar_url?: string
  is_active: boolean
  is_superuser: boolean
  created_at: string
  updated_at: string
}

// Token信息类型
export interface Token {
  access_token: string
  token_type: string
  expires_in: number
}

// 登录响应类型
export interface LoginResponse {
  user: User
  token: Token
}

// 认证状态接口
interface AuthState {
  // 状态
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean

  // 操作方法
  login: (username: string, password: string) => Promise<void>
  register: (userData: {
    username: string
    email: string
    password: string
    full_name?: string
    phone?: string
  }) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  updateUser: (userData: Partial<User>) => void
  setLoading: (loading: boolean) => void
  initDevMode: () => void
}

// API基础URL - 在开发环境中使用代理，生产环境使用环境变量
const API_V1_URL = import.meta.env.DEV ? '/api/v1' : `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001'}/api/v1`

// 开发模式检测
const isDevMode = import.meta.env.VITE_DEV_MODE === 'true'

// 开发模式日志
if (isDevMode) {
  console.log('🚀 开发模式已启用，将自动跳过认证流程')
} else {
  console.log('🔒 生产模式已启用，需要正常认证流程')
}

// 开发模式模拟用户数据
const DEV_USER: User = {
  id: 1,
  username: 'dev_user',
  email: '<EMAIL>',
  full_name: '开发用户',
  phone: '13800138000',
  avatar_url: null,
  is_active: true,
  is_superuser: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
}

const DEV_TOKEN = 'dev-mode-mock-token'

// 清理函数：在非开发模式时清除开发模式残留数据
const cleanupDevModeData = () => {
  if (!isDevMode) {
    const stored = localStorage.getItem('auth-storage')
    if (stored) {
      try {
        const parsedStored = JSON.parse(stored)
        // 检查是否有开发用户数据或开发token
        if (parsedStored.state?.user?.username === 'dev_user' ||
            parsedStored.state?.token === 'dev-mode-mock-token') {
          console.log('🧹 生产模式下检测到开发模式残留数据，正在清除...')
          localStorage.removeItem('auth-storage')
          return true
        }
      } catch (e) {
        // 解析错误时也清除
        console.log('🧹 localStorage数据解析错误，正在清除...')
        localStorage.removeItem('auth-storage')
        return true
      }
    }
  }
  return false
}

// 执行清理
const wasCleared = cleanupDevModeData()

// 创建认证状态store
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => {
      // 开发模式下的初始状态
      const initialState = isDevMode ? {
        user: DEV_USER,
        token: DEV_TOKEN,
        isAuthenticated: true,
        isLoading: false,
      } : {
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      }

      return {
        // 初始状态
        ...initialState,

      // 用户登录
      login: async (username: string, password: string) => {
        // 开发模式下直接模拟登录成功
        if (isDevMode) {
          set({
            user: DEV_USER,
            token: DEV_TOKEN,
            isAuthenticated: true,
            isLoading: false,
          })
          return
        }

        set({ isLoading: true })

        try {
          const response = await fetch(`${API_V1_URL}/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password }),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.detail || '登录失败')
          }

          const data: LoginResponse = await response.json()

          set({
            user: data.user,
            token: data.token.access_token,
            isAuthenticated: true,
            isLoading: false,
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      // 用户注册
      register: async (userData) => {
        set({ isLoading: true })

        try {
          const response = await fetch(`${API_V1_URL}/auth/register`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(userData),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.detail || '注册失败')
          }

          const user: User = await response.json()

          // 注册成功后自动登录
          await get().login(userData.username, userData.password)
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      // 用户登出
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        })
      },

      // 刷新Token
      refreshToken: async () => {
        const { token } = get()
        if (!token) return

        try {
          const response = await fetch(`${API_V1_URL}/auth/refresh`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          })

          if (!response.ok) {
            // Token无效，登出用户
            get().logout()
            return
          }

          const newToken: Token = await response.json()
          set({ token: newToken.access_token })
        } catch (error) {
          console.error('刷新Token失败:', error)
          get().logout()
        }
      },

      // 更新用户信息
      updateUser: (userData) => {
        const { user } = get()
        if (user) {
          set({ user: { ...user, ...userData } })
        }
      },

      // 设置加载状态
      setLoading: (loading) => {
        set({ isLoading: loading })
      },

      // 初始化开发模式
      initDevMode: () => {
        if (isDevMode) {
          console.log('🚀 开发模式已启用，自动登录开发用户')
          set({
            user: DEV_USER,
            token: DEV_TOKEN,
            isAuthenticated: true,
            isLoading: false,
          })
        }
      },
      }
    },
    {
      name: 'auth-storage', // localStorage中的key
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

// 获取当前用户信息的API调用
export const getCurrentUser = async (token: string): Promise<User> => {
  const response = await fetch(`${API_V1_URL}/auth/me`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  })

  if (!response.ok) {
    throw new Error('获取用户信息失败')
  }

  return response.json()
}

// 修改密码的API调用
export const changePassword = async (
  token: string,
  currentPassword: string,
  newPassword: string
): Promise<void> => {
  const response = await fetch(`${API_V1_URL}/auth/change-password`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      current_password: currentPassword,
      new_password: newPassword,
    }),
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.detail || '修改密码失败')
  }
}
