import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { FileText, ExternalLink, Clock, Star } from 'lucide-react';
import type { SearchResult } from '@/types/morphik';

interface SearchResultCardProps {
  result: SearchResult;
  index: number;
  onSelect?: (result: SearchResult) => void;
  showMetadata?: boolean;
}

function SearchResultCard({
  result,
  index,
  onSelect,
  showMetadata = true
}: SearchResultCardProps) {
  // 格式化相似度分数
  const formatScore = (score: number) => {
    return (score * 100).toFixed(1);
  };

  // 截断文本
  const truncateText = (text: string, maxLength: number = 200) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  // 获取文档类型图标
  const getDocumentIcon = () => {
    const fileType = result.metadata?.file_type || 'unknown';
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-500" />;
      case 'docx':
      case 'doc':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'txt':
        return <FileText className="h-4 w-4 text-gray-500" />;
      default:
        return <FileText className="h-4 w-4 text-muted-foreground" />;
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* 头部信息 */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              {getDocumentIcon()}
              <div className="flex flex-wrap gap-1">
                <Badge variant="secondary" className="text-xs">
                  <Star className="h-3 w-3 mr-1" />
                  {formatScore(result.similarity_score)}%
                </Badge>
                
                {result.document_id && (
                  <Badge variant="outline" className="text-xs">
                    ID: {result.document_id}
                  </Badge>
                )}
                
                {result.chunk_index !== undefined && (
                  <Badge variant="outline" className="text-xs">
                    块: {result.chunk_index}
                  </Badge>
                )}
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onSelect?.(result)}
              className="h-8 w-8 p-0"
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>

          {/* 内容预览 */}
          <div className="space-y-2">
            <div className="text-sm text-foreground leading-relaxed">
              {truncateText(result.content)}
            </div>
            
            {result.content.length > 200 && (
              <Button
                variant="link"
                size="sm"
                onClick={() => onSelect?.(result)}
                className="h-auto p-0 text-xs text-muted-foreground"
              >
                查看完整内容
              </Button>
            )}
          </div>

          {/* 元数据信息 */}
          {result.metadata && (
            <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
              {result.metadata.file_name && (
                <span className="flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  {result.metadata.file_name}
                </span>
              )}
              
              {result.metadata.created_at && (
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {new Date(result.metadata.created_at).toLocaleDateString()}
                </span>
              )}
            </div>
          )}

          {/* 详细元数据 */}
          {showMetadata && result.metadata && Object.keys(result.metadata).length > 0 && (
            <Accordion type="single" collapsible>
              <AccordionItem value={`metadata-${index}`} className="border-none">
                <AccordionTrigger className="py-2 text-xs text-muted-foreground hover:no-underline">
                  查看详细元数据
                </AccordionTrigger>
                <AccordionContent>
                  <div className="bg-muted rounded p-3">
                    <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                      {JSON.stringify(result.metadata, null, 2)}
                    </pre>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default SearchResultCard;
