import {
  Home,
  Brain,
  MessageSquare,
  Sparkles,
  Database,
  CheckSquare,
  ScrollText,
  Shield,
  GraduationCap,
  Building2,
  Globe,
  BarChart3,
  Users,
  Lightbulb,
} from "lucide-react"

import { ThemeToggle } from "@/components/theme-toggle"
import { useAuthStore } from "@/store/auth"
import { usePermissions, MENU_PERMISSIONS } from "@/hooks/usePermissions"
import { useEffect, useState } from "react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from "@/components/ui/sidebar"

// 主要功能菜单项
const mainItems = [
  {
    title: "工作台",
    url: "/",
    icon: Home,
    permission: MENU_PERMISSIONS.DASHBOARD_ACCESS,
  },
  {
    title: "任务管理",
    url: "/tasks",
    icon: CheckSquare,
    permission: MENU_PERMISSIONS.TASKS_ACCESS,
  },
]

// 贸易教官菜单项
const tradeCoachItems = [
  {
    title: "企业知识库",
    url: "/trade-coach/company-knowledge",
    icon: Building2,
    permission: MENU_PERMISSIONS.COMPANY_KNOWLEDGE_ACCESS,
  },
  {
    title: "外贸知识库",
    url: "/trade-coach/trade-knowledge",
    icon: Globe,
    permission: MENU_PERMISSIONS.TRADE_KNOWLEDGE_ACCESS,
  },
  {
    title: "全球市场分析",
    url: "/trade-coach/market-analysis",
    icon: BarChart3,
    permission: MENU_PERMISSIONS.MARKET_ANALYSIS_ACCESS,
  },
  {
    title: "特定群体需求分析",
    url: "/trade-coach/group-analysis",
    icon: Users,
    permission: MENU_PERMISSIONS.GROUP_ANALYSIS_ACCESS,
  },
  {
    title: "销冠实战训练",
    url: "/sales-training",
    icon: GraduationCap,
    permission: MENU_PERMISSIONS.SALES_TRAINING_ACCESS,
  },
  {
    title: "增强版训练演示",
    url: "/enhanced-training-demo",
    icon: Sparkles,
    permission: MENU_PERMISSIONS.ENHANCED_TRAINING_ACCESS,
  },
  {
    title: "个性化学习方案",
    url: "/trade-coach/learning-plan",
    icon: Lightbulb,
    permission: MENU_PERMISSIONS.LEARNING_PLAN_ACCESS,
  },
]

// AI工具菜单项
const aiItems = [
  {
    title: "智能知识库",
    url: "/knowledge-base",
    icon: Brain,
    permission: MENU_PERMISSIONS.AI_KNOWLEDGE_ACCESS,
  },
  {
    title: "智能对话",
    url: "/chat",
    icon: MessageSquare,
    permission: MENU_PERMISSIONS.AI_CHAT_ACCESS,
  },
  {
    title: "话术模板",
    url: "/market-analysis",
    icon: ScrollText,
    permission: MENU_PERMISSIONS.TEMPLATES_ACCESS,
  },
]

// 系统功能菜单项
const systemItems = [
  {
    title: "管理面板",
    url: "/admin",
    icon: Shield,
    permission: MENU_PERMISSIONS.ADMIN_ACCESS,
  },
]

export function AppSidebar() {
  const { user } = useAuthStore()
  const { hasMenuAccess, hasPermission, userPermissions, loading } = usePermissions()
  const [forceUpdate, setForceUpdate] = useState(0)

  // 当权限加载完成时强制重新渲染
  useEffect(() => {
    if (!loading) {
      setForceUpdate(prev => prev + 1)
    }
  }, [loading, userPermissions.roles.length])

  // 调试信息
  console.log('侧边栏权限调试:', {
    user: user ? { id: user.id, username: user.username, is_superuser: user.is_superuser } : null,
    userPermissions,
    loading
  })

  // 过滤有权限访问的菜单项
  const filterMenuItems = (items: any[]) => {
    return items.filter(item => {
      if (item.permission) {
        return hasMenuAccess(item.permission)
      }
      return true
    })
  }

  // 检查是否有贸易教官访问权限
  const hasTradeCoachAccess = hasMenuAccess(MENU_PERMISSIONS.TRADE_COACH_ACCESS)

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="flex items-center justify-between px-2 py-2">
          <div className="flex items-center space-x-2">
            <div className="flex items-center justify-center w-8 h-8 bg-primary rounded-lg">
              <Sparkles className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-sidebar-foreground">智汇兔</h2>
              <p className="text-xs text-sidebar-foreground/70">AI外贸收割机</p>
            </div>
          </div>
          <ThemeToggle />
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* 主要功能 */}
        <SidebarGroup>
          <SidebarGroupLabel>主要功能</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {filterMenuItems(mainItems).map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}

              {/* 贸易教官主页面 - 只有有权限时才显示 */}
              {hasTradeCoachAccess && (
                <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                    <a href="/trade-coach">
                      <GraduationCap />
                      <span>贸易教官</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              )}

              {/* 贸易教官子菜单 - 根据权限过滤显示 */}
              {filterMenuItems(tradeCoachItems).map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild className="pl-8">
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* AI工具 */}
        <SidebarGroup>
          <SidebarGroupLabel>AI工具</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {filterMenuItems(aiItems).map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarSeparator />

        {/* 系统功能 */}
        <SidebarGroup>
          <SidebarGroupLabel>系统功能</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {filterMenuItems(systemItems).map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <a href={item.url}>
                      <item.icon />
                      <span>{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <div className="px-2 py-2">
          <div className="flex items-center space-x-2 text-xs text-sidebar-foreground/70">
            <Database className="h-4 w-4" />
            <span>Morphik Core 已连接</span>
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}
