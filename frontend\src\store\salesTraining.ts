/**
 * 销售训练数据全局状态管理
 * 使用 Zustand 管理国家、产品、职位、训练场景数据的全局状态
 */
import React from 'react'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import type { Country, Product, TrainingScenarioData, TrainingScenarioCreate, TrainingScenarioUpdate } from '@/types/salesTraining'
import salesTrainingService, { SalesTrainingError, scenarioAPI } from '@/services/salesTrainingService'

interface SalesTrainingState {
  // 数据状态
  countries: Country[]
  products: Product[]
  scenarios: TrainingScenarioData[]

  // 加载状态
  isLoading: boolean
  isInitialLoading: boolean
  error: string | null

  // 数据操作
  refreshData: () => Promise<void>
  clearError: () => void

  // 国家操作
  addCountry: (data: Omit<Country, 'id'>) => Promise<boolean>
  updateCountry: (id: number, data: Partial<Omit<Country, 'id'>>) => Promise<boolean>
  deleteCountry: (id: number) => Promise<boolean>

  // 产品操作
  addProduct: (data: Omit<Product, 'id'>) => Promise<boolean>
  updateProduct: (id: number, data: Partial<Omit<Product, 'id'>>) => Promise<boolean>
  deleteProduct: (id: number) => Promise<boolean>

  // 训练场景操作
  addScenario: (data: TrainingScenarioCreate) => Promise<boolean>
  updateScenario: (id: number, data: TrainingScenarioUpdate) => Promise<boolean>
  deleteScenario: (id: number, hardDelete?: boolean) => Promise<boolean>
  refreshScenarios: () => Promise<void>
}

// 错误处理函数
const handleError = (error: unknown, defaultMessage: string): string => {
  const message = error instanceof SalesTrainingError
    ? error.message
    : error instanceof Error
      ? error.message
      : defaultMessage

  toast.error(message)
  console.error('Sales Training Store Error:', error)
  return message
}

export const useSalesTrainingStore = create<SalesTrainingState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      countries: [],
      products: [],

      scenarios: [],
      isLoading: false,
      isInitialLoading: true,
      error: null,

      // 清除错误
      clearError: () => set({ error: null }),

      // 刷新所有数据
      refreshData: async () => {
        try {
          set({ isLoading: true, error: null })

          const data = await salesTrainingService.batch.getAllData()
          const scenarios = await scenarioAPI.getAll()

          set({
            countries: data.countries,
            products: data.products,
            scenarios: scenarios,
            isLoading: false,
            isInitialLoading: false
          })
        } catch (error) {
          const errorMessage = handleError(error, '获取数据失败')
          set({
            error: errorMessage,
            isLoading: false,
            isInitialLoading: false
          })
        }
      },

      // 国家操作
      addCountry: async (data) => {
        try {
          set({ isLoading: true, error: null })

          const newCountry = await salesTrainingService.country.create(data)

          set(state => ({
            countries: [...state.countries, newCountry],
            isLoading: false
          }))

          toast.success(`国家 "${newCountry.name}" 已添加`)
          return true
        } catch (error) {
          const errorMessage = handleError(error, '添加国家失败')
          set({ error: errorMessage, isLoading: false })
          return false
        }
      },

      updateCountry: async (id, data) => {
        try {
          set({ isLoading: true, error: null })

          const updatedCountry = await salesTrainingService.country.update(id, data)

          set(state => ({
            countries: state.countries.map(country =>
              country.id === id ? updatedCountry : country
            ),
            isLoading: false
          }))

          toast.success(`国家 "${updatedCountry.name}" 已更新`)
          return true
        } catch (error) {
          const errorMessage = handleError(error, '更新国家失败')
          set({ error: errorMessage, isLoading: false })
          return false
        }
      },

      deleteCountry: async (id) => {
        try {
          set({ isLoading: true, error: null })

          const { countries } = get()
          const countryToDelete = countries.find(c => c.id === id)

          await salesTrainingService.country.delete(id)

          set(state => ({
            countries: state.countries.filter(country => country.id !== id),
            isLoading: false
          }))

          if (countryToDelete) {
            toast.success(`国家 "${countryToDelete.name}" 已删除`)
          }
          return true
        } catch (error) {
          const errorMessage = handleError(error, '删除国家失败')
          set({ error: errorMessage, isLoading: false })
          return false
        }
      },

      // 产品操作
      addProduct: async (data) => {
        try {
          set({ isLoading: true, error: null })

          const newProduct = await salesTrainingService.product.create(data)

          set(state => ({
            products: [...state.products, newProduct],
            isLoading: false
          }))

          toast.success(`产品 "${newProduct.name}" 已添加`)
          return true
        } catch (error) {
          const errorMessage = handleError(error, '添加产品失败')
          set({ error: errorMessage, isLoading: false })
          return false
        }
      },

      updateProduct: async (id, data) => {
        try {
          set({ isLoading: true, error: null })

          const updatedProduct = await salesTrainingService.product.update(id, data)

          set(state => ({
            products: state.products.map(product =>
              product.id === id ? updatedProduct : product
            ),
            isLoading: false
          }))

          toast.success(`产品 "${updatedProduct.name}" 已更新`)
          return true
        } catch (error) {
          const errorMessage = handleError(error, '更新产品失败')
          set({ error: errorMessage, isLoading: false })
          return false
        }
      },

      deleteProduct: async (id) => {
        try {
          set({ isLoading: true, error: null })

          const { products } = get()
          const productToDelete = products.find(p => p.id === id)

          await salesTrainingService.product.delete(id)

          set(state => ({
            products: state.products.filter(product => product.id !== id),
            isLoading: false
          }))

          if (productToDelete) {
            toast.success(`产品 "${productToDelete.name}" 已删除`)
          }
          return true
        } catch (error) {
          const errorMessage = handleError(error, '删除产品失败')
          set({ error: errorMessage, isLoading: false })
          return false
        }
      },



      // 训练场景操作
      addScenario: async (data: TrainingScenarioCreate) => {
        try {
          set({ isLoading: true, error: null })
          const newScenario = await scenarioAPI.create(data)

          set(state => ({
            scenarios: [...state.scenarios, newScenario],
            isLoading: false
          }))

          toast.success('训练场景创建成功')
          return true
        } catch (error) {
          const errorMessage = handleError(error, '创建训练场景失败')
          set({ error: errorMessage, isLoading: false })
          return false
        }
      },

      updateScenario: async (id: number, data: TrainingScenarioUpdate) => {
        try {
          set({ isLoading: true, error: null })
          const updatedScenario = await scenarioAPI.update(id, data)

          set(state => ({
            scenarios: state.scenarios.map(scenario =>
              scenario.id === id ? updatedScenario : scenario
            ),
            isLoading: false
          }))

          toast.success('训练场景更新成功')
          return true
        } catch (error) {
          const errorMessage = handleError(error, '更新训练场景失败')
          set({ error: errorMessage, isLoading: false })
          return false
        }
      },

      deleteScenario: async (id: number, hardDelete = false) => {
        try {
          set({ isLoading: true, error: null })
          await scenarioAPI.delete(id, hardDelete)

          set(state => ({
            scenarios: state.scenarios.filter(scenario => scenario.id !== id),
            isLoading: false
          }))

          toast.success('训练场景删除成功')
          return true
        } catch (error: any) {
          set({ isLoading: false })
          // 重新抛出错误，让上层组件处理
          throw error
        }
      },

      refreshScenarios: async () => {
        try {
          set({ isLoading: true, error: null })
          const scenarios = await scenarioAPI.getAll()

          set({
            scenarios: scenarios,
            isLoading: false
          })
        } catch (error) {
          const errorMessage = handleError(error, '获取训练场景失败')
          set({ error: errorMessage, isLoading: false })
        }
      },
    }),
    {
      name: 'sales-training-store',
    }
  )
)

// 便捷的选择器hooks
export const useSalesTrainingData = () => useSalesTrainingStore(state => ({
  countries: state.countries,
  products: state.products,
  scenarios: state.scenarios,
  isLoading: state.isLoading,
  isInitialLoading: state.isInitialLoading,
  error: state.error,
  refreshData: state.refreshData,
  clearError: state.clearError,
}))

export const useSalesTrainingActions = () => useSalesTrainingStore(state => ({
  addCountry: state.addCountry,
  updateCountry: state.updateCountry,
  deleteCountry: state.deleteCountry,
  addProduct: state.addProduct,
  updateProduct: state.updateProduct,
  deleteProduct: state.deleteProduct,
  addScenario: state.addScenario,
  updateScenario: state.updateScenario,
  deleteScenario: state.deleteScenario,
  refreshScenarios: state.refreshScenarios,
}))

// 初始化数据的hook
export const useInitializeSalesTrainingData = () => {
  const isInitialLoading = useSalesTrainingStore(state => state.isInitialLoading)
  const countries = useSalesTrainingStore(state => state.countries)
  const refreshData = useSalesTrainingStore(state => state.refreshData)

  // 使用useRef来跟踪是否已经初始化，避免重复调用
  const hasInitializedRef = React.useRef(false)

  React.useEffect(() => {
    if (isInitialLoading && countries.length === 0 && !hasInitializedRef.current) {
      hasInitializedRef.current = true
      refreshData()
    }
  }, [isInitialLoading, countries.length, refreshData])

  return { refreshData, isInitialLoading }
}
