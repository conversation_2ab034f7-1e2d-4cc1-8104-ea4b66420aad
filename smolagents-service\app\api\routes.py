"""
API 路由定义
包含所有的 API 端点实现
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import StreamingResponse

from app.config import get_settings, Settings
from app.core.agent_service import AgentService
from app.core.redis_client import RedisClient
from app.core.concurrent_search import ConcurrentSearchService
from app.core.data_sources import IntelligentSourceDetector, ResultQualityFilter
from app.core.config_validator import (
    TaskType, validate_config,
    get_task_config, get_agent_request_config
)
from app.models.schemas import (
    AgentRequest, AgentResponse, TaskRequest, TaskResponse,
    HealthResponse, ServiceInfo, WebSearchRequest, WebSearchResponse,
    CodeExecutionRequest, CodeExecutionResponse, MetricsResponse,
    StrategyGenerationRequest, StrategyGenerationResponse, SearchStrategy,
    MarketAnalysisRequest, MarketAnalysisResponse, MarketDataSource
)





# 创建路由器
agent_router = APIRouter()
health_router = APIRouter()


def get_agent_service() -> AgentService:
    """获取 Agent 服务实例的依赖注入函数"""
    # 这个函数会在 main.py 中被重写
    raise HTTPException(status_code=503, detail="Agent 服务未初始化")


def get_redis_client() -> RedisClient:
    """获取 Redis 客户端实例的依赖注入函数"""
    # 这个函数会在 main.py 中被重写
    raise HTTPException(status_code=503, detail="Redis 客户端未初始化")


def get_concurrent_search_service() -> ConcurrentSearchService:
    """获取并发搜索服务实例"""
    # 这个函数会在 main.py 中被重写
    raise HTTPException(status_code=503, detail="并发搜索服务未初始化")


def get_source_detector() -> IntelligentSourceDetector:
    """获取智能数据源检测器实例"""
    return IntelligentSourceDetector()


# ==================== 健康检查路由 ====================

@health_router.get("/health", response_model=HealthResponse)
async def health_check(
    agent_service: AgentService = Depends(get_agent_service)
):
    """
    健康检查端点
    检查服务、模型、工具和 Redis 的状态
    """
    try:
        health_data = await agent_service.health_check()

        return HealthResponse(
            service=health_data.get("service", "unknown"),
            model=health_data.get("model", "unknown"),
            tools=health_data.get("tools", "unknown"),
            redis=health_data.get("redis", "unknown"),
            timestamp=datetime.fromisoformat(health_data.get("timestamp", datetime.now().isoformat())),
            version=get_settings().service_version,
            error=health_data.get("error")
        )

    except Exception as e:
        logging.getLogger(__name__).error(f"健康检查失败: {e}")
        raise HTTPException(status_code=503, detail=f"健康检查失败: {e}")


@health_router.get("/info", response_model=ServiceInfo)
async def service_info():
    """
    服务信息端点
    返回服务的基本信息和功能列表
    """
    settings = get_settings()

    return ServiceInfo(
        name=settings.service_name,
        version=settings.service_version,
        description="基于官方 SmoLAgents 库的智能代理微服务",
        status="running",
        endpoints=[
            "/api/v1/health",
            "/api/v1/info",
            "/api/v1/agent/query",
            "/api/v1/agent/task/{task_id}",
            "/api/v1/agent/search",
            "/api/v1/agent/execute",
            "/api/v1/agent/strategies",
            "/api/v1/agent/analysis",
            "/api/v1/metrics"
        ],
        features=[
            "智能代理对话",
            "网络搜索",
            "代码执行",
            "任务管理",
            "结果缓存",
            "健康监控",
            "智能策略生成",
            "市场分析执行"
        ]
    )


# ==================== 智能代理路由 ====================

@agent_router.post("/agent/query", response_model=AgentResponse)
async def query_agent(
    request: AgentRequest,
    background_tasks: BackgroundTasks,
    agent_service: AgentService = Depends(get_agent_service)
):
    """
    智能代理查询端点
    处理用户的自然语言查询请求
    """
    try:
        logger = logging.getLogger(__name__)
        logger.info(f"收到代理查询请求: {request.query[:100]}...")

        # 验证请求
        if not request.query.strip():
            raise HTTPException(status_code=400, detail="查询内容不能为空")

        # 处理查询
        start_time = datetime.now()
        response = await agent_service.process_query(request)
        end_time = datetime.now()

        # 计算执行时间
        execution_time = (end_time - start_time).total_seconds()
        response.execution_time = execution_time

        logger.info(f"代理查询完成: {response.task_id}, 耗时: {execution_time:.2f}s")

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"代理查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询处理失败: {e}")


@agent_router.get("/agent/task/{task_id}", response_model=TaskResponse)
async def get_task_status(
    task_id: str,
    agent_service: AgentService = Depends(get_agent_service)
):
    """
    获取任务状态端点
    查询指定任务的执行状态和结果
    """
    try:
        if not task_id.strip():
            raise HTTPException(status_code=400, detail="任务ID不能为空")

        response = await agent_service.get_task_status(task_id)
        return response

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {e}")


@agent_router.post("/agent/task/{task_id}/cancel", response_model=TaskResponse)
async def cancel_task(
    task_id: str,
    agent_service: AgentService = Depends(get_agent_service)
):
    """
    取消任务端点
    取消指定的正在执行的任务
    """
    try:
        if not task_id.strip():
            raise HTTPException(status_code=400, detail="任务ID不能为空")

        response = await agent_service.cancel_task(task_id)
        return response

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {e}")



@agent_router.post("/agent/search", response_model=WebSearchResponse)
async def web_search(
    request: WebSearchRequest,
    agent_service: AgentService = Depends(get_agent_service)
):
    """
    网络搜索端点
    执行网络搜索并返回结果
    """
    try:
        logger = logging.getLogger(__name__)
        logger.info(f"收到网络搜索请求: {request.query}")

        # 验证请求
        if not request.query.strip():
            raise HTTPException(status_code=400, detail="搜索查询不能为空")

        # 构建搜索查询
        search_query = f"请搜索以下内容并返回相关结果: {request.query}"

        # 创建代理请求
        agent_request = AgentRequest(
            query=search_query,
            max_steps=5,
            use_cache=True,
            enable_web_search=True,
            timeout=request.timeout
        )

        # 执行搜索
        start_time = datetime.now()
        agent_response = await agent_service.process_query(agent_request)
        end_time = datetime.now()

        search_time = (end_time - start_time).total_seconds()

        # 构建搜索响应
        if agent_response.success:
            # 这里简化处理，实际应该解析代理返回的搜索结果
            return WebSearchResponse(
                success=True,
                results=[],  # 实际应该解析搜索结果
                total_results=0,
                search_time=search_time,
                timestamp=datetime.now()
            )
        else:
            return WebSearchResponse(
                success=False,
                results=[],
                total_results=0,
                search_time=search_time,
                error=agent_response.error,
                timestamp=datetime.now()
            )

    except HTTPException:
        raise
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"网络搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {e}")


@agent_router.post("/agent/execute", response_model=CodeExecutionResponse)
async def execute_code(
    request: CodeExecutionRequest,
    agent_service: AgentService = Depends(get_agent_service)
):
    """
    代码执行端点
    执行用户提供的代码并返回结果
    """
    try:
        logger = logging.getLogger(__name__)
        logger.info(f"收到代码执行请求: {request.language}")

        # 验证请求
        if not request.code.strip():
            raise HTTPException(status_code=400, detail="代码内容不能为空")

        if request.language.lower() != "python":
            raise HTTPException(status_code=400, detail="目前只支持 Python 代码执行")

        # 构建执行查询
        execution_query = f"请执行以下 Python 代码并返回结果:\n```python\n{request.code}\n```"

        # 使用统一配置管理器获取代码执行配置
        config_params = get_agent_request_config(
            task_type=TaskType.CODE_EXECUTION,
            user_timeout=request.timeout
        )

        agent_request = AgentRequest(
            query=execution_query,
            **config_params
        )

        # 执行代码
        start_time = datetime.now()
        agent_response = await agent_service.process_query(agent_request)
        end_time = datetime.now()

        execution_time = (end_time - start_time).total_seconds()

        # 构建执行响应
        return CodeExecutionResponse(
            success=agent_response.success,
            output=agent_response.result if agent_response.success else None,
            error=agent_response.error,
            execution_time=execution_time,
            timestamp=datetime.now()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"代码执行失败: {e}")
        raise HTTPException(status_code=500, detail=f"代码执行失败: {e}")


@agent_router.get("/metrics", response_model=MetricsResponse)
async def get_metrics(
    redis_client: RedisClient = Depends(get_redis_client)
):
    """
    指标端点
    返回服务的运行指标和统计信息
    """
    try:
        # 从 Redis 获取指标数据
        metrics_data = await redis_client.hgetall("service:metrics")

        return MetricsResponse(
            total_requests=int(metrics_data.get("total_requests", 0)),
            successful_requests=int(metrics_data.get("successful_requests", 0)),
            failed_requests=int(metrics_data.get("failed_requests", 0)),
            average_response_time=float(metrics_data.get("average_response_time", 0.0)),
            active_tasks=int(metrics_data.get("active_tasks", 0)),
            cache_hit_rate=float(metrics_data.get("cache_hit_rate", 0.0)),
            uptime=float(metrics_data.get("uptime", 0.0)),
            timestamp=datetime.now()
        )

    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"获取指标失败: {e}")
        # 返回默认指标
        return MetricsResponse(timestamp=datetime.now())


@agent_router.post("/agent/strategies", response_model=StrategyGenerationResponse)
async def generate_strategies(
    request: StrategyGenerationRequest,
    agent_service: AgentService = Depends(get_agent_service)
):
    """
    智能策略生成端点
    基于产品和查询内容动态生成搜索策略
    """
    try:
        logger = logging.getLogger(__name__)
        logger.info(f"收到策略生成请求: {request.product} - {request.query}")

        # 调用共享的策略生成逻辑
        strategies, generation_time = await _generate_search_strategies(request, agent_service)

        logger.info(f"策略生成完成: 生成了 {len(strategies)} 个策略，耗时: {generation_time:.2f}s")

        return StrategyGenerationResponse(
            success=True,
            strategies=strategies,
            total_count=len(strategies),
            generation_time=generation_time
        )

    except HTTPException:
        raise
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"策略生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"策略生成过程中发生错误: {str(e)}")


async def _generate_search_strategies(
    request: StrategyGenerationRequest,
    agent_service: AgentService
) -> tuple[list, float]:
    """
    共享的策略生成逻辑
    返回: (strategies, generation_time)
    """
    start_time = datetime.now()

    # 获取完整分析主题
    full_topic = getattr(request, 'full_topic', None)
    if not full_topic:
        full_topic = f"{request.product} {request.query}"
    full_topic = full_topic.strip()

    # 动态生成时间周期（如果未提供）
    time_period = request.time_period
    if not time_period:
        current_year = datetime.now().year
        time_period = f"{current_year}-{current_year + 1}"

    # 根据分析类型构建不同的策略生成提示词
    analysis_type = getattr(request, 'analysis_type', 'market_analysis')

    if analysis_type == 'group_analysis':
        # 🔥 修正版：特定群体需求分析的平衡策略生成提示词
        strategy_prompt = f"""
请为以下特定群体需求分析主题创新性地生成 {request.strategy_count} 个独特且精准的搜索策略。
每个策略都应该专注于特定群体的真实需求，而非整体市场分析：

🎯 完整分析主题: "{full_topic}"
📊 产品类型: {request.product}
🔍 关键词: {request.query}
🌍 分析地区: {request.region}
📅 时间周期: {time_period}

🎯 核心要求：
1. 策略必须专注于特定群体的需求分析，避免整体市场分析
2. 从群体需求角度生成多样化的分析策略，重点关注：
   - 该群体对产品的具体功能需求
   - 该群体的使用场景和痛点
   - 该群体的偏好和期望
   - 该群体未来可能产生的新需求

3. 每个策略应该：
   - 针对特定群体的独特需求特征
   - 体现群体的真实使用场景和痛点
   - 从不同需求维度深入分析（如功能需求、使用场景、痛点、偏好、未来需求等）
   - 具有实际的群体洞察价值

4. 搜索查询要求：
   - 优先使用中文搜索查询，以获得更准确的本土化数据
   - 包含具体的产品名称和群体特征
   - 针对性强，能获取该群体的真实需求数据
   - 避免过于宽泛的市场查询，专注群体需求
   - 如需英文查询，请同时提供中文翻译

5. 策略分类要求：
   - 使用中文分类名称，便于后续处理和展示
   - 建议分类包括但不限于：功能需求、使用场景、痛点分析、偏好研究、未来需求、行为模式、人群洞察等
   - 分类要具体明确，体现群体需求的独特性
   - 优先级1为核心需求策略，2为重要需求策略，3为补充需求策略

6. 输出语言要求：
   - 输出语言：{request.language or 'zh-CN'}
   - 所有策略描述必须使用中文
   - 搜索查询优先使用中文
   - 分类名称必须使用中文

请严格按照以下JSON格式返回策略列表：
[
  {{
    "id": "strategy_1",
    "query": "具体的中文搜索查询（专注群体需求）",
    "category": "具体需求分析分类（中文）",
    "priority": 1,
    "description": "策略的具体描述和预期获取的群体需求数据类型（中文）"
  }}
]

重要：只返回JSON数组，不要任何其他文字说明。所有内容必须使用中文。专注于群体需求分析，避免整体市场分析。
"""
    else:
        # 🔥 修正版：全面市场分析的平衡策略生成提示词
        strategy_prompt = f"""
请为以下具体的市场分析主题创新性地生成 {request.strategy_count} 个独特且精准的搜索策略。
每个策略都应该从不同角度切入，体现产品的独特性和查询的针对性：

🎯 完整分析主题: "{full_topic}"
📊 产品类型: {request.product}
🔍 关键词: {request.query}
🌍 分析地区: {request.region}
📅 时间周期: {time_period}

🎯 核心要求：
1. 策略必须紧密围绕"{full_topic}"这个具体主题进行设计
2. 根据产品特性和查询内容，从不同商业角度生成多样化的深度分析策略，重点关注：
   - 市场规模和增长趋势分析
   - 竞争格局和主要参与者
   - 消费者需求和行为模式
   - 技术创新和发展趋势
   - 区域市场特征和机会

3. 每个策略应该：
   - 针对具体产品的独特市场特征
   - 体现查询关键词的核心关注点
   - 从不同维度深入分析（如市场、技术、消费者、竞争等）
   - 具有实际的商业价值和可操作性
   - 能够获取详细的市场洞察和数据支撑

4. 搜索查询要求：
   - 优先使用中文搜索查询，以获得更准确的本土化数据
   - 包含具体的产品名称和关键词组合
   - 针对性强，能获取真实可靠的市场数据
   - 避免过于宽泛的通用查询，确保数据精准性
   - 如需英文查询，请同时提供中文翻译

5. 策略分类要求：
   - 使用中文分类名称，便于后续处理和展示
   - 建议分类包括但不限于：市场趋势、竞争格局、消费者需求、技术创新、区域市场、价格趋势、增长预测、品牌分析、供应链分析等
   - 分类要具体明确，体现产品和查询的独特性
   - 优先级1为核心市场策略，2为重要分析策略，3为补充洞察策略

6. 输出语言要求：
   - 输出语言：zh-CN
   - 所有策略描述必须使用中文
   - 搜索查询优先使用中文
   - 分类名称必须使用中文

请严格按照以下JSON格式返回策略列表：
[
  {{
    "id": "strategy_1",
    "query": "具体的中文搜索查询（专注市场分析）",
    "category": "具体策略分类（中文）",
    "priority": 1,
    "description": "策略的具体描述和预期获取的市场数据类型（中文）"
  }}
]

重要：只返回JSON数组，不要任何其他文字说明。所有内容必须使用中文。确保生成深度的市场分析策略。
"""

    # 使用统一配置管理器获取策略生成配置
    config_params = get_agent_request_config(
        task_type=TaskType.STRATEGY_GENERATION,
        strategy_count=request.strategy_count
    )

    # 根据策略数量动态调整超时时间
    strategy_timeout = max(config_params['timeout'], request.strategy_count * 20)
    config_params['timeout'] = strategy_timeout

    agent_request = AgentRequest(
        query=strategy_prompt,
        **config_params
    )

    # 调用AI生成策略
    response = await agent_service.process_query(agent_request)

    if not response.success:
        raise HTTPException(status_code=500, detail=f"策略生成失败: {response.error}")

    # 检查响应结果
    if not response.result:
        raise HTTPException(status_code=500, detail="AI响应为空，无法生成策略")

    # 解析AI响应
    strategies = []
    try:
        import json
        import re

        # 提取JSON内容
        result_text = str(response.result).strip()
        logger = logging.getLogger(__name__)
        logger.info(f"AI原始响应长度: {len(result_text)}")

        if not result_text:
            raise HTTPException(status_code=500, detail="AI响应内容为空")

        # 尝试多种JSON提取方式
        json_patterns = [
            r'\[.*\]',  # 标准JSON数组
            r'```json\s*(\[.*?\])\s*```',  # Markdown代码块
            r'```\s*(\[.*?\])\s*```',  # 普通代码块
        ]

        strategies_data = None
        for i, pattern in enumerate(json_patterns):
            json_match = re.search(pattern, result_text, re.DOTALL)
            if json_match:
                try:
                    json_text = json_match.group(1) if json_match.groups() else json_match.group()
                    # 清理可能的格式问题
                    json_text = json_text.strip()
                    strategies_data = json.loads(json_text)
                    logger.info(f"JSON解析成功，策略数量: {len(strategies_data)}")
                    break
                except json.JSONDecodeError as json_error:
                    logger.warning(f"JSON模式 {i+1} 解析失败: {json_error}")
                    continue

        if strategies_data:
            for i, strategy_data in enumerate(strategies_data):
                strategy = SearchStrategy(
                    id=strategy_data.get('id', f'strategy_{i+1}'),
                    query=strategy_data.get('query', ''),
                    category=strategy_data.get('category', '未分类'),
                    priority=strategy_data.get('priority', 2),
                    description=strategy_data.get('description', '')
                )
                strategies.append(strategy)
        else:
            logger.error("所有JSON解析模式都失败了")

    except Exception as parse_error:
        logger = logging.getLogger(__name__)
        logger.error(f"解析AI响应失败: {parse_error}")
        logger.error(f"AI原始响应: {response.result}")
        raise HTTPException(
            status_code=500,
            detail=f"AI策略生成响应解析失败，请稍后重试。错误详情: {str(parse_error)}"
        )

    # 验证策略质量
    if not strategies:
        raise HTTPException(
            status_code=500,
            detail="AI未生成任何有效策略，请稍后重试或简化查询内容"
        )

    # 检查策略是否包含必要信息
    valid_strategies = []
    for strategy in strategies:
        if strategy.query and strategy.query.strip():
            valid_strategies.append(strategy)

    if not valid_strategies:
        raise HTTPException(
            status_code=500,
            detail="AI生成的策略缺少有效的搜索查询，请稍后重试"
        )

    strategies = valid_strategies

    end_time = datetime.now()
    generation_time = (end_time - start_time).total_seconds()

    return strategies, generation_time


@agent_router.post("/agent/market-analysis", response_model=MarketAnalysisResponse)
async def unified_market_analysis(
    request: MarketAnalysisRequest,
    agent_service: AgentService = Depends(get_agent_service),
    source_detector: IntelligentSourceDetector = Depends(get_source_detector)
):
    """
    统一市场分析端点（一键完整分析）
    自动执行两个步骤：
    1. 如果request.strategies为空，则先生成策略
    2. 基于策略执行完整的市场分析（使用与execute_market_analysis相同的流程）
    """
    try:
        logger = logging.getLogger(__name__)
        logger.info(f"收到一键完整市场分析请求: {request.product} - {request.query}")

        start_time = datetime.now()
        strategies = request.strategies

        # 第一步：如果没有提供策略，先生成策略
        if not strategies:
            logger.info("🎯 第一步：开始生成搜索策略...")

            # 从请求中获取策略数量，如果没有则默认为4
            strategy_count = getattr(request, 'strategy_count', 4)
            logger.info(f"将生成 {strategy_count} 个搜索策略")

            # 构建策略生成请求
            strategy_request = StrategyGenerationRequest(
                product=request.product,
                query=request.query,
                analysis_type=request.analysis_type,
                strategy_count=strategy_count,  # 使用动态策略数量
                region=request.region,
                time_period=request.time_period
            )

            # 生成策略
            strategies, strategy_generation_time = await _generate_search_strategies(
                strategy_request, agent_service
            )
            logger.info(f"✅ 策略生成完成，生成了 {len(strategies)} 个策略，耗时: {strategy_generation_time:.2f}s")
        else:
            logger.info(f"📋 使用提供的 {len(strategies)} 个策略")

        # 第二步：基于策略执行完整的市场分析（使用与execute_market_analysis相同的流程）
        logger.info("🚀 第二步：开始执行完整市场分析...")

        # 动态生成时间周期（如果未提供）
        time_period = request.time_period
        if not time_period:
            current_year = datetime.now().year
            time_period = f"{current_year}-{current_year + 1}"

        # 根据分析类型构建不同的分析查询
        analysis_type = getattr(request, 'analysis_type', 'market_analysis')

        if analysis_type == 'group_analysis':
            # 从query中解析群体和场景信息来生成正确的标题
            query_parts = request.query.split()
            # 尝试从query中提取信息，格式通常是：产品 国家 群体 场景 群体需求分析
            if len(query_parts) >= 5 and "群体需求分析" in request.query:
                # 格式：沙滩包 美国 女性 办公 群体需求分析
                target_group = query_parts[2] if len(query_parts) > 2 else "目标群体"
                scenario = query_parts[3] if len(query_parts) > 3 else "使用场景"
                expected_title = f"{target_group}对{request.product}在{scenario}场景的需求分析报告"
            else:
                expected_title = f"特定群体对{request.product}的需求分析报告"

            # 🔥 修正版：特定群体需求分析的深度分析执行提示词
            analysis_query = f"""
请执行以下特定群体需求分析任务，生成深度的中文分析报告：

🎯 分析目标: {request.product} - {request.query}
🌍 分析地区: {request.region}
📅 时间周期: {time_period}

📋 执行策略:
{chr(10).join([f"{i+1}. [{strategy.category}] {strategy.description}" for i, strategy in enumerate(strategies)])}

🔍 具体搜索查询:
{chr(10).join([f"- {strategy.query}" for strategy in strategies])}

🎯 重要要求：
1. 必须使用中文输出所有分析内容
2. 专注于特定群体的需求分析，避免整体市场分析
3. 基于真实数据进行深度分析，避免使用模拟数据
4. 每个策略维度都要有详细的分析和洞察
5. **报告标题必须是：{expected_title}**

请按照以下步骤执行深度群体需求分析：

1. 🔍 数据收集阶段
   - 根据每个策略的搜索查询，使用网络搜索工具获取该特定群体的真实需求数据
   - 重点关注该群体的使用场景、痛点、偏好等需求信息
   - 避免收集泛泛而谈的整体市场数据，专注群体特征
   - 确保数据来源权威可靠，收集足够的数据支撑分析结论

2. 📊 数据分析阶段
   - 基于收集的真实数据进行群体需求深度分析
   - 严格按照群体需求维度组织分析内容
   - 专注于该群体的独特需求特征，避免整体市场分析
   - 深入挖掘该群体的需求洞察和行为模式

3. 📝 报告生成阶段
   - 生成专门针对该特定群体的详细需求分析报告，标题为：{expected_title}
   - 每个策略维度对应一个群体需求分析章节
   - 包含群体特征、功能需求、使用场景、痛点、偏好等内容
   - 提供针对该群体的具体建议和洞察
   - 确保报告结构完整，内容丰富，分析深入

🎯 输出要求：
- 必须使用中文输出所有内容
- 报告标题必须是：{expected_title}
- 生成结构化的详细分析报告
- 每个维度都要有深入的分析和数据支撑
- 提供具体的群体洞察和建议
- 避免简化或概括性的分析

请开始执行群体需求分析，确保专注于特定群体的真实需求，生成高质量的中文分析报告。
"""
        else:
            # 🔥 修正版：全面市场分析的深度分析执行提示词
            analysis_query = f"""
请执行以下全面市场分析任务，生成深度的中文市场分析报告：

🎯 分析目标: {request.product} - {request.query}
🌍 分析地区: {request.region}
📅 时间周期: {time_period}

📋 执行策略:
{chr(10).join([f"{i+1}. [{strategy.category}] {strategy.description}" for i, strategy in enumerate(strategies)])}

🔍 具体搜索查询:
{chr(10).join([f"- {strategy.query}" for strategy in strategies])}

🎯 重要要求：
1. 必须使用中文输出所有分析内容
2. 进行全面深度的市场分析，避免内容简化
3. 基于真实数据进行详细分析，避免使用模拟数据
4. 每个策略维度都要有深入的分析和市场洞察

请按照以下步骤执行深度市场分析：

1. 🔍 数据收集阶段
   - 根据每个策略的搜索查询，使用网络搜索工具获取真实市场数据
   - 确保数据来源权威可靠（如Statista、IBISWorld、Trading Economics等）
   - 收集足够的数据支撑分析结论
   - 重点关注市场规模、增长趋势、竞争格局等关键数据

2. 📊 数据分析阶段
   - 基于收集的真实数据进行深度市场分析
   - 严格按照策略维度组织分析内容
   - 避免使用模拟或假设数据
   - 深入挖掘市场趋势、竞争态势和发展机会

3. 📝 报告生成阶段
   - 生成结构化的详细市场分析报告
   - 每个策略维度对应一个深入的分析章节
   - 包含市场规模、竞争格局、消费者需求、技术趋势等内容
   - 提供具体的市场洞察、数据来源和商业建议
   - 确保报告结构完整，内容丰富，分析深入

🎯 输出要求：
- 必须使用中文输出所有内容
- 生成结构化的详细市场分析报告
- 每个维度都要有深入的分析和数据支撑
- 提供具体的市场洞察和商业建议
- 避免简化或概括性的分析

请开始执行全面市场分析，确保基于真实数据生成高质量的中文市场分析报告。
"""

        # 创建代理请求（使用统一配置管理器）
        config_params = get_agent_request_config(
            task_type=TaskType.MARKET_ANALYSIS,
            user_max_steps=request.max_steps,
            user_timeout=request.timeout
        )

        agent_request = AgentRequest(
            query=analysis_query,
            **config_params
        )

        # 执行完整的市场分析
        logger.info(f"🤖 开始执行完整市场分析, 使用 {len(strategies)} 个策略, 最大步骤数: {agent_request.max_steps}")
        agent_response = await agent_service.process_query(agent_request)

        # 计算总执行时间
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        if agent_response.success:
            # 解析分析结果
            analysis_report = agent_response.result if agent_response.result else "分析报告生成失败"

            # 构建数据源信息（从分析结果中提取，使用与execute_market_analysis相同的逻辑）
            data_sources = []
            if "statista" in analysis_report.lower():
                data_sources.append(MarketDataSource(
                    title="Statista Market Research",
                    url="https://www.statista.com",
                    content="市场统计数据",
                    data_type="market_statistics"
                ))
            if "ibisworld" in analysis_report.lower():
                data_sources.append(MarketDataSource(
                    title="IBISWorld Industry Analysis",
                    url="https://www.ibisworld.com",
                    content="行业分析报告",
                    data_type="industry_analysis"
                ))

            # 使用智能数据源检测器提取更多数据源
            additional_sources = source_detector.extract_data_sources(analysis_report)
            data_sources.extend(additional_sources)

            logger.info(f"✅ 一键完整市场分析成功: 执行了 {len(strategies)} 个策略，耗时: {execution_time:.2f}s")

            return MarketAnalysisResponse(
                success=True,
                analysis_report=analysis_report,
                data_sources=data_sources,
                strategies_executed=len(strategies),
                total_data_points=len(data_sources),
                execution_time=execution_time,
                generated_strategies=strategies  # 返回使用的策略
            )
        else:
            error_msg = agent_response.error or "一键完整市场分析执行失败"
            logger.error(f"一键完整市场分析失败: {error_msg}")

            return MarketAnalysisResponse(
                success=False,
                error=error_msg,
                strategies_executed=0,
                total_data_points=0,
                execution_time=execution_time
            )

    except HTTPException:
        raise
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"一键完整市场分析执行失败: {e}")
        raise HTTPException(status_code=500, detail=f"一键完整市场分析执行过程中发生错误: {str(e)}")


@agent_router.post("/agent/analysis", response_model=MarketAnalysisResponse)
async def execute_market_analysis(
    request: MarketAnalysisRequest,
    agent_service: AgentService = Depends(get_agent_service)
):
    """
    市场分析执行端点
    基于策略执行完整的市场分析流程
    """
    try:
        logger = logging.getLogger(__name__)
        logger.info(f"收到市场分析请求: {request.product} - {request.query}")

        start_time = datetime.now()

        # 动态生成时间周期（如果未提供）
        time_period = request.time_period
        if not time_period:
            current_year = datetime.now().year
            time_period = f"{current_year}-{current_year + 1}"

        # 根据分析类型构建不同的分析查询
        analysis_type = getattr(request, 'analysis_type', 'market_analysis')

        if analysis_type == 'group_analysis':
            # 从query中解析群体和场景信息来生成正确的标题
            query_parts = request.query.split()
            # 尝试从query中提取信息，格式通常是：产品 国家 群体 场景 群体需求分析
            if len(query_parts) >= 5 and "群体需求分析" in request.query:
                # 格式：沙滩包 美国 女性 办公 群体需求分析
                target_group = query_parts[2] if len(query_parts) > 2 else "目标群体"
                scenario = query_parts[3] if len(query_parts) > 3 else "使用场景"
                expected_title = f"{target_group}对{request.product}在{scenario}场景的需求分析报告"
            else:
                expected_title = f"特定群体对{request.product}的需求分析报告"

            # 🔥 优化版：特定群体需求分析的专注分析执行提示词（分步执行版）
            analysis_query = f"""
🎯 专注任务：基于用户精心选择的策略执行特定群体需求分析

你现在是一个专业的群体需求分析专家，你的唯一任务是基于用户精心选择和可能优化过的搜索策略执行深度的群体需求分析。
这些策略已经经过用户的审核和可能的优化，质量很高，请专注于执行分析。

📊 分析目标: {request.product} - {request.query}
🌍 分析地区: {request.region}
📅 时间周期: {time_period}

📋 用户精心选择的高质量策略（请严格按照这些策略执行）:
{chr(10).join([f"{i+1}. [{strategy.category}] {strategy.description}" for i, strategy in enumerate(request.strategies)])}

🔍 具体搜索查询（请逐一执行）:
{chr(10).join([f"- {strategy.query}" for strategy in request.strategies])}

🎯 专注分析执行要求：
1. 【专注度要求】你的唯一职责是基于上述用户选择的策略执行深度的群体需求分析
2. 【策略质量优势】这些策略已经过用户审核，质量很高，请充分利用
3. 【群体需求导向】所有分析必须专注于特定群体的真实需求，避免整体市场分析
4. **【标题要求】报告标题必须是：{expected_title}**

请按照以下步骤专注执行群体需求分析：

1. 🔍 专注数据收集阶段
   - 严格按照每个策略的搜索查询，使用网络搜索工具获取该特定群体的真实需求数据
   - 重点关注该群体的使用场景、痛点、偏好、行为模式等需求信息
   - 避免收集泛泛而谈的整体市场数据，专注群体特征和需求
   - 确保每个策略都能获取到有价值的群体需求数据

2. 📊 专注数据分析阶段
   - 基于收集的真实数据进行群体需求深度分析
   - 严格按照群体需求维度组织分析内容
   - 专注于该群体的独特需求特征，避免整体市场分析
   - 深入挖掘该群体的需求洞察和行为模式

3. 📝 专注报告生成阶段
   - 生成专门针对该特定群体的需求分析报告，标题为：{expected_title}
   - 每个策略维度对应一个群体需求分析章节
   - 包含群体特征、功能需求、使用场景、痛点、偏好、未来需求等内容
   - 提供针对该群体的具体建议和洞察

🎯 分析质量要求：
- 基于真实数据进行分析，避免使用模拟或假设数据
- 专注于该群体的独特需求特征，避免泛泛而谈
- 每个策略维度都要有深入的分析和洞察
- 提供具体的数据支撑和来源
- **报告标题必须是：{expected_title}**

请开始专注执行群体需求分析，确保专注于特定群体的真实需求，避免整体市场分析。
基于用户精心选择的高质量策略生成专业的群体需求分析报告。
"""
        else:
            # 🔥 修正版：全面市场分析的深度分析执行提示词（分步执行版）
            analysis_query = f"""
请执行以下全面市场分析任务，生成深度的中文市场分析报告：

🎯 分析目标: {request.product} - {request.query}
🌍 分析地区: {request.region}
📅 时间周期: {time_period}

📋 执行策略:
{chr(10).join([f"{i+1}. [{strategy.category}] {strategy.description}" for i, strategy in enumerate(request.strategies)])}

🔍 具体搜索查询:
{chr(10).join([f"- {strategy.query}" for strategy in request.strategies])}

🎯 重要要求：
1. 必须使用中文输出所有分析内容
2. 进行全面深度的市场分析，避免内容简化
3. 基于真实数据进行详细分析，避免使用模拟数据
4. 每个策略维度都要有深入的分析和市场洞察

请按照以下步骤执行深度市场分析：

1. 🔍 数据收集阶段
   - 根据每个策略的搜索查询，使用网络搜索工具获取真实市场数据
   - 确保数据来源权威可靠（如Statista、IBISWorld、Trading Economics等）
   - 收集足够的数据支撑分析结论
   - 重点关注市场规模、增长趋势、竞争格局等关键数据

2. 📊 数据分析阶段
   - 基于收集的真实数据进行深度市场分析
   - 严格按照策略维度组织分析内容
   - 避免使用模拟或假设数据
   - 深入挖掘市场趋势、竞争态势和发展机会

3. 📝 报告生成阶段
   - 生成结构化的详细市场分析报告
   - 每个策略维度对应一个深入的分析章节
   - 包含市场规模、竞争格局、消费者需求、技术趋势等内容
   - 提供具体的市场洞察、数据来源和商业建议
   - 确保报告结构完整，内容丰富，分析深入

🎯 输出要求：
- 必须使用中文输出所有内容
- 生成结构化的详细市场分析报告
- 每个维度都要有深入的分析和数据支撑
- 提供具体的市场洞察和商业建议
- 避免简化或概括性的分析

请开始执行全面市场分析，确保基于真实数据生成高质量的中文市场分析报告。
"""

        # 创建代理请求（使用统一配置管理器）
        config_params = get_agent_request_config(
            task_type=TaskType.MARKET_ANALYSIS,
            user_max_steps=request.max_steps,
            user_timeout=request.timeout
        )

        agent_request = AgentRequest(
            query=analysis_query,
            **config_params
        )

        # 执行分析
        agent_response = await agent_service.process_query(agent_request)

        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        if agent_response.success:
            # 解析分析结果
            analysis_report = agent_response.result if agent_response.result else "分析报告生成失败"

            # 构建数据源信息（从分析结果中提取）
            data_sources = []
            if "statista" in analysis_report.lower():
                data_sources.append(MarketDataSource(
                    title="Statista Market Research",
                    url="https://www.statista.com",
                    content="市场统计数据",
                    data_type="market_statistics"
                ))
            if "ibisworld" in analysis_report.lower():
                data_sources.append(MarketDataSource(
                    title="IBISWorld Industry Analysis",
                    url="https://www.ibisworld.com",
                    content="行业分析报告",
                    data_type="industry_analysis"
                ))

            logger.info(f"市场分析完成: 执行了 {len(request.strategies)} 个策略，耗时: {execution_time:.2f}s")

            return MarketAnalysisResponse(
                success=True,
                analysis_report=analysis_report,
                data_sources=data_sources,
                strategies_executed=len(request.strategies),
                total_data_points=len(data_sources),
                execution_time=execution_time
            )
        else:
            error_msg = agent_response.error or "市场分析执行失败"
            logger.error(f"市场分析失败: {error_msg}")

            return MarketAnalysisResponse(
                success=False,
                error=error_msg,
                strategies_executed=0,
                total_data_points=0,
                execution_time=execution_time
            )

    except HTTPException:
        raise
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"市场分析执行失败: {e}")
        raise HTTPException(status_code=500, detail=f"市场分析执行过程中发生错误: {str(e)}")



