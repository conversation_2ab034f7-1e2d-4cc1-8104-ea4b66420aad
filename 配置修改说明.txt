1. 登录与不登录切换 - 开发模式管理

方案1：使用管理脚本（推荐）
# 启用开发模式（无需登录）
.\manage-zht-system.ps1 --dev-mode on

# 禁用开发模式（需要登录）
.\manage-zht-system.ps1 --dev-mode off

-----------------------------
2. zht_smolagents_0624 切换模型
修改docker-compose.yml环境变量
# 切换到Qwen在线模型
- MODEL_PROVIDER=qwen

# 切换回Ollama本地模型  
- MODEL_PROVIDER=ollama

然后重启服务

-------------------------------
配置 config_validator.py 
修改 max_steps 步数，实现检索内容丰富程度的控制

class UnifiedConfigManager:
    """统一配置管理器"""

    # 基础任务配置 - 所有配置的唯一来源
    BASE_CONFIGS = {
        TaskType.STRATEGY_GENERATION: TaskConfig(
            max_steps=5,
            timeout=90,
            description="策略生成",
            use_cache=False,
            enable_web_search=False
        ),
        TaskType.SINGLE_SEARCH: TaskConfig(
            max_steps=8,
            timeout=160,
            description="单次搜索",
            use_cache=True,
            enable_web_search=True
        ),
        TaskType.MARKET_ANALYSIS: TaskConfig(
            max_steps=12,
            timeout=360,
            description="市场分析",
            use_cache=False,
            enable_web_search=True
        ),
        TaskType.CODE_EXECUTION: TaskConfig(
            max_steps=3,
            timeout=90,
            description="代码执行",
            use_cache=False,
            enable_web_search=False
        ),
        TaskType.FINAL_ANALYSIS: TaskConfig(
            max_steps=12,
            timeout=240,
            description="最终分析",
            use_cache=True,
            enable_web_search=False
        ),
        TaskType.AGENT_DEFAULT: TaskConfig(
            max_steps=8,
            timeout=120,
            description="代理默认",
            use_cache=True,
            enable_web_search=True
        )
    }


# 重置数据库命令
python reset_db.py


