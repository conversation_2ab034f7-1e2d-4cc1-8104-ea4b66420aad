import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export type Theme = 'light' | 'dark' | 'system'

interface ThemeStore {
  theme: Theme
  setTheme: (theme: Theme) => void
  actualTheme: 'light' | 'dark'
  updateActualTheme: () => void
}

const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light'
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}

const applyTheme = (theme: 'light' | 'dark') => {
  const root = window.document.documentElement
  root.classList.remove('light', 'dark')
  root.classList.add(theme)
}

export const useThemeStore = create<ThemeStore>()(
  persist(
    (set, get) => ({
      theme: 'system',
      actualTheme: getSystemTheme(),
      
      setTheme: (theme: Theme) => {
        set({ theme })
        
        let actualTheme: 'light' | 'dark'
        if (theme === 'system') {
          actualTheme = getSystemTheme()
        } else {
          actualTheme = theme
        }
        
        set({ actualTheme })
        applyTheme(actualTheme)
      },
      
      updateActualTheme: () => {
        const { theme } = get()
        if (theme === 'system') {
          const systemTheme = getSystemTheme()
          set({ actualTheme: systemTheme })
          applyTheme(systemTheme)
        }
      },
    }),
    {
      name: 'theme-storage',
      onRehydrateStorage: () => (state) => {
        if (state) {
          // 初始化时应用主题
          let actualTheme: 'light' | 'dark'
          if (state.theme === 'system') {
            actualTheme = getSystemTheme()
          } else {
            actualTheme = state.theme
          }
          
          state.actualTheme = actualTheme
          applyTheme(actualTheme)
          
          // 监听系统主题变化
          if (typeof window !== 'undefined') {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
            const handleChange = () => {
              state.updateActualTheme()
            }
            
            mediaQuery.addEventListener('change', handleChange)
            
            // 清理函数
            return () => {
              mediaQuery.removeEventListener('change', handleChange)
            }
          }
        }
      },
    }
  )
)
