import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import { ChevronDown, Trash2, Plus } from 'lucide-react'
import type { ProductOption } from '@/hooks/useProductOptionsBase'

interface ProductSelectorProps {
  productOptions: ProductOption[]
  selectedProduct: string
  onProductChange: (value: string) => void
  onDeleteProduct: (productId: string, productLabel: string) => void
  onAddProduct: () => void
  isLoading?: boolean
  placeholder?: string
  className?: string
}

// 通用选择器选项接口
export interface SelectorOption {
  id: string
  value: string
  label: string
  icon?: React.ComponentType<{ className?: string }>
  iconName?: string
  createdAt?: string
}

// 通用选择器组件属性接口
export interface GenericSelectorProps {
  options: SelectorOption[]
  selectedValue: string
  onValueChange: (value: string) => void
  onDeleteOption: (optionId: string, optionLabel: string) => void
  onAddOption: () => void
  isLoading?: boolean
  placeholder?: string
  className?: string
}

export const ProductSelector: React.FC<ProductSelectorProps> = ({
  productOptions,
  selectedProduct,
  onProductChange,
  onDeleteProduct,
  onAddProduct,
  isLoading = false,
  placeholder = "请选择要分析的产品...",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false)

  // 获取当前选中的产品
  const selectedOption = productOptions.find(option => option.value === selectedProduct)

  const handleProductSelect = (value: string) => {
    onProductChange(value)
    setIsOpen(false)
  }

  const handleAddClick = () => {
    onAddProduct()
    setIsOpen(false)
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full h-10 lg:h-12 text-sm lg:text-base justify-between"
            disabled={isLoading}
          >
            <div className="flex items-center space-x-2">
              {selectedOption ? (
                <>
                  {renderIcon(selectedOption.icon)}
                  <span>{selectedOption.label}</span>
                </>
              ) : (
                <span className="text-muted-foreground">{placeholder}</span>
              )}
            </div>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent
          className="w-full min-w-[var(--radix-dropdown-menu-trigger-width)] max-h-80 overflow-y-auto"
          align="start"
          side="bottom"
          sideOffset={4}
        >
          {isLoading ? (
            <DropdownMenuItem disabled>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                <span>加载中...</span>
              </div>
            </DropdownMenuItem>
          ) : (
            <>
              {/* 产品选项列表 */}
              {productOptions.length === 0 ? (
                <DropdownMenuItem disabled className="py-3 text-center text-muted-foreground">
                  暂无产品选项，请添加新产品
                </DropdownMenuItem>
              ) : (
                productOptions.map((product) => (
                <DropdownMenuItem
                  key={product.id}
                  className="flex items-center justify-between py-3 cursor-pointer group"
                  onSelect={() => handleProductSelect(product.value)}
                >
                  <div className="flex items-center space-x-2">
                    {renderIcon(product.icon)}
                    <span>{product.label}</span>
                  </div>

                  {/* 删除按钮 - 当只有一个选项时禁用 */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-opacity disabled:opacity-30"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      onDeleteProduct(product.id, product.label)
                    }}
                    disabled={productOptions.length <= 1}
                    title={productOptions.length <= 1 ? "至少需要保留一个产品选项" : "删除此产品选项"}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </DropdownMenuItem>
                ))
              )}

              {/* 分隔线 */}
              {productOptions.length > 0 && <DropdownMenuSeparator />}
              
              {/* 添加新产品按钮 */}
              <DropdownMenuItem
                className="flex items-center space-x-2 py-3 cursor-pointer text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                onSelect={(e) => {
                  e.preventDefault()
                  handleAddClick()
                }}
              >
                <Plus className="w-4 h-4" />
                <span>添加新产品</span>
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

// 安全的图标渲染函数
const renderIcon = (IconComponent: React.ComponentType<{ className?: string }> | undefined, className: string = "w-4 h-4") => {
  if (!IconComponent) return null
  try {
    return React.createElement(IconComponent, { className })
  } catch (error) {
    console.error('图标渲染错误:', error)
    return null
  }
}

// 通用选择器组件
export const GenericSelector: React.FC<GenericSelectorProps> = ({
  options,
  selectedValue,
  onValueChange,
  onDeleteOption,
  onAddOption,
  isLoading = false,
  placeholder = "请选择选项...",
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false)

  // 获取当前选中的选项
  const selectedOption = options.find(option => option.value === selectedValue)

  const handleOptionSelect = (value: string) => {
    onValueChange(value)
    setIsOpen(false)
  }

  const handleAddClick = () => {
    onAddOption()
    setIsOpen(false)
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full h-10 lg:h-12 text-sm lg:text-base justify-between"
            disabled={isLoading}
          >
            <div className="flex items-center space-x-2">
              {selectedOption ? (
                <>
                  {renderIcon(selectedOption.icon)}
                  <span>{selectedOption.label}</span>
                </>
              ) : (
                <span className="text-muted-foreground">{placeholder}</span>
              )}
            </div>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          className="w-full min-w-[var(--radix-dropdown-menu-trigger-width)] max-h-80 overflow-y-auto"
          align="start"
          side="bottom"
          sideOffset={4}
        >
          {isLoading ? (
            <DropdownMenuItem disabled>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                <span>加载中...</span>
              </div>
            </DropdownMenuItem>
          ) : (
            <>
              {/* 选项列表 */}
              {options.length === 0 ? (
                <DropdownMenuItem disabled className="py-3 text-center text-muted-foreground">
                  暂无选项，请添加新选项
                </DropdownMenuItem>
              ) : (
                options.map((option) => (
                <DropdownMenuItem
                  key={option.id}
                  className="flex items-center justify-between py-3 cursor-pointer group"
                  onSelect={() => handleOptionSelect(option.value)}
                >
                  <div className="flex items-center space-x-2">
                    {renderIcon(option.icon)}
                    <span>{option.label}</span>
                  </div>

                  {/* 删除按钮 - 当只有一个选项时禁用 */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-red-100 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-opacity disabled:opacity-30"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      onDeleteOption(option.id, option.label)
                    }}
                    disabled={options.length <= 1}
                    title={options.length <= 1 ? "至少需要保留一个选项" : "删除此选项"}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </DropdownMenuItem>
                ))
              )}

              {/* 分隔线 */}
              {options.length > 0 && <DropdownMenuSeparator />}

              {/* 添加新选项按钮 */}
              <DropdownMenuItem
                className="flex items-center space-x-2 py-3 cursor-pointer text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                onSelect={(e) => {
                  e.preventDefault()
                  handleAddClick()
                }}
              >
                <Plus className="w-4 h-4" />
                <span>添加新选项</span>
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

// 使用命名导出，不需要默认导出
