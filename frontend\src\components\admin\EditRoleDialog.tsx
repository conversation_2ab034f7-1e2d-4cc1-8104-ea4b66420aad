/**
 * 编辑角色对话框组件
 */
import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Loader2, Shield, Check, AlertCircle, Edit } from 'lucide-react'
import { useAuthStore } from '@/store/auth'
import { PermissionSelector } from './PermissionSelector'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface Permission {
  id: number
  name: string
  description: string
  category: string
}

interface Role {
  id: number
  name: string
  description: string
  is_active: boolean
  created_at: string
  permissions: Permission[]
}

interface EditRoleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
  role: Role | null
}

export const EditRoleDialog: React.FC<EditRoleDialogProps> = ({
  open,
  onOpenChange,
  onSuccess,
  role,
}) => {
  const { token } = useAuthStore()
  
  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true,
  })
  
  // 权限相关状态
  const [allPermissions, setAllPermissions] = useState<Permission[]>([])
  const [selectedPermissionIds, setSelectedPermissionIds] = useState<number[]>([])
  const [originalPermissionIds, setOriginalPermissionIds] = useState<number[]>([])
  
  // UI状态
  const [isLoading, setIsLoading] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // 当角色数据变化时，更新表单
  useEffect(() => {
    if (role) {
      setFormData({
        name: role.name,
        description: role.description || '',
        is_active: role.is_active,
      })
      const permissionIds = role.permissions.map(p => p.id)
      setSelectedPermissionIds(permissionIds)
      setOriginalPermissionIds(permissionIds)
    }
  }, [role])

  // 获取所有权限
  const fetchPermissions = async () => {
    if (!token) return

    setIsLoading(true)
    try {
      const response = await fetch(`${API_BASE_URL}/admin/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data: Permission[] = await response.json()
        setAllPermissions(data)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '获取权限列表失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 对话框打开时获取权限
  useEffect(() => {
    if (open && role) {
      fetchPermissions()
    }
  }, [open, role, token])

  // 处理表单输入
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  // 处理权限选择
  const handlePermissionToggle = (permissionId: number, checked: boolean) => {
    setSelectedPermissionIds(prev => {
      if (checked) {
        return [...prev, permissionId]
      } else {
        return prev.filter(id => id !== permissionId)
      }
    })
  }

  // 检查是否有变更
  const hasChanges = () => {
    if (!role) return false
    
    const formChanged = (
      formData.name !== role.name ||
      formData.description !== (role.description || '') ||
      formData.is_active !== role.is_active
    )
    
    const permissionsChanged = (
      selectedPermissionIds.length !== originalPermissionIds.length ||
      selectedPermissionIds.some(id => !originalPermissionIds.includes(id)) ||
      originalPermissionIds.some(id => !selectedPermissionIds.includes(id))
    )
    
    return formChanged || permissionsChanged
  }



  // 更新角色
  const handleUpdate = async () => {
    if (!token || !role) return

    // 表单验证
    if (!formData.name.trim()) {
      setError('角色名称不能为空')
      return
    }

    // 检查是否为系统内置角色
    if (['superadmin', 'admin', 'user'].includes(role.name) && formData.name !== role.name) {
      setError('不能修改系统内置角色的名称')
      return
    }

    setIsUpdating(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch(`${API_BASE_URL}/admin/roles/${role.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || null,
          is_active: formData.is_active,
          permission_ids: selectedPermissionIds,
        }),
      })

      if (response.ok) {
        setSuccess('角色更新成功')
        onSuccess()
        setTimeout(() => {
          handleClose()
        }, 1500)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '更新角色失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsUpdating(false)
    }
  }

  // 关闭对话框
  const handleClose = () => {
    setFormData({
      name: '',
      description: '',
      is_active: true,
    })
    setSelectedPermissionIds([])
    setOriginalPermissionIds([])
    setError('')
    setSuccess('')
    onOpenChange(false)
  }

  // 获取权限变更信息
  const getPermissionChanges = () => {
    const added = selectedPermissionIds.filter(id => !originalPermissionIds.includes(id))
    const removed = originalPermissionIds.filter(id => !selectedPermissionIds.includes(id))
    
    return {
      added: allPermissions.filter(p => added.includes(p.id)),
      removed: allPermissions.filter(p => removed.includes(p.id)),
    }
  }

  const permissionChanges = getPermissionChanges()
  const selectedPermissions = allPermissions.filter(p => selectedPermissionIds.includes(p.id))
  const selectedByCategory = selectedPermissions.reduce((groups, permission) => {
    const category = permission.category || '其他'
    groups[category] = (groups[category] || 0) + 1
    return groups
  }, {} as Record<string, number>)

  if (!role) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Edit className="h-5 w-5" />
            <span>编辑角色：{role.name}</span>
          </DialogTitle>
          <DialogDescription>
            修改角色信息和权限分配
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-4">
          {/* 左侧：基本信息 */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">基本信息</CardTitle>
                <CardDescription>
                  修改角色的基本信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="role-name">角色名称 *</Label>
                  <Input
                    id="role-name"
                    placeholder="输入角色名称"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    disabled={isUpdating || ['superadmin', 'admin', 'user'].includes(role.name)}
                  />
                  {['superadmin', 'admin', 'user'].includes(role.name) && (
                    <p className="text-xs text-muted-foreground">
                      系统内置角色名称不可修改
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role-description">角色描述</Label>
                  <Textarea
                    id="role-description"
                    placeholder="输入角色描述（可选）"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    disabled={isUpdating}
                    rows={3}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="role-active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => handleInputChange('is_active', checked as boolean)}
                    disabled={isUpdating}
                  />
                  <Label htmlFor="role-active">启用角色</Label>
                </div>
              </CardContent>
            </Card>

            {/* 权限预览和变更 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">权限预览</CardTitle>
                <CardDescription>
                  已选择 {selectedPermissionIds.length} 个权限
                </CardDescription>
              </CardHeader>
              <CardContent>
                {selectedPermissionIds.length === 0 ? (
                  <p className="text-muted-foreground text-sm">尚未选择任何权限</p>
                ) : (
                  <div className="space-y-2">
                    {Object.entries(selectedByCategory).map(([category, count]) => (
                      <div key={category} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{category}</span>
                        <Badge variant="secondary">{count}</Badge>
                      </div>
                    ))}
                  </div>
                )}

                {/* 权限变更提示 */}
                {(permissionChanges.added.length > 0 || permissionChanges.removed.length > 0) && (
                  <div className="mt-4 pt-4 border-t space-y-2">
                    <h4 className="text-sm font-medium">权限变更：</h4>
                    {permissionChanges.added.length > 0 && (
                      <div className="flex items-center space-x-2">
                        <Badge variant="default" className="text-xs">
                          +{permissionChanges.added.length}
                        </Badge>
                        <span className="text-xs text-muted-foreground">新增权限</span>
                      </div>
                    )}
                    {permissionChanges.removed.length > 0 && (
                      <div className="flex items-center space-x-2">
                        <Badge variant="destructive" className="text-xs">
                          -{permissionChanges.removed.length}
                        </Badge>
                        <span className="text-xs text-muted-foreground">移除权限</span>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 右侧：权限选择 */}
          <div className="space-y-4">
            {isLoading ? (
              <Card>
                <CardContent className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">加载权限中...</span>
                </CardContent>
              </Card>
            ) : (
              <PermissionSelector
                permissions={allPermissions}
                selectedPermissionIds={selectedPermissionIds}
                onPermissionToggle={handlePermissionToggle}
                originalPermissionIds={originalPermissionIds}
                disabled={isUpdating}
                showChanges={true}
                className="flex-1"
              />
            )}
          </div>
        </div>

        {/* 错误和成功消息 */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <Check className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isUpdating}
          >
            取消
          </Button>
          <Button
            onClick={handleUpdate}
            disabled={isUpdating || !formData.name.trim() || !hasChanges()}
          >
            {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            保存更改
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
