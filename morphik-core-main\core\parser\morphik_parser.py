import io
import logging
import os
import tempfile
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Tuple

import chardet
import filetype
from unstructured.partition.auto import partition

from core.models.chunk import Chunk
from core.parser.base_parser import BaseParser
from core.parser.video.parse_video import VideoParser, load_config

# Custom RecursiveCharacterTextSplitter replaces langchain's version


logger = logging.getLogger(__name__)


class BaseChunker(ABC):
    """Base class for text chunking strategies"""

    @abstractmethod
    def split_text(self, text: str) -> List[Chunk]:
        """Split text into chunks"""
        pass


class StandardChunker(BaseChunker):
    """Standard chunking using langchain's RecursiveCharacterTextSplitter"""

    def __init__(self, chunk_size: int, chunk_overlap: int):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", ". ", " ", ""],
        )

    def split_text(self, text: str) -> List[Chunk]:
        return self.text_splitter.split_text(text)


class RecursiveCharacterTextSplitter:
    def __init__(self, chunk_size: int, chunk_overlap: int, length_function=len, separators=None):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.length_function = length_function
        self.separators = separators or ["\n\n", "\n", ". ", " ", ""]

    def split_text(self, text: str) -> list[str]:
        chunks = self._split_recursive(text, self.separators)
        return [Chunk(content=chunk, metadata={}) for chunk in chunks]

    def _split_recursive(self, text: str, separators: list[str]) -> list[str]:
        if self.length_function(text) <= self.chunk_size:
            return [text] if text else []
        if not separators:
            # No separators left, split at chunk_size boundaries
            return [text[i : i + self.chunk_size] for i in range(0, len(text), self.chunk_size)]
        sep = separators[0]
        if sep:
            splits = text.split(sep)
        else:
            # Last fallback: split every character
            splits = list(text)
        chunks = []
        current = ""
        for part in splits:
            add_part = part + (sep if sep and part != splits[-1] else "")
            if self.length_function(current + add_part) > self.chunk_size:
                if current:
                    chunks.append(current)
                current = add_part
            else:
                current += add_part
        if current:
            chunks.append(current)
        # If any chunk is too large, recurse further
        final_chunks = []
        for chunk in chunks:
            if self.length_function(chunk) > self.chunk_size and len(separators) > 1:
                final_chunks.extend(self._split_recursive(chunk, separators[1:]))
            else:
                final_chunks.append(chunk)
        # Handle overlap
        if self.chunk_overlap > 0 and len(final_chunks) > 1:
            overlapped = []
            for i in range(len(final_chunks)):
                chunk = final_chunks[i]
                if i > 0:
                    prev = final_chunks[i - 1]
                    overlap = prev[-self.chunk_overlap :]
                    chunk = overlap + chunk
                overlapped.append(chunk)
            return overlapped
        return final_chunks


class ContextualChunker(BaseChunker):
    """Contextual chunking using LLMs to add context to each chunk"""

    DOCUMENT_CONTEXT_PROMPT = """
    <document>
    {doc_content}
    </document>
    """

    CHUNK_CONTEXT_PROMPT = """
    Here is the chunk we want to situate within the whole document
    <chunk>
    {chunk_content}
    </chunk>

    Please give a short succinct context to situate this chunk within the overall document for the purposes of improving search retrieval of the chunk.
    Answer only with the succinct context and nothing else.
    """

    def __init__(self, chunk_size: int, chunk_overlap: int, anthropic_api_key: str):
        self.standard_chunker = StandardChunker(chunk_size, chunk_overlap)

        # Get the config for contextual chunking
        config = load_config()
        parser_config = config.get("parser", {})
        self.model_key = parser_config.get("contextual_chunking_model", "claude_sonnet")

        # Get the settings for registered models
        from core.config import get_settings

        self.settings = get_settings()

        # Make sure the model exists in registered_models
        if not hasattr(self.settings, "REGISTERED_MODELS") or self.model_key not in self.settings.REGISTERED_MODELS:
            raise ValueError(f"Model '{self.model_key}' not found in registered_models configuration")

        self.model_config = self.settings.REGISTERED_MODELS[self.model_key]
        logger.info(f"Initialized ContextualChunker with model_key={self.model_key}")

    def _situate_context(self, doc: str, chunk: str) -> str:
        import litellm

        # Extract model name from config
        model_name = self.model_config.get("model_name")

        # Create system and user messages
        system_message = {
            "role": "system",
            "content": "You are an AI assistant that situates a chunk within a document for the purposes of improving search retrieval of the chunk.",
        }

        # Add document context and chunk to user message
        user_message = {
            "role": "user",
            "content": f"{self.DOCUMENT_CONTEXT_PROMPT.format(doc_content=doc)}\n\n{self.CHUNK_CONTEXT_PROMPT.format(chunk_content=chunk)}",
        }

        # Prepare parameters for litellm
        model_params = {
            "model": model_name,
            "messages": [system_message, user_message],
            "max_tokens": 1024,
            "temperature": 0.0,
        }

        # Add all model-specific parameters from the config
        for key, value in self.model_config.items():
            if key != "model_name":
                model_params[key] = value

        # Use litellm for completion
        response = litellm.completion(**model_params)
        return response.choices[0].message.content

    def split_text(self, text: str) -> List[Chunk]:
        base_chunks = self.standard_chunker.split_text(text)
        contextualized_chunks = []

        for chunk in base_chunks:
            context = self._situate_context(text, chunk.content)
            content = f"{context}; {chunk.content}"
            contextualized_chunks.append(Chunk(content=content, metadata=chunk.metadata))

        return contextualized_chunks


class MorphikParser(BaseParser):
    """Unified parser that handles different file types and chunking strategies"""

    def __init__(
        self,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        use_unstructured_api: bool = False,
        unstructured_api_key: Optional[str] = None,
        assemblyai_api_key: Optional[str] = None,
        anthropic_api_key: Optional[str] = None,
        frame_sample_rate: int = 1,
        use_contextual_chunking: bool = False,
    ):
        # Initialize basic configuration
        self.use_unstructured_api = use_unstructured_api
        self._unstructured_api_key = unstructured_api_key
        self._assemblyai_api_key = assemblyai_api_key
        self._anthropic_api_key = anthropic_api_key
        self.frame_sample_rate = frame_sample_rate

        # Load OCR configuration from morphik.toml
        config = load_config()
        parser_config = config.get("parser", {})
        self.ocr_languages = parser_config.get("ocr_languages", ["chi_sim", "eng"])
        self.ocr_strategy = parser_config.get("ocr_strategy", "hi_res")

        # Initialize chunker based on configuration
        if use_contextual_chunking:
            self.chunker = ContextualChunker(chunk_size, chunk_overlap, anthropic_api_key)
        else:
            self.chunker = StandardChunker(chunk_size, chunk_overlap)

    def _is_video_file(self, file: bytes, filename: str) -> bool:
        """Check if the file is a video file."""
        try:
            kind = filetype.guess(file)
            return kind is not None and kind.mime.startswith("video/")
        except Exception as e:
            logging.error(f"Error detecting file type: {str(e)}")
            return False

    def _detect_and_decode_text(self, file_bytes: bytes, filename: str) -> str:
        """
        检测文本文件的编码并解码为UTF-8字符串

        Args:
            file_bytes: 原始文件字节
            filename: 文件名

        Returns:
            str: 解码后的UTF-8文本
        """
        try:
            # 首先尝试UTF-8解码
            try:
                return file_bytes.decode('utf-8')
            except UnicodeDecodeError:
                pass

            # 使用chardet检测编码
            detected = chardet.detect(file_bytes)
            encoding = detected.get('encoding', 'utf-8')
            confidence = detected.get('confidence', 0)

            logger.info(f"检测到文件 {filename} 的编码: {encoding} (置信度: {confidence:.2f})")

            # 定义编码尝试顺序，优先使用更宽泛的编码
            # GB18030 > GBK > GB2312，因为GB18030包含GBK，GBK包含GB2312
            encoding_attempts = []

            if encoding and encoding.lower() in ['gb2312', 'gbk']:
                # 如果检测到GB2312或GBK，优先尝试GB18030和GBK
                encoding_attempts = ['gb18030', 'gbk', 'gb2312']
            elif encoding and encoding.lower() == 'gb18030':
                encoding_attempts = ['gb18030', 'gbk']
            elif encoding and encoding.lower() == 'big5':
                encoding_attempts = ['big5', 'gb18030', 'gbk']
            else:
                # 对于其他编码或未知编码，使用通用顺序
                encoding_attempts = ['gb18030', 'gbk', 'gb2312', 'big5', 'utf-8', 'latin1']

            # 如果检测到的编码不在列表中，添加到开头
            if encoding and encoding.lower() not in [e.lower() for e in encoding_attempts]:
                encoding_attempts.insert(0, encoding)

            # 尝试各种编码
            for enc in encoding_attempts:
                try:
                    # 使用errors='ignore'来跳过无效字节
                    decoded_text = file_bytes.decode(enc, errors='ignore')
                    logger.info(f"成功使用编码 {enc} 解码文件 {filename}")

                    # 检查解码结果是否合理（不能太多空白或特殊字符）
                    if len(decoded_text.strip()) > 0:
                        return decoded_text

                except (UnicodeDecodeError, LookupError) as e:
                    logger.debug(f"编码 {enc} 解码失败: {str(e)}")
                    continue

            # 如果所有编码都失败，使用最宽泛的方法
            logger.warning(f"所有编码尝试都失败，使用GB18030并替换错误字符解码文件 {filename}")
            try:
                return file_bytes.decode('gb18030', errors='replace')
            except:
                # 最终回退方案
                logger.warning(f"GB18030也失败，使用UTF-8并替换错误字符解码文件 {filename}")
                return file_bytes.decode('utf-8', errors='replace')

        except Exception as e:
            logger.error(f"解码文件 {filename} 时发生错误: {str(e)}")
            # 最终回退：使用latin1（几乎不会失败）
            return file_bytes.decode('latin1')

    async def _parse_video(self, file: bytes) -> Tuple[Dict[str, Any], str]:
        """Parse video file to extract transcript and frame descriptions"""
        if not self._assemblyai_api_key:
            raise ValueError("AssemblyAI API key is required for video parsing")

        # Save video to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as temp_file:
            temp_file.write(file)
            video_path = temp_file.name

        try:
            # Load the config to get the frame_sample_rate from morphik.toml
            config = load_config()
            parser_config = config.get("parser", {})
            vision_config = parser_config.get("vision", {})
            frame_sample_rate = vision_config.get("frame_sample_rate", self.frame_sample_rate)

            # Process video
            parser = VideoParser(
                video_path,
                assemblyai_api_key=self._assemblyai_api_key,
                frame_sample_rate=frame_sample_rate,
            )
            results = await parser.process_video()

            # Combine frame descriptions and transcript
            frame_text = "\n".join(results.frame_descriptions.time_to_content.values())
            transcript_text = "\n".join(results.transcript.time_to_content.values())
            combined_text = f"Frame Descriptions:\n{frame_text}\n\nTranscript:\n{transcript_text}"

            metadata = {
                "video_metadata": results.metadata,
                "frame_timestamps": list(results.frame_descriptions.time_to_content.keys()),
                "transcript_timestamps": list(results.transcript.time_to_content.keys()),
            }

            return metadata, combined_text
        finally:
            os.unlink(video_path)

    async def _parse_document(self, file: bytes, filename: str) -> Tuple[Dict[str, Any], str]:
        """Parse document using unstructured"""
        try:
            # 特殊处理文本文件的编码问题
            if filename.lower().endswith(".txt"):
                logger.info(f"开始处理文本文件: {filename}")
                try:
                    # 使用自定义编码检测和解码
                    decoded_text = self._detect_and_decode_text(file, filename)
                    logger.info(f"成功解码文本文件 {filename}，长度: {len(decoded_text)}")
                    if decoded_text and len(decoded_text.strip()) > 0:
                        logger.info(f"文本文件 {filename} 解码成功，直接返回结果")
                        return {}, decoded_text
                    else:
                        logger.warning(f"文本文件 {filename} 解码结果为空，回退到unstructured解析")
                except Exception as e:
                    logger.error(f"自定义文本解码失败，回退到unstructured解析: {str(e)}", exc_info=True)
                    # 继续使用unstructured解析

            # Choose a lighter parsing strategy for text-based files. Using
            # `hi_res` on plain PDFs/Word docs invokes OCR which can be 20-30×
            # slower.  A simple extension check covers the majority of cases.
            strategy = self.ocr_strategy  # Use configured OCR strategy
            file_content_type: Optional[str] = None  # Default to None for auto-detection

            # For images, always use hi_res strategy with OCR
            if filename.lower().endswith((".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff")):
                strategy = "hi_res"
            elif filename.lower().endswith((".pdf", ".doc", ".docx")):
                strategy = "fast"
            elif filename.lower().endswith(".txt"):
                strategy = "fast"
                file_content_type = "text/plain"  # Explicitly set for .txt files
            elif filename.lower().endswith(".json"):
                strategy = "fast"  # or can be omitted if it doesn't apply to json
                file_content_type = "application/json"  # Explicitly set for .json files

            # Prepare partition arguments
            partition_kwargs = {
                "file": io.BytesIO(file),
                "content_type": file_content_type,
                "metadata_filename": filename,
                "strategy": strategy,
            }

            # Add OCR languages for image files or hi_res strategy
            if strategy == "hi_res" or filename.lower().endswith((".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff")):
                partition_kwargs["languages"] = self.ocr_languages

            # Add API key if using unstructured API
            if self.use_unstructured_api:
                partition_kwargs["api_key"] = self._unstructured_api_key

            elements = partition(**partition_kwargs)

            text = "\n\n".join(str(element) for element in elements if str(element).strip())
            return {}, text
        except Exception as e:
            error_msg = str(e)
            if "partition_docx() is not available" in error_msg:
                raise ValueError(
                    f"无法处理 .docx 文件：缺少必要的依赖包。"
                    f"文件名：{filename}。"
                    f"请联系管理员安装 unstructured[docx] 依赖包。"
                ) from e
            elif "partition_doc() is not available" in error_msg:
                raise ValueError(
                    f"无法处理 .doc 文件：缺少必要的依赖包。"
                    f"文件名：{filename}。"
                    f"请联系管理员安装相关依赖包。"
                ) from e
            else:
                raise ValueError(
                    f"解析文档时发生错误：{error_msg}。"
                    f"文件名：{filename}。"
                    f"请检查文件格式是否正确或联系管理员。"
                ) from e

    async def parse_file_to_text(self, file: bytes, filename: str) -> Tuple[Dict[str, Any], str]:
        """Parse file content into text based on file type"""
        if self._is_video_file(file, filename):
            return await self._parse_video(file)
        return await self._parse_document(file, filename)

    async def split_text(self, text: str) -> List[Chunk]:
        """Split text into chunks using configured chunking strategy"""
        return self.chunker.split_text(text)
