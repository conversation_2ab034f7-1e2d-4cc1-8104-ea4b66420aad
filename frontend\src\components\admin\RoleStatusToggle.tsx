/**
 * 角色状态切换组件
 */
import React, { useState } from 'react'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Check } from 'lucide-react'
import { useAuthStore } from '@/store/auth'

const API_BASE_URL = 'http://localhost:8001/api/v1'

interface Role {
  id: number
  name: string
  description: string
  is_active: boolean
  created_at: string
}

interface RoleStatusToggleProps {
  role: Role
  onStatusChange: (role: Role, newStatus: boolean) => void
  disabled?: boolean
}

export const RoleStatusToggle: React.FC<RoleStatusToggleProps> = ({
  role,
  onStatusChange,
  disabled = false,
}) => {
  const { token } = useAuthStore()
  const [isUpdating, setIsUpdating] = useState(false)
  const [error, setError] = useState('')

  // 检查是否为系统内置角色
  const isSystemRole = ['superadmin', 'admin', 'user'].includes(role.name)

  const handleToggle = async (checked: boolean) => {
    if (!token || isSystemRole) return

    setIsUpdating(true)
    setError('')

    try {
      const response = await fetch(`${API_BASE_URL}/admin/roles/${role.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: role.name,
          description: role.description,
          is_active: checked,
        }),
      })

      if (response.ok) {
        const updatedRole = await response.json()
        onStatusChange(updatedRole, checked)
      } else {
        const errorData = await response.json()
        setError(errorData.detail || '更新角色状态失败')
      }
    } catch (err) {
      setError('网络错误，请重试')
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <div className="flex items-center space-x-2">
      <div className="flex items-center space-x-2">
        {isUpdating ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Switch
            checked={role.is_active}
            onCheckedChange={handleToggle}
            disabled={disabled || isSystemRole || isUpdating}
          />
        )}
        <Badge variant={role.is_active ? "default" : "secondary"}>
          {role.is_active ? "激活" : "禁用"}
        </Badge>
      </div>
      
      {isSystemRole && (
        <Badge variant="outline" className="text-xs">
          系统角色
        </Badge>
      )}
      
      {error && (
        <Alert variant="destructive" className="mt-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}
