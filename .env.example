# ==========================================
# ZHT系统主配置文件 - 统一环境变量控制
# ==========================================
# 此文件是所有服务的主配置文件，优先级最高
# 子目录的 .env 文件不应包含开发模式配置

# ==========================================
# 🔧 开发模式控制 (统一开关)
# ==========================================
# 主开发模式开关 - 控制整个系统行为
SYSTEM_DEV_MODE=true

# 各服务开发模式配置 (由 SYSTEM_DEV_MODE 统一控制)
DEV_MODE=true
VITE_DEV_MODE=true
MORPHIK_DEV_MODE=true

# 调试模式配置 (通常与开发模式保持一致)
DEBUG=true

# 生产环境配置
NODE_ENV=development

# 热重载配置（可选择性启用，即使在开发模式下也可能关闭以提升性能）
MORPHIK_RELOAD=false
MORPHIK_WORKER_RELOAD=false

# 如果需要完全跟随开发模式，可以改为：
# MORPHIK_RELOAD=${SYSTEM_DEV_MODE}
# MORPHIK_WORKER_RELOAD=false  # 工作进程建议始终关闭热重载

# Volume挂载模式（生产模式只读）
MORPHIK_VOLUME_MODE=rw
FRONTEND_VOLUME_MODE=rw
BACKEND_VOLUME_MODE=rw

# ==========================================
# 🗄️ 数据库配置
# ==========================================
# 容器环境数据库连接
DATABASE_URL=postgresql+asyncpg://zht_user:zht_password@zht_db_0624:5432/zht_db

# ==========================================
# 🔐 JWT密钥配置
# ==========================================
JWT_SECRET_KEY=production-super-secret-jwt-key-change-this-in-real-production

# ==========================================
# 📝 日志配置
# ==========================================
LOG_LEVEL=DEBUG

# HuggingFace镜像
HF_ENDPOINT=https://hf-mirror.com
