/**
 * Ollama聊天Hook
 * 管理聊天状态、会话历史和消息处理
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ollamaApi } from '@/services/ollamaApi';
import {
  ChatSession,
  ChatMessage,
  OllamaModel,
  OllamaChatMessage,
  OllamaChatState,
  OllamaChatActions,
} from '@/types/ollama';

// 本地存储键名
const STORAGE_KEYS = {
  SESSIONS: 'ollama_chat_sessions',
  CURRENT_SESSION: 'ollama_current_session',
  SELECTED_MODEL: 'ollama_selected_model',
};

// 生成唯一ID
const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

// 生成会话标题
const generateSessionTitle = (firstMessage: string): string => {
  const title = firstMessage.slice(0, 30);
  return title.length < firstMessage.length ? `${title}...` : title;
};

export function useOllamaChat(): OllamaChatState & OllamaChatActions {
  // 状态管理
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [selectedModel, setSelectedModelState] = useState<string>('llama3.2');
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const queryClient = useQueryClient();
  const abortControllerRef = useRef<AbortController | null>(null);

  // 获取可用模型
  const {
    data: availableModels = [],
    isLoading: isLoadingModels,
    refetch: refetchModels,
  } = useQuery({
    queryKey: ['ollama-models'],
    queryFn: () => ollamaApi.getModels(),
    staleTime: 5 * 60 * 1000, // 5分钟
    retry: 2,
  });

  // 发送消息的mutation
  const sendMessageMutation = useMutation({
    mutationFn: async ({ content, sessionId }: { content: string; sessionId: string }) => {
      const session = sessions.find(s => s.id === sessionId);
      if (!session) throw new Error('会话不存在');

      // 构建消息历史
      const messages: OllamaChatMessage[] = session.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      }));

      // 添加用户消息
      messages.push({ role: 'user', content });

      // 构建请求
      const request = ollamaApi.buildChatRequest(selectedModel, messages);

      // 创建AbortController
      abortControllerRef.current = new AbortController();

      // 流式响应处理
      const responseContent: string[] = [];

      for await (const chunk of ollamaApi.chatStream(request)) {
        if (abortControllerRef.current?.signal.aborted) {
          throw new Error('请求已取消');
        }

        if (chunk.error) {
          throw new Error(chunk.error);
        }

        responseContent.push(chunk.content);

        // 实时更新UI中的流式消息
        setSessions(prevSessions =>
          prevSessions.map(s => {
            if (s.id === sessionId) {
              const updatedMessages = [...s.messages];
              const lastMessage = updatedMessages[updatedMessages.length - 1];

              if (lastMessage && lastMessage.role === 'assistant' && lastMessage.isStreaming) {
                lastMessage.content = responseContent.join('');
              }

              return { ...s, messages: updatedMessages, updatedAt: new Date() };
            }
            return s;
          })
        );

        if (chunk.done) {
          break;
        }
      }

      return responseContent.join('');
    },
    onSuccess: (response, { sessionId }) => {
      // 完成流式响应，更新最终消息
      setSessions(prevSessions =>
        prevSessions.map(s => {
          if (s.id === sessionId) {
            const updatedMessages = [...s.messages];
            const lastMessage = updatedMessages[updatedMessages.length - 1];

            if (lastMessage && lastMessage.role === 'assistant' && lastMessage.isStreaming) {
              lastMessage.content = response;
              lastMessage.isStreaming = false;
            }

            return { ...s, messages: updatedMessages, updatedAt: new Date() };
          }
          return s;
        })
      );
      setIsStreaming(false);
      setError(null);
    },
    onError: (err, { sessionId }) => {
      // 处理错误
      const errorMessage = err instanceof Error ? err.message : '发送消息失败';
      setError(errorMessage);
      setIsStreaming(false);

      // 更新错误消息
      setSessions(prevSessions =>
        prevSessions.map(s => {
          if (s.id === sessionId) {
            const updatedMessages = [...s.messages];
            const lastMessage = updatedMessages[updatedMessages.length - 1];

            if (lastMessage && lastMessage.role === 'assistant' && lastMessage.isStreaming) {
              lastMessage.error = errorMessage;
              lastMessage.isStreaming = false;
            }

            return { ...s, messages: updatedMessages, updatedAt: new Date() };
          }
          return s;
        })
      );
    },
  });

  // 初始化：从localStorage加载数据
  useEffect(() => {
    try {
      const savedSessions = localStorage.getItem(STORAGE_KEYS.SESSIONS);
      const savedCurrentSession = localStorage.getItem(STORAGE_KEYS.CURRENT_SESSION);
      const savedSelectedModel = localStorage.getItem(STORAGE_KEYS.SELECTED_MODEL);

      if (savedSessions) {
        const parsedSessions = JSON.parse(savedSessions).map((session: any) => ({
          ...session,
          createdAt: new Date(session.createdAt),
          updatedAt: new Date(session.updatedAt),
          messages: session.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        }));
        setSessions(parsedSessions);
      }

      if (savedCurrentSession) {
        setCurrentSessionId(savedCurrentSession);
      }

      if (savedSelectedModel) {
        setSelectedModelState(savedSelectedModel);
      }
    } catch (error) {
      console.error('加载本地数据失败:', error);
    }
  }, []);

  // 保存数据到localStorage
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEYS.SESSIONS, JSON.stringify(sessions));
    } catch (error) {
      console.error('保存会话数据失败:', error);
    }
  }, [sessions]);

  useEffect(() => {
    if (currentSessionId) {
      localStorage.setItem(STORAGE_KEYS.CURRENT_SESSION, currentSessionId);
    }
  }, [currentSessionId]);

  useEffect(() => {
    localStorage.setItem(STORAGE_KEYS.SELECTED_MODEL, selectedModel);
  }, [selectedModel]);

  // 获取当前会话
  const currentSession = sessions.find(s => s.id === currentSessionId) || null;

  // Actions实现
  const createNewSession = useCallback((): string => {
    const newSession: ChatSession = {
      id: generateId(),
      title: '新对话',
      messages: [],
      model: selectedModel,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setSessions(prev => [newSession, ...prev]);
    setCurrentSessionId(newSession.id);
    setError(null);

    return newSession.id;
  }, [selectedModel]);

  const loadSession = useCallback((sessionId: string) => {
    setCurrentSessionId(sessionId);
    setError(null);
  }, []);

  const deleteSession = useCallback((sessionId: string) => {
    setSessions(prev => prev.filter(s => s.id !== sessionId));
    if (currentSessionId === sessionId) {
      setCurrentSessionId(null);
    }
  }, [currentSessionId]);

  const updateSessionTitle = useCallback((sessionId: string, title: string) => {
    setSessions(prev =>
      prev.map(s => s.id === sessionId ? { ...s, title, updatedAt: new Date() } : s)
    );
  }, []);

  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isStreaming) return;

    let sessionId = currentSessionId;

    // 如果没有当前会话，创建新会话
    if (!sessionId) {
      sessionId = createNewSession();
    }

    const userMessage: ChatMessage = {
      id: generateId(),
      role: 'user',
      content: content.trim(),
      timestamp: new Date(),
    };

    const assistantMessage: ChatMessage = {
      id: generateId(),
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      isStreaming: true,
    };

    // 添加用户消息和占位助手消息
    setSessions(prev =>
      prev.map(s => {
        if (s.id === sessionId) {
          const updatedMessages = [...s.messages, userMessage, assistantMessage];
          const newTitle = s.messages.length === 0 ? generateSessionTitle(content) : s.title;
          return {
            ...s,
            messages: updatedMessages,
            title: newTitle,
            updatedAt: new Date()
          };
        }
        return s;
      })
    );

    setIsStreaming(true);
    setError(null);

    // 发送消息
    try {
      await sendMessageMutation.mutateAsync({ content, sessionId });
    } catch (error) {
      console.error('发送消息失败:', error);
    }
  }, [currentSessionId, selectedModel, isStreaming, createNewSession, sendMessageMutation]);

  const regenerateLastMessage = useCallback(async () => {
    if (!currentSession || isStreaming) return;

    const messages = currentSession.messages;
    if (messages.length < 2) return;

    const lastUserMessage = messages[messages.length - 2];
    if (lastUserMessage.role !== 'user') return;

    // 移除最后的助手消息
    setSessions(prev =>
      prev.map(s => {
        if (s.id === currentSessionId) {
          return {
            ...s,
            messages: s.messages.slice(0, -1),
            updatedAt: new Date()
          };
        }
        return s;
      })
    );

    // 重新发送最后的用户消息
    await sendMessage(lastUserMessage.content);
  }, [currentSession, currentSessionId, isStreaming, sendMessage]);

  const deleteMessage = useCallback((messageId: string) => {
    setSessions(prev =>
      prev.map(s => {
        if (s.id === currentSessionId) {
          return {
            ...s,
            messages: s.messages.filter(m => m.id !== messageId),
            updatedAt: new Date()
          };
        }
        return s;
      })
    );
  }, [currentSessionId]);

  const setSelectedModel = useCallback((model: string) => {
    setSelectedModelState(model);
    setError(null);
  }, []);

  const refreshModels = useCallback(async () => {
    await refetchModels();
  }, [refetchModels]);

  const clearCurrentSession = useCallback(() => {
    if (currentSessionId) {
      setSessions(prev =>
        prev.map(s => {
          if (s.id === currentSessionId) {
            return {
              ...s,
              messages: [],
              title: '新对话',
              updatedAt: new Date()
            };
          }
          return s;
        })
      );
    }
    setError(null);
  }, [currentSessionId]);

  const exportSession = useCallback((sessionId: string): string => {
    const session = sessions.find(s => s.id === sessionId);
    if (!session) throw new Error('会话不存在');

    return JSON.stringify({
      version: '1.0',
      session,
      exportedAt: new Date(),
    }, null, 2);
  }, [sessions]);

  const importSession = useCallback((sessionData: string) => {
    try {
      const data = JSON.parse(sessionData);
      const session: ChatSession = {
        ...data.session,
        id: generateId(), // 生成新ID避免冲突
        createdAt: new Date(data.session.createdAt),
        updatedAt: new Date(),
        messages: data.session.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp),
        })),
      };

      setSessions(prev => [session, ...prev]);
      setCurrentSessionId(session.id);
    } catch (error) {
      throw new Error('导入会话数据格式错误');
    }
  }, []);

  return {
    // State
    sessions,
    currentSessionId,
    currentSession,
    availableModels: availableModels || [],
    selectedModel,
    isLoading: isLoadingModels,
    isStreaming,
    error,

    // Actions
    createNewSession,
    loadSession,
    deleteSession,
    updateSessionTitle,
    sendMessage,
    regenerateLastMessage,
    deleteMessage,
    setSelectedModel,
    refreshModels,
    clearCurrentSession,
    exportSession,
    importSession,
  };
}
