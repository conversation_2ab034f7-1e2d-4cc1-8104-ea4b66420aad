import { useEffect } from 'react'
import { useThemeStore } from '@/store/theme'

interface ThemeProviderProps {
  children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { theme, setTheme, updateActualTheme } = useThemeStore()

  useEffect(() => {
    // 确保主题正确应用
    setTheme(theme)
    
    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleChange = () => {
      updateActualTheme()
    }
    
    mediaQuery.addEventListener('change', handleChange)
    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [theme, setTheme, updateActualTheme])

  return <>{children}</>
}
