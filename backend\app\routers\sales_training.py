"""
销冠实战训练相关的API路由
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.schemas.sales_training import (
    SalesTrainingCustomer,
    SalesTrainingCustomerCreate,
    SalesTrainingCustomerUpdate,
    SalesTrainingCountry,
    SalesTrainingCountryCreate,
    SalesTrainingCountryUpdate,
    SalesTrainingProduct,
    SalesTrainingProductCreate,
    SalesTrainingProductUpdate,
    SalesTrainingScenario,
    SalesTrainingScenarioCreate,
    SalesTrainingScenarioUpdate
)
from app.crud.sales_training import (
    SalesTrainingCustomerCRUD,
    SalesTrainingCountryCRUD,
    SalesTrainingProductCRUD,
    SalesTrainingScenarioCRUD
)

router = APIRouter(prefix="/api/sales-training", tags=["sales-training"])


# 客户相关接口
@router.get("/customers", response_model=List[SalesTrainingCustomer])
async def get_customers(
    name: str = None,
    company: str = None,
    industry: str = None,
    level: str = None,
    region: str = None,
    db: AsyncSession = Depends(get_db)
):
    """获取所有客户或搜索客户"""
    try:
        if any([name, company, industry, level, region]):
            # 执行搜索
            customers = await SalesTrainingCustomerCRUD.search_customers(
                db,
                name=name,
                company=company,
                industry=industry,
                level=level,
                region=region
            )
        else:
            # 获取所有客户
            customers = await SalesTrainingCustomerCRUD.get_all(db)
        return customers
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取客户列表失败: {str(e)}"
        )


@router.post("/customers", response_model=SalesTrainingCustomer)
async def create_customer(
    customer_data: SalesTrainingCustomerCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建新的客户"""
    try:
        # 检查是否已存在相同姓名的客户
        existing_customer = await SalesTrainingCustomerCRUD.get_by_name(db, customer_data.name)
        if existing_customer:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="客户姓名已存在"
            )

        customer = await SalesTrainingCustomerCRUD.create(db, customer_data)
        return customer
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建客户失败: {str(e)}"
        )


@router.get("/customers/{customer_id}", response_model=SalesTrainingCustomer)
async def get_customer(
    customer_id: int,
    db: AsyncSession = Depends(get_db)
):
    """根据ID获取客户"""
    try:
        customer = await SalesTrainingCustomerCRUD.get_by_id(db, customer_id)
        if not customer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="客户不存在"
            )
        return customer
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取客户失败: {str(e)}"
        )


@router.put("/customers/{customer_id}", response_model=SalesTrainingCustomer)
async def update_customer(
    customer_id: int,
    customer_data: SalesTrainingCustomerUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新客户信息"""
    try:
        # 如果更新姓名，检查是否与其他客户重复
        if customer_data.name:
            existing_customer = await SalesTrainingCustomerCRUD.get_by_name(db, customer_data.name)
            if existing_customer and existing_customer.id != customer_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="客户姓名已存在"
                )

        customer = await SalesTrainingCustomerCRUD.update(db, customer_id, customer_data)
        if not customer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="客户不存在"
            )
        return customer
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新客户失败: {str(e)}"
        )


@router.delete("/customers/{customer_id}")
async def delete_customer(
    customer_id: int,
    db: AsyncSession = Depends(get_db)
):
    """删除客户（硬删除 - 从数据库中永久删除）"""
    try:
        success = await SalesTrainingCustomerCRUD.delete(db, customer_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="客户不存在"
            )
        return {"message": "客户已永久删除"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除客户失败: {str(e)}"
        )


# 国家相关接口
@router.get("/countries", response_model=List[SalesTrainingCountry])
async def get_countries(
    include_inactive: bool = False,
    db: AsyncSession = Depends(get_db)
):
    """获取所有国家选项"""
    try:
        countries = await SalesTrainingCountryCRUD.get_all(db, include_inactive=include_inactive)
        return countries
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取国家列表失败: {str(e)}"
        )


@router.post("/countries", response_model=SalesTrainingCountry)
async def create_country(
    country_data: SalesTrainingCountryCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建新的国家选项"""
    try:
        # 检查是否已存在相同名称或代码的国家
        existing_by_name = await SalesTrainingCountryCRUD.get_by_name(db, country_data.name)
        if existing_by_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="国家名称已存在"
            )

        existing_by_code = await SalesTrainingCountryCRUD.get_by_code(db, country_data.code)
        if existing_by_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="国家代码已存在"
            )

        country = await SalesTrainingCountryCRUD.create(db, country_data)
        return country
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建国家失败: {str(e)}"
        )


@router.put("/countries/{country_id}", response_model=SalesTrainingCountry)
async def update_country(
    country_id: int,
    country_data: SalesTrainingCountryUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新国家选项"""
    try:
        country = await SalesTrainingCountryCRUD.update(db, country_id, country_data)
        if not country:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="国家不存在"
            )
        return country
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新国家失败: {str(e)}"
        )


@router.delete("/countries/{country_id}")
async def delete_country(
    country_id: int,
    hard_delete: bool = True,
    db: AsyncSession = Depends(get_db)
):
    """删除国家选项（支持硬删除）"""
    try:
        # 检查是否为最后一个活跃国家
        active_count = await SalesTrainingCountryCRUD.count_active(db)
        if active_count <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法删除最后一个国家，系统至少需要保留一个国家选项"
            )

        if hard_delete:
            # 硬删除：从数据库中永久删除
            success = await SalesTrainingCountryCRUD.hard_delete(db, country_id)
        else:
            # 软删除：标记为不活跃
            success = await SalesTrainingCountryCRUD.delete(db, country_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="国家不存在"
            )

        delete_type = "永久删除" if hard_delete else "删除"
        return {"message": f"国家{delete_type}成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除国家失败: {str(e)}"
        )


# 产品相关接口
@router.get("/products", response_model=List[SalesTrainingProduct])
async def get_products(
    include_inactive: bool = False,
    db: AsyncSession = Depends(get_db)
):
    """获取所有产品选项"""
    try:
        products = await SalesTrainingProductCRUD.get_all(db, include_inactive=include_inactive)
        return products
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取产品列表失败: {str(e)}"
        )


@router.post("/products", response_model=SalesTrainingProduct)
async def create_product(
    product_data: SalesTrainingProductCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建新的产品选项"""
    try:
        # 检查是否已存在相同名称和分类的产品
        existing = await SalesTrainingProductCRUD.get_by_name_and_category(
            db, product_data.name, product_data.category
        )
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该分类下已存在同名产品"
            )

        product = await SalesTrainingProductCRUD.create(db, product_data)
        return product
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建产品失败: {str(e)}"
        )


@router.put("/products/{product_id}", response_model=SalesTrainingProduct)
async def update_product(
    product_id: int,
    product_data: SalesTrainingProductUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新产品选项"""
    try:
        product = await SalesTrainingProductCRUD.update(db, product_id, product_data)
        if not product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="产品不存在"
            )
        return product
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新产品失败: {str(e)}"
        )


@router.delete("/products/{product_id}")
async def delete_product(
    product_id: int,
    hard_delete: bool = False,
    db: AsyncSession = Depends(get_db)
):
    """删除产品选项（支持硬删除）"""
    try:
        # 检查是否为最后一个活跃产品
        active_count = await SalesTrainingProductCRUD.count_active(db)
        if active_count <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法删除最后一个产品，系统至少需要保留一个产品选项"
            )

        if hard_delete:
            # 硬删除：从数据库中永久删除
            success = await SalesTrainingProductCRUD.hard_delete(db, product_id)
        else:
            # 软删除：标记为不活跃
            success = await SalesTrainingProductCRUD.delete(db, product_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="产品不存在"
            )

        delete_type = "永久删除" if hard_delete else "删除"
        return {"message": f"产品{delete_type}成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除产品失败: {str(e)}"
        )





# 训练场景相关接口
@router.get("/scenarios", response_model=List[SalesTrainingScenario])
async def get_scenarios(
    category: str = None,
    difficulty: str = None,
    db: AsyncSession = Depends(get_db)
):
    """获取所有训练场景或按条件筛选"""
    try:
        if category:
            scenarios = await SalesTrainingScenarioCRUD.get_by_category(db, category)
        elif difficulty:
            scenarios = await SalesTrainingScenarioCRUD.get_by_difficulty(db, difficulty)
        else:
            scenarios = await SalesTrainingScenarioCRUD.get_all(db)
        return scenarios
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取训练场景列表失败: {str(e)}"
        )


@router.post("/scenarios", response_model=SalesTrainingScenario)
async def create_scenario(
    scenario_data: SalesTrainingScenarioCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建新的训练场景"""
    try:
        # 检查是否已存在相同名称的活跃场景
        existing_scenario = await SalesTrainingScenarioCRUD.get_active_by_name(db, scenario_data.name)
        if existing_scenario:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="训练场景名称已存在"
            )

        scenario = await SalesTrainingScenarioCRUD.create(db, scenario_data)
        return scenario
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建训练场景失败: {str(e)}"
        )


@router.get("/scenarios/{scenario_id}", response_model=SalesTrainingScenario)
async def get_scenario(
    scenario_id: int,
    db: AsyncSession = Depends(get_db)
):
    """根据ID获取训练场景"""
    try:
        scenario = await SalesTrainingScenarioCRUD.get_by_id(db, scenario_id)
        if not scenario:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="训练场景不存在"
            )
        return scenario
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取训练场景失败: {str(e)}"
        )


@router.put("/scenarios/{scenario_id}", response_model=SalesTrainingScenario)
async def update_scenario(
    scenario_id: int,
    scenario_data: SalesTrainingScenarioUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新训练场景"""
    try:
        scenario = await SalesTrainingScenarioCRUD.update(db, scenario_id, scenario_data)
        if not scenario:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="训练场景不存在"
            )
        return scenario
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新训练场景失败: {str(e)}"
        )


@router.delete("/scenarios/{scenario_id}")
async def delete_scenario(
    scenario_id: int,
    hard_delete: bool = True,
    db: AsyncSession = Depends(get_db)
):
    """删除训练场景（默认硬删除）"""
    try:
        # 检查是否为最后一个活跃训练场景
        active_count = await SalesTrainingScenarioCRUD.count_active(db)
        if active_count <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法删除最后一个训练场景，系统至少需要保留一个训练场景"
            )

        if hard_delete:
            success = await SalesTrainingScenarioCRUD.hard_delete(db, scenario_id)
        else:
            success = await SalesTrainingScenarioCRUD.soft_delete(db, scenario_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="训练场景不存在"
            )

        delete_type = "永久删除" if hard_delete else "删除"
        return {"message": f"训练场景{delete_type}成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除训练场景失败: {str(e)}"
        )
