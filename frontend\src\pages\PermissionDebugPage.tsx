import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuthStore } from '@/store/auth';
import { usePermissions } from '@/hooks/usePermissions';
import { Shield, User, Key, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';

const API_BASE_URL = 'http://localhost:8001/api/v1';

interface UserPermissionData {
  permissions: Array<{ id: number; name: string; description: string; category: string }>;
  roles: Array<{ id: number; name: string; description: string; is_active: boolean }>;
}

const PermissionDebugPage: React.FC = () => {
  const { user, token } = useAuthStore();
  const { userPermissions, loading, error, hasPermission, hasRole, hasAnyRole, refetch } = usePermissions();
  const [detailedPermissions, setDetailedPermissions] = useState<UserPermissionData | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);

  // 获取详细的权限信息
  const fetchDetailedPermissions = async () => {
    if (!user || !token) return;

    try {
      setApiError(null);
      const response = await fetch(`${API_BASE_URL}/admin/users/${user.id}/permissions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setDetailedPermissions(data);
      } else {
        const errorData = await response.json();
        setApiError(`API错误: ${errorData.detail || response.statusText}`);
      }
    } catch (err) {
      setApiError(`网络错误: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  };

  useEffect(() => {
    fetchDetailedPermissions();
  }, [user, token]);

  const testPermissions = [
    'user:read', 'user:create', 'user:update', 'user:delete',
    'role:read', 'role:create', 'role:update', 'role:delete',
    'permission:read', 'system:config', 'content:read'
  ];

  const testRoles = ['superadmin', 'admin', 'moderator', 'user', 'guest'];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8" />
            权限调试工具
          </h1>
          <p className="text-muted-foreground">
            诊断和调试用户权限问题
          </p>
        </div>
        <Button onClick={() => { refetch(); fetchDetailedPermissions(); }} className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          刷新权限
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="permissions">权限测试</TabsTrigger>
          <TabsTrigger value="roles">角色测试</TabsTrigger>
          <TabsTrigger value="api">API调试</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 用户信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  用户信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div><strong>用户名:</strong> {user?.username || '未登录'}</div>
                <div><strong>邮箱:</strong> {user?.email || 'N/A'}</div>
                <div><strong>全名:</strong> {user?.full_name || 'N/A'}</div>
                <div className="flex items-center gap-2">
                  <strong>超级用户:</strong>
                  {user?.is_superuser ? (
                    <Badge variant="default" className="bg-green-500">是</Badge>
                  ) : (
                    <Badge variant="secondary">否</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <strong>账户状态:</strong>
                  {user?.is_active ? (
                    <Badge variant="default" className="bg-green-500">激活</Badge>
                  ) : (
                    <Badge variant="destructive">禁用</Badge>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 权限状态 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  权限状态
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center gap-2">
                  <strong>权限加载:</strong>
                  {loading ? (
                    <Badge variant="secondary">加载中...</Badge>
                  ) : (
                    <Badge variant="default" className="bg-green-500">完成</Badge>
                  )}
                </div>
                <div><strong>权限数量:</strong> {userPermissions.permissions.length}</div>
                <div><strong>角色数量:</strong> {userPermissions.roles.length}</div>
                {error && (
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 当前权限和角色 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>当前权限</CardTitle>
              </CardHeader>
              <CardContent>
                {userPermissions.permissions.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {userPermissions.permissions.map((permission) => (
                      <Badge key={permission} variant="outline">
                        {permission}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">无权限</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>当前角色</CardTitle>
              </CardHeader>
              <CardContent>
                {userPermissions.roles.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {userPermissions.roles.map((role) => (
                      <Badge key={role} variant="default">
                        {role}
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">无角色</p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>权限测试</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {testPermissions.map((permission) => {
                  const hasThisPermission = hasPermission(permission);
                  return (
                    <div key={permission} className="flex items-center justify-between p-3 border rounded-lg">
                      <span className="font-mono text-sm">{permission}</span>
                      {hasThisPermission ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <AlertTriangle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>角色测试</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {testRoles.map((role) => {
                  const hasThisRole = hasRole(role);
                  return (
                    <div key={role} className="flex items-center justify-between p-3 border rounded-lg">
                      <span className="font-mono text-sm">{role}</span>
                      {hasThisRole ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <AlertTriangle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                  );
                })}
              </div>

              <div className="mt-6 p-4 bg-muted rounded-lg">
                <h4 className="font-semibold mb-2">管理员权限测试</h4>
                <div className="flex items-center gap-2">
                  <span>hasAnyRole(['admin', 'superadmin']):</span>
                  {hasAnyRole(['admin', 'superadmin']) ? (
                    <Badge variant="default" className="bg-green-500">通过</Badge>
                  ) : (
                    <Badge variant="destructive">失败</Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API调试信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {apiError && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{apiError}</AlertDescription>
                </Alert>
              )}

              {detailedPermissions && (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">详细权限信息</h4>
                    <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto">
                      {JSON.stringify(detailedPermissions, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              <div>
                <h4 className="font-semibold mb-2">Hook状态</h4>
                <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto">
                  {JSON.stringify({
                    loading,
                    error,
                    userPermissions,
                    user: user ? {
                      id: user.id,
                      username: user.username,
                      is_superuser: user.is_superuser,
                      is_active: user.is_active
                    } : null
                  }, null, 2)}
                </pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PermissionDebugPage;
