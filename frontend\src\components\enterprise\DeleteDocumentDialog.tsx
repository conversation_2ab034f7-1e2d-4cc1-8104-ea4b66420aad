/**
 * 企业知识库文档删除确认对话框
 */
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Trash2, AlertTriangle, FileText, Calendar, HardDrive, Loader2 } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface DeleteDocumentDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  document: any
  isDeleting?: boolean
}

export function DeleteDocumentDialog({
  isOpen,
  onClose,
  onConfirm,
  document,
  isDeleting = false
}: DeleteDocumentDialogProps) {
  if (!document) return null

  const filename = document.filename || document.metadata?.title || '未命名文档'
  const fileSize = document.metadata?.fileSize
  const createdAt = document.system_metadata?.created_at
  const categoryName = document.metadata?.category_display_name || '未分类'

  const formatFileSize = (bytes: number) => {
    if (!bytes) return '未知大小'
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return '未知时间'
    return new Date(dateString).toLocaleString('zh-CN')
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => open ? null : onClose()} modal={true}>
      <DialogContent
        className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto"
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader className="space-y-4">
          <div className="flex items-center justify-center w-12 h-12 mx-auto bg-orange-100 rounded-full">
            <AlertTriangle className="h-6 w-6 text-orange-600" />
          </div>
          <DialogTitle className="text-center text-xl font-semibold text-gray-900">
            确认删除文档
          </DialogTitle>
          <DialogDescription className="text-center text-gray-600 leading-relaxed">
            您即将删除以下文档，此操作不可撤销。
          </DialogDescription>
        </DialogHeader>

        {/* 文档信息卡片 */}
        <div className="bg-gradient-to-br from-gray-50 to-gray-100/50 border border-gray-200 rounded-xl p-5 space-y-4 shadow-sm">
          {/* 文档标题和图标 */}
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-md">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 text-lg leading-tight mb-2">
                {filename}
              </h3>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs font-medium bg-blue-100 text-blue-700 border-blue-200">
                  {categoryName}
                </Badge>
              </div>
            </div>
          </div>

          {/* 文档详细信息 */}
          <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
            {fileSize && (
              <div className="flex items-center gap-3 text-sm text-gray-600 bg-white rounded-lg p-3 border border-gray-100">
                <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                  <HardDrive className="h-4 w-4 text-gray-500" />
                </div>
                <div>
                  <p className="text-xs text-gray-500 mb-1">文件大小</p>
                  <p className="font-medium text-gray-900">{formatFileSize(fileSize)}</p>
                </div>
              </div>
            )}
            {createdAt && (
              <div className="flex items-center gap-3 text-sm text-gray-600 bg-white rounded-lg p-3 border border-gray-100">
                <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                  <Calendar className="h-4 w-4 text-gray-500" />
                </div>
                <div>
                  <p className="text-xs text-gray-500 mb-1">创建时间</p>
                  <p className="font-medium text-gray-900">{formatDate(createdAt)}</p>
                </div>
              </div>
            )}
          </div>

          {/* 警告提示 */}
          <div className="flex items-start gap-3 p-4 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-lg">
            <div className="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
            </div>
            <div className="text-sm">
              <p className="font-semibold text-amber-800 mb-1">重要提醒</p>
              <p className="text-amber-700 leading-relaxed">删除后的文档无法恢复，请确认您真的要删除此文档。</p>
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-row justify-center gap-3 pt-6 border-t border-gray-100">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isDeleting}
            className="px-6 py-2.5 font-medium border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors"
          >
            取消
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isDeleting}
            className="min-w-[120px] px-6 py-2.5 font-medium bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDeleting ? (
              <div className="flex items-center">
                <Loader2 className="animate-spin h-4 w-4 mr-2" />
                删除中...
              </div>
            ) : (
              <div className="flex items-center">
                <Trash2 className="h-4 w-4 mr-2" />
                确认删除
              </div>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
