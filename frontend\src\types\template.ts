/**
 * 话术模板系统类型定义
 * 基于 Morphik Core prompt_overrides 规范
 */

// 实体提取示例
export interface EntityExtractionExample {
  label: string;
  type: string;
  properties?: Record<string, any>;
}

// 实体解析示例
export interface EntityResolutionExample {
  canonical: string;
  variants: string[];
}

// 实体提取提示词覆盖
export interface EntityExtractionPromptOverride {
  prompt_template?: string;
  examples?: EntityExtractionExample[];
}

// 实体解析提示词覆盖
export interface EntityResolutionPromptOverride {
  prompt_template?: string;
  examples?: EntityResolutionExample[];
}

// 查询提示词覆盖
export interface QueryPromptOverride {
  prompt_template: string;
}

// 完整的提示词覆盖配置
export interface PromptOverrides {
  entity_extraction?: EntityExtractionPromptOverride;
  entity_resolution?: EntityResolutionPromptOverride;
  query?: QueryPromptOverride;
}

// 角色配置
export interface RoleConfig {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'customer_service' | 'sales' | 'technical' | 'medical' | 'legal' | 'general';
  system_prompt: string;
  style: string;
  temperature?: number;
  max_tokens?: number;
}

// 回答风格配置
export interface StyleConfig {
  id: string;
  name: string;
  description: string;
  template: string;
  category: 'detailed' | 'summary' | 'step_by_step' | 'comparison' | 'troubleshooting' | 'creative';
}

// 话术模板
export interface Template {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  created_at: string;
  updated_at: string;
  category: string;
  tags: string[];
  
  // 核心配置
  role: RoleConfig;
  style: StyleConfig;
  prompt_overrides: PromptOverrides;
  
  // 用户上下文配置
  user_context_schema?: Record<string, any>;
  custom_instructions?: string;
  
  // 元数据
  usage_count: number;
  rating: number;
  is_active: boolean;
  is_default: boolean;
}

// 模板管理操作
export interface TemplateOperation {
  type: 'create' | 'update' | 'delete' | 'activate' | 'deactivate';
  template_id: string;
  data?: Partial<Template>;
}

// 模板导入/导出格式
export interface TemplateExport {
  version: string;
  export_date: string;
  templates: Template[];
  metadata: {
    total_count: number;
    categories: string[];
    tags: string[];
  };
}

// 模板使用统计
export interface TemplateStats {
  template_id: string;
  usage_count: number;
  success_rate: number;
  average_rating: number;
  last_used: string;
  performance_metrics: {
    response_time: number;
    user_satisfaction: number;
    error_rate: number;
  };
}

// 模板搜索和过滤
export interface TemplateFilter {
  category?: string;
  tags?: string[];
  author?: string;
  is_active?: boolean;
  rating_min?: number;
  search_query?: string;
}

// 模板验证结果
export interface TemplateValidation {
  is_valid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// API 响应类型
export interface TemplateResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface TemplateListResponse extends TemplateResponse {
  data: {
    templates: Template[];
    total: number;
    page: number;
    limit: number;
  };
}

export interface TemplateDetailResponse extends TemplateResponse {
  data: Template;
}

// 模板管理器配置
export interface TemplateManagerConfig {
  storage_type: 'localStorage' | 'database' | 'hybrid';
  auto_backup: boolean;
  max_templates: number;
  validation_strict: boolean;
  enable_analytics: boolean;
}

// 预定义模板类别
export const TEMPLATE_CATEGORIES = {
  CUSTOMER_SERVICE: 'customer_service',
  SALES: 'sales',
  TECHNICAL_SUPPORT: 'technical_support',
  MEDICAL: 'medical',
  LEGAL: 'legal',
  EDUCATION: 'education',
  MARKETING: 'marketing',
  GENERAL: 'general'
} as const;

// 预定义回答风格
export const RESPONSE_STYLES = {
  DETAILED: 'detailed',
  SUMMARY: 'summary',
  STEP_BY_STEP: 'step_by_step',
  COMPARISON: 'comparison',
  TROUBLESHOOTING: 'troubleshooting',
  CREATIVE: 'creative',
  PROFESSIONAL: 'professional',
  CASUAL: 'casual'
} as const;

// 模板状态
export const TEMPLATE_STATUS = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  ARCHIVED: 'archived'
} as const;

export type TemplateCategoryType = typeof TEMPLATE_CATEGORIES[keyof typeof TEMPLATE_CATEGORIES];
export type ResponseStyleType = typeof RESPONSE_STYLES[keyof typeof RESPONSE_STYLES];
export type TemplateStatusType = typeof TEMPLATE_STATUS[keyof typeof TEMPLATE_STATUS];
