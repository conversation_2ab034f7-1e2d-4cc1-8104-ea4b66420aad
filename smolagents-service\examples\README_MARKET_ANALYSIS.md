# 智能市场分析演示系统

基于 SmoLAgents 微服务实现的智能市场分析工具，能够自动解析复杂搜索词，执行多角度网络搜索，并生成专业的市场分析报告。

## 🌟 核心功能

### 1. 智能搜索词解析
- **自动识别产品类型**: 从复杂搜索词中提取产品名称
- **地理区域识别**: 支持亚洲、中国、美国、欧洲、全球等区域
- **时间维度提取**: 自动识别年份和时间范围
- **分析类型判断**: 识别销售分析、趋势预测、竞争分析等需求

### 2. 多角度搜索策略
- **市场规模搜索**: 获取行业规模和基础数据
- **销售数据搜索**: 收集销售统计和表现数据
- **趋势预测搜索**: 查找市场趋势和未来预测
- **竞争分析搜索**: 分析竞争格局和主要参与者
- **价格分析搜索**: 获取价格趋势和定价策略
- **消费者分析搜索**: 了解消费者行为和需求变化
- **行业动态搜索**: 收集最新行业新闻和发展

### 3. AI 驱动的综合分析
- **真实数据基础**: 基于网络搜索的真实数据，禁止模拟数据
- **结构化报告**: 生成专业的市场分析报告
- **多维度洞察**: 提供现状分析、趋势预测、竞争分析等
- **可行性建议**: 给出具体的战略建议和行动计划

## 🚀 快速开始

### 环境要求

1. **SmoLAgents 微服务运行中**
   ```bash
   # 检查服务状态
   docker ps | grep smolagents
   
   # 启动服务（如果未运行）
   docker-compose up -d zht_smolagents_0624
   ```

2. **服务健康检查**
   ```bash
   curl http://localhost:8002/api/v1/health
   ```

### 运行演示

#### 方式1: 快速测试（推荐）
```bash
cd smolagents-service/examples
python quick_market_test.py
```

#### 方式2: 完整演示系统
```bash
cd smolagents-service/examples
python market_analysis_demo.py
```

## 📝 使用示例

### 示例搜索词

1. **沙滩包市场分析**
   ```
   沙滩包2024亚洲市场销售情况分析及2025市场增长预期
   ```

2. **智能设备分析**
   ```
   智能手表欧洲市场2024年竞争格局分析
   ```

3. **汽车行业分析**
   ```
   电动汽车中国市场2024销售数据及价格趋势
   ```

4. **家电市场分析**
   ```
   咖啡机全球市场2024年消费者需求分析
   ```

### 分析结果示例

系统会生成包含以下结构的专业报告：

```markdown
# 产品名称 - 目标市场分析报告

## 1. 执行摘要
- 核心发现和关键洞察
- 主要市场机会和挑战
- 关键数据指标

## 2. 市场现状分析
- 市场规模和增长情况
- 主要参与者和竞争格局
- 价格水平和趋势

## 3. 消费者和需求分析
- 目标消费群体特征
- 需求趋势和驱动因素
- 消费行为变化

## 4. 竞争环境分析
- 主要竞争对手
- 市场份额分布
- 竞争优势和劣势

## 5. 市场趋势和预测
- 当前年度市场表现
- 未来增长预期
- 影响因素分析

## 6. 机会与挑战
- 市场机会点
- 潜在风险和挑战
- 应对策略建议

## 7. 结论和建议
- 核心结论
- 战略建议
- 行动计划
```

## 🔧 技术架构

### 核心组件

1. **SmoLAgents CodeAgent**
   - 基于 Qwen3-8B-M 大语言模型
   - 智能任务规划和执行
   - 自然语言理解和生成

2. **DuckDuckGoSearchTool**
   - 真实网络数据搜索
   - 多角度信息收集
   - 搜索结果智能筛选

3. **智能分析引擎**
   - 搜索词自动解析
   - 搜索策略生成
   - 数据整合和分析

### 工作流程

```mermaid
graph TD
    A[输入搜索词] --> B[智能解析]
    B --> C[生成搜索策略]
    C --> D[执行网络搜索]
    D --> E[数据收集和筛选]
    E --> F[AI综合分析]
    F --> G[生成分析报告]
```

## 📊 性能特点

- **执行时间**: 通常 2-5 分钟完成完整分析
- **数据源**: 每次分析使用 5-7 个不同角度的搜索策略
- **报告质量**: 基于真实数据的专业级市场分析报告
- **可扩展性**: 支持任意市场分析类搜索词

## 🛠️ 自定义配置

### 修改搜索策略

编辑 `market_analysis_demo.py` 中的 `generate_search_queries` 方法：

```python
def generate_search_queries(self, context: AnalysisContext) -> List[SearchQuery]:
    queries = []
    
    # 添加自定义搜索策略
    queries.append(SearchQuery(
        query=f"your custom search query for {context.product}",
        category="自定义类别",
        priority=1,
        description="自定义搜索描述"
    ))
    
    return queries
```

### 调整分析框架

修改 `generate_comprehensive_analysis` 方法中的分析提示词，自定义报告结构和分析维度。

## 🔍 故障排除

### 常见问题

1. **服务连接失败**
   ```bash
   # 检查服务状态
   docker logs zht_smolagents_0624
   
   # 重启服务
   docker-compose restart zht_smolagents_0624
   ```

2. **搜索结果为空**
   - 检查网络连接
   - 确认 DuckDuckGoSearchTool 正常工作
   - 尝试简化搜索词

3. **分析报告质量不佳**
   - 增加搜索策略的多样性
   - 优化分析提示词
   - 调整 max_steps 参数

### 调试模式

启用详细日志输出：

```python
# 在脚本开头添加
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 扩展功能

### 支持的分析类型

- 市场规模分析
- 竞争格局分析
- 价格趋势分析
- 消费者行为分析
- 行业趋势预测
- 投资机会分析

### 支持的地理区域

- 全球市场
- 亚洲市场
- 中国市场
- 美国市场
- 欧洲市场
- 其他特定国家/地区

## 🤝 贡献指南

欢迎提交改进建议和功能扩展：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

本项目基于现有的 ZHT_SYSTEM_0624 项目许可证。
