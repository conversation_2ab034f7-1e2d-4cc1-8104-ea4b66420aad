/**
 * 智能问答聊天界面组件
 * 基于RAG的智能问答系统，支持多轮对话历史、来源引用、知识图谱增强
 */
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';

import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  MessageSquare,
  Send,
  Bot,
  User,
  FileText,
  Loader2,
  RefreshCw,
  Copy,
  ThumbsUp,
  ThumbsDown,
  ExternalLink,
  Trash2,
  Settings,
  Network,
  Plus
} from 'lucide-react';
import { useGraphs } from '@/hooks/useMorphik';
import { useChatSession } from '@/hooks/useChatSession';
import type { SearchResult } from '@/types/morphik';
import { toast } from 'sonner';

interface ChatInterfaceProps {
  onSourceSelect?: (source: SearchResult) => void;
  maxMessages?: number;
  showSources?: boolean;
}

function ChatInterface({
  onSourceSelect,
  maxMessages = 50,
  showSources = true
}: ChatInterfaceProps) {
  // 知识图谱增强相关状态
  const [useGraphEnhancement, setUseGraphEnhancement] = useState(false);
  const [selectedGraph, setSelectedGraph] = useState<string>('');
  const [availableGraphs, setAvailableGraphs] = useState<Array<{id: string, name: string}>>([]);
  const [inputValue, setInputValue] = useState('');

  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 使用新的聊天会话管理Hook
  const chatSession = useChatSession({
    maxMessages,
    showSources,
    useGraphEnhancement,
    selectedGraph,
    autoSave: true,
  });

  const graphsQuery = useGraphs();

  // 初始化可用图谱列表
  useEffect(() => {
    if (graphsQuery.data) {
      const graphs = graphsQuery.data.map(graph => ({
        id: graph.name,
        name: graph.name
      }));
      setAvailableGraphs(graphs);

      // 如果还没有选择图谱且有可用图谱，默认选择第一个
      if (!selectedGraph && graphs.length > 0) {
        setSelectedGraph(graphs[0].id);
      }
    } else if (graphsQuery.error) {
      console.warn('获取图谱列表失败:', graphsQuery.error);
      setAvailableGraphs([]);
    }
  }, [graphsQuery.data, graphsQuery.error, selectedGraph]);

  // 更新聊天会话配置
  useEffect(() => {
    chatSession.updateOptions({
      useGraphEnhancement,
      selectedGraph,
      showSources,
      maxMessages,
    });
  }, [useGraphEnhancement, selectedGraph, showSources, maxMessages, chatSession]);





  // 自动滚动到底部
  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatSession.messages]);

  // 聚焦输入框
  useEffect(() => {
    if (inputRef.current && !chatSession.isLoading) {
      inputRef.current.focus();
    }
  }, [chatSession.isLoading]);

  // 发送消息
  const handleSendMessage = async () => {
    const query = inputValue.trim();
    if (!query || chatSession.isLoading) return;

    // 使用chatSession发送消息
    await chatSession.sendMessage(query);
    setInputValue('');

  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 复制消息内容
  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  // 消息反馈
  const handleFeedback = (messageId: string, feedback: 'positive' | 'negative') => {
    chatSession.updateMessage(messageId, { feedback });
  };

  // 清空对话
  const clearChat = () => {
    chatSession.clearChat();
  };

  // 重新生成回答
  const regenerateResponse = async (messageId: string) => {
    await chatSession.regenerateResponse(messageId);
  };

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="flex flex-col h-[600px]">
      {/* 聊天头部 */}
      <Card className="flex-none">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              <CardTitle>AI智能问答</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                会话ID: {chatSession.currentChatId.slice(0, 8)}...
              </Badge>
              <Badge variant="outline" className="text-xs">
                {chatSession.messages.length - 1} 条对话
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={clearChat}
                disabled={chatSession.isLoading}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => chatSession.createNewSession()}
                disabled={chatSession.isLoading}
                title="创建新会话"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <CardDescription>
            基于知识库的智能问答，为您提供准确的信息和来源引用。
          </CardDescription>

          {/* 知识图谱增强选项 - 强制显示 */}
          <div className="mt-3 pt-3 border-t bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center space-x-2">
                  <Button
                    variant={useGraphEnhancement ? "default" : "outline"}
                    size="sm"
                    onClick={() => setUseGraphEnhancement(!useGraphEnhancement)}
                  >
                    <Network className="h-4 w-4 mr-2" />
                    知识图谱增强 {useGraphEnhancement ? '(已启用)' : '(未启用)'}
                  </Button>
                </div>



                {useGraphEnhancement && (
                  <Select
                    value={selectedGraph}
                    onValueChange={setSelectedGraph}
                    disabled={availableGraphs.length === 0 || graphsQuery.isLoading}
                  >
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder={
                        graphsQuery.isLoading
                          ? "加载图谱列表..."
                          : availableGraphs.length === 0
                            ? "暂无可用图谱"
                            : "选择知识图谱"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {graphsQuery.isLoading ? (
                        <SelectItem value="loading" disabled>
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-3 w-3 animate-spin" />
                            加载中...
                          </div>
                        </SelectItem>
                      ) : availableGraphs.length === 0 ? (
                        <SelectItem value="empty" disabled>
                          <div className="flex items-center gap-2">
                            <Network className="h-3 w-3" />
                            暂无可用图谱
                          </div>
                        </SelectItem>
                      ) : (
                        availableGraphs.map((graph) => (
                          <SelectItem key={graph.id} value={graph.id}>
                            <div className="flex items-center gap-2">
                              <Network className="h-3 w-3" />
                              {graph.name}
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                )}
              </div>

              {useGraphEnhancement && (
                <Badge variant="secondary" className="text-xs">
                  <Network className="h-3 w-3 mr-1" />
                  图谱增强模式
                </Badge>
              )}
            </div>

            {useGraphEnhancement && (
              <p className="text-xs text-muted-foreground mt-2">
                启用知识图谱增强后，系统将通过图谱关系发现更多相关信息，提供更全面的回答。
              </p>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* 消息列表 */}
      <Card className="flex-1 flex flex-col mt-4">
        <CardContent className="flex-1 p-0">
          <ScrollArea className="h-full p-4" ref={scrollAreaRef}>
            <div className="space-y-4">
              {chatSession.messages.map((message) => (
                <div key={message.id} className="space-y-2">
                  {/* 消息气泡 */}
                  <div className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                    {message.type === 'assistant' && (
                      <div className="flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                        <Bot className="h-4 w-4 text-primary-foreground" />
                      </div>
                    )}

                    <div className={`max-w-[80%] ${message.type === 'user' ? 'order-1' : ''}`}>
                      <div className={`
                        rounded-lg px-4 py-2
                        ${message.type === 'user'
                          ? 'bg-primary text-primary-foreground ml-auto'
                          : 'bg-muted'
                        }
                      `}>
                        {message.isLoading ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span className="text-sm">正在思考...</span>
                          </div>
                        ) : (
                          <div className="text-sm whitespace-pre-wrap">
                            {message.content}
                          </div>
                        )}
                      </div>

                      {/* 消息元信息 */}
                      <div className={`flex items-center gap-2 mt-1 text-xs text-muted-foreground ${
                        message.type === 'user' ? 'justify-end' : 'justify-start'
                      }`}>
                        <span>{formatTime(message.timestamp)}</span>

                        {message.type === 'assistant' && !message.isLoading && (
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyMessage(message.content)}
                              className="h-6 w-6 p-0"
                            >
                              <Copy className="h-3 w-3" />
                            </Button>

                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => regenerateResponse(message.id)}
                              className="h-6 w-6 p-0"
                              disabled={chatSession.isLoading}
                              title="重新生成回答"
                            >
                              <RefreshCw className="h-3 w-3" />
                            </Button>

                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleFeedback(message.id, 'positive')}
                              className={`h-6 w-6 p-0 ${
                                message.feedback === 'positive' ? 'text-green-600' : ''
                              }`}
                            >
                              <ThumbsUp className="h-3 w-3" />
                            </Button>

                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleFeedback(message.id, 'negative')}
                              className={`h-6 w-6 p-0 ${
                                message.feedback === 'negative' ? 'text-red-600' : ''
                              }`}
                            >
                              <ThumbsDown className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>

                      {/* 错误信息 */}
                      {message.error && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-600">
                          错误: {message.error}
                        </div>
                      )}
                    </div>

                    {message.type === 'user' && (
                      <div className="flex-shrink-0 w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-secondary-foreground" />
                      </div>
                    )}
                  </div>

                  {/* 来源引用 */}
                  {message.sources && message.sources.length > 0 && showSources && (
                    <div className="ml-11 space-y-2">
                      <Separator />
                      <div className="text-xs font-medium text-muted-foreground">
                        参考来源 ({message.sources.length})
                      </div>
                      <div className="space-y-1">
                        {message.sources.map((source, index) => (
                          <div
                            key={`${source.id}-${index}`}
                            className="p-2 bg-accent/50 rounded border cursor-pointer hover:bg-accent transition-colors"
                            onClick={() => onSourceSelect?.(source)}
                          >
                            <div className="flex items-center justify-between mb-1">
                              <div className="flex items-center gap-1">
                                <FileText className="h-3 w-3" />
                                <span className="text-xs font-medium">
                                  文档 #{source.document_id.slice(-8)}
                                </span>
                                {source.chunk_index !== undefined && (
                                  <Badge variant="outline" className="text-xs h-4">
                                    块 {source.chunk_index}
                                  </Badge>
                                )}
                              </div>
                              <div className="flex items-center gap-1">
                                <Badge variant="secondary" className="text-xs h-4">
                                  {(source.similarity_score * 100).toFixed(1)}%
                                </Badge>
                                <ExternalLink className="h-3 w-3" />
                              </div>
                            </div>
                            <p className="text-xs text-muted-foreground line-clamp-2">
                              {source.content}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* 输入区域 */}
      <Card className="flex-none mt-4">
        <CardContent className="p-4">
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Input
                ref={inputRef}
                placeholder="输入您的问题..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyPress}
                disabled={chatSession.isLoading}
                className="pr-12"
              />
              {inputValue && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setInputValue('')}
                  className="absolute right-1 top-1 h-8 w-8 p-0"
                >
                  ×
                </Button>
              )}
            </div>
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || chatSession.isLoading}
              className="px-4"
            >
              {chatSession.isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* 快捷问题 */}
          <div className="mt-3">
            <div className="text-xs text-muted-foreground mb-2">快捷问题:</div>
            <div className="flex flex-wrap gap-2">
              {[
                '请你自我介绍一下？',
                '这个知识库包含什么内容？',
                '你是哪个大模型，母公司是哪个？',
                '如何上传文档？'
              ].map((question, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="cursor-pointer hover:bg-primary hover:text-primary-foreground text-xs"
                  onClick={() => setInputValue(question)}
                >
                  {question}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ChatInterface;
