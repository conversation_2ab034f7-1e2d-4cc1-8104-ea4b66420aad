/**
 * 权限守卫组件
 * 根据用户权限控制组件显示
 */
import React from 'react'
import { usePermissions } from '@/hooks/usePermissions'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import { AlertTriangle, Lock } from 'lucide-react'

interface PermissionGuardProps {
  children: React.ReactNode
  permissions?: string[]
  roles?: string[]
  requireAll?: boolean // true: 需要所有权限/角色, false: 需要任一权限/角色
  fallback?: React.ReactNode
  showError?: boolean
  loading?: React.ReactNode
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permissions = [],
  roles = [],
  requireAll = false,
  fallback,
  showError = true,
  loading
}) => {
  const {
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    hasAnyRole,
    loading: permissionsLoading,
    error
  } = usePermissions()

  // 加载中状态
  if (permissionsLoading) {
    return loading || (
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
    )
  }

  // 错误状态
  if (error && showError) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          权限检查失败: {error}
        </AlertDescription>
      </Alert>
    )
  }

  // 检查权限
  let hasRequiredPermissions = true
  if (permissions.length > 0) {
    hasRequiredPermissions = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions)
  }

  // 检查角色
  let hasRequiredRoles = true
  if (roles.length > 0) {
    hasRequiredRoles = requireAll
      ? roles.every(role => hasRole(role))
      : hasAnyRole(roles)
  }

  // 最终权限检查
  const hasAccess = hasRequiredPermissions && hasRequiredRoles

  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>
    }

    if (showError) {
      return (
        <Alert variant="destructive">
          <Lock className="h-4 w-4" />
          <AlertDescription>
            您没有访问此内容的权限
            {permissions.length > 0 && (
              <div className="mt-1 text-xs">
                需要权限: {permissions.join(', ')}
              </div>
            )}
            {roles.length > 0 && (
              <div className="mt-1 text-xs">
                需要角色: {roles.join(', ')}
              </div>
            )}
          </AlertDescription>
        </Alert>
      )
    }

    return null
  }

  return <>{children}</>
}

// 便捷的权限检查组件
export const RequirePermission: React.FC<{
  permission: string
  children: React.ReactNode
  fallback?: React.ReactNode
}> = ({ permission, children, fallback }) => (
  <PermissionGuard permissions={[permission]} fallback={fallback}>
    {children}
  </PermissionGuard>
)

export const RequireRole: React.FC<{
  role: string
  children: React.ReactNode
  fallback?: React.ReactNode
}> = ({ role, children, fallback }) => (
  <PermissionGuard roles={[role]} fallback={fallback}>
    {children}
  </PermissionGuard>
)

export const RequireAdmin: React.FC<{
  children: React.ReactNode
  fallback?: React.ReactNode
}> = ({ children, fallback }) => (
  <PermissionGuard roles={['admin', 'superadmin']} fallback={fallback}>
    {children}
  </PermissionGuard>
)

// 使用示例组件
export const PermissionGuardExample: React.FC = () => {
  return (
    <div className="space-y-4">
      {/* 需要特定权限 */}
      <RequirePermission permission="knowledge:create">
        <button>创建知识库</button>
      </RequirePermission>

      {/* 需要管理员角色 */}
      <RequireAdmin>
        <button>管理员功能</button>
      </RequireAdmin>

      {/* 需要多个权限（任一） */}
      <PermissionGuard 
        permissions={['knowledge:read', 'content:read']}
        requireAll={false}
      >
        <div>可以查看知识库或内容</div>
      </PermissionGuard>

      {/* 需要多个权限（全部） */}
      <PermissionGuard 
        permissions={['knowledge:update', 'ai:config']}
        requireAll={true}
      >
        <div>可以更新知识库并配置AI</div>
      </PermissionGuard>

      {/* 自定义无权限提示 */}
      <PermissionGuard 
        permissions={['system:config']}
        fallback={<div className="text-muted-foreground">需要系统配置权限</div>}
        showError={false}
      >
        <button>系统配置</button>
      </PermissionGuard>
    </div>
  )
}
