import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Search, Loader2 } from 'lucide-react';

interface SimpleSearchProps {
  onResultSelect?: (result: any) => void;
  defaultQuery?: string;
  showFilters?: boolean;
}

function SimpleSearch({
  onResultSelect,
  defaultQuery = '',
  showFilters = true
}: SimpleSearchProps) {
  const [query, setQuery] = useState(defaultQuery);
  const [isLoading, setIsLoading] = useState(false);

  const handleSearch = async (searchQuery: string = query) => {
    if (!searchQuery.trim()) return;
    setIsLoading(true);

    try {
      console.log('Searching for:', searchQuery);
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (value: string) => {
    setQuery(value);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            智能搜索
          </CardTitle>
          <CardDescription>
            在知识库中搜索相关文档和内容
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="输入您的问题或关键词..."
                value={query}
                onChange={(e) => handleInputChange(e.target.value)}
                onKeyDown={handleKeyPress}
                className="pl-10 pr-20"
              />
              <Button
                onClick={() => handleSearch()}
                disabled={!query.trim() || isLoading}
                className="absolute right-1 top-1 h-8"
                size="sm"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {query && (
        <Card>
          <CardContent className="text-center py-12">
            <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">智能搜索功能</h3>
            <p className="text-muted-foreground">
              搜索功能正在开发中，敬请期待
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default SimpleSearch;
