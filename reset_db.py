#!/usr/bin/env python3
"""
数据库管理脚本 - 合并版本
整合数据库初始化、重置、用户权限管理和销冠实战训练数据初始化功能
支持多种操作模式：初始化、重置、清理、权限管理等
"""
import asyncio
import sys
import subprocess
import argparse
import os
from pathlib import Path

# 确保脚本可以从任何位置运行
script_dir = Path(__file__).parent.absolute()

# 检测项目结构并设置正确的路径
if (script_dir / 'backend').exists():
    # 脚本在项目根目录
    backend_dir = script_dir / 'backend'
    project_root = script_dir
else:
    # 脚本在backend目录
    backend_dir = script_dir
    project_root = script_dir.parent

# 添加backend目录到Python路径
sys.path.insert(0, str(backend_dir))

# 切换到backend目录以确保相对路径正确
original_cwd = os.getcwd()
os.chdir(backend_dir)

print(f"📁 工作目录: {os.getcwd()}")
print(f"📁 Backend目录: {backend_dir}")
print(f"📁 项目根目录: {project_root}")

from app.core.database import init_db, AsyncSessionLocal, Base, engine
from app.core.auth import get_password_hash
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from datetime import datetime

# 导入所有模型
from app.models.user import User
from app.models.role import Role, Permission
from app.models.user_role import UserRole, RolePermission
from app.models.task import Task, TaskStatus, TaskPriority
from app.models.password_reset import PasswordResetToken
from app.models.sales_training import (
    SalesTrainingCustomer,
    SalesTrainingCountry,
    SalesTrainingProduct,
    SalesTrainingScenario
)


async def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    try:
        async with AsyncSessionLocal() as session:
            await session.execute(text("SELECT 1"))
            print("✅ 数据库连接正常")
            return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


async def reset_database():
    """重置数据库 - 删除所有表并重新创建"""
    print("⚠️ 正在重置数据库...")
    print("   这将删除所有现有数据！")

    try:
        # 先尝试标准删除
        try:
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.drop_all)
                print("✅ 所有表已删除")
        except Exception as drop_error:
            print(f"⚠️ 标准删除失败，尝试强制删除: {drop_error}")
            # 如果标准删除失败，使用 CASCADE 强制删除
            async with engine.begin() as conn:
                await conn.execute(text("DROP SCHEMA public CASCADE"))
                await conn.execute(text("CREATE SCHEMA public"))
                await conn.execute(text("GRANT ALL ON SCHEMA public TO zht_user"))
                await conn.execute(text("GRANT ALL ON SCHEMA public TO public"))
                print("✅ 使用 CASCADE 强制删除所有表")

        # 重新创建所有表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
            print("✅ 所有表已重新创建")

        return True
    except Exception as e:
        print(f"❌ 数据库重置失败: {e}")
        return False



def run_database_migrations():
    """运行数据库迁移"""
    print("运行数据库迁移...")
    try:
        # 运行 Alembic 迁移
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            capture_output=True,
            text=True,
            cwd=backend_dir
        )

        if result.returncode == 0:
            print("✅ 数据库迁移完成")
            return True
        else:
            print(f"⚠️ 数据库迁移可能失败: {result.stderr}")
            # 不抛出异常，继续执行其他初始化
            return False
    except Exception as e:
        print(f"⚠️ 数据库迁移执行失败: {e}")
        return False


async def create_permissions():
    """创建基于菜单的系统权限"""
    print("创建基于菜单的系统权限...")

    async with AsyncSessionLocal() as session:
        permissions_data = [
            # 主要功能权限
            ("dashboard:access", "工作台访问", "主要功能"),
            ("tasks:access", "任务管理访问", "主要功能"),

            # 贸易教官权限
            ("trade_coach:access", "贸易教官访问", "贸易教官"),
            ("company_knowledge:access", "企业知识库访问", "贸易教官"),
            ("trade_knowledge:access", "外贸知识库访问", "贸易教官"),
            ("market_analysis:access", "全球市场分析访问", "贸易教官"),
            ("group_analysis:access", "特定群体需求分析访问", "贸易教官"),
            ("sales_training:access", "销冠实战训练访问", "贸易教官"),
            ("enhanced_training:access", "增强版训练演示访问", "贸易教官"),
            ("learning_plan:access", "个性化学习方案访问", "贸易教官"),

            # AI工具权限
            ("ai_knowledge:access", "智能知识库访问", "AI工具"),
            ("ai_chat:access", "智能对话访问", "AI工具"),
            ("templates:access", "话术模板访问", "AI工具"),

            # 系统功能权限
            ("admin:access", "管理面板访问", "系统功能"),

            # 管理员专用权限
            ("user:manage", "用户管理", "管理员功能"),
            ("role:manage", "角色管理", "管理员功能"),
            ("permission:manage", "权限管理", "管理员功能"),
        ]

        permissions = []
        for name, description, category in permissions_data:
            permission = Permission(
                name=name,
                description=description,
                category=category
            )
            session.add(permission)
            permissions.append(permission)

        await session.commit()
        print(f"✅ 创建了 {len(permissions)} 个基于菜单的权限")
        return permissions


async def create_roles():
    """创建基于菜单的系统角色"""
    print("创建基于菜单的系统角色...")

    async with AsyncSessionLocal() as session:
        # 获取所有权限
        from sqlalchemy import select
        result = await session.execute(select(Permission))
        permissions = result.scalars().all()

        # 创建角色
        admin_role = Role(name="admin", description="管理员", is_active=True)

        session.add(admin_role)
        await session.commit()

        # 刷新以获取ID
        await session.refresh(admin_role)

        # 刷新权限对象以确保ID可用
        for permission in permissions:
            await session.refresh(permission)

        # 为管理员分配所有权限
        for permission in permissions:
            role_permission = RolePermission(
                role_id=admin_role.id,
                permission_id=permission.id
            )
            session.add(role_permission)

        await session.commit()
        print("✅ 创建了 1 个角色并分配权限")

        # 使用SQL查询获取角色ID，避免ORM的异步问题
        result = await session.execute(text("""
            SELECT name, id FROM roles
            WHERE name = 'admin'
        """))
        role_rows = result.fetchall()

        role_ids = {}
        for row in role_rows:
            role_ids[row[0]] = row[1]

        return role_ids


async def create_admin_user(role_ids):
    """创建初始管理员用户"""
    print("创建初始管理员用户...")

    async with AsyncSessionLocal() as session:
        # 创建管理员用户
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            full_name="系统管理员",
            is_active=True,
            is_superuser=True
        )
        session.add(admin_user)
        await session.commit()
        await session.refresh(admin_user)

        # 为管理员分配管理员角色
        user_role = UserRole(
            user_id=admin_user.id,
            role_id=role_ids["admin"]
        )
        session.add(user_role)



        await session.commit()
        print("✅ 创建了管理员用户")

        return admin_user


async def create_sample_tasks(force_recreate=False):
    """创建示例任务数据"""
    async with AsyncSessionLocal() as session:
        # 检查是否已有数据
        from sqlalchemy import select, func
        result = await session.execute(select(func.count(Task.id)))
        count = result.scalar()

        if count > 0 and not force_recreate:
            print(f"数据库中已有 {count} 个任务，跳过示例数据创建")
            return
        elif count > 0 and force_recreate:
            print(f"强制重新创建模式：删除现有 {count} 个任务")
            # 删除现有任务
            await session.execute(text("DELETE FROM tasks"))
            await session.commit()

        # 创建示例任务
        sample_tasks = [
            Task(
                title="完成项目文档",
                description="编写项目的技术文档和用户手册，包括API文档和部署指南",
                status=TaskStatus.PENDING,
                priority=TaskPriority.HIGH,
                assignee="张三",
                is_active=True
            ),
            Task(
                title="修复登录bug",
                description="解决用户登录时的验证问题，确保安全性",
                status=TaskStatus.IN_PROGRESS,
                priority=TaskPriority.URGENT,
                assignee="李四",
                is_active=True
            ),
            Task(
                title="数据库优化",
                description="优化查询性能，添加必要的索引",
                status=TaskStatus.COMPLETED,
                priority=TaskPriority.MEDIUM,
                assignee="王五",
                is_active=True
            ),
            Task(
                title="前端界面优化",
                description="改进用户界面的响应式设计",
                status=TaskStatus.PENDING,
                priority=TaskPriority.MEDIUM,
                assignee="赵六",
                is_active=True
            ),
            Task(
                title="API性能测试",
                description="对所有API端点进行性能测试和优化",
                status=TaskStatus.IN_PROGRESS,
                priority=TaskPriority.HIGH,
                assignee="钱七",
                is_active=True
            ),
            Task(
                title="系统初始化完成",
                description="数据库已重置并创建了初始管理员账号",
                status=TaskStatus.COMPLETED,
                priority=TaskPriority.HIGH,
                assignee="系统",
                is_active=True
            ),
        ]

        # 添加到数据库
        for task in sample_tasks:
            session.add(task)

        await session.commit()
        print(f"✅ 创建了 {len(sample_tasks)} 个示例任务")


def init_sales_training_data(force_recreate=False):
    """初始化销冠实战训练数据"""
    if force_recreate:
        print("强制重新创建销冠实战训练数据...")
    else:
        print("初始化销冠实战训练数据...")

    # 创建同步数据库引擎和会话
    sync_database_url = settings.DATABASE_URL.replace("+asyncpg", "")
    sync_engine = create_engine(sync_database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=sync_engine)
    db = SessionLocal()

    try:
        # 初始化国家数据
        countries_data = [
            {'name': '美国', 'code': 'US', 'flag': '🇺🇸'},
            {'name': '德国', 'code': 'DE', 'flag': '🇩🇪'},
            {'name': '日本', 'code': 'JP', 'flag': '🇯🇵'},
            {'name': '英国', 'code': 'GB', 'flag': '🇬🇧'},
            {'name': '法国', 'code': 'FR', 'flag': '🇫🇷'},
            {'name': '加拿大', 'code': 'CA', 'flag': '🇨🇦'},
            {'name': '澳大利亚', 'code': 'AU', 'flag': '🇦🇺'},
            {'name': '新加坡', 'code': 'SG', 'flag': '🇸🇬'}
        ]

        countries_added = 0
        if force_recreate:
            # 删除现有国家数据
            db.query(SalesTrainingCountry).delete()
            print("   - 已删除现有国家数据")

        for country_data in countries_data:
            existing = db.query(SalesTrainingCountry).filter(
                SalesTrainingCountry.name == country_data['name']
            ).first()

            if not existing or force_recreate:
                if existing and force_recreate:
                    db.delete(existing)
                country = SalesTrainingCountry(**country_data)
                db.add(country)
                countries_added += 1

        # 初始化产品数据
        products_data = [
            {'name': '智能制造系统', 'category': '工业自动化', 'description': '工业4.0智能制造解决方案'},
            {'name': '数据分析平台', 'category': '大数据', 'description': '企业级数据分析和可视化平台'},
            {'name': '云服务解决方案', 'category': '云计算', 'description': '企业云服务和基础设施解决方案'},
            {'name': 'AI客服系统', 'category': '人工智能', 'description': '智能客服和自动化服务系统'},
            {'name': '物联网平台', 'category': '物联网', 'description': '工业物联网和设备管理平台'},
            {'name': '区块链解决方案', 'category': '区块链', 'description': '企业级区块链技术应用'},
            {'name': '网络安全系统', 'category': '网络安全', 'description': '全方位网络安全防护解决方案'},
            {'name': '移动应用平台', 'category': '移动开发', 'description': '跨平台移动应用开发解决方案'}
        ]

        products_added = 0
        if force_recreate:
            # 删除现有产品数据
            db.query(SalesTrainingProduct).delete()
            print("   - 已删除现有产品数据")

        for product_data in products_data:
            existing = db.query(SalesTrainingProduct).filter(
                SalesTrainingProduct.name == product_data['name'],
                SalesTrainingProduct.category == product_data['category']
            ).first()

            if not existing or force_recreate:
                if existing and force_recreate:
                    db.delete(existing)
                product = SalesTrainingProduct(**product_data)
                db.add(product)
                products_added += 1

        # 提交所有更改
        db.commit()

        # 获取最终统计
        countries_count = db.query(SalesTrainingCountry).filter(SalesTrainingCountry.is_active == True).count()
        products_count = db.query(SalesTrainingProduct).filter(SalesTrainingProduct.is_active == True).count()

        # 初始化训练场景数据
        scenarios_data = [
            {
                'name': '价格敏感型客户谈判',
                'description': '面对对价格极其敏感的客户，如何在保持利润的同时达成交易',
                'category': '价格谈判',
                'difficulty': 'intermediate',
                'duration': 15,
                'objectives': '["掌握价值销售技巧", "学会处理价格异议", "建立长期合作关系"]',
                'challenges': '["客户预算有限", "竞争对手报价更低", "需要快速决策"]',
                'tags': '["价格谈判", "价值销售", "异议处理"]',
                'icon': '💰',
                'color': 'bg-yellow-500'
            },
            {
                'name': '技术导向型客户沟通',
                'description': '与注重技术细节的客户进行深度技术交流和产品演示',
                'category': '技术销售',
                'difficulty': 'advanced',
                'duration': 25,
                'objectives': '["掌握技术销售技巧", "建立技术可信度", "处理技术异议"]',
                'challenges': '["客户技术要求高", "需要深度技术知识", "竞品技术对比"]',
                'tags': '["技术销售", "产品演示", "技术异议"]',
                'icon': '🔧',
                'color': 'bg-blue-500'
            },
            {
                'name': '决策者关系建立',
                'description': '与企业高层决策者建立信任关系，推进重大项目合作',
                'category': '关系建立',
                'difficulty': 'expert',
                'duration': 30,
                'objectives': '["建立高层信任", "理解决策流程", "推进战略合作"]',
                'challenges': '["时间有限", "竞争激烈", "决策复杂"]',
                'tags': '["高层沟通", "战略合作", "信任建立"]',
                'icon': '🤝',
                'color': 'bg-purple-500'
            },
            {
                'name': '跨文化商务沟通',
                'description': '在不同文化背景下进行有效的商务沟通和谈判',
                'category': '跨文化沟通',
                'difficulty': 'advanced',
                'duration': 20,
                'objectives': '["理解文化差异", "适应沟通风格", "避免文化冲突"]',
                'challenges': '["语言障碍", "文化误解", "沟通方式差异"]',
                'tags': '["跨文化", "国际业务", "沟通技巧"]',
                'icon': '🌍',
                'color': 'bg-green-500'
            },
            {
                'name': '紧急需求响应',
                'description': '快速响应客户紧急需求，在时间压力下完成销售',
                'category': '应急处理',
                'difficulty': 'intermediate',
                'duration': 10,
                'objectives': '["快速响应", "高效沟通", "紧急决策"]',
                'challenges': '["时间紧迫", "信息不完整", "压力巨大"]',
                'tags': '["应急响应", "快速决策", "时间管理"]',
                'icon': '⚡',
                'color': 'bg-red-500'
            },
            {
                'name': '新客户开发',
                'description': '开发新客户，建立初步合作关系',
                'category': '客户开发',
                'difficulty': 'beginner',
                'duration': 15,
                'objectives': '["客户需求挖掘", "产品介绍", "初步合作"]',
                'challenges': '["客户陌生", "信任缺失", "竞争激烈"]',
                'tags': '["新客户", "需求挖掘", "关系建立"]',
                'icon': '🎯',
                'color': 'bg-indigo-500'
            }
        ]

        scenarios_added = 0
        if force_recreate:
            # 删除现有训练场景数据
            db.query(SalesTrainingScenario).delete()
            print("   - 已删除现有训练场景数据")

        for scenario_data in scenarios_data:
            existing = db.query(SalesTrainingScenario).filter(
                SalesTrainingScenario.name == scenario_data['name']
            ).first()

            if not existing or force_recreate:
                if existing and force_recreate:
                    db.delete(existing)
                scenario = SalesTrainingScenario(**scenario_data)
                db.add(scenario)
                scenarios_added += 1

        db.commit()
        scenarios_count = db.query(SalesTrainingScenario).filter(SalesTrainingScenario.is_active == True).count()

        # 初始化客户数据（暂无预设客户）
        customers_data = []

        customers_added = 0
        if force_recreate:
            # 删除现有客户数据
            db.query(SalesTrainingCustomer).delete()
            print("   - 已删除现有客户数据")

        for customer_data in customers_data:
            existing = db.query(SalesTrainingCustomer).filter(
                SalesTrainingCustomer.name == customer_data['name']
            ).first()

            if not existing or force_recreate:
                if existing and force_recreate:
                    db.delete(existing)
                customer = SalesTrainingCustomer(**customer_data)
                db.add(customer)
                customers_added += 1

        db.commit()
        customers_count = db.query(SalesTrainingCustomer).filter(SalesTrainingCustomer.is_active == True).count()

        print(f"✅ 销冠实战训练数据初始化完成")
        print(f"   - 新增国家: {countries_added} 个，总计: {countries_count} 个")
        print(f"   - 新增产品: {products_added} 个，总计: {products_count} 个")
        print(f"   - 新增场景: {scenarios_added} 个，总计: {scenarios_count} 个")
        print(f"   - 新增客户: {customers_added} 个，总计: {customers_count} 个")

        return True

    except Exception as e:
        print(f"❌ 销冠实战训练数据初始化失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()


async def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='数据库管理脚本')
    parser.add_argument('--mode', choices=['reset'],
                       default='reset', help='操作模式')
    parser.add_argument('--force', action='store_true',
                       help='强制执行，不询问确认')
    parser.add_argument('--skip-migrations', action='store_true',
                       help='跳过数据库迁移')
    parser.add_argument('--skip-sales-training', action='store_true',
                       help='跳过销冠实战训练数据初始化')
    parser.add_argument('--skip-auth', action='store_true',
                       help='跳过用户权限系统初始化')
    args = parser.parse_args()

    print("=" * 60)
    print("ZHT系统数据库管理工具")
    print("=" * 60)

    # 模式说明
    mode_descriptions = {
        'reset': '重置模式 - 删除所有表并重新创建（完全重置）'
    }

    print(f"\n📋 当前模式: {args.mode}")
    print(f"📝 模式说明: {mode_descriptions[args.mode]}")

    # 危险操作确认
    if args.mode == 'reset' and not args.force:
        print(f"\n⚠️ 警告：{args.mode} 模式将删除数据！")
        confirmation = input("确认继续？输入 'yes' 继续，其他任何输入将取消: ")
        if confirmation != "yes":
            print("❌ 操作已取消")
            return

    try:
        # 0. 测试数据库连接
        print("\n0. 测试数据库连接...")
        if not await test_database_connection():
            print("❌ 数据库连接失败，请检查数据库配置")
            sys.exit(1)

        # 1. 重置数据库
        print(f"\n1. 重置数据库...")
        reset_success = await reset_database()
        if not reset_success:
            print("❌ 数据库重置失败")
            sys.exit(1)

        # 2. 运行数据库迁移
        if not args.skip_migrations:
            print("\n2. 运行数据库迁移...")
            migration_success = run_database_migrations()
        else:
            print("\n2. 跳过数据库迁移")
            migration_success = True

        # 3. 初始化用户权限系统
        if not args.skip_auth:
            print("\n3. 初始化用户权限系统...")
            await create_permissions()
            roles = await create_roles()
            admin_user = await create_admin_user(roles)
            print("✅ 用户权限系统初始化完成")
        else:
            print("\n3. 跳过用户权限系统初始化")

        # 4. 创建示例任务数据
        print("\n4. 创建示例任务数据...")
        await create_sample_tasks(force_recreate=True)

        # 5. 初始化销冠实战训练数据
        if not args.skip_sales_training:
            print("\n5. 初始化销冠实战训练数据...")
            sales_training_success = init_sales_training_data(force_recreate=True)
        else:
            print("\n5. 跳过销冠实战训练数据初始化")
            sales_training_success = True

        # 总结
        print("\n" + "="*60)
        print("🎉 数据库管理操作完成！")
        print("="*60)

        if not args.skip_auth:
            print("\n📋 初始账号信息:")
            print(f"管理员账号: admin")
            print(f"管理员密码: admin123")
            print(f"管理员邮箱: <EMAIL>")
            print(f"管理员权限: 管理员 (所有权限)")

        print("\n🌐 访问地址:")
        print(f"前端地址: http://localhost:5173")
        print(f"后端API: http://localhost:8001")
        print(f"API文档: http://localhost:8001/docs")

        print("\n📋 功能模块:")
        print("  ✅ 任务管理系统")
        if not args.skip_auth:
            print("  ✅ 用户认证系统")
        if not args.skip_sales_training:
            print("  ✅ 销冠实战训练模块")

        print("="*60)

    except Exception as e:
        print(f"\n❌ 数据库管理操作失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
