#!/usr/bin/env python3
"""
修复版智能市场分析演示系统
解决分析报告生成阶段的异常问题

核心修复：
1. 绕过 SmoLAgents CodeAgent 的格式限制
2. 直接调用 Ollama API 生成分析报告
3. 简化提示词，避免过长问题
4. 增强异常处理和调试信息
"""

import asyncio
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
import httpx
from dataclasses import dataclass


@dataclass
class SearchQuery:
    """搜索查询结构"""
    query: str
    category: str
    priority: int
    description: str


@dataclass
class AnalysisContext:
    """分析上下文"""
    product: str
    region: str
    time_period: str
    analysis_type: str


class FixedMarketAnalysis:
    """修复版智能市场分析系统"""
    
    def __init__(self, base_url: str = "http://localhost:8002"):
        self.base_url = base_url
        self.ollama_url = "http://localhost:11434"
        self.session: Optional[httpx.AsyncClient] = None
        
    async def __aenter__(self):
        self.session = httpx.AsyncClient(timeout=180.0)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.aclose()
    
    def parse_search_term(self, search_term: str) -> AnalysisContext:
        """智能解析搜索词"""
        print(f"🔍 解析搜索词: {search_term}")
        
        # 简化的解析逻辑
        product_match = re.search(r'([^0-9\s]+?)(?=\s*20\d{2}|\s*市场|\s*销售|\s*分析)', search_term)
        product = product_match.group(1).strip() if product_match else "未知产品"
        
        region = "全球"
        if "亚洲" in search_term: region = "亚洲"
        elif "中国" in search_term: region = "中国"
        elif "美国" in search_term: region = "美国"
        elif "欧洲" in search_term: region = "欧洲"
        
        time_match = re.search(r'20\d{2}', search_term)
        time_period = time_match.group(0) if time_match else "2024"
        
        analysis_type = "综合分析"
        if "销售" in search_term: analysis_type = "销售分析"
        elif "竞争" in search_term: analysis_type = "竞争分析"
        elif "趋势" in search_term: analysis_type = "趋势分析"
        
        context = AnalysisContext(product, region, time_period, analysis_type)
        
        print(f"✅ 解析结果: {context.product} | {context.region} | {context.time_period} | {context.analysis_type}")
        return context
    
    def generate_search_queries(self, context: AnalysisContext) -> List[SearchQuery]:
        """生成搜索查询（优化版）"""
        print("🎯 生成搜索策略...")
        
        queries = [
            SearchQuery(
                f"{context.product} {context.region} market size {context.time_period}",
                "市场规模", 1, "获取市场规模数据"
            ),
            SearchQuery(
                f"{context.product} sales trends {context.region} {context.time_period}",
                "销售趋势", 1, "获取销售趋势数据"
            ),
            SearchQuery(
                f"{context.product} competitors {context.region} {context.time_period}",
                "竞争分析", 2, "分析竞争格局"
            )
        ]
        
        print(f"✅ 生成了 {len(queries)} 个搜索策略")
        return queries
    
    async def execute_search(self, queries: List[SearchQuery]) -> Dict[str, Any]:
        """执行搜索"""
        print("🌐 开始执行搜索...")
        
        search_results = {}
        
        for i, query in enumerate(queries, 1):
            print(f"   [{i}/{len(queries)}] 搜索: {query.category}")
            
            try:
                search_prompt = f"""
请使用网络搜索工具查找关于"{query.query}"的市场信息。

请提供简洁的搜索结果，重点关注：
- 市场数据和统计信息
- 关键趋势和洞察
- 相关的数字和事实

保持结果简洁明了。
"""
                
                data = {
                    "query": search_prompt,
                    "max_steps": 8,
                    "use_cache": False,
                    "enable_web_search": True,
                    "timeout": 90
                }
                
                response = await self.session.post(f"{self.base_url}/api/v1/agent/query", json=data)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        search_results[query.category] = {
                            'query': query.query,
                            'result': result.get('result', ''),
                            'priority': query.priority
                        }
                        print(f"      ✅ 成功")
                    else:
                        print(f"      ❌ 失败: {result.get('error', '未知错误')}")
                else:
                    print(f"      ❌ HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"      ❌ 异常: {e}")
            
            if i < len(queries):
                await asyncio.sleep(3)
        
        print(f"✅ 搜索完成，获取 {len(search_results)} 个数据源")
        return search_results
    
    async def generate_analysis_direct(self, context: AnalysisContext, search_results: Dict[str, Any]) -> str:
        """直接调用 Ollama 生成分析报告（绕过 SmoLAgents）"""
        print("📊 直接调用 Ollama 生成分析报告...")
        
        if not search_results:
            return "无法生成分析报告：未获取到有效的搜索数据"
        
        # 构建简化的分析提示词
        data_summary = ""
        for category, data in search_results.items():
            result_summary = str(data['result'])[:300] if data['result'] else "无数据"
            data_summary += f"{category}: {result_summary}\n\n"
        
        # 简化的分析提示词
        analysis_prompt = f"""请基于以下市场搜索数据，为"{context.product}"在{context.region}市场生成简洁的分析报告：

搜索数据：
{data_summary[:1000]}

请生成包含以下内容的报告：

# {context.product} - {context.region}市场分析报告

## 市场现状
- 市场规模和增长情况
- 主要参与者

## 关键趋势
- 销售趋势和发展方向

## 竞争分析
- 主要竞争对手和市场份额

## 未来预测
- 2025年展望和增长驱动因素

## 结论建议
- 核心洞察和战略建议

要求：基于真实数据，逻辑清晰，控制在400字以内。请直接生成分析报告，不要包含代码。"""
        
        try:
            print(f"   提示词长度: {len(analysis_prompt)} 字符")
            print("   调用 Ollama API...")
            
            # 直接调用 Ollama API，绕过 SmoLAgents
            ollama_data = {
                "model": "Qwen3-8B-M:latest",  # 使用当前配置的模型
                "prompt": analysis_prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_predict": 1000  # 限制输出长度
                }
            }
            
            response = await self.session.post(f"{self.ollama_url}/api/generate", json=ollama_data)
            
            if response.status_code == 200:
                result = response.json()
                analysis_report = result.get('response', '')
                
                if analysis_report:
                    print("✅ 分析报告生成完成")
                    return analysis_report.strip()
                else:
                    return "分析报告生成失败: Ollama 返回空结果"
            else:
                error_text = response.text[:200] if response.text else "无响应内容"
                return f"分析报告生成失败: HTTP {response.status_code} - {error_text}"
                
        except asyncio.TimeoutError:
            return "分析报告生成失败: 请求超时"
        except Exception as e:
            error_detail = str(e) if str(e) else "未知异常"
            print(f"❌ 详细异常信息: {error_detail}")
            print(f"❌ 异常类型: {type(e).__name__}")
            return f"分析报告生成失败: {error_detail}"
    
    async def run_analysis(self, search_term: str) -> Dict[str, Any]:
        """运行完整分析流程"""
        start_time = datetime.now()
        print("🚀 启动修复版市场分析系统")
        print("=" * 60)
        print(f"搜索词: {search_term}")
        print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        print()
        
        try:
            # 1. 解析搜索词
            context = self.parse_search_term(search_term)
            
            # 2. 生成搜索策略
            queries = self.generate_search_queries(context)
            
            # 3. 执行搜索
            search_results = await self.execute_search(queries)
            
            if not search_results:
                return {
                    'success': False,
                    'error': '未能获取有效的市场数据',
                    'context': context.__dict__
                }
            
            # 4. 直接生成分析报告（绕过 SmoLAgents）
            analysis_report = await self.generate_analysis_direct(context, search_results)
            
            # 计算执行时间
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            print("=" * 60)
            print("🎉 修复版分析完成!")
            print(f"执行时间: {execution_time:.2f} 秒")
            print(f"数据源数量: {len(search_results)}")
            print("=" * 60)
            
            return {
                'success': True,
                'context': context.__dict__,
                'search_results': search_results,
                'analysis_report': analysis_report,
                'execution_time': execution_time,
                'data_sources_count': len(search_results),
                'timestamp': end_time.isoformat()
            }
            
        except Exception as e:
            error_msg = f"分析执行失败: {str(e)} (类型: {type(e).__name__})"
            print(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'context': context.__dict__ if 'context' in locals() else None
            }


async def main():
    """主演示函数"""
    search_term = "沙滩包2024亚洲市场销售情况分析及2025市场增长预期"
    
    print("🌟 修复版智能市场分析演示")
    print("解决分析报告生成异常问题")
    print("=" * 60)
    print()
    
    async with FixedMarketAnalysis() as analyzer:
        result = await analyzer.run_analysis(search_term)
        
        if result['success']:
            print("\n" + "=" * 60)
            print("📊 市场分析报告")
            print("=" * 60)
            print(result['analysis_report'])
            print("\n" + "=" * 60)
            print("📈 分析统计")
            print(f"数据源数量: {result['data_sources_count']}")
            print(f"执行时间: {result['execution_time']:.2f} 秒")
            print(f"完成时间: {result['timestamp']}")
        else:
            print(f"\n❌ 分析失败: {result['error']}")


if __name__ == "__main__":
    asyncio.run(main())
