"""
销冠实战训练相关的异步CRUD操作
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.exc import IntegrityError
from app.models.sales_training import (
    SalesTrainingCustomer,
    SalesTrainingCountry,
    SalesTrainingProduct,
    SalesTrainingScenario
)
from app.schemas.sales_training import (
    SalesTrainingCustomerCreate,
    SalesTrainingCustomerUpdate,
    SalesTrainingCountryCreate,
    SalesTrainingCountryUpdate,
    SalesTrainingProductCreate,
    SalesTrainingProductUpdate,
    SalesTrainingScenarioCreate,
    SalesTrainingScenarioUpdate
)


class SalesTrainingCustomerCRUD:
    """客户CRUD操作"""

    @staticmethod
    async def get_all(db: AsyncSession) -> List[SalesTrainingCustomer]:
        """获取所有客户"""
        query = select(SalesTrainingCustomer).order_by(SalesTrainingCustomer.created_at.desc())
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def get_by_id(db: AsyncSession, customer_id: int) -> Optional[SalesTrainingCustomer]:
        """根据ID获取客户"""
        query = select(SalesTrainingCustomer).filter(SalesTrainingCustomer.id == customer_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_by_name(db: AsyncSession, name: str) -> Optional[SalesTrainingCustomer]:
        """根据姓名获取客户"""
        query = select(SalesTrainingCustomer).filter(SalesTrainingCustomer.name == name)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def create(db: AsyncSession, customer_data: SalesTrainingCustomerCreate) -> SalesTrainingCustomer:
        """创建客户"""
        customer = SalesTrainingCustomer(**customer_data.dict())
        db.add(customer)
        await db.commit()
        await db.refresh(customer)
        return customer

    @staticmethod
    async def update(db: AsyncSession, customer_id: int, customer_data: SalesTrainingCustomerUpdate) -> Optional[SalesTrainingCustomer]:
        """更新客户"""
        query = select(SalesTrainingCustomer).filter(SalesTrainingCustomer.id == customer_id)
        result = await db.execute(query)
        customer = result.scalar_one_or_none()

        if not customer:
            return None

        update_data = customer_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(customer, field, value)

        await db.commit()
        await db.refresh(customer)
        return customer

    @staticmethod
    async def delete(db: AsyncSession, customer_id: int) -> bool:
        """删除客户（硬删除）"""
        query = select(SalesTrainingCustomer).filter(SalesTrainingCustomer.id == customer_id)
        result = await db.execute(query)
        customer = result.scalar_one_or_none()

        if not customer:
            return False

        await db.delete(customer)
        await db.commit()
        return True

    @staticmethod
    async def soft_delete(db: AsyncSession, customer_id: int) -> bool:
        """软删除客户（标记为不活跃）"""
        query = select(SalesTrainingCustomer).filter(SalesTrainingCustomer.id == customer_id)
        result = await db.execute(query)
        customer = result.scalar_one_or_none()

        if not customer:
            return False

        customer.is_active = False
        await db.commit()
        return True

    @staticmethod
    async def get_by_company(db: AsyncSession, company: str) -> List[SalesTrainingCustomer]:
        """根据公司获取客户列表"""
        query = select(SalesTrainingCustomer).filter(
            SalesTrainingCustomer.company == company
        ).order_by(SalesTrainingCustomer.created_at.desc())
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def get_by_industry(db: AsyncSession, industry: str) -> List[SalesTrainingCustomer]:
        """根据行业获取客户列表"""
        query = select(SalesTrainingCustomer).filter(
            SalesTrainingCustomer.industry == industry
        ).order_by(SalesTrainingCustomer.created_at.desc())
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def search_customers(
        db: AsyncSession,
        *,
        name: Optional[str] = None,
        company: Optional[str] = None,
        industry: Optional[str] = None,
        level: Optional[str] = None,
        region: Optional[str] = None
    ) -> List[SalesTrainingCustomer]:
        """搜索客户"""
        query = select(SalesTrainingCustomer)

        conditions = []
        if name:
            conditions.append(SalesTrainingCustomer.name.ilike(f"%{name}%"))
        if company:
            conditions.append(SalesTrainingCustomer.company.ilike(f"%{company}%"))
        if industry:
            conditions.append(SalesTrainingCustomer.industry == industry)
        if level:
            conditions.append(SalesTrainingCustomer.level == level)
        if region:
            conditions.append(SalesTrainingCustomer.region.ilike(f"%{region}%"))

        if conditions:
            query = query.filter(and_(*conditions))

        query = query.order_by(SalesTrainingCustomer.created_at.desc())
        result = await db.execute(query)
        return result.scalars().all()


class SalesTrainingCountryCRUD:
    """国家CRUD操作"""

    @staticmethod
    async def get_all(db: AsyncSession, include_inactive: bool = False) -> List[SalesTrainingCountry]:
        """获取所有国家"""
        query = select(SalesTrainingCountry)
        if not include_inactive:
            query = query.filter(SalesTrainingCountry.is_active == True)
        query = query.order_by(SalesTrainingCountry.name)
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def get_by_id(db: AsyncSession, country_id: int) -> Optional[SalesTrainingCountry]:
        """根据ID获取国家"""
        query = select(SalesTrainingCountry).filter(SalesTrainingCountry.id == country_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_by_name(db: AsyncSession, name: str) -> Optional[SalesTrainingCountry]:
        """根据名称获取国家"""
        query = select(SalesTrainingCountry).filter(SalesTrainingCountry.name == name)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_by_code(db: AsyncSession, code: str) -> Optional[SalesTrainingCountry]:
        """根据代码获取国家"""
        query = select(SalesTrainingCountry).filter(SalesTrainingCountry.code == code)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def count_active(db: AsyncSession) -> int:
        """获取活跃国家数量"""
        query = select(func.count(SalesTrainingCountry.id)).filter(SalesTrainingCountry.is_active == True)
        result = await db.execute(query)
        return result.scalar()

    @staticmethod
    async def create(db: AsyncSession, country_data: SalesTrainingCountryCreate) -> SalesTrainingCountry:
        """创建国家"""
        try:
            db_country = SalesTrainingCountry(**country_data.dict())
            db.add(db_country)
            await db.commit()
            await db.refresh(db_country)
            return db_country
        except IntegrityError:
            await db.rollback()
            raise ValueError("国家名称或代码已存在")

    @staticmethod
    async def update(db: AsyncSession, country_id: int, country_data: SalesTrainingCountryUpdate) -> Optional[SalesTrainingCountry]:
        """更新国家"""
        db_country = await SalesTrainingCountryCRUD.get_by_id(db, country_id)
        if not db_country:
            return None

        try:
            update_data = country_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_country, field, value)

            await db.commit()
            await db.refresh(db_country)
            return db_country
        except IntegrityError:
            await db.rollback()
            raise ValueError("国家名称或代码已存在")

    @staticmethod
    async def delete(db: AsyncSession, country_id: int) -> bool:
        """删除国家（软删除）"""
        db_country = await SalesTrainingCountryCRUD.get_by_id(db, country_id)
        if not db_country:
            return False

        db_country.is_active = False
        await db.commit()
        return True

    @staticmethod
    async def hard_delete(db: AsyncSession, country_id: int) -> bool:
        """硬删除国家"""
        db_country = await SalesTrainingCountryCRUD.get_by_id(db, country_id)
        if not db_country:
            return False

        await db.delete(db_country)
        await db.commit()
        return True




class SalesTrainingProductCRUD:
    """产品CRUD操作"""

    @staticmethod
    async def get_all(db: AsyncSession, include_inactive: bool = False) -> List[SalesTrainingProduct]:
        """获取所有产品"""
        query = select(SalesTrainingProduct)
        if not include_inactive:
            query = query.filter(SalesTrainingProduct.is_active == True)
        query = query.order_by(SalesTrainingProduct.category, SalesTrainingProduct.name)
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def get_by_id(db: AsyncSession, product_id: int) -> Optional[SalesTrainingProduct]:
        """根据ID获取产品"""
        query = select(SalesTrainingProduct).filter(SalesTrainingProduct.id == product_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_by_name_and_category(db: AsyncSession, name: str, category: str) -> Optional[SalesTrainingProduct]:
        """根据名称和分类获取产品"""
        query = select(SalesTrainingProduct).filter(
            SalesTrainingProduct.name == name,
            SalesTrainingProduct.category == category
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def count_active(db: AsyncSession) -> int:
        """获取活跃产品数量"""
        query = select(func.count(SalesTrainingProduct.id)).filter(SalesTrainingProduct.is_active == True)
        result = await db.execute(query)
        return result.scalar()

    @staticmethod
    async def create(db: AsyncSession, product_data: SalesTrainingProductCreate) -> SalesTrainingProduct:
        """创建产品"""
        try:
            db_product = SalesTrainingProduct(**product_data.dict())
            db.add(db_product)
            await db.commit()
            await db.refresh(db_product)
            return db_product
        except IntegrityError:
            await db.rollback()
            raise ValueError("该分类下已存在同名产品")

    @staticmethod
    async def update(db: AsyncSession, product_id: int, product_data: SalesTrainingProductUpdate) -> Optional[SalesTrainingProduct]:
        """更新产品"""
        db_product = await SalesTrainingProductCRUD.get_by_id(db, product_id)
        if not db_product:
            return None

        try:
            update_data = product_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_product, field, value)

            await db.commit()
            await db.refresh(db_product)
            return db_product
        except IntegrityError:
            await db.rollback()
            raise ValueError("该分类下已存在同名产品")

    @staticmethod
    async def delete(db: AsyncSession, product_id: int) -> bool:
        """删除产品（软删除）"""
        db_product = await SalesTrainingProductCRUD.get_by_id(db, product_id)
        if not db_product:
            return False

        db_product.is_active = False
        await db.commit()
        return True

    @staticmethod
    async def hard_delete(db: AsyncSession, product_id: int) -> bool:
        """硬删除产品"""
        db_product = await SalesTrainingProductCRUD.get_by_id(db, product_id)
        if not db_product:
            return False

        await db.delete(db_product)
        await db.commit()
        return True




class SalesTrainingScenarioCRUD:
    """训练场景CRUD操作"""

    @staticmethod
    async def get_all(db: AsyncSession) -> List[SalesTrainingScenario]:
        """获取所有训练场景"""
        query = select(SalesTrainingScenario).filter(
            SalesTrainingScenario.is_active == True
        ).order_by(SalesTrainingScenario.created_at.desc())
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def get_by_id(db: AsyncSession, scenario_id: int) -> Optional[SalesTrainingScenario]:
        """根据ID获取训练场景"""
        query = select(SalesTrainingScenario).filter(SalesTrainingScenario.id == scenario_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_by_name(db: AsyncSession, name: str) -> Optional[SalesTrainingScenario]:
        """根据名称获取训练场景（包括非活跃的）"""
        query = select(SalesTrainingScenario).filter(SalesTrainingScenario.name == name)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def get_active_by_name(db: AsyncSession, name: str) -> Optional[SalesTrainingScenario]:
        """根据名称获取活跃的训练场景"""
        query = select(SalesTrainingScenario).filter(
            SalesTrainingScenario.name == name,
            SalesTrainingScenario.is_active == True
        )
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def count_active(db: AsyncSession) -> int:
        """获取活跃训练场景数量"""
        query = select(func.count(SalesTrainingScenario.id)).filter(SalesTrainingScenario.is_active == True)
        result = await db.execute(query)
        return result.scalar()

    @staticmethod
    async def create(db: AsyncSession, scenario_data: SalesTrainingScenarioCreate) -> SalesTrainingScenario:
        """创建训练场景"""
        try:
            db_scenario = SalesTrainingScenario(**scenario_data.model_dump())
            db.add(db_scenario)
            await db.commit()
            await db.refresh(db_scenario)
            return db_scenario
        except IntegrityError:
            await db.rollback()
            raise ValueError("训练场景名称已存在")

    @staticmethod
    async def update(db: AsyncSession, scenario_id: int, scenario_data: SalesTrainingScenarioUpdate) -> Optional[SalesTrainingScenario]:
        """更新训练场景"""
        db_scenario = await SalesTrainingScenarioCRUD.get_by_id(db, scenario_id)
        if not db_scenario:
            return None

        # 更新字段
        update_data = scenario_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_scenario, field, value)

        try:
            await db.commit()
            await db.refresh(db_scenario)
            return db_scenario
        except IntegrityError:
            await db.rollback()
            raise ValueError("训练场景名称已存在")

    @staticmethod
    async def soft_delete(db: AsyncSession, scenario_id: int) -> bool:
        """软删除训练场景"""
        db_scenario = await SalesTrainingScenarioCRUD.get_by_id(db, scenario_id)
        if not db_scenario:
            return False

        db_scenario.is_active = False
        await db.commit()
        return True

    @staticmethod
    async def hard_delete(db: AsyncSession, scenario_id: int) -> bool:
        """硬删除训练场景"""
        db_scenario = await SalesTrainingScenarioCRUD.get_by_id(db, scenario_id)
        if not db_scenario:
            return False

        await db.delete(db_scenario)
        await db.commit()
        return True

    @staticmethod
    async def get_by_category(db: AsyncSession, category: str) -> List[SalesTrainingScenario]:
        """根据分类获取训练场景"""
        query = select(SalesTrainingScenario).filter(
            SalesTrainingScenario.category == category,
            SalesTrainingScenario.is_active == True
        ).order_by(SalesTrainingScenario.created_at.desc())
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def get_by_difficulty(db: AsyncSession, difficulty: str) -> List[SalesTrainingScenario]:
        """根据难度获取训练场景"""
        query = select(SalesTrainingScenario).filter(
            SalesTrainingScenario.difficulty == difficulty,
            SalesTrainingScenario.is_active == True
        ).order_by(SalesTrainingScenario.created_at.desc())
        result = await db.execute(query)
        return result.scalars().all()


# 创建CRUD实例
countries_crud = SalesTrainingCountryCRUD()
products_crud = SalesTrainingProductCRUD()
scenarios_crud = SalesTrainingScenarioCRUD()
