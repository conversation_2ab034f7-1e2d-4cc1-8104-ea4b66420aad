/**
 * 任务管理页面
 * 集成任务的CRUD操作和统计展示
 */
import React, { useState } from 'react';
import { Button } from '../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '../components/ui/dialog';
import { Plus, CheckSquare, Clock, AlertCircle, XCircle, Edit, Trash2 } from 'lucide-react';
import { TaskForm } from '../components/TaskForm';
import {
  useTasks,
  useTaskStats,
  useCreateTask,
  useUpdateTask,
  useDeleteTask,
} from '../hooks/useTasks';
import {
  Task,
  CreateTaskRequest,
  UpdateTaskRequest,
  TaskQueryParams,
  statusLabels,
  priorityLabels,
  statusColors,
  priorityColors,
} from '../services/taskApi';

export function TaskManagement() {
  const [selectedTask, setSelectedTask] = useState<Task | undefined>();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState<Task | undefined>();
  const [queryParams, setQueryParams] = useState<TaskQueryParams>({
    skip: 0,
    limit: 20,
  });

  // 查询hooks
  const { data: tasksData, isLoading: isLoadingTasks, error: tasksError } = useTasks(queryParams);
  const { data: statsData, isLoading: isLoadingStats, error: statsError } = useTaskStats();

  // 调试信息
  console.log('TaskManagement - tasksData:', tasksData);
  console.log('TaskManagement - statsData:', statsData);
  console.log('TaskManagement - tasksError:', tasksError);
  console.log('TaskManagement - statsError:', statsError);

  // 变更hooks
  const createTaskMutation = useCreateTask();
  const updateTaskMutation = useUpdateTask();
  const deleteTaskMutation = useDeleteTask();

  // 事件处理函数
  const handleCreateTask = () => {
    setSelectedTask(undefined);
    setIsFormOpen(true);
  };

  const handleEditTask = (task: Task) => {
    setSelectedTask(task);
    setIsFormOpen(true);
  };

  const handleDeleteTask = (task: Task) => {
    setTaskToDelete(task);
    setIsDeleteDialogOpen(true);
  };

  const handleFormSubmit = (data: CreateTaskRequest | UpdateTaskRequest) => {
    if (selectedTask) {
      // 更新任务
      updateTaskMutation.mutate(
        { id: selectedTask.id, data },
        {
          onSuccess: () => {
            setIsFormOpen(false);
            setSelectedTask(undefined);
          },
        }
      );
    } else {
      // 创建任务
      createTaskMutation.mutate(data as CreateTaskRequest, {
        onSuccess: () => {
          setIsFormOpen(false);
        },
      });
    }
  };

  const handleFormCancel = () => {
    setIsFormOpen(false);
    setSelectedTask(undefined);
  };

  const confirmDelete = () => {
    if (taskToDelete) {
      deleteTaskMutation.mutate(taskToDelete.id, {
        onSuccess: () => {
          setIsDeleteDialogOpen(false);
          setTaskToDelete(undefined);
        },
      });
    }
  };

  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setTaskToDelete(undefined);
  };

  // 使用真实数据或模拟数据
  const tasks = tasksData?.items || [];
  const stats = statsData || {
    total: 0,
    pending: 0,
    in_progress: 0,
    completed: 0,
    cancelled: 0,
    by_priority: {
      low: 0,
      medium: 0,
      high: 0,
      urgent: 0,
    },
  };

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">任务管理</h1>
          <p className="text-muted-foreground">
            管理您的任务，跟踪进度和优先级
          </p>
        </div>
        <Button onClick={handleCreateTask}>
          <Plus className="h-4 w-4 mr-2" />
          创建新任务
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总任务数</CardTitle>
            <CheckSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              所有任务的总数
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待处理</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">
              等待开始的任务
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">进行中</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.in_progress}</div>
            <p className="text-xs text-muted-foreground">
              正在执行的任务
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <CheckSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completed}</div>
            <p className="text-xs text-muted-foreground">
              已完成的任务
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 任务列表 */}
      <Tabs defaultValue="list" className="space-y-4">
        <TabsList>
          <TabsTrigger value="list">任务列表</TabsTrigger>
          <TabsTrigger value="stats">详细统计</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>任务列表</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingTasks ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">加载中...</p>
                </div>
              ) : tasks.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">暂无任务</p>
                  <Button onClick={handleCreateTask} className="mt-4">
                    <Plus className="h-4 w-4 mr-2" />
                    创建第一个任务
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {tasks.map((task) => (
                    <div
                      key={task.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex-1">
                        <h3 className="font-medium">{task.title}</h3>
                        <p className="text-sm text-muted-foreground">
                          {task.description}
                        </p>
                        <div className="flex items-center space-x-2 mt-2">
                          <span className={`px-2 py-1 text-xs rounded-full ${statusColors[task.status]}`}>
                            {statusLabels[task.status]}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${priorityColors[task.priority]}`}>
                            {priorityLabels[task.priority]}
                          </span>
                          {task.assignee && (
                            <span className="text-sm text-muted-foreground">
                              负责人: {task.assignee}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditTask(task)}
                          disabled={updateTaskMutation.isPending}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          编辑
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteTask(task)}
                          disabled={deleteTaskMutation.isPending}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          删除
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>按优先级统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(stats.by_priority).map(([priority, count]) => (
                    <div key={priority} className="flex justify-between">
                      <span className="capitalize">
                        {priority === 'urgent'
                          ? '紧急'
                          : priority === 'high'
                          ? '高'
                          : priority === 'medium'
                          ? '中'
                          : '低'}
                      </span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>完成率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0}%
                </div>
                <p className="text-sm text-muted-foreground">
                  {stats.completed} / {stats.total} 任务已完成
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* 任务表单对话框 */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-2xl">
          <TaskForm
            task={selectedTask}
            onSubmit={handleFormSubmit}
            onCancel={handleFormCancel}
            isLoading={createTaskMutation.isPending || updateTaskMutation.isPending}
          />
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除任务 "{taskToDelete?.title}" 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={cancelDelete}>
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={deleteTaskMutation.isPending}
            >
              {deleteTaskMutation.isPending ? '删除中...' : '确认删除'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
