"""
邮件服务
用于发送各种类型的邮件
"""
import logging
from typing import Optional
from app.core.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """邮件服务类"""
    
    def __init__(self):
        self.frontend_url = "http://localhost:5173"  # 前端应用URL
    
    def send_password_reset_email(
        self,
        email: str,
        username: str,
        reset_token: str,
        full_name: Optional[str] = None
    ) -> bool:
        """
        发送密码重置邮件
        
        Args:
            email: 收件人邮箱
            username: 用户名
            reset_token: 重置令牌
            full_name: 用户全名（可选）
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 构建重置链接
            reset_url = f"{self.frontend_url}/reset-password?token={reset_token}"
            
            # 构建邮件内容
            display_name = full_name or username
            subject = "ZHT系统 - 密码重置请求"
            
            html_content = self._build_password_reset_html(
                display_name=display_name,
                reset_url=reset_url,
                username=username
            )
            
            text_content = self._build_password_reset_text(
                display_name=display_name,
                reset_url=reset_url,
                username=username
            )
            
            # 模拟发送邮件（在实际环境中，这里会调用真实的邮件服务）
            logger.info(f"模拟发送密码重置邮件到: {email}")
            logger.info(f"邮件主题: {subject}")
            logger.info(f"重置链接: {reset_url}")
            logger.info(f"邮件内容:\n{text_content}")
            
            # 在开发环境中，将邮件内容写入文件
            # if settings.DEBUG:
            #     self._save_email_to_file(email, subject, html_content, text_content)
            
            return True
            
        except Exception as e:
            logger.error(f"发送密码重置邮件失败: {e}")
            return False
    
    def _build_password_reset_html(self, display_name: str, reset_url: str, username: str) -> str:
        """构建HTML格式的密码重置邮件"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>密码重置请求</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #3b82f6; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background-color: #f9f9f9; }}
                .button {{ 
                    display: inline-block; 
                    padding: 12px 24px; 
                    background-color: #3b82f6; 
                    color: white; 
                    text-decoration: none; 
                    border-radius: 5px; 
                    margin: 20px 0;
                }}
                .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
                .warning {{ background-color: #fef3c7; padding: 15px; border-left: 4px solid #f59e0b; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>ZHT系统 - 密码重置</h1>
                </div>
                <div class="content">
                    <h2>您好，{display_name}！</h2>
                    <p>我们收到了您的密码重置请求。如果这是您本人的操作，请点击下面的按钮重置您的密码：</p>
                    
                    <div style="text-align: center;">
                        <a href="{reset_url}" class="button">重置密码</a>
                    </div>
                    
                    <p>或者复制以下链接到浏览器地址栏：</p>
                    <p style="word-break: break-all; background-color: #e5e7eb; padding: 10px; border-radius: 5px;">
                        {reset_url}
                    </p>
                    
                    <div class="warning">
                        <strong>安全提醒：</strong>
                        <ul>
                            <li>此链接将在24小时后失效</li>
                            <li>如果您没有申请密码重置，请忽略此邮件</li>
                            <li>请不要将此链接分享给他人</li>
                        </ul>
                    </div>
                    
                    <p>如果您有任何问题，请联系我们的技术支持。</p>
                </div>
                <div class="footer">
                    <p>此邮件由ZHT系统自动发送，请勿回复。</p>
                    <p>© 2025 ZHT系统. 保留所有权利。</p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _build_password_reset_text(self, display_name: str, reset_url: str, username: str) -> str:
        """构建纯文本格式的密码重置邮件"""
        return f"""
ZHT系统 - 密码重置请求

您好，{display_name}！

我们收到了您的密码重置请求。如果这是您本人的操作，请复制以下链接到浏览器中重置您的密码：

{reset_url}

安全提醒：
- 此链接将在24小时后失效
- 如果您没有申请密码重置，请忽略此邮件
- 请不要将此链接分享给他人

如果您有任何问题，请联系我们的技术支持。

此邮件由ZHT系统自动发送，请勿回复。
© 2025 ZHT系统. 保留所有权利。
        """
    
    def _save_email_to_file(self, email: str, subject: str, html_content: str, text_content: str):
        """在开发环境中将邮件内容保存到文件"""
        try:
            import os
            from datetime import datetime

            # 创建邮件目录
            email_dir = "/tmp/temp_emails"
            os.makedirs(email_dir, exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{email_dir}/email_{timestamp}_{email.replace('@', '_at_')}.html"

            # 保存HTML内容
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

            logger.info(f"邮件内容已保存到文件: {filename}")
        except Exception as e:
            logger.warning(f"保存邮件文件失败: {e}")
            # 不抛出异常，因为这只是开发环境的辅助功能


# 创建全局实例
email_service = EmailService()
