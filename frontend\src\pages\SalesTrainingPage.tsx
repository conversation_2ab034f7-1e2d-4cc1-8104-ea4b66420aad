/**
 * 销冠实战训练主页面 - 重新设计版本
 * 提供更加美观、现代化和用户友好的销售训练界面
 */

import { useState, useEffect } from 'react'
import {
  GraduationCap,
  Users,
  Brain,
  Activity
} from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

import RealCustomerManagement from '@/components/sales-training/RealCustomerManagement'
import { AITrainingDialog } from '@/components/sales-training/AITrainingDialog'

import type { TrainingCustomer, Customer } from '@/types/salesTraining'

// 国家映射辅助函数
const getCountryName = (countryCode?: string): string => {
  const countryMap: Record<string, string> = {
    'US': '美国',
    'CN': '中国',
    'JP': '日本',
    'DE': '德国',
    'GB': '英国',
    'FR': '法国',
    'CA': '加拿大',
    'AU': '澳大利亚',
    'SG': '新加坡',
    'KR': '韩国',
    'IN': '印度',
    'BR': '巴西',
    'RU': '俄罗斯',
    'IT': '意大利',
    'ES': '西班牙',
    'NL': '荷兰',
    'SE': '瑞典',
    'CH': '瑞士',
    'NO': '挪威',
    'DK': '丹麦'
  }
  return countryMap[countryCode || ''] || '其他'
}

const getCountryFlag = (countryCode?: string): string => {
  const flagMap: Record<string, string> = {
    'US': '🇺🇸',
    'CN': '🇨🇳',
    'JP': '🇯🇵',
    'DE': '🇩🇪',
    'GB': '🇬🇧',
    'FR': '🇫🇷',
    'CA': '🇨🇦',
    'AU': '🇦🇺',
    'SG': '🇸🇬',
    'KR': '🇰🇷',
    'IN': '🇮🇳',
    'BR': '🇧🇷',
    'RU': '🇷🇺',
    'IT': '🇮🇹',
    'ES': '🇪🇸',
    'NL': '🇳🇱',
    'SE': '🇸🇪',
    'CH': '🇨🇭',
    'NO': '🇳🇴',
    'DK': '🇩🇰'
  }
  return flagMap[countryCode || ''] || '🌍'
}

export default function SalesTrainingPage() {
  const [selectedCustomer, setSelectedCustomer] = useState<TrainingCustomer | null>(null)
  const [showTrainingDialog, setShowTrainingDialog] = useState(false)

  const [animationLoaded, setAnimationLoaded] = useState(false)

  // 加载动画效果
  useEffect(() => {
    const timer = setTimeout(() => setAnimationLoaded(true), 100)
    return () => clearTimeout(timer)
  }, [])



  const handleStartTraining = (customer: TrainingCustomer) => {
    setSelectedCustomer(customer)
    setShowTrainingDialog(true)
  }

  // 适配器函数：将 Customer 转换为 TrainingCustomer
  const handleStartTrainingFromCustomer = (customer: Customer) => {
    // 创建一个模拟的 TrainingCustomer 对象
    const trainingCustomer: TrainingCustomer = {
      id: customer.id.toString(),
      name: customer.name,
      country: {
        id: 1,
        name: getCountryName(customer.country_code),
        code: customer.country_code || 'US',
        flag: getCountryFlag(customer.country_code)
      },
      product: {
        id: 1,
        name: customer.notes?.includes('产品兴趣:') ?
              customer.notes.split('产品兴趣:')[1]?.trim() || '云计算平台' : '云计算平台',
        category: '云服务',
        description: '企业级云计算解决方案'
      },
      createdAt: new Date(customer.created_at || Date.now()),
      updatedAt: new Date(customer.updated_at || Date.now()),
      background: {
        company: customer.company || '未知公司',
        position: '采购经理', // 默认职位
        experience: customer.training_scenario || '通用销售场景', // 使用训练场景作为经验描述
        // preferences: ['质量保证', '技术支持', '性价比']
      }
    }

    handleStartTraining(trainingCustomer)
  }

  const handleCloseTraining = () => {
    setShowTrainingDialog(false)
    setSelectedCustomer(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* 页面容器 */}
      <div className={`flex-1 p-6 transition-all duration-700 ${animationLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>

        {/* 页面头部 - 重新设计 */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-6">
              {/* 左侧标题区域 */}
              <div className="relative group">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 group-hover:rotate-2">
                  <GraduationCap className="w-8 h-8 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white dark:border-gray-900 flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                </div>
              </div>
              <div className="space-y-1">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-900 dark:from-white dark:via-blue-200 dark:to-blue-100 bg-clip-text text-transparent">
                  销冠实战训练
                </h1>
                <p className="text-gray-600 dark:text-gray-400 text-lg">
                  AI驱动的智能销售技能提升平台
                </p>
                <div className="flex items-center space-x-4 mt-2">
                  <Badge variant="secondary" className="bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 dark:from-blue-900/30 dark:to-indigo-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800">
                    <Brain className="w-3 h-3 mr-1.5" />
                    AI 训练模式
                  </Badge>
                  <Badge variant="secondary" className="bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 dark:from-green-900/30 dark:to-emerald-900/30 dark:text-green-400 border border-green-200 dark:border-green-800">
                    <Activity className="w-3 h-3 mr-1.5" />
                    实时评分
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 主要内容区域 - 重新设计 */}
        <Card className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-lg border border-gray-200/50 dark:border-gray-700/50 shadow-2xl rounded-2xl overflow-hidden">
          {/* 标题栏 */}
          <div className="border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-gray-50/80 via-white/80 to-gray-50/80 dark:from-gray-800/80 dark:via-gray-800/80 dark:to-gray-800/80 backdrop-blur-sm">
            <div className="flex items-center justify-center p-4 h-14">
              <div className="flex items-center space-x-3">
                <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg">
                  <Users className="w-4 h-4 text-white" />
                </div>
                <span className="text-base font-medium text-gray-700 dark:text-gray-300">开始训练</span>
              </div>
            </div>
          </div>

          {/* 训练内容 */}
          <div className="p-8">
            {/* <div className="mb-6 space-y-2">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">选择训练客户</h2>
              <p className="text-gray-600 dark:text-gray-400">
                选择一个客户开始您的销售技能训练，与AI客户进行实时对话练习，获得专业反馈和指导
              </p>
            </div> */}
            <RealCustomerManagement onStartTraining={handleStartTrainingFromCustomer} />
          </div>
        </Card>

        {/* 训练对话弹窗 */}
        {selectedCustomer && (
          <AITrainingDialog
            customer={selectedCustomer}
            open={showTrainingDialog}
            onClose={handleCloseTraining}
          />
        )}
      </div>
    </div>
  )
}
