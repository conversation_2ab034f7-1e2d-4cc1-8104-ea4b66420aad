"""
SmoLAgents 微服务主应用
基于官方 SmoLAgents 库的 FastAPI 应用
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from app.config import get_settings, Settings
from app.core.agent_service import AgentService
from app.core.redis_client import RedisClient
from app.core.concurrent_search import ConcurrentSearchService
from app.core.data_sources import IntelligentSourceDetector
from app.api.routes import agent_router, health_router
from app.utils.logger import setup_logger


# 全局服务实例
agent_service: AgentService = None
redis_client: RedisClient = None
concurrent_search_service: ConcurrentSearchService = None
source_detector: IntelligentSourceDetector = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global agent_service, redis_client, concurrent_search_service, source_detector
    
    settings = get_settings()
    logger = logging.getLogger(__name__)
    
    try:
        # 启动时初始化服务
        logger.info("🚀 启动 SmoLAgents 微服务...")
        
        # 初始化 Redis 客户端
        redis_client = RedisClient(settings)
        await redis_client.connect()
        logger.info("✅ Redis 连接成功")
        
        # 初始化 Agent 服务
        agent_service = AgentService(settings, redis_client)
        await agent_service.initialize()
        logger.info("✅ SmoLAgents 服务初始化成功")

        # 初始化并发搜索服务
        concurrent_search_service = ConcurrentSearchService(agent_service, redis_client, settings)
        logger.info("✅ 并发搜索服务初始化成功")

        # 初始化数据源检测器
        source_detector = IntelligentSourceDetector()
        logger.info("✅ 智能数据源检测器初始化成功")

        # 将服务实例添加到应用状态
        app.state.agent_service = agent_service
        app.state.redis_client = redis_client
        app.state.concurrent_search_service = concurrent_search_service
        app.state.source_detector = source_detector
        
        logger.info("🎉 SmoLAgents 微服务启动完成")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        raise
    finally:
        # 关闭时清理资源
        logger.info("🔄 正在关闭 SmoLAgents 微服务...")
        
        if agent_service:
            await agent_service.cleanup()
            logger.info("✅ Agent 服务已清理")
        
        if redis_client:
            await redis_client.disconnect()
            logger.info("✅ Redis 连接已关闭")
        
        logger.info("👋 SmoLAgents 微服务已关闭")


def create_app() -> FastAPI:
    """创建 FastAPI 应用实例"""
    settings = get_settings()
    
    # 设置日志
    setup_logger(settings.log_level, settings.log_format)
    
    # 创建应用实例
    app = FastAPI(
        title="SmoLAgents 微服务",
        description="基于官方 SmoLAgents 库的智能代理服务",
        version=settings.service_version,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan
    )
    
    # 配置 CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 重写依赖注入函数
    from app.api.routes import get_agent_service as _get_agent_service
    from app.api.routes import get_redis_client as _get_redis_client
    from app.api.routes import get_concurrent_search_service as _get_concurrent_search_service
    from app.api.routes import get_source_detector as _get_source_detector

    def override_get_agent_service():
        return app.state.agent_service

    def override_get_redis_client():
        return app.state.redis_client

    def override_get_concurrent_search_service():
        return app.state.concurrent_search_service

    def override_get_source_detector():
        return app.state.source_detector

    # 覆盖依赖注入
    app.dependency_overrides[_get_agent_service] = override_get_agent_service
    app.dependency_overrides[_get_redis_client] = override_get_redis_client
    app.dependency_overrides[_get_concurrent_search_service] = override_get_concurrent_search_service
    app.dependency_overrides[_get_source_detector] = override_get_source_detector

    # 注册路由
    app.include_router(health_router, prefix="/api/v1", tags=["健康检查"])
    app.include_router(agent_router, prefix="/api/v1", tags=["智能代理"])
    
    # 全局异常处理
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        logger = logging.getLogger(__name__)
        logger.error(f"未处理的异常: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": "内部服务器错误",
                "detail": str(exc) if settings.debug else "服务暂时不可用"
            }
        )
    
    # 根路径
    @app.get("/")
    async def root():
        """根路径信息"""
        return {
            "service": settings.service_name,
            "version": settings.service_version,
            "status": "running",
            "docs": "/docs",
            "health": "/api/v1/health"
        }
    
    return app


# 创建应用实例
app = create_app()


def get_agent_service() -> AgentService:
    """获取 Agent 服务实例"""
    if hasattr(app.state, 'agent_service') and app.state.agent_service:
        return app.state.agent_service
    raise HTTPException(status_code=503, detail="Agent 服务未初始化")


def get_redis_client() -> RedisClient:
    """获取 Redis 客户端实例"""
    if hasattr(app.state, 'redis_client') and app.state.redis_client:
        return app.state.redis_client
    raise HTTPException(status_code=503, detail="Redis 客户端未初始化")


if __name__ == "__main__":
    settings = get_settings()
    uvicorn.run(
        "app.main:app",
        host=settings.service_host,
        port=settings.service_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
