# 特定群体需求分析提示词优化对比

## 🎯 优化目标

专门优化"特定群体需求分析"的一键完整分析功能，使其更聚焦于先生成策略再执行分析的模式，提升分析质量。

## 📝 提示词差异对比

### 1. 策略生成提示词优化

#### 🔴 优化前（原版）
```
请为以下特定群体需求分析主题创新性地生成 4 个独特且精准的搜索策略。
每个策略都应该专注于特定群体的真实需求，而非整体市场分析：

🎯 核心要求：
1. 策略必须专注于特定群体的需求分析，避免整体市场分析
2. 从群体需求角度生成多样化的分析策略
3. 每个策略应该针对特定群体的独特需求特征
4. 搜索查询要求：优先使用中文搜索查询
5. 策略分类要求：使用中文分类名称
```

#### 🟢 优化后（专注版）
```
🎯 专注任务：为特定群体需求分析生成高质量搜索策略

你现在是一个专业的群体需求分析策略专家，你的唯一任务是为特定群体需求分析设计精准的搜索策略。
请专注于策略生成，不要考虑后续的分析执行过程。

🎯 专注策略生成要求：
1. 【专注度要求】你的唯一职责是生成 4 个高质量的群体需求分析策略
2. 【忘记后续任务】不要考虑策略执行、数据收集或分析报告，只专注于策略设计
3. 【群体需求导向】每个策略必须专注于特定群体的真实需求，避免整体市场分析

🔍 策略设计维度（必须覆盖以下维度）：
   - 该群体对产品的核心功能需求和期望
   - 该群体的典型使用场景和环境
   - 该群体当前遇到的痛点和问题
   - 该群体的产品偏好和决策因素
   - 该群体未来可能产生的新需求
   - 该群体的行为模式和习惯
   - 该群体的人口统计特征和洞察

🎯 高质量策略标准：
1. 【精准性】每个策略针对特定群体的独特需求特征
2. 【差异化】策略之间要有明显的维度差异，避免重复
3. 【可执行性】搜索查询要具体可操作，能获取真实数据
4. 【洞察价值】策略要能挖掘该群体的深层需求洞察
```

#### 🔥 关键差异标记：
- ✨ **新增专注任务声明**：明确AI的唯一职责
- ✨ **新增忘记后续任务指令**：避免考虑分析执行过程
- ✨ **新增策略设计维度**：具体指导策略覆盖范围
- ✨ **新增高质量策略标准**：明确策略质量要求
- ✨ **强化专注度要求**：多次强调专注于策略生成

### 2. 分析执行提示词优化（一键完整分析）

#### 🔴 优化前（原版）
```
请执行以下特定群体需求分析任务：

📋 执行策略:
[策略列表]

请按照以下步骤执行群体需求分析：

1. 🔍 数据收集阶段
   - 根据每个策略的搜索查询，使用网络搜索工具获取该特定群体的真实需求数据
   - 重点关注该群体的使用场景、痛点、偏好等需求信息

2. 📊 数据分析阶段
   - 基于收集的真实数据进行群体需求深度分析
   - 严格按照群体需求维度组织分析内容

3. 📝 报告生成阶段
   - 生成专门针对该特定群体的需求分析报告
   - 每个策略维度对应一个群体需求分析章节
```

#### 🟢 优化后（专注版）
```
🎯 专注任务：基于高质量策略执行特定群体需求分析

你现在是一个专业的群体需求分析专家，你的唯一任务是基于已经生成的高质量搜索策略执行深度的群体需求分析。
请专注于分析执行，不要考虑策略生成过程。

📋 已提供的高质量策略（请严格按照这些策略执行）:
[策略列表]

🎯 专注分析执行要求：
1. 【专注度要求】你的唯一职责是基于上述策略执行深度的群体需求分析
2. 【忘记策略生成】不要考虑策略是如何生成的，只专注于执行这些策略
3. 【群体需求导向】所有分析必须专注于特定群体的真实需求，避免整体市场分析

请按照以下步骤专注执行群体需求分析：

1. 🔍 专注数据收集阶段
   - 严格按照每个策略的搜索查询，使用网络搜索工具获取该特定群体的真实需求数据
   - 确保每个策略都能获取到有价值的群体需求数据

2. 📊 专注数据分析阶段
   - 基于收集的真实数据进行群体需求深度分析
   - 深入挖掘该群体的需求洞察和行为模式

3. 📝 专注报告生成阶段
   - 生成专门针对该特定群体的需求分析报告
   - 提供针对该群体的具体建议和洞察

🎯 分析质量要求：
- 基于真实数据进行分析，避免使用模拟或假设数据
- 专注于该群体的独特需求特征，避免泛泛而谈
- 每个策略维度都要有深入的分析和洞察
- 提供具体的数据支撑和来源
```

#### 🔥 关键差异标记：
- ✨ **新增专注任务声明**：明确AI专注于分析执行
- ✨ **新增忘记策略生成指令**：避免考虑策略生成过程
- ✨ **强化策略质量认知**：强调策略是高质量的
- ✨ **新增专注分析执行要求**：明确分析执行的专注度
- ✨ **新增分析质量要求**：具体指导分析质量标准

### 3. 分析执行提示词优化（分步执行）

#### 🔴 优化前（原版）
```
请执行以下特定群体需求分析任务：

📋 执行策略:
[用户选择的策略列表]

请按照以下步骤执行群体需求分析：
[相同的执行步骤]
```

#### 🟢 优化后（专注版）
```
🎯 专注任务：基于用户精心选择的策略执行特定群体需求分析

你现在是一个专业的群体需求分析专家，你的唯一任务是基于用户精心选择和可能优化过的搜索策略执行深度的群体需求分析。
这些策略已经经过用户的审核和可能的优化，质量很高，请专注于执行分析。

📋 用户精心选择的高质量策略（请严格按照这些策略执行）:
[策略列表]

🎯 专注分析执行要求：
1. 【专注度要求】你的唯一职责是基于上述用户选择的策略执行深度的群体需求分析
2. 【策略质量优势】这些策略已经过用户审核，质量很高，请充分利用
3. 【群体需求导向】所有分析必须专注于特定群体的真实需求，避免整体市场分析
```

#### 🔥 关键差异标记：
- ✨ **新增用户精心选择强调**：突出策略经过用户审核
- ✨ **新增策略质量优势认知**：强调策略质量很高
- ✨ **强化用户参与感**：让AI认识到用户的参与和优化

## 🎯 优化效果预期

### 策略生成阶段
1. **专注度提升**：AI只考虑策略生成，不被分析任务干扰
2. **策略质量提升**：更精准的群体需求策略设计
3. **维度覆盖完整**：确保覆盖群体需求的各个维度

### 分析执行阶段
1. **执行专注度提升**：AI专注于基于策略的分析执行
2. **策略利用充分**：充分利用高质量策略进行深度分析
3. **分析质量提升**：基于高质量策略的专业分析

### 整体效果
1. **质量差异缩小**：一键分析与分步执行的质量趋于一致
2. **用户体验提升**：可以放心使用一键完整分析
3. **分析深度提升**：更深入的群体需求洞察

## 🚀 部署说明

### 修改文件
- `smolagents-service/app/api/routes.py` - 主要提示词优化

### 兼容性
- ✅ 向后兼容：不影响全球市场分析功能
- ✅ API兼容：保持原有接口不变
- ✅ 功能兼容：分步执行和一键分析都得到优化

### 验证方法
1. 对比测试：同一群体需求分析使用两种方式，对比结果质量
2. 策略质量评估：评估生成策略的精准度和群体针对性
3. 分析深度评估：评估分析报告的群体需求洞察深度

## 💡 使用建议

1. **推荐使用一键分析**：优化后质量显著提升
2. **对比验证**：可以测试对比两种方式的结果质量
3. **反馈收集**：如发现问题请及时反馈以便进一步优化

## 🎉 总结

通过专注度优化，成功解决了特定群体需求分析一键完整分析的质量问题：

✅ **策略生成专注化**：AI专门负责策略创建，不考虑后续分析
✅ **分析执行专注化**：AI专门负责基于策略的分析，不考虑策略生成
✅ **提示词针对性优化**：每个阶段都有专门的专注提示词
✅ **质量标准明确化**：具体的质量要求和执行标准
✅ **用户参与感强化**：分步执行中强调用户的策略选择和优化

预期特定群体需求分析的一键完整分析质量将显著提升，与分步执行的质量差异将大幅缩小。
