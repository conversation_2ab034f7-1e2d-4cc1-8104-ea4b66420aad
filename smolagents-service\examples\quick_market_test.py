#!/usr/bin/env python3
"""
快速市场分析测试脚本
用于验证 SmoLAgents 微服务的市场分析能力
"""

import asyncio
import httpx
from datetime import datetime


async def quick_market_analysis_test():
    """快速市场分析测试"""
    
    print("🚀 快速市场分析测试")
    print("=" * 50)
    
    # 测试搜索词
    search_term = "沙滩包2024亚洲市场销售情况分析及2025市场增长预期"
    print(f"测试搜索词: {search_term}")
    print()
    
    # 构建智能分析查询
    analysis_query = f"""
请作为专业市场分析师，对以下搜索词进行智能市场分析：

搜索词: {search_term}

请执行以下分析步骤：

1. 首先解析搜索词，识别：
   - 产品类型：沙滩包
   - 目标市场：亚洲
   - 时间范围：2024年
   - 分析需求：销售情况分析及2025增长预期

2. 使用网络搜索工具，从多个角度搜集真实市场数据：
   - 沙滩包亚洲市场规模和销售数据
   - 2024年市场表现和趋势
   - 主要品牌和竞争格局
   - 消费者需求和行为变化
   - 2025年市场增长预测

3. 基于搜索到的真实数据，生成结构化的市场分析报告，包含：
   - 市场现状分析
   - 销售数据和趋势
   - 竞争环境分析
   - 2025年增长预期
   - 关键洞察和建议

请确保使用真实的网络搜索数据，避免虚构信息。生成专业的市场分析报告。
"""
    
    async with httpx.AsyncClient(timeout=300.0) as client:
        try:
            print("🔍 开始智能市场分析...")
            start_time = datetime.now()
            
            # 调用 SmoLAgents 服务
            data = {
                "query": analysis_query,
                "max_steps": 25,
                "use_cache": False,
                "enable_web_search": True,
                "timeout": 300
            }
            
            response = await client.post("http://localhost:8002/api/v1/agent/query", json=data)
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            print(f"⏱️  执行时间: {execution_time:.2f} 秒")
            print(f"📊 状态码: {response.status_code}")
            print()
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    print("✅ 分析成功完成!")
                    print("=" * 80)
                    print("📈 市场分析报告")
                    print("=" * 80)
                    
                    analysis_result = result.get('result', '')
                    print(analysis_result)
                    
                    print("\n" + "=" * 80)
                    print("📋 执行统计")
                    print(f"任务ID: {result.get('task_id', 'N/A')}")
                    print(f"执行步骤: {result.get('steps_taken', 'N/A')}")
                    print(f"缓存使用: {result.get('cached', False)}")
                    print(f"执行时间: {result.get('execution_time', 'N/A')} 秒")
                    
                else:
                    print("❌ 分析失败:")
                    print(f"错误信息: {result.get('error', '未知错误')}")
                    
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except Exception as e:
            print(f"❌ 执行异常: {e}")


async def test_service_health():
    """测试服务健康状态"""
    print("🏥 检查服务健康状态...")
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        try:
            response = await client.get("http://localhost:8002/api/v1/health")
            
            if response.status_code == 200:
                health_data = response.json()
                print("✅ 服务健康状态正常")
                print(f"   服务状态: {health_data.get('status', 'unknown')}")
                print(f"   模型状态: {health_data.get('model', 'unknown')}")
                print(f"   工具状态: {health_data.get('tools', 'unknown')}")
                print(f"   Redis状态: {health_data.get('redis', 'unknown')}")
                return True
            else:
                print(f"❌ 服务健康检查失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 服务连接失败: {e}")
            return False


async def main():
    """主函数"""
    print("🌟 SmoLAgents 智能市场分析测试")
    print("基于真实网络数据的AI驱动市场分析")
    print("=" * 60)
    print()
    
    # 1. 检查服务健康状态
    if not await test_service_health():
        print("❌ 服务不可用，请检查 SmoLAgents 微服务是否正常运行")
        print("启动命令: docker-compose up -d zht_smolagents_0624")
        return
    
    print()
    
    # 2. 执行快速市场分析测试
    await quick_market_analysis_test()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
