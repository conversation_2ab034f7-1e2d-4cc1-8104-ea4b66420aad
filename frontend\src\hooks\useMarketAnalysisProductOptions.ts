import { ShoppingBag, Package, Laptop, Smartphone, Watch } from 'lucide-react'
import { useProductOptionsBase, type ProductOption, type UseProductOptionsBaseReturn } from './useProductOptionsBase'

// 全球市场分析页面的默认产品选项
const DEFAULT_MARKET_ANALYSIS_PRODUCTS: ProductOption[] = [
  {
    id: 'market_1',
    value: 'luxury-handbag',
    label: '奢侈手提包',
    icon: ShoppingBag,
    iconName: 'ShoppingBag',
    createdAt: new Date().toISOString()
  },
  {
    id: 'market_2',
    value: 'smart-watch',
    label: '智能手表',
    icon: Watch,
    iconName: 'Watch',
    createdAt: new Date().toISOString()
  },
  {
    id: 'market_3',
    value: 'gaming-laptop',
    label: '游戏笔记本',
    icon: Laptop,
    iconName: 'Laptop',
    createdAt: new Date().toISOString()
  },
  {
    id: 'market_4',
    value: 'flagship-smartphone',
    label: '旗舰手机',
    icon: Smartphone,
    iconName: 'Smartphone',
    createdAt: new Date().toISOString()
  },
  {
    id: 'market_5',
    value: 'premium-headphones',
    label: '高端耳机',
    icon: Package,
    iconName: 'Package',
    createdAt: new Date().toISOString()
  },
]

const STORAGE_KEY = 'market-analysis-product-options'

/**
 * 全球市场分析页面专用的产品选项Hook
 * 使用独立的localStorage存储，与其他页面完全分离
 */
export const useMarketAnalysisProductOptions = (): UseProductOptionsBaseReturn => {
  return useProductOptionsBase({
    storageKey: STORAGE_KEY,
    defaultOptions: DEFAULT_MARKET_ANALYSIS_PRODUCTS
  })
}

export default useMarketAnalysisProductOptions
