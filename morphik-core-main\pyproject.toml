[project]
name = "morphik-core"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "anthropic>=0.51.0",
    "arq>=0.26.3",
    "assemblyai>=0.40.2",
    "asyncio>=3.4.3",
    "asyncpg>=0.30.0",
    "boto3>=1.38.14",
    "build>=1.2.2.post1",
    "chardet>=5.2.0",
    "dotenv>=0.9.9",
    "fastapi>=0.115.12",
    "filetype>=1.2.0",
    "flagembedding>=1.3.4",
    "google-api-python-client>=2.169.0",
    "google-auth>=2.40.1",
    "google-auth-oauthlib>=1.2.2",
    "google-genai>=1.15.0",
    "greenlet>=3.2.2",
    "httpx[http2]>=0.28.1",
    "importlib>=1.0.4",
    "instructor>=1.8.1",
    "itsdangerous>=2.2.0",
    "litellm>=1.69.1",
    "morphik>=0.1.9",
    "ollama>=0.4.8",
    "openai>=1.75.0",
    "opencv-python>=*********",
    "opentelemetry-exporter-otlp>=1.33.0",
    "opentelemetry-instrumentation-fastapi>=0.54b0",
    "pdf2image>=1.17.0",
    "pgvector>=0.4.1",
    "pre-commit>=4.2.0",
    "psycopg>=3.2.9",
    "psycopg-binary>=3.2.9",
    "psycopg-pool>=3.2.6",
    "psycopg2-binary>=2.9.9",
    "pydantic>=2.11.4",
    "pydantic-settings>=2.9.1",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.26.0",
    "python-multipart>=0.0.20",
    "pyzotero>=1.6.11",
    "requests>=2.32.3",
    "sqlalchemy>=2.0.40",
    "stripe>=12.1.0",
    "toml>=0.10.2",
    "tomli>=2.2.1",
    "torch==2.5.1",
    "torchaudio==2.5.1",
    "torchvision==0.20.1",
    "tqdm>=4.67.1",
    "transformers==4.51.3",
    "twine>=6.1.0",
    "ty>=0.0.1a6",
    "unstructured[pdf,docx]>=0.17.2",
    "uvicorn>=0.34.2",
]

[tool.black]
line-length = 120

[tool.isort]
profile = "black"
line_length = 120

[tool.ruff]
line-length = 120
ignore = ["I"]
