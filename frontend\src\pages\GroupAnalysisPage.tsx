import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

import { Search, BarChart3, Users, TrendingUp, Package, AlertCircle, Trash2, Edit, Plus, History, X, RefreshCw } from 'lucide-react'
import {
  smolagentsService,
  type StrategyGenerationRequest,
  type AnalysisExecutionRequest,
  type SearchStrategy,
  type MarketDataSource
} from '@/services/smolagentsService'
import { useGroupAnalysisProductOptions } from '@/hooks/useGroupAnalysisProductOptions'
import { useCountryOptions } from '@/hooks/useCountryOptions'
import { useAudienceOptions } from '@/hooks/useAudienceOptions'
import { useScenarioOptions } from '@/hooks/useScenarioOptions'
import { useGroupAnalysisHistory } from '@/hooks/useGroupAnalysisHistory'
import { GroupAnalysisRecord } from '@/types/groupAnalysis'
import { ProductSelector, GenericSelector } from '@/components/ProductSelector'
import AddProductDialog from '@/components/AddProductDialog'
import AddOptionDialog from '@/components/AddOptionDialog'
import AnalysisHistoryList from '@/components/group-analysis/AnalysisHistoryList'
import { toast } from 'sonner'

// 简化的动态进度指示器组件
function DynamicProgressIndicator({ message }: { message: string }) {
  return (
    <div className="flex items-center space-x-3">
      {/* 简单的旋转指示器 */}
      <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>

      {/* 消息文本 */}
      <span className="text-blue-700 dark:text-blue-300 font-medium text-base">
        {message}
      </span>
    </div>
  )
}

// 策略编辑表单组件
function StrategyEditForm({
  strategy,
  onSave,
  onCancel
}: {
  strategy: SearchStrategy
  onSave: (strategy: SearchStrategy) => void
  onCancel: () => void
}) {
  const [editedStrategy, setEditedStrategy] = useState<SearchStrategy>(strategy)

  const handleSave = () => {
    if (!editedStrategy.query.trim() || !editedStrategy.category.trim() || !editedStrategy.description.trim()) {
      toast.error('请填写完整的策略信息')
      return
    }
    onSave(editedStrategy)
  }

  return (
    <div className="space-y-3">
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          策略分类
        </label>
        <Input
          value={editedStrategy.category}
          onChange={(e) => setEditedStrategy(prev => ({ ...prev, category: e.target.value }))}
          className="text-sm"
        />
      </div>
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          搜索查询
        </label>
        <Input
          value={editedStrategy.query}
          onChange={(e) => setEditedStrategy(prev => ({ ...prev, query: e.target.value }))}
          className="text-sm font-mono"
        />
      </div>
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          策略描述
        </label>
        <Input
          value={editedStrategy.description}
          onChange={(e) => setEditedStrategy(prev => ({ ...prev, description: e.target.value }))}
          className="text-sm"
        />
      </div>
      <div>
        <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
          优先级
        </label>
        <Select
          value={editedStrategy.priority.toString()}
          onValueChange={(value) => setEditedStrategy(prev => ({ ...prev, priority: parseInt(value) }))}
        >
          <SelectTrigger className="text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">优先级 1 (最高)</SelectItem>
            <SelectItem value="2">优先级 2 (中等)</SelectItem>
            <SelectItem value="3">优先级 3 (较低)</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="flex space-x-2 pt-2">
        <Button onClick={handleSave} size="sm" className="flex items-center space-x-1">
          <span>保存</span>
        </Button>
        <Button onClick={onCancel} variant="outline" size="sm">
          取消
        </Button>
      </div>
    </div>
  )
}

export default function GroupAnalysisPage() {
  // 基础状态
  const [selectedProduct, setSelectedProduct] = useState<string>('')
  const [selectedCountry, setSelectedCountry] = useState<string>('')
  const [selectedAudience, setSelectedAudience] = useState<string>('')
  const [selectedScenario, setSelectedScenario] = useState<string>('')
  const [error, setError] = useState<string>('')
  const [serviceAvailable, setServiceAvailable] = useState<boolean | null>(null)

  // 分析流程状态
  const [currentStep, setCurrentStep] = useState<'selection' | 'strategies' | 'analysis'>('selection')
  const [isGeneratingStrategies, setIsGeneratingStrategies] = useState(false)
  const [isExecutingAnalysis, setIsExecutingAnalysis] = useState(false)
  const [generatedStrategies, setGeneratedStrategies] = useState<SearchStrategy[]>([])
  const [analysisResult, setAnalysisResult] = useState<string>('')
  const [dataSources, setDataSources] = useState<MarketDataSource[]>([])

  // 策略数量限制常量
  const MAX_STRATEGIES = 8

  // 策略管理状态
  const [strategyCount, setStrategyCount] = useState<number>(4)
  const [editingStrategy, setEditingStrategy] = useState<SearchStrategy | null>(null)
  const [newStrategy, setNewStrategy] = useState<{
    query?: string
    category?: string
    description?: string
    priority?: number
  }>({})
  const [showAddStrategyDialog, setShowAddStrategyDialog] = useState(false)

  // 历史记录对话框状态
  const [showHistoryDialog, setShowHistoryDialog] = useState(false)
  const [analysisStartTime, setAnalysisStartTime] = useState<Date | null>(null)

  // 进度显示相关状态
  const [progressSteps, setProgressSteps] = useState<string[]>([])
  const [currentProgressStep, setCurrentProgressStep] = useState(0)
  const [progressMessage, setProgressMessage] = useState('')
  const [progressPercentage, setProgressPercentage] = useState(0)
  const [progressTimer, setProgressTimer] = useState<number | null>(null)
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null)
  const [isCancelling, setIsCancelling] = useState(false)



  // 获取选项数据
  const { productOptions, isLoading: productLoading, addProductOption, removeProductOption } = useGroupAnalysisProductOptions()
  const { countryOptions, isLoading: countryLoading, addCountryOption, removeCountryOption } = useCountryOptions()
  const { audienceOptions, isLoading: audienceLoading, addAudienceOption, removeAudienceOption } = useAudienceOptions()
  const { scenarioOptions, isLoading: scenarioLoading, addScenarioOption, removeScenarioOption } = useScenarioOptions()

  // 历史记录管理
  const historyHook = useGroupAnalysisHistory()

  // 对话框状态
  const [showAddProductDialog, setShowAddProductDialog] = useState(false)
  const [showAddCountryDialog, setShowAddCountryDialog] = useState(false)
  const [showAddAudienceDialog, setShowAddAudienceDialog] = useState(false)
  const [showAddScenarioDialog, setShowAddScenarioDialog] = useState(false)

  // 删除确认对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<{
    id: string
    label: string
    type: 'product' | 'country' | 'audience' | 'scenario'
  } | null>(null)

  // 检查服务状态
  useEffect(() => {
    const checkServiceHealth = async () => {
      try {
        const healthResult = await smolagentsService.healthCheck()
        const available = !healthResult.error
        setServiceAvailable(available)

        if (!available && healthResult.error) {
          setError(`AI服务连接失败: ${healthResult.error}`)
        }
      } catch (error) {
        console.error('健康检查失败:', error)
        setServiceAvailable(false)
        setError('无法连接到AI服务，请检查服务状态')
      }
    }

    checkServiceHealth()
  }, [])



  // 启动真实进度步骤模拟
  const startProgressSteps = (steps: string[], isAnalysis: boolean = false) => {
    setProgressSteps(steps)
    setCurrentProgressStep(0)
    setProgressMessage(steps[0])

    // 清除之前的定时器
    if (progressTimer) {
      clearInterval(progressTimer)
    }

    // 根据任务类型设置不同的步骤间隔
    const stepInterval = isAnalysis ? 8000 : 5000 // 分析任务步骤间隔更长

    const timer = window.setInterval(() => {
      setCurrentProgressStep(prev => {
        const nextStep = prev + 1
        if (nextStep < steps.length) {
          setProgressMessage(steps[nextStep])
          return nextStep
        } else {
          // 到达最后一步，保持显示
          return prev
        }
      })
    }, stepInterval)

    setProgressTimer(timer)
  }

  // 停止进度步骤
  const stopProgressSteps = () => {
    if (progressTimer) {
      clearInterval(progressTimer)
      setProgressTimer(null)
    }
    setProgressSteps([])
    setCurrentProgressStep(0)
    setProgressMessage('')
  }

  // 取消当前任务
  const handleCancelTask = async () => {
    if (!currentTaskId) {
      return
    }

    setIsCancelling(true)
    try {
      const result = await smolagentsService.cancelTask(currentTaskId)

      if (result.success) {
        setIsGeneratingStrategies(false)
        setIsExecutingAnalysis(false)
        setCurrentStep('selection')
        stopProgressSteps()
        setCurrentTaskId(null)
        setError('任务已取消')
        toast.success('任务已成功取消')
      } else {
        setError(result.error || '取消任务失败')
        toast.error('取消任务失败')

        // 即使取消失败，也强制重置状态
        setIsGeneratingStrategies(false)
        setIsExecutingAnalysis(false)
        setCurrentStep('selection')
        stopProgressSteps()
        setCurrentTaskId(null)
      }
    } catch (error) {
      console.error('取消任务失败:', error)
      setError('取消任务失败')
      toast.error('取消任务失败')

      // 即使出现异常，也强制重置状态
      setIsGeneratingStrategies(false)
      setIsExecutingAnalysis(false)
      setCurrentStep('selection')
      stopProgressSteps()
      setCurrentTaskId(null)
    } finally {
      setIsCancelling(false)
    }
  }

  // 策略编辑功能
  const handleEditStrategy = (strategy: SearchStrategy) => {
    setEditingStrategy(strategy)
  }

  const handleSaveStrategy = (updatedStrategy: SearchStrategy) => {
    setGeneratedStrategies(prev =>
      prev.map(s => s.id === updatedStrategy.id ? updatedStrategy : s)
    )
    setEditingStrategy(null)
    toast.success('策略已更新')
  }

  const handleDeleteStrategy = (strategyId: string) => {
    setGeneratedStrategies(prev => prev.filter(s => s.id !== strategyId))
    toast.success('策略已删除')
  }

  const handleAddStrategy = () => {
    // 检查策略数量限制
    if (generatedStrategies.length >= MAX_STRATEGIES) {
      toast.error(`最多只能添加${MAX_STRATEGIES}个策略`)
      return
    }

    if (!newStrategy.query || !newStrategy.category || !newStrategy.description) {
      toast.error('请填写完整的策略信息')
      return
    }

    // 检查是否有重复的策略分类
    const existingCategories = generatedStrategies.map(s => s.category.toLowerCase())
    if (existingCategories.includes(newStrategy.category!.toLowerCase())) {
      toast.error('该策略分类已存在，请使用不同的分类名称')
      return
    }

    const strategy: SearchStrategy = {
      id: `custom_${Date.now()}`,
      query: newStrategy.query!,
      category: newStrategy.category!,
      priority: newStrategy.priority || 2,
      description: newStrategy.description!
    }

    setGeneratedStrategies(prev => [...prev, strategy])
    setNewStrategy({
      query: '',
      category: '',
      priority: 2,
      description: ''
    })
    setShowAddStrategyDialog(false)
    toast.success('策略已添加')
  }

  // 生成搜索策略
  const handleGenerateStrategies = async () => {
    if (!selectedProduct || !selectedCountry || !selectedAudience || !selectedScenario) {
      setError('请选择所有必需的选项')
      return
    }

    if (serviceAvailable === false) {
      setError('AI服务不可用，请确保服务正在运行')
      return
    }

    setIsGeneratingStrategies(true)
    setError('')
    setAnalysisStartTime(new Date())

    // 生成任务ID
    const taskId = `strategy_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    setCurrentTaskId(taskId)

    // 启动策略生成的真实进度步骤
    const strategySteps = [
      '正在初始化AI分析引擎...',
      '正在分析群体特征和需求定位...',
      '正在生成多维度搜索策略...',
      '正在优化策略查询关键词...',
      '正在验证策略完整性和有效性...',
      '正在分析数据并生成报告...'
    ]
    startProgressSteps(strategySteps, false)

    try {
      const selectedProductLabel = productOptions.find(p => p.value === selectedProduct)?.label || selectedProduct
      const selectedCountryLabel = countryOptions.find(c => c.value === selectedCountry)?.label || selectedCountry
      const selectedAudienceLabel = audienceOptions.find(a => a.value === selectedAudience)?.label || selectedAudience
      const selectedScenarioLabel = scenarioOptions.find(s => s.value === selectedScenario)?.label || selectedScenario

      const query = `${selectedProductLabel} ${selectedCountryLabel} ${selectedAudienceLabel} ${selectedScenarioLabel} 群体需求分析`

      const request: StrategyGenerationRequest = {
        product: selectedProductLabel,
        query: query,
        analysisType: 'group_analysis',
        strategyCount: strategyCount,
        language: 'zh-CN'
      }

      const result = await smolagentsService.generateSearchStrategies(request)

      if (result.success && result.strategies) {
        setGeneratedStrategies(result.strategies)
        setCurrentStep('strategies')
        stopProgressSteps() // 停止进度步骤
        toast.success(`成功生成 ${result.strategies.length} 个搜索策略`)
      } else {
        setError(result.error || '策略生成失败，请稍后重试')
        stopProgressSteps() // 停止进度步骤
      }
    } catch (error) {
      console.error('策略生成失败:', error)
      setError('策略生成过程中发生错误，请检查AI服务状态或稍后重试')
      stopProgressSteps() // 停止进度步骤
    } finally {
      setIsGeneratingStrategies(false)
      setCurrentTaskId(null)
    }
  }

  // 执行分析
  const handleExecuteAnalysis = async () => {
    if (generatedStrategies.length === 0) {
      setError('请先生成搜索策略')
      return
    }

    setIsExecutingAnalysis(true)
    setError('')
    setAnalysisResult('')

    // 生成新的任务ID用于取消功能
    const taskId = `analysis_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    setCurrentTaskId(taskId)

    try {
      const selectedProductLabel = productOptions.find(p => p.value === selectedProduct)?.label || selectedProduct
      const query = `${selectedProductLabel} 群体需求分析`

      const request: AnalysisExecutionRequest = {
        product: selectedProductLabel,
        query: query,
        strategies: generatedStrategies,
        analysisType: 'group_analysis'
      }

      const result = await smolagentsService.executeUnifiedMarketAnalysis(
        request.product,
        request.query,
        request.strategies,
        request.strategies.length, // 策略数量
        undefined
      )

      if (result.success && result.data) {
        setAnalysisResult(result.data)
        setDataSources(result.dataSources || [])
        setCurrentStep('analysis')

        // 保存到历史记录
        const duration = analysisStartTime ? Math.floor((new Date().getTime() - analysisStartTime.getTime()) / 1000) : undefined
        const analysisRecord: GroupAnalysisRecord = {
          id: `analysis_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          productType: selectedProduct,
          productLabel: selectedProductLabel,
          searchStrategies: generatedStrategies,
          analysisResult: result.data,
          dataSources: result.dataSources || [],
          timestamp: new Date().toISOString(),
          status: 'success',
          strategyCount: generatedStrategies.length,
          analysisType: 'group_analysis',
          duration
        }

        historyHook.saveRecord(analysisRecord)
        toast.success('分析完成并已保存到历史记录')
      } else {
        setError(result.error || '分析执行失败，请稍后重试')
      }
    } catch (error) {
      console.error('分析执行失败:', error)
      setError('分析执行过程中发生错误，请检查AI服务状态或稍后重试')
    } finally {
      setIsExecutingAnalysis(false)
      setCurrentTaskId(null)
    }
  }

  // 一键完整分析
  const handleOneClickAnalysis = async () => {
    if (!selectedProduct || !selectedCountry || !selectedAudience || !selectedScenario) {
      setError('请选择所有必需的选项')
      return
    }

    if (serviceAvailable === false) {
      setError('AI服务不可用，请确保服务正在运行')
      return
    }

    setIsGeneratingStrategies(true)
    setIsExecutingAnalysis(false)
    setError('')
    setAnalysisStartTime(new Date())

    // 生成任务ID
    const taskId = `full_analysis_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    setCurrentTaskId(taskId)

    // 启动一键完整分析的真实进度步骤
    const fullAnalysisSteps = [
      '正在初始化AI分析系统...',
      '正在分析群体特征和需求定位...',
      '正在生成智能搜索策略...',
      '正在执行全球数据搜索...',
      '正在收集竞争对手信息...',
      '正在分析价格趋势和市场规模...',
      '正在整合消费者洞察数据...',
      '正在生成综合群体分析报告...'
    ]
    startProgressSteps(fullAnalysisSteps, true)

    try {
      // 第一步：生成策略
      const selectedProductLabel = productOptions.find(p => p.value === selectedProduct)?.label || selectedProduct
      const selectedCountryLabel = countryOptions.find(c => c.value === selectedCountry)?.label || selectedCountry
      const selectedAudienceLabel = audienceOptions.find(a => a.value === selectedAudience)?.label || selectedAudience
      const selectedScenarioLabel = scenarioOptions.find(s => s.value === selectedScenario)?.label || selectedScenario

      const query = `${selectedProductLabel} ${selectedCountryLabel} ${selectedAudienceLabel} ${selectedScenarioLabel} 群体需求分析`

      const strategyRequest: StrategyGenerationRequest = {
        product: selectedProductLabel,
        query: query,
        analysisType: 'group_analysis',
        strategyCount: strategyCount,
        language: 'zh-CN'
      }

      const strategyResult = await smolagentsService.generateSearchStrategies(strategyRequest)

      if (!strategyResult.success || !strategyResult.strategies) {
        setError(strategyResult.error || '策略生成失败，请稍后重试')
        return
      }

      setGeneratedStrategies(strategyResult.strategies)
      setIsGeneratingStrategies(false)
      setIsExecutingAnalysis(true)

      // 第二步：执行分析
      const analysisRequest: AnalysisExecutionRequest = {
        product: selectedProductLabel,
        query: query,
        strategies: strategyResult.strategies,
        analysisType: 'group_analysis'
      }

      const analysisResult = await smolagentsService.executeUnifiedMarketAnalysis(
        analysisRequest.product,
        analysisRequest.query,
        analysisRequest.strategies,
        analysisRequest.strategies.length, // 策略数量
        undefined
      )

      if (analysisResult.success && analysisResult.data) {
        setAnalysisResult(analysisResult.data)
        setDataSources(analysisResult.dataSources || [])
        setCurrentStep('analysis')

        // 保存到历史记录
        const duration = analysisStartTime ? Math.floor((new Date().getTime() - analysisStartTime.getTime()) / 1000) : undefined
        const analysisRecord: GroupAnalysisRecord = {
          id: `analysis_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          productType: selectedProduct,
          productLabel: selectedProductLabel,
          searchStrategies: strategyResult.strategies,
          analysisResult: analysisResult.data,
          dataSources: analysisResult.dataSources || [],
          timestamp: new Date().toISOString(),
          status: 'success',
          strategyCount: strategyResult.strategies.length,
          analysisType: 'group_analysis',
          duration
        }

        historyHook.saveRecord(analysisRecord)
        stopProgressSteps() // 停止进度步骤
        toast.success('一键分析完成并已保存到历史记录')
      } else {
        setError(analysisResult.error || '分析执行失败，请稍后重试')
        stopProgressSteps() // 停止进度步骤
      }
    } catch (error) {
      console.error('一键分析失败:', error)
      setError('一键分析过程中发生错误，请检查AI服务状态或稍后重试')
      stopProgressSteps() // 停止进度步骤
    } finally {
      setIsGeneratingStrategies(false)
      setIsExecutingAnalysis(false)
      setCurrentTaskId(null)
    }
  }

  // 处理删除选项
  const handleDeleteOption = (optionId: string, optionLabel: string, type: 'product' | 'country' | 'audience' | 'scenario') => {
    setItemToDelete({ id: optionId, label: optionLabel, type })
    setDeleteDialogOpen(true)
  }

  // 确认删除选项
  const confirmDeleteOption = async () => {
    if (!itemToDelete) return

    let result: { success: boolean; error?: string }

    switch (itemToDelete.type) {
      case 'product':
        result = await removeProductOption(itemToDelete.id)
        // 如果删除的是当前选中的产品，清空选择
        if (result.success && selectedProduct === productOptions.find(p => p.id === itemToDelete.id)?.value) {
          setSelectedProduct('')
        }
        break
      case 'country':
        result = await removeCountryOption(itemToDelete.id)
        if (result.success && selectedCountry === countryOptions.find(c => c.id === itemToDelete.id)?.value) {
          setSelectedCountry('')
        }
        break
      case 'audience':
        result = await removeAudienceOption(itemToDelete.id)
        if (result.success && selectedAudience === audienceOptions.find(a => a.id === itemToDelete.id)?.value) {
          setSelectedAudience('')
        }
        break
      case 'scenario':
        result = await removeScenarioOption(itemToDelete.id)
        if (result.success && selectedScenario === scenarioOptions.find(s => s.id === itemToDelete.id)?.value) {
          setSelectedScenario('')
        }
        break
      default:
        result = { success: false, error: '未知的选项类型' }
    }

    if (result.success) {
      toast.success(`选项 "${itemToDelete.label}" 已成功删除`)
    } else {
      setError(result.error || '删除选项失败')
      toast.error(result.error || '删除选项失败')
    }

    setDeleteDialogOpen(false)
    setItemToDelete(null)
  }

  // 取消删除
  const cancelDeleteOption = () => {
    setDeleteDialogOpen(false)
    setItemToDelete(null)
  }

  // 处理添加选项
  const handleAddProduct = () => setShowAddProductDialog(true)
  const handleAddCountry = () => setShowAddCountryDialog(true)
  const handleAddAudience = () => setShowAddAudienceDialog(true)
  const handleAddScenario = () => setShowAddScenarioDialog(true)

  // 重新检查服务状态
  const handleRefreshService = async () => {
    setServiceAvailable(null)
    const healthResult = await smolagentsService.healthCheck()
    const available = !healthResult.error
    setServiceAvailable(available)
  }

  // 测试AI连接
  const handleTestConnection = async () => {
    setError('')
    setProgressMessage('正在测试AI连接...')

    try {
      const result = await smolagentsService.healthCheck()
      if (!result.error) {
        setProgressMessage('')
        alert(`连接测试成功！\n服务: ${result.service}\n模型: ${result.model}`)
      } else {
        setError(`连接测试失败: ${result.error}`)
        setProgressMessage('')
      }
    } catch (error) {
      setError(`测试过程中发生错误: ${error instanceof Error ? error.message : '未知错误'}`)
      setProgressMessage('')
    }
  }

  return (
    <div className="flex-1 p-8 pt-6 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* 页面头部 - 完全居中显示 */}
        <div className="relative text-center w-full">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
              <Users className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">特定群体需求分析</h1>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
          </div>
          <p className="text-gray-600 dark:text-gray-300 text-lg max-w-2xl mx-auto mb-6">
            基于真实网络数据深入分析AI驱动需求分析，深入了解不同国家消费习惯和文化偏好
          </p>

          {/* 历史记录按钮 - 绝对定位到右侧，与描述文字同行 */}
          <div className="absolute right-6 top-[60px]">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHistoryDialog(true)}
              className="group relative flex items-center space-x-3 bg-white/95 dark:bg-gray-800/95 hover:bg-blue-50 dark:hover:bg-blue-900/30 border border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600 shadow-md hover:shadow-lg transition-all duration-200 rounded-full px-6 py-2 min-w-[140px] h-9"
            >
              <div className="w-5 h-5 bg-gradient-to-r from-purple-500 to-violet-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-sm">
                <History className="w-3 h-3 text-white" />
              </div>
              <div className="flex items-center space-x-2 flex-1">
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300 group-hover:text-blue-800 dark:group-hover:text-blue-200 whitespace-nowrap">
                  分析历史
                </span>
                <span className="text-xs text-blue-500 dark:text-blue-400 group-hover:text-blue-600 dark:group-hover:text-blue-300 font-medium whitespace-nowrap">
                  {historyHook.records.length > 0 ? `${historyHook.records.length} 条` : '查看'}
                </span>
              </div>
            </Button>
          </div>
        </div>

        {/* 主要内容区域 - 群体需求分析 */}
        <div className="space-y-8">
            {/* AI服务状态详情 */}
            {serviceAvailable === false && (
          <div className="bg-red-50 dark:bg-red-900/20 rounded-2xl p-6 border border-red-200 dark:border-red-800">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <AlertCircle className="w-6 h-6 text-red-500" />
                  <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">AI服务连接失败</h3>
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={handleTestConnection}
                    variant="outline"
                    size="sm"
                    className="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-800"
                  >
                    测试连接
                  </Button>
                  <Button
                    onClick={handleRefreshService}
                    variant="outline"
                    size="sm"
                    className="border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-300 dark:hover:bg-red-800"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    重新检查
                  </Button>
                </div>
              </div>
              <div className="text-red-700 dark:text-red-300 space-y-2">
                <p>无法连接到本地Ollama服务，请检查以下项目：</p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>确保Ollama服务正在运行 (端口: 11434)</li>
                  <li>确保模型 <code className="bg-red-100 dark:bg-red-800 px-2 py-1 rounded">Qwen3-8B-M:latest</code> 已安装</li>
                  <li>检查防火墙设置是否阻止了本地连接</li>
                </ul>
                <div className="mt-4 p-3 bg-red-100 dark:bg-red-800 rounded-lg">
                  <p className="font-medium">安装命令:</p>
                  <code className="block mt-1 text-sm">ollama pull Qwen3-8B-M:latest</code>
                </div>
              </div>
            </div>
          </div>
        )}



        {/* 搜索功能区域 */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl border border-gray-200 dark:border-gray-700 backdrop-blur-sm">
          <div className="space-y-8">
            {/* 分析参数选择区域 */}
            <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center">
                  <Package className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    选择分析参数
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">选择要进行群体需求分析的参数</p>
                </div>
              </div>

              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                {/* 产品选择 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">产品类型</label>
                  <GenericSelector
                    options={productOptions}
                    selectedValue={selectedProduct}
                    onValueChange={setSelectedProduct}
                    onDeleteOption={(id, label) => handleDeleteOption(id, label, 'product')}
                    onAddOption={handleAddProduct}
                    isLoading={productLoading}
                    placeholder="选择产品..."
                    className="bg-white dark:bg-gray-800"
                  />
                </div>

                {/* 国家选择 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">目标国家</label>
                  <GenericSelector
                    options={countryOptions}
                    selectedValue={selectedCountry}
                    onValueChange={setSelectedCountry}
                    onDeleteOption={(id, label) => handleDeleteOption(id, label, 'country')}
                    onAddOption={handleAddCountry}
                    isLoading={countryLoading}
                    placeholder="选择国家..."
                    className="bg-white dark:bg-gray-800"
                  />
                </div>

                {/* 人群选择 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">目标人群</label>
                  <GenericSelector
                    options={audienceOptions}
                    selectedValue={selectedAudience}
                    onValueChange={setSelectedAudience}
                    onDeleteOption={(id, label) => handleDeleteOption(id, label, 'audience')}
                    onAddOption={handleAddAudience}
                    isLoading={audienceLoading}
                    placeholder="选择人群..."
                    className="bg-white dark:bg-gray-800"
                  />
                </div>

                {/* 场景选择 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">使用场景</label>
                  <GenericSelector
                    options={scenarioOptions}
                    selectedValue={selectedScenario}
                    onValueChange={setSelectedScenario}
                    onDeleteOption={(id, label) => handleDeleteOption(id, label, 'scenario')}
                    onAddOption={handleAddScenario}
                    isLoading={scenarioLoading}
                    placeholder="选择场景..."
                    className="bg-white dark:bg-gray-800"
                  />
                </div>
              </div>
            </div>

            {/* 策略生成控制区域 */}
            <div className="space-y-4">
              {/* 策略数量选择 */}
              {currentStep === 'selection' && (
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl border border-gray-200 dark:border-gray-600">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center">
                      <Search className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                        策略数量
                      </label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">选择生成的搜索策略数量</p>
                    </div>
                  </div>
                  <Select value={strategyCount.toString()} onValueChange={(value) => setStrategyCount(parseInt(value))}>
                    <SelectTrigger className="w-36 h-10">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="4">4个策略 (推荐)</SelectItem>
                      <SelectItem value="7">7个策略 (详细)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* 操作按钮区域 */}
              <div className="flex justify-center">
                {/* 第一步：生成策略按钮 */}
                {currentStep === 'selection' && (
                  <div className="space-y-4 w-full">
                    {/* 默认状态按钮区域 */}
                    {!isGeneratingStrategies && !isExecutingAnalysis && (
                      <div className="flex space-x-4 justify-center">
                        <Button
                          onClick={handleGenerateStrategies}
                          disabled={!selectedProduct || !selectedCountry || !selectedAudience || !selectedScenario || serviceAvailable === false}
                          size="lg"
                          variant="outline"
                          className="h-14 px-8 min-w-[180px] border-green-300 text-green-700 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900/20 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-200"
                        >
                          <div className="flex items-center space-x-2">
                            <Search className="w-5 h-5" />
                            <span className="font-medium">
                              {serviceAvailable === false ? 'AI服务不可用' : '生成搜索策略'}
                            </span>
                          </div>
                        </Button>
                        <Button
                          onClick={handleOneClickAnalysis}
                          disabled={!selectedProduct || !selectedCountry || !selectedAudience || !selectedScenario || serviceAvailable === false}
                          size="lg"
                          className="h-14 px-8 min-w-[180px] bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transition-all duration-200"
                        >
                          <div className="flex items-center space-x-2">
                            <BarChart3 className="w-5 h-5" />
                            <span className="font-medium">
                              {serviceAvailable === false ? 'AI服务不可用' : '一键完整分析'}
                            </span>
                          </div>
                        </Button>
                      </div>
                    )}
                  </div>
                )}

                {/* 第二步：执行分析按钮 */}
                {currentStep === 'strategies' && !isExecutingAnalysis && (
                  <div className="flex space-x-4">
                    <Button
                      onClick={() => setCurrentStep('selection')}
                      variant="outline"
                      size="lg"
                      className="h-14 px-6"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      重新开始
                    </Button>
                    <Button
                      onClick={handleExecuteAnalysis}
                      size="lg"
                      className="h-14 px-8 min-w-[200px] bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      <div className="flex items-center space-x-2">
                        <BarChart3 className="w-5 h-5" />
                        <span className="font-medium">执行全面分析</span>
                      </div>
                    </Button>
                  </div>
                )}

                {/* 分析完成后的重置按钮 */}
                {currentStep === 'analysis' && (
                  <Button
                    onClick={() => setCurrentStep('selection')}
                    variant="outline"
                    size="lg"
                    className="h-14 px-8 min-w-[200px]"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    重新分析
                  </Button>
                )}
              </div>
            </div>

            {/* 错误提示区域 */}
            {error && (
              <div className="p-6 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-xl border border-red-200 dark:border-red-800 shadow-sm">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-red-100 dark:bg-red-900/50 rounded-lg flex items-center justify-center flex-shrink-0">
                      <AlertCircle className="w-5 h-5 text-red-500" />
                    </div>
                    <span className="text-red-700 dark:text-red-300 font-medium">{error}</span>
                  </div>
                  <Button
                    onClick={() => setError('')}
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-100 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/30"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}

              {/* 进度显示区域 */}
              {(isGeneratingStrategies || isExecutingAnalysis) && progressMessage && (
                <div className="mt-6 p-8 bg-gradient-to-br from-blue-50 via-cyan-50 to-indigo-50 dark:from-blue-900/20 dark:via-cyan-900/20 dark:to-indigo-900/20 rounded-2xl border border-blue-200 dark:border-blue-800 shadow-lg backdrop-blur-sm">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <div className="flex-1">
                      <DynamicProgressIndicator message={progressMessage} />
                    </div>
                    <div className="flex-shrink-0">
                      <Button
                        onClick={handleCancelTask}
                        disabled={isCancelling}
                        size="sm"
                        variant="outline"
                        className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md transition-all duration-200"
                      >
                        <div className="flex items-center space-x-1">
                          {isCancelling ? (
                            <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin"></div>
                          ) : (
                            <X className="w-4 h-4" />
                          )}
                          <span className="font-medium">
                            {isCancelling ? '取消中...' : '取消任务'}
                          </span>
                        </div>
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

        {/* 策略显示区域 */}
        {currentStep === 'strategies' && generatedStrategies.length > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                  <Search className="w-4 h-4 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">智能搜索策略</h2>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                  generatedStrategies.length >= MAX_STRATEGIES
                    ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300'
                    : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                }`}>
                  {generatedStrategies.length}/{MAX_STRATEGIES} 个策略
                </div>
              </div>
              <Button
                onClick={() => {
                  if (generatedStrategies.length >= MAX_STRATEGIES) {
                    toast.error(`最多只能添加${MAX_STRATEGIES}个策略`)
                  } else {
                    setShowAddStrategyDialog(true)
                  }
                }}
                variant="outline"
                size="sm"
                disabled={generatedStrategies.length >= MAX_STRATEGIES}
                className="group relative flex items-center space-x-2 bg-white dark:bg-gray-800 hover:bg-green-50 dark:hover:bg-green-900/10 border border-green-200 dark:border-green-700 hover:border-green-300 dark:hover:border-green-600 shadow-sm hover:shadow-md transition-all duration-200 rounded-lg px-4 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
                title={generatedStrategies.length >= MAX_STRATEGIES ? `最多只能添加${MAX_STRATEGIES}个策略` : '添加新的搜索策略'}
              >
                <div className="w-5 h-5 bg-green-500 rounded-md flex items-center justify-center">
                  <Plus className="w-3 h-3 text-white" />
                </div>
                <span className="font-medium text-gray-700 dark:text-gray-300 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-150">
                  添加策略
                </span>
                {generatedStrategies.length >= MAX_STRATEGIES && (
                  <span className="text-xs text-gray-500 dark:text-gray-400">({generatedStrategies.length}/{MAX_STRATEGIES})</span>
                )}
              </Button>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              {generatedStrategies.map((strategy, index) => (
                <div
                  key={strategy.id}
                  className="p-4 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-700 dark:to-blue-900/20 rounded-lg border border-gray-200 dark:border-gray-600"
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-blue-600 dark:text-blue-400">
                        {index + 1}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      {editingStrategy?.id === strategy.id ? (
                        <StrategyEditForm
                          strategy={strategy}
                          onSave={handleSaveStrategy}
                          onCancel={() => setEditingStrategy(null)}
                        />
                      ) : (
                        <>
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                                {strategy.category}
                              </h3>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                strategy.priority === 1
                                  ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
                                  : strategy.priority === 2
                                  ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
                                  : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                              }`}>
                                优先级 {strategy.priority}
                              </span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Button
                                onClick={() => handleEditStrategy(strategy)}
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                              >
                                <Edit className="w-3 h-3" />
                              </Button>
                              <Button
                                onClick={() => handleDeleteStrategy(strategy.id)}
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                            {strategy.description}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 font-mono bg-white dark:bg-gray-800 p-2 rounded border">
                            {strategy.query}
                          </p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 space-y-4">
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-xs font-bold text-white">✓</span>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-1">
                      策略生成完成！
                    </p>
                    <p className="text-xs text-blue-600 dark:text-blue-400 mb-2">
                      基于AI智能分析，已为您生成 {generatedStrategies.length} 个搜索策略。您可以：
                    </p>
                    <ul className="text-xs text-blue-600 dark:text-blue-400 space-y-1">
                      <li className="flex items-center space-x-1">
                        <Edit className="w-3 h-3" />
                        <span>点击编辑按钮修改策略内容</span>
                      </li>
                      <li className="flex items-center space-x-1">
                        <Trash2 className="w-3 h-3" />
                        <span>点击删除按钮移除不需要的策略</span>
                      </li>
                      <li className="flex items-center space-x-1">
                        <Plus className="w-3 h-3" />
                        <span>点击"添加策略"按钮创建自定义策略（最多{MAX_STRATEGIES}个）</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>


            </div>
          </div>
        )}

        {/* 分析结果 */}
        {currentStep === 'analysis' && analysisResult && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold flex items-center space-x-2">
                <BarChart3 className="w-5 h-5" />
                <span>分析结果</span>
              </h2>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentStep('selection')}
                >
                  重新分析
                </Button>
              </div>
            </div>

            <div className="prose prose-lg dark:prose-invert max-w-none">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    h1: ({ children }) => <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">{children}</h1>,
                    h2: ({ children }) => <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3 mt-6">{children}</h2>,
                    h3: ({ children }) => <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2 mt-4">{children}</h3>,
                    p: ({ children }) => <p className="text-gray-700 dark:text-gray-300 mb-3 leading-relaxed">{children}</p>,
                    ul: ({ children }) => <ul className="list-disc list-inside text-gray-700 dark:text-gray-300 mb-3 space-y-1">{children}</ul>,
                    ol: ({ children }) => <ol className="list-decimal list-inside text-gray-700 dark:text-gray-300 mb-3 space-y-1">{children}</ol>,
                    li: ({ children }) => <li className="ml-2">{children}</li>,
                    strong: ({ children }) => <strong className="font-semibold text-gray-900 dark:text-white">{children}</strong>,
                    em: ({ children }) => <em className="italic text-gray-800 dark:text-gray-200">{children}</em>,
                    blockquote: ({ children }) => <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600 dark:text-gray-400 my-4">{children}</blockquote>,
                    code: ({ children }) => <code className="bg-gray-200 dark:bg-gray-700 px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                    pre: ({ children }) => <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto text-sm font-mono mb-4">{children}</pre>
                  }}
                >
                  {analysisResult}
                </ReactMarkdown>
              </div>
            </div>

            {/* 数据源信息 */}
            {dataSources.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-3">数据源</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {dataSources.map((source, index) => (
                    <div key={index} className="text-xs bg-blue-50 dark:bg-blue-900/20 p-3 rounded border">
                      <div className="font-medium">{source.title}</div>
                      <div className="text-gray-600 dark:text-gray-300">{source.dataType}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}



        {/* 功能特色卡片区域 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* 真实数据分析 */}
          <div className="group bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-2xl hover:scale-105 transition-all duration-300">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">真实数据分析</h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                基于真实网络数据进行深度分析，涵盖消费习惯、文化偏好等多维度信息
              </p>
            </div>
          </div>

          {/* 多源数据整合 */}
          <div className="group bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-2xl hover:scale-105 transition-all duration-300">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">多源数据整合</h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                自动整合多个权威数据源，包含消费习惯分析和文化偏好，提供可靠的分析依据
              </p>
            </div>
          </div>

          {/* AI智能分析 */}
          <div className="group bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-2xl hover:scale-105 transition-all duration-300">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <TrendingUp className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">AI智能分析</h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                结合先进AI分析能力，生成专业的群体需求分析报告，助力商业决策和市场拓展
              </p>
            </div>
          </div>
        </div>
        </div>

        {/* 添加策略对话框 */}
        <Dialog open={showAddStrategyDialog} onOpenChange={setShowAddStrategyDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>添加新策略</DialogTitle>
              <DialogDescription>
                创建一个新的搜索策略来补充现有的分析维度
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  策略分类
                </label>
                <Input
                  value={newStrategy.category || ''}
                  onChange={(e) => setNewStrategy(prev => ({ ...prev, category: e.target.value }))}
                  placeholder="例如：技术分析、用户反馈等"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  搜索查询
                </label>
                <Input
                  value={newStrategy.query || ''}
                  onChange={(e) => setNewStrategy(prev => ({ ...prev, query: e.target.value }))}
                  placeholder="输入具体的搜索关键词"
                  className="font-mono"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  策略描述
                </label>
                <Input
                  value={newStrategy.description || ''}
                  onChange={(e) => setNewStrategy(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="描述这个策略的目的和预期结果"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  优先级
                </label>
                <Select
                  value={newStrategy.priority?.toString() || '2'}
                  onValueChange={(value) => setNewStrategy(prev => ({ ...prev, priority: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">优先级 1 (最高)</SelectItem>
                    <SelectItem value="2">优先级 2 (中等)</SelectItem>
                    <SelectItem value="3">优先级 3 (较低)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowAddStrategyDialog(false)}>
                取消
              </Button>
              <Button onClick={handleAddStrategy}>
                添加策略
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 删除确认对话框 */}
        <Dialog open={deleteDialogOpen} onOpenChange={(open) => open ? null : cancelDeleteOption()} modal={true}>
          <DialogContent
            onPointerDownOutside={(e) => e.preventDefault()}
            onEscapeKeyDown={(e) => e.preventDefault()}
          >
            <DialogHeader>
              <DialogTitle>确认删除选项</DialogTitle>
              <DialogDescription>
                您确定要删除选项 "{itemToDelete?.label}" 吗？此操作无法撤销。
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={cancelDeleteOption}>
                取消
              </Button>
              <Button variant="destructive" onClick={confirmDeleteOption}>
                删除
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 添加产品对话框 */}
        <AddProductDialog
          open={showAddProductDialog}
          onOpenChange={setShowAddProductDialog}
          onAddProduct={async (label, iconName) => {
            const result = await addProductOption(label, iconName)
            if (result.success && result.value) {
              setSelectedProduct(result.value)
              toast.success(`产品选项 "${label}" 已成功添加`)
            } else {
              toast.error(result.error || '添加产品选项失败')
            }
            return result
          }}
        />

        {/* 添加国家对话框 */}
        <AddOptionDialog
          open={showAddCountryDialog}
          onOpenChange={setShowAddCountryDialog}
          title="添加新国家"
          description="添加一个新的目标国家选项"
          placeholder="请输入国家名称..."
          optionType="国家"
          onAddOption={async (label) => {
            const result = await addCountryOption(label)
            if (result.success && result.value) {
              setSelectedCountry(result.value)
              toast.success(`国家选项 "${label}" 已成功添加`)
            } else {
              toast.error(result.error || '添加国家选项失败')
            }
            return result
          }}
        />

        {/* 添加人群对话框 */}
        <AddOptionDialog
          open={showAddAudienceDialog}
          onOpenChange={setShowAddAudienceDialog}
          title="添加新人群"
          description="添加一个新的目标人群选项"
          placeholder="请输入目标人群..."
          optionType="人群"
          onAddOption={async (label) => {
            const result = await addAudienceOption(label)
            if (result.success && result.value) {
              setSelectedAudience(result.value)
              toast.success(`人群选项 "${label}" 已成功添加`)
            } else {
              toast.error(result.error || '添加人群选项失败')
            }
            return result
          }}
        />

        {/* 添加场景对话框 */}
        <AddOptionDialog
          open={showAddScenarioDialog}
          onOpenChange={setShowAddScenarioDialog}
          title="添加新场景"
          description="添加一个新的使用场景选项"
          placeholder="请输入使用场景..."
          optionType="场景"
          onAddOption={async (label) => {
            const result = await addScenarioOption(label)
            if (result.success && result.value) {
              setSelectedScenario(result.value)
              toast.success(`场景选项 "${label}" 已成功添加`)
            } else {
              toast.error(result.error || '添加场景选项失败')
            }
            return result
          }}
        />

      {/* 历史记录对话框 */}
      <Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog} modal={true}>
        <DialogContent
          className="max-w-6xl max-h-[90vh] overflow-hidden"
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <History className="w-4 h-4 text-white" />
              </div>
              <span>分析历史记录</span>
            </DialogTitle>
            <DialogDescription>
              查看和管理您的群体需求分析历史记录
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            <div className="h-[70vh] overflow-y-auto">
              <AnalysisHistoryList
                records={historyHook.records}
                isLoading={historyHook.isLoading}
                onDeleteRecord={historyHook.deleteRecord}
                onDeleteMultipleRecords={historyHook.deleteMultipleRecords}
                onClearAllRecords={historyHook.clearAllRecords}
                onApplyFilters={historyHook.applyFilters}
                onResetFilters={historyHook.resetFilters}
                onRefreshData={historyHook.refreshData}
                currentFilters={historyHook.currentFilters}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </div>
  )
}
