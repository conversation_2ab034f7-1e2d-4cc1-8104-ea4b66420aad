import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    'import.meta.env.VITE_MORPHIK_API_URL': JSON.stringify(process.env.VITE_MORPHIK_API_URL || 'http://localhost:8000'),
    'import.meta.env.VITE_API_BASE_URL': JSON.stringify(process.env.VITE_API_BASE_URL || 'http://localhost:8001'),
    'import.meta.env.VITE_SMOLAGENTS_API_URL': JSON.stringify(process.env.VITE_SMOLAGENTS_API_URL || 'http://localhost:8002'),
    'import.meta.env.VITE_APP_VERSION': JSON.stringify(process.env.VITE_APP_VERSION || '1.0.0'),
    'import.meta.env.VITE_DEV_MODE': JSON.stringify(process.env.VITE_DEV_MODE || 'false'),
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    watch: {
      usePolling: true,
    },
    proxy: {
      '/api': {
        // target: 'http://localhost:8001',
        target: 'http://zht_backend_0624:8001',
        changeOrigin: true,
        secure: false,
      },
    },
  },
  preview: {
    host: '0.0.0.0',
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://zht_backend_0624:8001',
        changeOrigin: true,
        secure: false,
      },
    },
  },
})
