/**
 * 对话评价面板组件
 * 引导用户使用AI智能评分系统
 */

import { useMemo } from 'react'
import {
  BarChart3,
  Star,
  Brain,
  ArrowRight,
  Sparkles,
  Target,
  MessageCircle,
  Zap,
  Globe,
  FileText,
  Heart,
  Info
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import type { ChatMessage, TrainingCustomer } from '@/types/salesTraining'

interface EvaluationPanelProps {
  messages: ChatMessage[]
  customer: TrainingCustomer
}

// 评估维度配置
const evaluationDimensions = [
  {
    id: 'needsUnderstanding',
    name: '需求理解',
    icon: Target,
    color: 'text-blue-500',
    description: '对客户需求的理解和把握程度',
    weight: 30
  },
  {
    id: 'professionalism',
    name: '专业度',
    icon: Star,
    color: 'text-yellow-500',
    description: '产品知识和行业专业性',
    weight: 20
  },
  {
    id: 'problemSolving',
    name: '问题处理',
    icon: MessageCircle,
    color: 'text-green-500',
    description: '处理客户问题和异议的能力',
    weight: 15
  },
  {
    id: 'persuasiveness',
    name: '说服力',
    icon: Zap,
    color: 'text-purple-500',
    description: '说服和影响客户的能力',
    weight: 15
  },
  {
    id: 'culturalSensitivity',
    name: '跨文化敏感度',
    icon: Globe,
    color: 'text-indigo-500',
    description: '对不同文化背景的理解和适应',
    weight: 10
  },
  {
    id: 'termControl',
    name: '条款把控',
    icon: FileText,
    color: 'text-orange-500',
    description: '对合同条款和商务细节的把控',
    weight: 5
  },
  {
    id: 'customerSatisfaction',
    name: '客户满意度',
    icon: Heart,
    color: 'text-red-500',
    description: '客户对沟通过程的满意程度',
    weight: 5
  }
]

export function EvaluationPanel({ messages, customer }: EvaluationPanelProps) {
  // 计算用户消息数量
  const userMessages = useMemo(() =>
    messages.filter(m => m.role === 'user'), [messages]
  )

  const messageCount = userMessages.length

  return (
    <Card className="shadow-sm border-gray-200 dark:border-gray-700">
      <CardHeader className="pb-2 bg-gradient-to-r from-blue-50/50 to-white dark:from-blue-900/10 dark:to-gray-800">
        <CardTitle className="text-base flex items-center space-x-2">
          <div className="p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-md">
            <Brain className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </div>
          <span>AI智能评分</span>
        </CardTitle>
        <CardDescription className="text-xs">
          使用AI技术进行专业的销售能力评估
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 p-4">
        {/* AI评分提示 */}
        <Alert className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-900/20">
          <Sparkles className="h-4 w-4 text-blue-600" />
          <AlertDescription className="text-sm text-blue-800 dark:text-blue-200">
            <div className="space-y-2">
              <p className="font-medium">升级到AI智能评分系统！</p>
              <p className="text-xs">
                请使用右侧的"AI智能评分"功能获取基于真实AI分析的专业评分和详细反馈。
              </p>
              <div className="flex items-center space-x-1 text-xs">
                <span>点击</span>
                <Badge variant="outline" className="text-xs px-1">评分</Badge>
                <ArrowRight className="w-3 h-3" />
                <Badge variant="outline" className="text-xs px-1">开始AI评分</Badge>
              </div>
            </div>
          </AlertDescription>
        </Alert>

        <Separator />

        {/* 评估维度预览 */}
        <div className="space-y-3">
          <h4 className="font-semibold text-xs text-gray-700 dark:text-gray-300 flex items-center space-x-1">
            <Star className="w-3 h-3 text-yellow-500" />
            <span>AI评分维度</span>
          </h4>
          <div className="grid grid-cols-1 gap-2">
            {evaluationDimensions.map((dimension, index) => {
              const Icon = dimension.icon
              return (
                <div key={dimension.id} className="p-2 bg-gray-50/50 dark:bg-gray-800/50 rounded-md border border-gray-100 dark:border-gray-700 opacity-0 animate-fade-in-up" style={{animationDelay: `${index * 0.1}s`, animationFillMode: 'forwards'}}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="p-1 bg-white dark:bg-gray-700 rounded shadow-sm">
                        <Icon className={`w-3 h-3 ${dimension.color}`} />
                      </div>
                      <span className="text-xs font-medium">{dimension.name}</span>
                    </div>
                    <Badge variant="outline" className="text-xs px-1">
                      {dimension.weight}%
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 leading-relaxed mt-1 ml-6">
                    {dimension.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>

        <Separator />

        {/* 对话统计 */}
        <div className="space-y-2">
          <h4 className="font-semibold text-xs text-gray-700 dark:text-gray-300 flex items-center space-x-1">
            <BarChart3 className="w-3 h-3 text-blue-500" />
            <span>对话统计</span>
          </h4>
          <div className="grid grid-cols-2 gap-3">
            <div className="p-3 bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="text-lg font-bold text-blue-700 dark:text-blue-300">
                {messageCount}
              </div>
              <p className="text-xs text-blue-600 dark:text-blue-400">用户回复</p>
            </div>
            <div className="p-3 bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-800/20 rounded-lg border border-green-200 dark:border-green-800">
              <div className="text-lg font-bold text-green-700 dark:text-green-300">
                {Math.floor(messages.length / 2)}
              </div>
              <p className="text-xs text-green-600 dark:text-green-400">对话轮次</p>
            </div>
          </div>
        </div>

        {/* 使用提示 */}
        {messageCount >= 1 && (
          <>
            <Separator />
            <div className="p-3 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg border border-indigo-200 dark:border-indigo-800">
              <div className="flex items-start space-x-2">
                <Info className="w-4 h-4 text-indigo-600 dark:text-indigo-400 mt-0.5" />
                <div className="space-y-1">
                  <p className="text-xs font-medium text-indigo-800 dark:text-indigo-200">
                    可以开始AI评分了！
                  </p>
                  <p className="text-xs text-indigo-600 dark:text-indigo-300">
                    您已进行了 {messageCount} 轮对话，可以使用AI智能评分功能获取专业的销售能力分析和改进建议。
                  </p>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
