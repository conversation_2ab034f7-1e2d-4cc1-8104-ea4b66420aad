"""Remove position table and position field from customers

Revision ID: 008_remove_position_table
Revises: 007_remove_experience_level
Create Date: 2025-01-02 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '008_remove_position_table'
down_revision = '007_remove_experience_level'
branch_labels = None
depends_on = None


def upgrade():
    """删除职位表和客户表中的职位字段"""
    # 删除客户表中的 position 字段
    op.drop_column('sales_training_customers', 'position')
    
    # 删除职位表的索引
    op.drop_index('uk_position_name_category', table_name='sales_training_positions')
    op.drop_index('idx_position_name', table_name='sales_training_positions')
    op.drop_index('idx_position_category', table_name='sales_training_positions')
    op.drop_index('idx_position_active', table_name='sales_training_positions')
    
    # 删除职位表
    op.drop_table('sales_training_positions')


def downgrade():
    """恢复职位表和客户表中的职位字段"""
    # 重新创建职位表
    op.create_table('sales_training_positions',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(length=200), nullable=False, comment='职位名称'),
        sa.Column('category', sa.String(length=100), nullable=False, comment='职位部门'),
        sa.Column('description', sa.Text(), nullable=True, comment='职位描述'),
        sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否启用'),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 重新创建索引
    op.create_index('idx_position_active', 'sales_training_positions', ['is_active'], unique=False)
    op.create_index('idx_position_category', 'sales_training_positions', ['category'], unique=False)
    op.create_index('idx_position_name', 'sales_training_positions', ['name'], unique=False)
    op.create_index('uk_position_name_category', 'sales_training_positions', ['name', 'category'], unique=True)
    
    # 重新添加客户表中的 position 字段
    op.add_column('sales_training_customers', sa.Column('position', sa.String(100), comment='职位'))
