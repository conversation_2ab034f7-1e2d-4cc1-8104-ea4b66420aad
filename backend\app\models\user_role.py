"""
用户角色关联数据模型
定义用户与角色、角色与权限的关联关系
"""
from sqlalchemy import Column, Integer, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class UserRole(Base):
    """
    用户角色关联模型
    定义用户与角色的多对多关系
    """
    __tablename__ = "user_roles"
    
    # 主键ID
    id = Column(Integer, primary_key=True, index=True, comment="关联ID")
    
    # 用户ID（外键）
    user_id = Column(
        Integer, 
        ForeignKey("users.id", ondelete="CASCADE"), 
        nullable=False, 
        index=True,
        comment="用户ID"
    )
    
    # 角色ID（外键）
    role_id = Column(
        Integer, 
        ForeignKey("roles.id", ondelete="CASCADE"), 
        nullable=False, 
        index=True,
        comment="角色ID"
    )
    
    # 分配时间
    assigned_at = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        comment="分配时间"
    )
    
    # 分配者ID（外键，可选）
    assigned_by = Column(
        Integer, 
        ForeignKey("users.id"), 
        nullable=True,
        comment="分配者ID"
    )
    
    # 唯一约束：一个用户不能重复拥有同一个角色
    __table_args__ = (
        UniqueConstraint('user_id', 'role_id', name='uq_user_role'),
    )
    
    # 关联用户
    user = relationship("User", foreign_keys=[user_id], back_populates="user_roles")
    
    # 关联角色
    role = relationship("Role", back_populates="user_roles")
    
    # 关联分配者
    assigned_by_user = relationship("User", foreign_keys=[assigned_by])
    
    def __repr__(self):
        return f"<UserRole(user_id={self.user_id}, role_id={self.role_id})>"


class RolePermission(Base):
    """
    角色权限关联模型
    定义角色与权限的多对多关系
    """
    __tablename__ = "role_permissions"
    
    # 主键ID
    id = Column(Integer, primary_key=True, index=True, comment="关联ID")
    
    # 角色ID（外键）
    role_id = Column(
        Integer, 
        ForeignKey("roles.id", ondelete="CASCADE"), 
        nullable=False, 
        index=True,
        comment="角色ID"
    )
    
    # 权限ID（外键）
    permission_id = Column(
        Integer, 
        ForeignKey("permissions.id", ondelete="CASCADE"), 
        nullable=False, 
        index=True,
        comment="权限ID"
    )
    
    # 唯一约束：一个角色不能重复拥有同一个权限
    __table_args__ = (
        UniqueConstraint('role_id', 'permission_id', name='uq_role_permission'),
    )
    
    # 关联角色
    role = relationship("Role", back_populates="role_permissions")
    
    # 关联权限
    permission = relationship("Permission", back_populates="role_permissions")
    
    def __repr__(self):
        return f"<RolePermission(role_id={self.role_id}, permission_id={self.permission_id})>"
