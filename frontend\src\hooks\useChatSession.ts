/**
 * 聊天会话管理 Hook
 * 提供完整的聊天会话状态管理和操作功能
 */
import { useState, useCallback, useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useRAGQuery, useChatHistory, morphikQueryKeys } from '@/hooks/useMorphik';
import type { RAGQueryRequest } from '@/types/morphik';
import { generateUUID } from '@/utils/uuid';

interface ChatInterfaceMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  sources?: any[];
  isLoading?: boolean;
  error?: string;
  feedback?: 'positive' | 'negative';
  isSystem?: boolean;
}

interface UseChatSessionOptions {
  maxMessages?: number;
  showSources?: boolean;
  useGraphEnhancement?: boolean;
  selectedGraph?: string;
  autoSave?: boolean;
}

interface UseChatSessionReturn {
  // 会话状态
  currentChatId: string;
  messages: ChatInterfaceMessage[];
  isLoading: boolean;

  // 会话操作
  sendMessage: (query: string) => Promise<void>;
  regenerateResponse: (messageId: string) => Promise<void>;
  clearChat: () => void;
  createNewSession: () => string;

  // 消息操作
  addMessage: (message: Omit<ChatInterfaceMessage, 'id' | 'timestamp'>) => void;
  updateMessage: (messageId: string, updates: Partial<ChatInterfaceMessage>) => void;
  removeMessage: (messageId: string) => void;

  // 配置
  updateOptions: (options: Partial<UseChatSessionOptions>) => void;
}

export function useChatSession(
  initialOptions: UseChatSessionOptions = {}
): UseChatSessionReturn {
  const queryClient = useQueryClient();

  // 默认配置
  const [options, setOptions] = useState<UseChatSessionOptions>({
    maxMessages: 50,
    showSources: true,
    useGraphEnhancement: false,
    autoSave: true,
    ...initialOptions,
  });

  // 当前会话ID
  const [currentChatId, setCurrentChatId] = useState<string>(() => generateUUID());

  // 进度计时器
  const progressTimerRef = useRef<number | null>(null);

  // 本地消息状态
  const [messages, setMessages] = useState<ChatInterfaceMessage[]>([
    {
      id: 'welcome',
      type: 'assistant',
      content: '您好！我是您的智能助手，可以帮您在知识库中查找信息并回答问题。请随时向我提问！',
      timestamp: new Date(),
    }
  ]);

  // API hooks
  const ragQuery = useRAGQuery();
  const chatHistory = useChatHistory(currentChatId, options.autoSave || false);

  // 从服务器加载聊天历史
  useEffect(() => {
    if (chatHistory.data && chatHistory.data.length > 0) {
      const serverMessages: ChatInterfaceMessage[] = chatHistory.data.map((msg, index) => ({
        id: `${msg.role}-${index}-${Date.now()}`,
        type: msg.role,
        content: msg.content,
        timestamp: new Date(msg.timestamp),
        sources: msg.agent_data?.sources,
      }));

      // 如果服务器有历史记录，替换本地消息（除了欢迎消息）
      if (serverMessages.length > 0) {
        setMessages(prev => [
          prev.find(m => m.id === 'welcome') || prev[0],
          ...serverMessages
        ]);
      }
    }
  }, [chatHistory.data]);

  // 创建新会话
  const createNewSession = useCallback(() => {
    const newChatId = generateUUID();
    setCurrentChatId(newChatId);
    setMessages([
      {
        id: 'welcome',
        type: 'assistant',
        content: '您好！我是您的智能助手，可以帮您在知识库中查找信息并回答问题。请随时向我提问！',
        timestamp: new Date(),
      }
    ]);
    return newChatId;
  }, []);

  // 添加消息
  const addMessage = useCallback((message: Omit<ChatInterfaceMessage, 'id' | 'timestamp'>) => {
    const newMessage: ChatInterfaceMessage = {
      ...message,
      id: `${message.type}-${Date.now()}-${Math.random()}`,
      timestamp: new Date(),
    };

    setMessages(prev => {
      const updated = [...prev, newMessage];
      // 限制消息数量
      if (options.maxMessages && updated.length > options.maxMessages) {
        return updated.slice(-options.maxMessages);
      }
      return updated;
    });
  }, [options.maxMessages]);

  // 更新消息
  const updateMessage = useCallback((messageId: string, updates: Partial<ChatInterfaceMessage>) => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId ? { ...msg, ...updates } : msg
    ));
  }, []);

  // 删除消息
  const removeMessage = useCallback((messageId: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== messageId));
  }, []);

  // 发送消息
  const sendMessage = useCallback(async (query: string) => {
    if (!query.trim() || ragQuery.isPending) {
      console.warn('消息发送被阻止:', {
        queryEmpty: !query.trim(),
        isPending: ragQuery.isPending
      });
      return;
    }

    const assistantMessageId = `assistant-${Date.now()}`;

    // 添加用户消息
    addMessage({
      type: 'user',
      content: query,
    });

    // 添加加载中的助手消息
    addMessage({
      type: 'assistant',
      content: '',
      isLoading: true,
    });

    try {
      // 构建查询请求
      const queryRequest: RAGQueryRequest = {
        query,
        max_chunks: 5,
        temperature: 0.7,
        include_sources: options.showSources,
        chat_id: currentChatId, // 关键：传递chat_id实现多轮对话
      };

      // 如果启用知识图谱增强
      if (options.useGraphEnhancement && options.selectedGraph) {
        queryRequest.graph_name = options.selectedGraph;

        // 显示知识图谱状态
        updateMessage(assistantMessageId, {
          content: `正在使用知识图谱 "${options.selectedGraph}" 进行增强检索...\n\n这可能需要1-2分钟时间，请耐心等待。`,
          isLoading: true,
        });

        toast.info('🔍 知识图谱增强检索中，请耐心等待...', {
          duration: 5000,
        });
      }



      // 显示处理状态并启动进度计时器
      let progressSeconds = 0;
      updateMessage(assistantMessageId, {
        content: '正在处理您的问题，大模型响应需要30-60秒，请耐心等待...',
        isLoading: true,
      });

      // 启动进度计时器
      progressTimerRef.current = window.setInterval(() => {
        progressSeconds += 5;
        updateMessage(assistantMessageId, {
          content: `正在处理您的问题... (已等待 ${progressSeconds} 秒)\n\n大模型响应需要30-60秒，请耐心等待。`,
          isLoading: true,
        });
      }, 5000);

      const response = await ragQuery.mutateAsync({
        request: queryRequest,
        context: 'chat'
      });

      // 清理进度计时器
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
        progressTimerRef.current = null;
      }

      console.log('📝 查询响应:', {
        completionLength: response.completion?.length || 0,
        sourcesCount: response.sources?.length || 0,
        hasChatId: !!queryRequest.chat_id
      });

      // 更新助手消息
      updateMessage(assistantMessageId, {
        content: response.completion,
        sources: response.sources,
        isLoading: false,
      });

      // 如果启用自动保存，刷新聊天历史
      if (options.autoSave) {
        queryClient.invalidateQueries({
          queryKey: morphikQueryKeys.chatHistory(currentChatId)
        });
      }

    } catch (error: any) {
      // 清理进度计时器
      if (progressTimerRef.current) {
        clearInterval(progressTimerRef.current);
        progressTimerRef.current = null;
      }

      console.error('发送消息失败:', error);

      // 根据错误类型提供更具体的错误信息
      let errorMessage = '抱歉，处理您的问题时出现了错误。请稍后重试。';

      if (error.message?.includes('timeout')) {
        errorMessage = '请求超时，模型响应时间较长。请稍后重试或尝试简化问题。';
      } else if (error.message?.includes('network')) {
        errorMessage = '网络连接错误，请检查网络连接后重试。';
      } else if (error.message?.includes('500')) {
        errorMessage = '服务器内部错误，请稍后重试。';
      } else if (error.message?.includes('404')) {
        errorMessage = 'API端点未找到，请检查服务配置。';
      }

      updateMessage(assistantMessageId, {
        content: errorMessage,
        error: error.message,
        isLoading: false,
      });
    }
  }, [
    ragQuery,
    currentChatId,
    options,
    addMessage,
    updateMessage,
    queryClient
  ]);

  // 重新生成回答
  const regenerateResponse = useCallback(async (messageId: string) => {
    const message = messages.find(m => m.id === messageId);
    if (!message || message.type !== 'assistant') return;

    // 找到对应的用户消息
    const messageIndex = messages.findIndex(m => m.id === messageId);
    const userMessage = messages[messageIndex - 1];
    if (!userMessage || userMessage.type !== 'user') return;

    // 更新为加载状态
    updateMessage(messageId, {
      content: '',
      isLoading: true,
      error: undefined,
    });

    try {
      const queryRequest: RAGQueryRequest = {
        query: userMessage.content,
        max_chunks: 5,
        temperature: 0.8, // 稍微提高温度以获得不同的回答
        include_sources: options.showSources,
        chat_id: currentChatId,
      };

      if (options.useGraphEnhancement && options.selectedGraph) {
        queryRequest.graph_name = options.selectedGraph;
      }

      const response = await ragQuery.mutateAsync({
        request: queryRequest,
        context: 'chat'
      });

      updateMessage(messageId, {
        content: response.completion,
        sources: response.sources,
        isLoading: false,
      });

      if (options.autoSave) {
        queryClient.invalidateQueries({
          queryKey: morphikQueryKeys.chatHistory(currentChatId)
        });
      }

    } catch (error: any) {
      updateMessage(messageId, {
        content: '重新生成回答时出现错误，请稍后重试。',
        error: error.message,
        isLoading: false,
      });
    }
  }, [messages, ragQuery, currentChatId, options, updateMessage, queryClient]);

  // 清空聊天
  const clearChat = useCallback(() => {
    setMessages([
      {
        id: 'welcome',
        type: 'assistant',
        content: '您好！我是您的智能助手，可以帮您在知识库中查找信息并回答问题。请随时向我提问！',
        timestamp: new Date(),
      }
    ]);
  }, []);

  // 更新配置
  const updateOptions = useCallback((newOptions: Partial<UseChatSessionOptions>) => {
    setOptions(prev => ({ ...prev, ...newOptions }));
  }, []);

  return {
    // 会话状态
    currentChatId,
    messages,
    isLoading: ragQuery.isPending,

    // 会话操作
    sendMessage,
    regenerateResponse,
    clearChat,
    createNewSession,

    // 消息操作
    addMessage,
    updateMessage,
    removeMessage,

    // 配置
    updateOptions,
  };
}
