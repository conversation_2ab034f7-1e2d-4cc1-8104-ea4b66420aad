# 检查开发模式状态脚本
# 用于快速验证前端和后端的开发模式配置

Write-Host "🔍 检查开发模式状态..." -ForegroundColor Cyan
Write-Host ""

# 检查容器是否运行
$frontendRunning = docker ps --filter "name=zht_frontend_0624" --filter "status=running" --quiet
$backendRunning = docker ps --filter "name=zht_backend_0624" --filter "status=running" --quiet

if (-not $frontendRunning) {
    Write-Host "❌ 前端容器未运行" -ForegroundColor Red
} else {
    Write-Host "✅ 前端容器正在运行" -ForegroundColor Green
    try {
        $frontendDevMode = docker exec zht_frontend_0624 printenv VITE_DEV_MODE 2>$null
        Write-Host "   前端开发模式: VITE_DEV_MODE=$frontendDevMode" -ForegroundColor Yellow
    } catch {
        Write-Host "   ⚠️ 无法获取前端环境变量" -ForegroundColor Yellow
    }
}

if (-not $backendRunning) {
    Write-Host "❌ 后端容器未运行" -ForegroundColor Red
} else {
    Write-Host "✅ 后端容器正在运行" -ForegroundColor Green
    try {
        $backendDevMode = docker exec zht_backend_0624 printenv DEV_MODE 2>$null
        Write-Host "   后端开发模式: DEV_MODE=$backendDevMode" -ForegroundColor Yellow
    } catch {
        Write-Host "   ⚠️ 无法获取后端环境变量" -ForegroundColor Yellow
    }
}

Write-Host ""

# 检查当前系统环境变量
Write-Host "🌐 当前系统环境变量:" -ForegroundColor Cyan
Write-Host "   DEV_MODE=$env:DEV_MODE" -ForegroundColor White
Write-Host "   VITE_DEV_MODE=$env:VITE_DEV_MODE" -ForegroundColor White

Write-Host ""

# 提供建议
if ($frontendRunning -and $backendRunning) {
    $frontendDevMode = docker exec zht_frontend_0624 printenv VITE_DEV_MODE 2>$null
    $backendDevMode = docker exec zht_backend_0624 printenv DEV_MODE 2>$null
    $debugMode = docker exec zht_backend_0624 printenv DEBUG 2>$null

    Write-Host ""
    Write-Host "🔍 容器内配置状态:" -ForegroundColor Cyan
    Write-Host "   前端 VITE_DEV_MODE: $frontendDevMode" -ForegroundColor White
    Write-Host "   后端 DEV_MODE: $backendDevMode" -ForegroundColor White
    Write-Host "   后端 DEBUG: $debugMode" -ForegroundColor White

    if ($frontendDevMode -eq "false" -and $backendDevMode -eq "false" -and $debugMode -eq "false") {
        Write-Host "🔒 开发模式已禁用 - 需要登录才能访问应用" -ForegroundColor Green
    } elseif ($frontendDevMode -eq "true" -and $backendDevMode -eq "true" -and $debugMode -eq "true") {
        Write-Host "🚀 开发模式已启用 - 无需登录即可访问应用" -ForegroundColor Yellow
    } else {
        Write-Host "⚠️ 配置不一致！建议统一配置" -ForegroundColor Red
        Write-Host "   生产模式: .\switch-mode.ps1 prod" -ForegroundColor Yellow
        Write-Host "   开发模式: .\switch-mode.ps1 dev" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️ 请先启动服务: .\manage-zht-system.ps1 --start" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🌐 应用访问地址: http://localhost:5173" -ForegroundColor Cyan
