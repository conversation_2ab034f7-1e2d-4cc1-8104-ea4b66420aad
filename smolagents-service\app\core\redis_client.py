"""
Redis 客户端模块
提供异步 Redis 连接和操作功能
"""

import asyncio
import logging
from typing import Optional, Any, Dict
import json

import redis.asyncio as redis

from app.config import Settings
from app.utils.exceptions import RedisConnectionError


class RedisClient:
    """异步 Redis 客户端"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self.redis: Optional[redis.Redis] = None
        self._connection_pool = None
    
    async def connect(self):
        """建立 Redis 连接"""
        try:
            self.logger.info(f"🔗 连接 Redis: {self.settings.redis_host}:{self.settings.redis_port}")
            
            # 创建连接池
            self.redis = redis.from_url(
                self.settings.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_timeout=self.settings.redis_timeout,
                socket_connect_timeout=self.settings.redis_timeout,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 测试连接
            await self.ping()
            
            self.logger.info("✅ Redis 连接成功")
            
        except Exception as e:
            self.logger.error(f"❌ Redis 连接失败: {e}")
            raise RedisConnectionError(f"Redis 连接失败: {e}")
    
    async def disconnect(self):
        """断开 Redis 连接"""
        try:
            if self.redis:
                await self.redis.close()
                self.redis = None
                self.logger.info("✅ Redis 连接已关闭")
        except Exception as e:
            self.logger.error(f"❌ Redis 断开连接失败: {e}")
    
    async def ping(self) -> bool:
        """测试 Redis 连接"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            result = await self.redis.ping()
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Redis ping 失败: {e}")
            raise RedisConnectionError(f"Redis ping 失败: {e}")
    
    async def get(self, key: str) -> Optional[str]:
        """获取键值"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            return await self.redis.get(key)
            
        except Exception as e:
            self.logger.error(f"❌ Redis GET 失败 {key}: {e}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: str, 
        expire: Optional[int] = None
    ) -> bool:
        """设置键值"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            result = await self.redis.set(key, value, ex=expire)
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Redis SET 失败 {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除键"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            result = await self.redis.delete(key)
            return result > 0
            
        except Exception as e:
            self.logger.error(f"❌ Redis DELETE 失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            result = await self.redis.exists(key)
            return result > 0
            
        except Exception as e:
            self.logger.error(f"❌ Redis EXISTS 失败 {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置键过期时间"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            result = await self.redis.expire(key, seconds)
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Redis EXPIRE 失败 {key}: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """获取键剩余生存时间"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            return await self.redis.ttl(key)
            
        except Exception as e:
            self.logger.error(f"❌ Redis TTL 失败 {key}: {e}")
            return -1
    
    async def hget(self, name: str, key: str) -> Optional[str]:
        """获取哈希字段值"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            return await self.redis.hget(name, key)
            
        except Exception as e:
            self.logger.error(f"❌ Redis HGET 失败 {name}.{key}: {e}")
            return None
    
    async def hset(self, name: str, key: str, value: str) -> bool:
        """设置哈希字段值"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            result = await self.redis.hset(name, key, value)
            return result >= 0
            
        except Exception as e:
            self.logger.error(f"❌ Redis HSET 失败 {name}.{key}: {e}")
            return False
    
    async def hgetall(self, name: str) -> Dict[str, str]:
        """获取哈希所有字段"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            return await self.redis.hgetall(name)
            
        except Exception as e:
            self.logger.error(f"❌ Redis HGETALL 失败 {name}: {e}")
            return {}
    
    async def hdel(self, name: str, *keys: str) -> int:
        """删除哈希字段"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            return await self.redis.hdel(name, *keys)
            
        except Exception as e:
            self.logger.error(f"❌ Redis HDEL 失败 {name}: {e}")
            return 0
    
    async def lpush(self, name: str, *values: str) -> int:
        """向列表左侧推入元素"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            return await self.redis.lpush(name, *values)
            
        except Exception as e:
            self.logger.error(f"❌ Redis LPUSH 失败 {name}: {e}")
            return 0
    
    async def rpop(self, name: str) -> Optional[str]:
        """从列表右侧弹出元素"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            return await self.redis.rpop(name)
            
        except Exception as e:
            self.logger.error(f"❌ Redis RPOP 失败 {name}: {e}")
            return None
    
    async def llen(self, name: str) -> int:
        """获取列表长度"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            return await self.redis.llen(name)
            
        except Exception as e:
            self.logger.error(f"❌ Redis LLEN 失败 {name}: {e}")
            return 0
    
    async def incr(self, name: str, amount: int = 1) -> int:
        """递增计数器"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            return await self.redis.incr(name, amount)
            
        except Exception as e:
            self.logger.error(f"❌ Redis INCR 失败 {name}: {e}")
            return 0
    
    async def get_json(self, key: str) -> Optional[Any]:
        """获取 JSON 数据"""
        try:
            data = await self.get(key)
            if data:
                return json.loads(data)
            return None
            
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ JSON 解析失败 {key}: {e}")
            return None
    
    async def set_json(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[int] = None
    ) -> bool:
        """设置 JSON 数据"""
        try:
            json_data = json.dumps(value, ensure_ascii=False)
            return await self.set(key, json_data, expire)
            
        except (TypeError, ValueError) as e:
            self.logger.error(f"❌ JSON 序列化失败 {key}: {e}")
            return False
    
    async def get_info(self) -> Dict[str, Any]:
        """获取 Redis 信息"""
        try:
            if not self.redis:
                raise RedisConnectionError("Redis 未连接")
            
            info = await self.redis.info()
            return {
                "version": info.get("redis_version", "unknown"),
                "mode": info.get("redis_mode", "unknown"),
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "unknown"),
                "uptime": info.get("uptime_in_seconds", 0)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 获取 Redis 信息失败: {e}")
            return {}
