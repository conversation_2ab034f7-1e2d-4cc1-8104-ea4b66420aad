#!/usr/bin/env python3
"""
改进版智能市场分析演示系统
优化了性能和稳定性
"""

import asyncio
import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
import httpx
from dataclasses import dataclass


@dataclass
class SearchQuery:
    """搜索查询结构"""
    query: str
    category: str
    priority: int


@dataclass
class AnalysisContext:
    """分析上下文"""
    product: str
    region: str
    time_period: str
    analysis_type: str


class ImprovedMarketAnalysis:
    """改进版智能市场分析系统"""
    
    def __init__(self, base_url: str = "http://localhost:8002"):
        self.base_url = base_url
        self.session: Optional[httpx.AsyncClient] = None
        
    async def __aenter__(self):
        self.session = httpx.AsyncClient(timeout=180.0)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.aclose()
    
    def parse_search_term(self, search_term: str) -> AnalysisContext:
        """智能解析搜索词"""
        print(f"🔍 解析搜索词: {search_term}")
        
        # 简化的解析逻辑
        product_match = re.search(r'([^0-9\s]+?)(?=\s*20\d{2}|\s*市场)', search_term)
        product = product_match.group(1).strip() if product_match else "未知产品"
        
        region = "全球"
        if "亚洲" in search_term: region = "亚洲"
        elif "中国" in search_term: region = "中国"
        elif "美国" in search_term: region = "美国"
        elif "欧洲" in search_term: region = "欧洲"
        
        time_match = re.search(r'20\d{2}', search_term)
        time_period = time_match.group(0) if time_match else "2024"
        
        analysis_type = "综合分析"
        if "销售" in search_term: analysis_type = "销售分析"
        elif "竞争" in search_term: analysis_type = "竞争分析"
        elif "趋势" in search_term: analysis_type = "趋势分析"
        
        context = AnalysisContext(product, region, time_period, analysis_type)
        
        print(f"✅ 解析结果: {context.product} | {context.region} | {context.time_period} | {context.analysis_type}")
        return context
    
    def generate_search_queries(self, context: AnalysisContext) -> List[SearchQuery]:
        """生成搜索查询（简化版）"""
        print("🎯 生成搜索策略...")
        
        queries = [
            SearchQuery(
                f"{context.product} {context.region} market size {context.time_period}",
                "市场规模", 1
            ),
            SearchQuery(
                f"{context.product} sales data {context.region} {context.time_period}",
                "销售数据", 1
            ),
            SearchQuery(
                f"{context.product} market trends {context.time_period} forecast",
                "趋势预测", 2
            )
        ]
        
        print(f"✅ 生成了 {len(queries)} 个搜索策略")
        return queries
    
    async def execute_search(self, queries: List[SearchQuery]) -> Dict[str, Any]:
        """执行搜索（优化版）"""
        print("🌐 开始执行搜索...")
        
        search_results = {}
        
        for i, query in enumerate(queries, 1):
            print(f"   [{i}/{len(queries)}] 搜索: {query.category}")
            
            try:
                # 简化的搜索提示词
                search_prompt = f"""
请使用网络搜索工具查找关于"{query.query}"的信息。

请提供：
1. 关键数据和统计信息
2. 市场洞察
3. 相关趋势

保持结果简洁，重点关注数字和事实。
"""
                
                data = {
                    "query": search_prompt,
                    "max_steps": 10,
                    "use_cache": False,
                    "enable_web_search": True,
                    "timeout": 120
                }
                
                response = await self.session.post(f"{self.base_url}/api/v1/agent/query", json=data)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        search_results[query.category] = {
                            'query': query.query,
                            'result': result.get('result', ''),
                            'priority': query.priority
                        }
                        print(f"      ✅ 成功")
                    else:
                        print(f"      ❌ 失败: {result.get('error', '未知错误')}")
                else:
                    print(f"      ❌ HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"      ❌ 异常: {e}")
            
            # 添加延迟
            if i < len(queries):
                await asyncio.sleep(3)
        
        print(f"✅ 搜索完成，获取 {len(search_results)} 个数据源")
        return search_results
    
    async def generate_analysis(self, context: AnalysisContext, search_results: Dict[str, Any]) -> str:
        """生成分析报告（优化版）"""
        print("📊 生成分析报告...")
        
        # 构建简化的分析提示词
        data_summary = ""
        for category, data in search_results.items():
            result_text = str(data['result'])[:300] if data['result'] else "无数据"
            data_summary += f"{category}: {result_text}\n"
        
        analysis_prompt = f"""
基于以下数据为"{context.product}"在{context.region}市场生成简洁的分析报告：

数据摘要:
{data_summary}

请生成包含以下内容的报告：
1. 市场现状（市场规模、主要参与者）
2. 关键趋势（增长情况、发展方向）
3. 竞争分析（主要竞争对手、市场份额）
4. 未来预测（2025年展望）
5. 结论建议（核心洞察、战略建议）

要求：基于真实数据，逻辑清晰，适合商业决策，控制在800字以内。
"""
        
        try:
            print(f"   提示词长度: {len(analysis_prompt)} 字符")
            
            data = {
                "query": analysis_prompt,
                "max_steps": 12,
                "use_cache": False,
                "enable_web_search": False,
                "timeout": 120
            }
            
            response = await self.session.post(f"{self.base_url}/api/v1/agent/query", json=data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    analysis_report = result.get('result', '')
                    if analysis_report:
                        print("✅ 分析报告生成完成")
                        return analysis_report
                    else:
                        return "分析报告生成失败: 返回结果为空"
                else:
                    error_msg = result.get('error', '未知错误')
                    return f"分析报告生成失败: {error_msg}"
            else:
                return f"分析报告生成失败: HTTP {response.status_code}"
                
        except Exception as e:
            return f"分析报告生成失败: {str(e)}"
    
    async def run_analysis(self, search_term: str) -> Dict[str, Any]:
        """运行完整分析流程"""
        start_time = datetime.now()
        print("🚀 启动改进版市场分析系统")
        print("=" * 60)
        print(f"搜索词: {search_term}")
        print(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        print()
        
        try:
            # 1. 解析搜索词
            context = self.parse_search_term(search_term)
            
            # 2. 生成搜索策略
            queries = self.generate_search_queries(context)
            
            # 3. 执行搜索
            search_results = await self.execute_search(queries)
            
            if not search_results:
                return {
                    'success': False,
                    'error': '未能获取有效的市场数据',
                    'context': context.__dict__
                }
            
            # 4. 生成分析报告
            analysis_report = await self.generate_analysis(context, search_results)
            
            # 计算执行时间
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            print("=" * 60)
            print("🎉 分析完成!")
            print(f"执行时间: {execution_time:.2f} 秒")
            print(f"数据源数量: {len(search_results)}")
            print("=" * 60)
            
            return {
                'success': True,
                'context': context.__dict__,
                'search_results': search_results,
                'analysis_report': analysis_report,
                'execution_time': execution_time,
                'data_sources_count': len(search_results),
                'timestamp': end_time.isoformat()
            }
            
        except Exception as e:
            error_msg = f"分析执行失败: {e}"
            print(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'context': context.__dict__ if 'context' in locals() else None
            }


async def main():
    """主演示函数"""
    search_term = "沙滩包2024亚洲市场销售情况分析及2025市场增长预期"
    
    print("🌟 改进版智能市场分析演示")
    print("优化了性能和稳定性")
    print("=" * 60)
    print()
    
    async with ImprovedMarketAnalysis() as analyzer:
        result = await analyzer.run_analysis(search_term)
        
        if result['success']:
            print("\n" + "=" * 60)
            print("📊 市场分析报告")
            print("=" * 60)
            print(result['analysis_report'])
            print("\n" + "=" * 60)
            print("📈 分析统计")
            print(f"数据源数量: {result['data_sources_count']}")
            print(f"执行时间: {result['execution_time']:.2f} 秒")
            print(f"完成时间: {result['timestamp']}")
        else:
            print(f"\n❌ 分析失败: {result['error']}")


if __name__ == "__main__":
    asyncio.run(main())
