/**
 * 管理员面板页面
 */
import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { RoleManagement } from '@/components/admin/RoleManagement'
import { UserManagement } from '@/components/admin/UserManagement'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { Shield, Users, Settings, BarChart3, AlertTriangle } from 'lucide-react'
import { useAuthStore } from '@/store/auth'
import { usePermissions } from '@/hooks/usePermissions'

const AdminPage: React.FC = () => {
  const { user } = useAuthStore()
  const { hasAdminAccess, loading: permissionsLoading } = usePermissions()
  const [activeTab, setActiveTab] = useState('overview')

  // 权限加载中
  if (permissionsLoading) {
    return (
      <ProtectedRoute>
        <div className="container mx-auto py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">正在检查权限...</p>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    )
  }

  if (!hasAdminAccess()) {
    return (
      <ProtectedRoute>
        <div className="container mx-auto py-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              您没有访问管理员面板的权限。请联系系统管理员。
            </AlertDescription>
          </Alert>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="container mx-auto py-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold">管理员面板</h1>
          <p className="text-muted-foreground">
            系统管理和配置中心
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="users">用户管理</TabsTrigger>
            <TabsTrigger value="roles">角色权限</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* 统计卡片 */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">总用户数</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">--</div>
                  <p className="text-xs text-muted-foreground">
                    数据加载中...
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">--</div>
                  <p className="text-xs text-muted-foreground">
                    数据加载中...
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">系统角色</CardTitle>
                  <Shield className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">5</div>
                  <p className="text-xs text-muted-foreground">
                    包含系统预设角色
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">系统状态</CardTitle>
                  <Settings className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">正常</div>
                  <p className="text-xs text-muted-foreground">
                    所有服务运行正常
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* 快速操作 */}
            <Card>
              <CardHeader>
                <CardTitle>快速操作</CardTitle>
                <CardDescription>
                  常用的管理操作快捷入口
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card className="cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => setActiveTab('users')}>
                    <CardContent className="flex items-center space-x-4 pt-6">
                      <Users className="h-8 w-8 text-blue-600" />
                      <div>
                        <h3 className="font-semibold">用户管理</h3>
                        <p className="text-sm text-muted-foreground">管理系统用户</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="cursor-pointer hover:bg-muted/50 transition-colors"
                        onClick={() => setActiveTab('roles')}>
                    <CardContent className="flex items-center space-x-4 pt-6">
                      <Shield className="h-8 w-8 text-green-600" />
                      <div>
                        <h3 className="font-semibold">角色权限</h3>
                        <p className="text-sm text-muted-foreground">配置角色和权限</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>

            {/* 系统信息 */}
            <Card>
              <CardHeader>
                <CardTitle>系统信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">系统版本:</span>
                    <span className="ml-2 text-muted-foreground">ZHT System v1.0.0</span>
                  </div>
                  <div>
                    <span className="font-medium">数据库:</span>
                    <span className="ml-2 text-muted-foreground">PostgreSQL 16</span>
                  </div>
                  <div>
                    <span className="font-medium">后端框架:</span>
                    <span className="ml-2 text-muted-foreground">FastAPI + SQLAlchemy</span>
                  </div>
                  <div>
                    <span className="font-medium">前端框架:</span>
                    <span className="ml-2 text-muted-foreground">React 18 + TypeScript</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <UserManagement />
          </TabsContent>

          <TabsContent value="roles" className="space-y-6">
            <RoleManagement />
          </TabsContent>


        </Tabs>
      </div>
    </ProtectedRoute>
  )
}

export default AdminPage
