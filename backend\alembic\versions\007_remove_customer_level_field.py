"""Remove customer level field

Revision ID: 007_remove_customer_level_field
Revises: 006_add_sales_training_tables
Create Date: 2025-08-04 10:50:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '007_remove_customer_level_field'
down_revision = '006_add_sales_training_tables'
branch_labels = None
depends_on = None


def upgrade():
    """Remove level field from sales_training_customers table"""
    # Drop the index first
    op.drop_index('idx_customer_level', table_name='sales_training_customers')
    
    # Drop the level column
    op.drop_column('sales_training_customers', 'level')


def downgrade():
    """Add back level field to sales_training_customers table"""
    # Add the level column back
    op.add_column('sales_training_customers', 
                  sa.Column('level', sa.String(length=50), nullable=True, comment='客户级别'))
    
    # Recreate the index
    op.create_index('idx_customer_level', 'sales_training_customers', ['level'], unique=False)
