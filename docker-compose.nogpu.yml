# 🚀 智汇兔AI外贸收割机 + Morphik Core 统一部署配置
# 集成了 ZHT 系统和 Morphik-Core-Main 的完整服务栈



services:
  # ==================== ZHT 系统服务 ====================
  # ZHT PostgreSQL 数据库服务
  zht_db_0624:
    image: postgres:16
    container_name: zht_db_0624
    environment:
      POSTGRES_USER: zht_user
      POSTGRES_PASSWORD: zht_password
      POSTGRES_DB: zht_db
    ports:
      - "5433:5432"  # 避免与 Morphik 数据库端口冲突
    volumes:
      - zht_postgres_data_0624:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U zht_user -d zht_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - zht-network

  # ZHT 后端API服务
  zht_backend_0624:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: zht_backend_0624
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=${DATABASE_URL:-postgresql+asyncpg://zht_user:zht_password@zht_db_0624:5432/zht_db}
      - DEBUG=${DEBUG:-false}
      - DEV_MODE=${DEV_MODE:-false}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-production-super-secret-jwt-key-change-this-in-real-production}
    volumes:
      # 开发模式代码挂载
      - ./backend/app:/app/app:${BACKEND_VOLUME_MODE:-rw}
      - ./backend/alembic:/app/alembic:${BACKEND_VOLUME_MODE:-rw}
      - ./backend/alembic.ini:/app/alembic.ini:${BACKEND_VOLUME_MODE:-rw}
    depends_on:
      zht_db_0624:
        condition: service_healthy
    networks:
      - zht-network

  # ZHT 前端应用
  zht_frontend_0624:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - VITE_DEV_MODE=${VITE_DEV_MODE:-false}
        - VITE_API_BASE_URL=http://localhost:8001
        - VITE_MORPHIK_API_URL=http://localhost:8000
        - VITE_SMOLAGENTS_API_URL=http://localhost:8002
    container_name: zht_frontend_0624
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - VITE_DEV_MODE=${VITE_DEV_MODE:-false}
      - VITE_API_BASE_URL=http://localhost:8001
      - VITE_MORPHIK_API_URL=http://localhost:8000
      - VITE_SMOLAGENTS_API_URL=http://localhost:8002
    volumes:
      # 开发模式代码挂载
      - ./frontend/src:/app/src:${FRONTEND_VOLUME_MODE:-rw}
      - ./frontend/public:/app/public:${FRONTEND_VOLUME_MODE:-rw}
      - ./frontend/index.html:/app/index.html:${FRONTEND_VOLUME_MODE:-rw}
      - ./frontend/vite.config.ts:/app/vite.config.ts:${FRONTEND_VOLUME_MODE:-rw}
      - ./frontend/tailwind.config.js:/app/tailwind.config.js:${FRONTEND_VOLUME_MODE:-rw}
      - ./frontend/postcss.config.js:/app/postcss.config.js:${FRONTEND_VOLUME_MODE:-rw}
      - ./frontend/tsconfig.json:/app/tsconfig.json:${FRONTEND_VOLUME_MODE:-rw}
      - ./frontend/components.json:/app/components.json:${FRONTEND_VOLUME_MODE:-rw}
      # 排除 node_modules - 使用匿名卷避免宿主机代码覆盖容器依赖
      - /app/node_modules
    command: >
      sh -c "
      echo '🔍 环境变量检查: VITE_DEV_MODE=$$VITE_DEV_MODE';
      if [ \"$$VITE_DEV_MODE\" = \"true\" ]; then
        echo '🚀 前端开发模式启动';
        npm run dev;
      else
        echo '🔒 前端生产模式启动';
        npm run build && npm run preview;
      fi
      "
    networks:
      - zht-network
    depends_on:
      - zht_backend_0624
      - zht_morphik_0624
      - zht_smolagents_0624

  # ==================== SmoLAgents 微服务 ====================
  # SmoLAgents 智能代理服务
  zht_smolagents_0624:
    build:
      context: ./smolagents-service
      dockerfile: Dockerfile
    container_name: zht_smolagents_0624
    ports:
      - "8002:8002"
    environment:
      # 服务基本配置
      - SERVICE_NAME=smolagents-service
      - SERVICE_VERSION=1.0.0
      - SERVICE_HOST=0.0.0.0
      - SERVICE_PORT=8002
      - LOG_LEVEL=INFO
      - DEV_MODE=${DEV_MODE:-false}
      - SECRET_KEY=${JWT_SECRET_KEY:-production-super-secret-jwt-key-change-this-in-real-production}

      # 模型配置 - 支持ollama和qwen双模型切换
      - MODEL_PROVIDER=${SMOLAGENTS_MODEL_PROVIDER:-ollama}

      # Ollama配置
      - OLLAMA_BASE_URL=http://host.docker.internal:11434
      - OLLAMA_MODEL=qwen3-1.7b:latest
      # - OLLAMA_MODEL=qwen3-4B:latest
      # - OLLAMA_MODEL=Qwen3-8B-M:latest
      - OLLAMA_TIMEOUT=300

      # Qwen配置（备选）
      - QWEN_API_KEY=${QWEN_API_KEY:-}
      - QWEN_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
      - QWEN_MODEL_ID=qwen-max-2025-01-25
      - QWEN_TEMPERATURE=0.1
      - QWEN_MAX_TOKENS=4000

      # Redis配置
      - REDIS_HOST=zht_morphik_redis_0624
      - REDIS_PORT=6379
      - REDIS_DB=1
      - REDIS_PASSWORD=

      # 搜索配置
      - MAX_SEARCH_RESULTS=10
      - SEARCH_TIMEOUT=30
      - MAX_CONCURRENT_SEARCHES=5

      # 代理配置
      - HTTP_PROXY=${HTTP_PROXY:-}
      - HTTPS_PROXY=${HTTPS_PROXY:-}
      - NO_PROXY=localhost,127.0.0.1,zht_morphik_redis_0624
    volumes:
      # 日志和数据目录
      - ./smolagents-service/logs:/app/logs
      - ./smolagents-service/data:/app/data
      - ./smolagents-service/cache:/app/cache
      # 开发模式代码挂载
      - ./smolagents-service/app:/app/app:${SMOLAGENTS_VOLUME_MODE:-rw}
    extra_hosts:
      # 允许容器访问宿主机的Ollama服务
      - "host.docker.internal:host-gateway"
    depends_on:
      zht_morphik_redis_0624:
        condition: service_healthy
    networks:
      - zht-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/api/v1/health"]
      interval: 30s
      timeout: 10s
      start_period: 60s
      retries: 3

  # ==================== Morphik Core 系统服务 ====================
  # Morphik 核心 API 服务
  zht_morphik_0624:
    build:
      context: ./morphik-core-main
      dockerfile: dockerfile
    container_name: zht_morphik_0624
    ports:
      - "8000:8000"
    # GPU 支持已禁用 (macOS)
    environment:
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-here}
      - POSTGRES_URI=postgresql+asyncpg://morphik:morphik@zht_morphik_postgres_0624:5432/morphik
      - PGPASSWORD=morphik
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=DEBUG
      - REDIS_HOST=zht_morphik_redis_0624
      - REDIS_PORT=6379
      - HF_ENDPOINT=https://hf-mirror.com  # 国内 HuggingFace 镜像
      # GPU 环境变量
      # - NVIDIA_VISIBLE_DEVICES=all
      # - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      # - CUDA_VISIBLE_DEVICES=0
      # GPU 内存优化 - 主服务分配50%内存 (约4GB)，为ColPali预留空间
      # - PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
      # - CUDA_MEMORY_FRACTION=0.5
      # 代理配置（可选，用于加速 NLTK 等下载）
      - HTTP_PROXY=${HTTP_PROXY:-}
      - HTTPS_PROXY=${HTTPS_PROXY:-}
      - NO_PROXY=localhost,127.0.0.1,zht_morphik_postgres_0624,zht_morphik_redis_0624
      # 开发模式配置
      - MORPHIK_DEV_MODE=${MORPHIK_DEV_MODE:-false}
      - MORPHIK_RELOAD=${MORPHIK_RELOAD:-false}
      - MORPHIK_DEBUG_MODE=${MORPHIK_DEBUG_MODE:-false}
      - MORPHIK_DEBUG_PORT=${MORPHIK_DEBUG_PORT:-5678}
      - MORPHIK_DEBUG_WAIT=${MORPHIK_DEBUG_WAIT:-false}
    volumes:
      # 原有挂载（保持不变）
      - ./morphik-core-main/storage:/app/storage
      - ./morphik-core-main/logs:/app/logs
      - ./morphik-core-main/morphik.toml:/app/morphik.toml
      - morphik_huggingface_cache:/root/.cache/huggingface
      # 开发模式代码挂载
      - ./morphik-core-main/core:/app/core:${MORPHIK_VOLUME_MODE:-ro}
      - ./morphik-core-main/ee:/app/ee:${MORPHIK_VOLUME_MODE:-ro}
      - ./morphik-core-main/utils:/app/utils:${MORPHIK_VOLUME_MODE:-ro}
      - ./morphik-core-main/start_server.py:/app/start_server.py:${MORPHIK_VOLUME_MODE:-ro}
      - ./morphik-core-main/pyproject.toml:/app/pyproject.toml:${MORPHIK_VOLUME_MODE:-ro}
      - ./morphik-core-main/__init__.py:/app/__init__.py:${MORPHIK_VOLUME_MODE:-ro}
    # 开发模式启动命令 - 绕过 start_server.py 的 Docker 依赖
    command: >
      sh -c "
      if [ \"$$MORPHIK_DEV_MODE\" = \"true\" ]; then
        echo '🔧 启动开发模式 (热重载已启用)';
        if [ \"$$MORPHIK_DEBUG_MODE\" = \"true\" ]; then
          echo '🐛 启动调试模式 (端口: $$MORPHIK_DEBUG_PORT)';
          if [ \"$$MORPHIK_DEBUG_WAIT\" = \"true\" ]; then
            echo '⏳ 等待调试器连接...';
            python -m debugpy --listen 0.0.0.0:$$MORPHIK_DEBUG_PORT --wait-for-client -m uvicorn core.api:app --host 0.0.0.0 --port 8000 --log-level debug;
          else
            python -m debugpy --listen 0.0.0.0:$$MORPHIK_DEBUG_PORT -m uvicorn core.api:app --host 0.0.0.0 --port 8000 --log-level debug;
          fi
        else
          if [ \"$$MORPHIK_RELOAD\" = \"true\" ]; then
            echo '🔄 使用uvicorn热重载';
            uvicorn core.api:app --host 0.0.0.0 --port 8000 --log-level debug --reload;
          else
            uvicorn core.api:app --host 0.0.0.0 --port 8000 --log-level debug;
          fi
        fi
      else
        echo '🏭 启动生产模式';
        uvicorn core.api:app --host 0.0.0.0 --port 8000 --log-level info;
      fi
      "
    depends_on:
      zht_morphik_postgres_0624:
        condition: service_healthy
      zht_morphik_redis_0624:
        condition: service_healthy
    networks:
      - zht-network
    env_file:
      - ./morphik-core-main/.env

  # Morphik 工作队列服务
  zht_morphik_worker_0624:
    build:
      context: ./morphik-core-main
      dockerfile: dockerfile
    container_name: zht_morphik_worker_0624
    # GPU 支持已禁用 (macOS)
    environment:
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-secret-key-here}
      - POSTGRES_URI=postgresql+asyncpg://morphik:morphik@zht_morphik_postgres_0624:5432/morphik
      - PGPASSWORD=morphik
      - LOG_LEVEL=DEBUG
      - REDIS_HOST=zht_morphik_redis_0624
      - REDIS_PORT=6379
      - HF_ENDPOINT=https://hf-mirror.com  # 国内 HuggingFace 镜像
      # GPU 环境变量
      # - NVIDIA_VISIBLE_DEVICES=all
      # - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      # - CUDA_VISIBLE_DEVICES=0
      # GPU 内存优化 - Worker分配50%内存 (约4GB)，用于ColPali
      # - PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
      # - CUDA_MEMORY_FRACTION=0.5
      # 开发模式配置
      - MORPHIK_DEV_MODE=${MORPHIK_DEV_MODE:-false}
      - MORPHIK_WORKER_RELOAD=${MORPHIK_WORKER_RELOAD:-false}
      # ColPali 配置（暂时禁用，内存不足）
      - COLPALI_MODE=off
    volumes:
      # 原有挂载（保持不变）
      - ./morphik-core-main/storage:/app/storage
      - ./morphik-core-main/logs:/app/logs
      - ./morphik-core-main/morphik.toml:/app/morphik.toml
      - morphik_huggingface_cache:/root/.cache/huggingface
      # 开发模式代码挂载
      - ./morphik-core-main/core:/app/core:${MORPHIK_VOLUME_MODE:-ro}
      - ./morphik-core-main/ee:/app/ee:${MORPHIK_VOLUME_MODE:-ro}
      - ./morphik-core-main/utils:/app/utils:${MORPHIK_VOLUME_MODE:-ro}
      - ./morphik-core-main/pyproject.toml:/app/pyproject.toml:${MORPHIK_VOLUME_MODE:-ro}
      - ./morphik-core-main/__init__.py:/app/__init__.py:${MORPHIK_VOLUME_MODE:-ro}
    # 开发模式启动命令
    command: >
      sh -c "
      if [ \"$$MORPHIK_DEV_MODE\" = \"true\" ]; then
        echo '🔧 启动Worker开发模式';
        if [ \"$$MORPHIK_WORKER_RELOAD\" = \"true\" ]; then
          echo '🔄 Worker热重载已启用';
          if command -v watchmedo >/dev/null 2>&1; then
            watchmedo auto-restart --directory=/app/core --pattern='*.py' --recursive -- arq core.workers.ingestion_worker.WorkerSettings;
          else
            echo '⚠️ watchmedo未安装，使用标准模式';
            arq core.workers.ingestion_worker.WorkerSettings;
          fi
        else
          echo '📋 Worker标准模式';
          arq core.workers.ingestion_worker.WorkerSettings;
        fi
      else
        echo '🏭 启动Worker生产模式';
        arq core.workers.ingestion_worker.WorkerSettings;
      fi
      "
    depends_on:
      zht_morphik_postgres_0624:
        condition: service_healthy
      zht_morphik_redis_0624:
        condition: service_healthy
    networks:
      - zht-network
    env_file:
      - ./morphik-core-main/.env

  # Morphik Redis 缓存服务
  zht_morphik_redis_0624:
    image: redis:7-alpine
    container_name: zht_morphik_redis_0624
    ports:
      - "6379:6379"
    volumes:
      - morphik_redis_data_0624:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - zht-network

  # Morphik PostgreSQL 数据库服务 (带 pgvector 扩展)
  zht_morphik_postgres_0624:
    build:
      context: ./morphik-core-main
      dockerfile: postgres.dockerfile
    container_name: zht_morphik_postgres_0624
    shm_size: 128mb
    environment:
      - POSTGRES_USER=morphik
      - POSTGRES_PASSWORD=morphik
      - POSTGRES_DB=morphik
      - PGDATA=/var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - morphik_postgres_data_0624:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U morphik -d morphik"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    networks:
      - zht-network



# ==================== 网络配置 ====================
networks:
  zht-network:
    driver: bridge
    name: zht_network

# ==================== 存储卷配置 ====================
volumes:
  # ZHT 系统存储卷
  zht_postgres_data_0624:
    name: zht_postgres_data_0624

  # Morphik 系统存储卷
  morphik_postgres_data_0624:
    name: zht_morphik_postgres_data_0624
  morphik_huggingface_cache: #使用之前项目构建过的缓存，不修改名字
    name: zht_morphik_huggingface_cache
  morphik_redis_data_0624:
    name: zht_morphik_redis_data_0624