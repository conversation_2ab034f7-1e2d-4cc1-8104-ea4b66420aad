# SmoLAgents 微服务依赖包
# 基于官方 SmoLAgents 库构建的智能代理服务

# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# SmoLAgents 官方库及相关依赖
smolagents[litellm]>=1.19.0
transformers>=4.40.0
torch>=2.0.0
huggingface-hub>=0.20.0

# 网络搜索工具依赖
requests>=2.31.0
beautifulsoup4>=4.12.0
duckduckgo-search>=3.9.0
markdownify>=0.11.6

# Redis 客户端
redis>=5.0.0

# 异步HTTP客户端
httpx>=0.25.0
aiohttp>=3.9.0

# 日志和监控
loguru>=0.7.0
prometheus-client>=0.19.0

# 工具库
python-multipart>=0.0.6
python-dotenv>=1.0.0
tenacity>=8.2.0

# 开发和测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0

# 安全
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# 数据处理
pandas>=2.1.0
numpy>=1.24.0

# 文档生成
mkdocs>=1.5.0
mkdocs-material>=9.4.0
