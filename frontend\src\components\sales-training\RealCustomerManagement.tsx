/**
 * 真实客户管理组件 - 使用后端API
 * 解决数据持久化问题，确保刷新后数据不丢失
 */

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useSalesTrainingData, useInitializeSalesTrainingData } from '@/store/salesTraining'
import { customerAPI } from '@/services/salesTrainingService'
import type { Customer, CustomerCreate, CustomerUpdate } from '@/types/salesTraining'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  RefreshCw,
  Building2,
  Calendar,

  Grid,
  List,
  Users,
  Target,
  Shuffle,
  Play,
  Settings,
  X
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'

import { SettingsDialog } from './SettingsDialog'






// 表单数据类型 - 简化为与原始组件一致
interface FormData {
  name: string
  countryId: number
  productId: number
  company: string
  trainingScenario: string
}

// 视图模式类型
type ViewMode = 'grid' | 'list'

// 组件属性接口
export interface RealCustomerManagementProps {
  onStartTraining?: (customer: Customer) => void
}

export default function RealCustomerManagement({ onStartTraining }: RealCustomerManagementProps = {}) {
  // 状态管理
  const [customers, setCustomers] = useState<Customer[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showDialog, setShowDialog] = useState(false)
  const [showSettingsDialog, setShowSettingsDialog] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null)
  const [errorMessage, setErrorMessage] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState('')

  // 显示错误信息的函数
  const showError = useCallback((message: string) => {
    setErrorMessage(message)
  }, [])
  const [viewMode, setViewMode] = useState<ViewMode>('grid')


  // 表单数据
  const [formData, setFormData] = useState<FormData>({
    name: '',
    countryId: 0,
    productId: 0,
    company: '',
    trainingScenario: ''
  })

  // 获取选项数据
  const { countries, products, scenarios } = useSalesTrainingData()

  // 初始化销售训练数据
  useInitializeSalesTrainingData()

  // 加载客户数据
  const loadCustomers = useCallback(async () => {
    try {
      setIsLoading(true)
      const data = await customerAPI.getAll(false) // 只获取活跃客户
      setCustomers(data)
    } catch (error) {
      console.error('加载客户数据失败:', error)
      // 这里不显示错误，因为这是初始加载
    } finally {
      setIsLoading(false)
    }
  }, [])

  // 初始化加载数据
  useEffect(() => {
    loadCustomers()
  }, [loadCustomers])

  // 表单验证
  const validateForm = useCallback(() => {
    const errors: string[] = []
    if (!formData.name.trim()) errors.push('客户姓名不能为空')
    if (!formData.countryId) errors.push('请选择国家')
    if (!formData.productId) errors.push('请选择产品')
    if (formData.name.length < 2) errors.push('客户姓名至少需要2个字符')
    return errors
  }, [formData])

  // 提交表单
  const handleSubmit = useCallback(async () => {
    const errors = validateForm()
    if (errors.length > 0) {
      showError(errors.join(', '))
      return
    }

    setIsLoading(true)

    try {
      // 获取选择的国家和产品信息
      const country = countries.find(c => c.id === formData.countryId)
      const product = products.find(p => p.id === formData.productId)

      if (editingCustomer) {
        // 更新客户
        const updateData: CustomerUpdate = {
          name: formData.name.trim(),
          company: formData.company.trim() || undefined,
          country_code: country?.code || undefined,
          training_scenario: formData.trainingScenario || undefined,
          notes: `产品兴趣: ${product?.name || '未指定'}`
        }

        const updatedCustomer = await customerAPI.update(editingCustomer.id, updateData)
        setCustomers(prev => prev.map(c => c.id === editingCustomer.id ? updatedCustomer : c))
      } else {
        // 创建新客户
        const createData: CustomerCreate = {
          name: formData.name.trim(),
          company: formData.company.trim() || `${country?.name || ''}科技公司`,
          country_code: country?.code || undefined,
          training_scenario: formData.trainingScenario || undefined,
          notes: `产品兴趣: ${product?.name || '未指定'}`
        }

        const newCustomer = await customerAPI.create(createData)
        setCustomers(prev => [...prev, newCustomer])
      }

      // 重置表单
      setShowDialog(false)
      setEditingCustomer(null)
      setFormData({
        name: '',
        countryId: 0,
        productId: 0,
        company: '',
        trainingScenario: ''
      })
    } catch (error: any) {
      console.error('保存客户失败:', error)

      // 解析错误信息
      let errorMessage = editingCustomer ? '更新客户失败' : '添加客户失败'
      let errorDescription = '请稍后重试'

      // 处理 SalesTrainingError
      if (error?.message) {
        errorDescription = error.message
      } else if (error?.detail) {
        errorDescription = error.detail
      } else if (typeof error === 'string') {
        errorDescription = error
      }

      // 显示错误提示
      showError(errorMessage + (errorDescription ? ': ' + errorDescription : ''))
    } finally {
      setIsLoading(false)
    }
  }, [formData, validateForm, editingCustomer])

  // 编辑客户
  const handleEdit = useCallback((customer: Customer) => {
    setEditingCustomer(customer)
    setErrorMessage('') // 清除错误信息
    // 根据客户数据反向查找对应的ID
    const country = countries.find(c => c.code === customer.country_code)

    // 从notes中提取产品信息
    let productId = 0
    if (customer.notes && customer.notes.includes('产品兴趣:')) {
      const productName = customer.notes.split('产品兴趣:')[1]?.trim()
      if (productName && productName !== '未指定') {
        const product = products.find(p => p.name === productName)
        if (product) {
          productId = product.id
        }
      }
    }

    setFormData({
      name: customer.name,
      countryId: country?.id || 0,
      productId: productId,
      company: customer.company || '',
      trainingScenario: customer.training_scenario || ''
    })
    setShowDialog(true)
  }, [countries, products])

  // 显示删除确认对话框
  const handleDeleteClick = useCallback((customerId: number) => {
    const customer = customers.find(c => c.id === customerId)
    if (!customer) return

    setCustomerToDelete(customer)
    setShowDeleteDialog(true)
  }, [customers])

  // 确认删除客户
  const handleConfirmDelete = useCallback(async () => {
    if (!customerToDelete) return

    try {
      setIsLoading(true)
      await customerAPI.delete(customerToDelete.id)

      // 重新加载客户列表以确保数据同步
      await loadCustomers()

      setShowDeleteDialog(false)
      setCustomerToDelete(null)
    } catch (error) {
      console.error('删除客户失败:', error)
      showError('删除失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }, [customerToDelete, loadCustomers])

  // 取消删除
  const handleCancelDelete = useCallback(() => {
    setShowDeleteDialog(false)
    setCustomerToDelete(null)
  }, [])

  // 随机生成客户
  const generateRandomCustomer = useCallback(() => {
    const randomNames = [
      'Alex Thompson', 'Emma Wilson', 'Michael Brown', 'Sophie Davis',
      'James Miller', 'Lisa Anderson', 'Robert Taylor', 'Anna Martinez',
      'David Garcia', 'Jennifer Lee', 'Christopher White', 'Michelle Clark'
    ]

    const randomName = randomNames[Math.floor(Math.random() * randomNames.length)]
    const randomCountry = countries[Math.floor(Math.random() * countries.length)]
    const randomProduct = products[Math.floor(Math.random() * products.length)]

    // 从实际可用的场景中随机选择
    let randomScenario = ''
    if (scenarios.length > 0) {
      const randomScenarioObj = scenarios[Math.floor(Math.random() * scenarios.length)]
      randomScenario = randomScenarioObj.name
    }

    setFormData({
      name: randomName,
      countryId: randomCountry?.id || 0,
      productId: randomProduct?.id || 0,
      company: `${randomCountry?.name || ''} ${randomProduct?.category || ''} Corp`,
      trainingScenario: randomScenario
    })
  }, [countries, products, scenarios])

  // 筛选客户列表
  const filteredCustomers = useMemo(() => {
    return customers.filter(customer => {
      const matchesSearch = !searchTerm ||
        customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (customer.company && customer.company.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase()))

      return matchesSearch
    })
  }, [customers, searchTerm])

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">客户管理</h2>
          <p className="text-gray-600 mt-1">管理销冠客户，并能够实战训练</p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="outline" className="text-blue-600 border-blue-200">
            总客户: {customers.length}
          </Badge>
          {/* <Badge variant="outline" className="text-green-600 border-green-200">
            已筛选: {filteredCustomers.length}
          </Badge> */}
        </div>
      </div>

      {/* 工具栏 */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center space-x-4">
          {/* 搜索框 */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="搜索客户姓名"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* 筛选按钮 */}


          {/* 刷新按钮 */}
          <Button
            variant="outline"
            onClick={loadCustomers}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>

        <div className="flex items-center space-x-3">
          {/* 视图切换 */}
          <div className="flex border rounded-lg">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>

          {/* 设置按钮 */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSettingsDialog(true)}
          >
            <Settings className="w-4 h-4" />
          </Button>

          {/* 新增客户按钮 */}
          <Dialog open={showDialog} onOpenChange={setShowDialog}>
            <DialogTrigger asChild>
              <Button
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-300"
                onClick={() => {
                  setEditingCustomer(null)
                  setErrorMessage('') // 清除错误信息
                  setFormData({
                    name: '',
                    countryId: 0,
                    productId: 0,
                    company: '',
                    trainingScenario: ''
                  })
                }}
              >
                <Plus className="w-4 h-4 mr-2" />
                新增客户
              </Button>
            </DialogTrigger>
          </Dialog>
        </div>
      </div>



      {/* 客户列表 */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
          <span className="ml-2 text-gray-600">加载中...</span>
        </div>
      ) : filteredCustomers.length === 0 ? (
        <Card className="border-dashed border-2 border-gray-200">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Users className="w-12 h-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无客户数据</h3>
            <p className="text-gray-500 text-center mb-4">
              {searchTerm
                ? '没有找到符合条件的客户，请调整搜索条件'
                : '开始添加您的第一个客户'}
            </p>
            <Button onClick={() => setShowDialog(true)}>
              <Plus className="w-4 h-4 mr-2" />
              新增客户
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className={viewMode === 'grid'
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
          : 'space-y-3'
        }>
          {filteredCustomers.map((customer) => (
            <CustomerCard
              key={customer.id}
              customer={customer}
              viewMode={viewMode}
              onEdit={handleEdit}
              onDelete={handleDeleteClick}
              onStartTraining={onStartTraining}
            />
          ))}
        </div>
      )}

      {/* 新增/编辑客户对话框 */}
      <Dialog
        open={showDialog}
        onOpenChange={(open) => {
          setShowDialog(open)
          if (!open) {
            setErrorMessage('') // 关闭对话框时清除错误信息
          }
        }}
        modal={true}
      >
        <DialogContent
          className="max-w-2xl max-h-[90vh] overflow-y-auto"
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle>
              {editingCustomer ? '编辑客户' : '新增客户'}
            </DialogTitle>
          </DialogHeader>

          {/* 错误信息显示区域 */}
          {errorMessage && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3 mx-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-2">
                  <div className="w-4 h-4 text-red-600 mt-0.5">⚠️</div>
                  <div className="text-sm text-red-800">
                    <p className="font-medium">错误：</p>
                    <p>{errorMessage}</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setErrorMessage('')}
                  className="h-6 w-6 p-0 text-red-600 hover:text-red-800 hover:bg-red-100"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          <div className="space-y-6 py-4">
            {/* 基本信息分组 */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2 pb-2 border-b border-gray-100 dark:border-gray-700">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300">基本信息</h4>
              </div>

              {/* 客户姓名 */}
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">客户姓名 *</Label>
                <div className="flex space-x-2">
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="输入客户姓名"
                    className="flex-1 transition-all duration-200 focus:scale-[1.01] focus:shadow-md"
                    required
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={generateRandomCustomer}
                    className="px-3 transition-all duration-200 hover:scale-105"
                    type="button"
                  >
                    <Shuffle className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* 国家和产品并排 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* 国家选择 */}
                <div className="space-y-2">
                  <Label htmlFor="country" className="text-sm font-medium text-gray-700 dark:text-gray-300">国家 *</Label>
                  <Select
                    value={formData.countryId ? formData.countryId.toString() : ''}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, countryId: parseInt(value) || 0 }))}
                  >
                    <SelectTrigger className="transition-all duration-200 focus:scale-[1.01] focus:shadow-md">
                      <SelectValue placeholder="选择国家" />
                    </SelectTrigger>
                    <SelectContent>
                      {countries.map(country => (
                        <SelectItem key={country.id} value={country.id.toString()}>
                          <div className="flex items-center space-x-3">
                            <span className="text-lg">{country.flag}</span>
                            <span className="font-medium">{country.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* 产品选择 */}
                <div className="space-y-2">
                  <Label htmlFor="product" className="text-sm font-medium text-gray-700 dark:text-gray-300">产品 *</Label>
                  <Select
                    value={formData.productId ? formData.productId.toString() : ''}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, productId: parseInt(value) || 0 }))}
                  >
                    <SelectTrigger className="transition-all duration-200 focus:scale-[1.01] focus:shadow-md">
                      <SelectValue placeholder="选择产品" />
                    </SelectTrigger>
                    <SelectContent>
                      {products.map(product => (
                        <SelectItem key={product.id} value={product.id.toString()}>
                          <div className="space-y-1">
                            <div className="font-medium">{product.name}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">{product.category}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 公司名称 */}
              <div className="space-y-2">
                <Label htmlFor="company" className="text-sm font-medium text-gray-700 dark:text-gray-300">公司名称</Label>
                <Input
                  id="company"
                  value={formData.company}
                  onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                  placeholder="输入公司名称"
                  className="transition-all duration-200 focus:scale-[1.01] focus:shadow-md"
                />
              </div>
            </div>


            {/* 训练设置分组 */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2 pb-2 border-b border-gray-100 dark:border-gray-700">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300">训练设置</h4>
              </div>

              {/* 训练场景 */}
              <div className="space-y-2">
                <Label htmlFor="trainingScenario" className="text-sm font-medium text-gray-700 dark:text-gray-300">训练场景</Label>
                <Select
                  value={formData.trainingScenario}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, trainingScenario: value }))}
                >
                  <SelectTrigger className="transition-all duration-200 focus:scale-[1.01] focus:shadow-md">
                    <SelectValue placeholder="选择训练场景" />
                  </SelectTrigger>
                  <SelectContent className="max-h-60">
                    {scenarios.length === 0 ? (
                      <SelectItem value="" disabled>
                        <div className="text-gray-500 dark:text-gray-400 text-sm">
                          暂无可用的训练场景
                        </div>
                      </SelectItem>
                    ) : (
                      scenarios.map(scenario => (
                        <SelectItem key={scenario.id} value={scenario.name} className="py-3">
                          <div className="flex items-center space-x-3 w-full">
                            <div className="flex-shrink-0">
                              <span className="text-xl">{scenario.icon || '🎯'}</span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
                                {scenario.name}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                                {scenario.category}
                              </div>
                            </div>
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-3 pt-6 border-t border-gray-100 dark:border-gray-700">
              <Button
                onClick={handleSubmit}
                disabled={!formData.name.trim() || !formData.countryId || !formData.productId || isLoading}
                className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    处理中...
                  </>
                ) : (
                  <>
                    {editingCustomer ? <Edit className="w-4 h-4 mr-2" /> : <Plus className="w-4 h-4 mr-2" />}
                    {editingCustomer ? '更新客户' : '添加客户'}
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowDialog(false)}
                disabled={isLoading}
                className="flex-1 transition-all duration-200 hover:scale-105"
              >
                取消
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={(open) => open ? null : handleCancelDelete()} modal={true}>
        <DialogContent
          className="max-w-md"
          onPointerDownOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Trash2 className="w-5 h-5 text-red-500" />
              <span>确认删除客户</span>
            </DialogTitle>
          </DialogHeader>

          <div className="py-4">
            <p className="text-gray-600 mb-4">
              您确定要删除客户 <span className="font-medium text-gray-900">"{customerToDelete?.name}"</span> 吗？
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <div className="w-4 h-4 text-yellow-600 mt-0.5">⚠️</div>
                <div className="text-sm text-yellow-800">
                  <p className="font-medium">注意：</p>
                  <p>删除后的客户数据将无法恢复，但不会影响已有的训练记录。</p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleCancelDelete}
              disabled={isLoading}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              onClick={handleConfirmDelete}
              disabled={isLoading}
              className="flex-1 bg-red-500 hover:bg-red-600 text-white"
            >
              {isLoading ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  删除中...
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4 mr-2" />
                  确认删除
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* 设置对话框 */}
      <SettingsDialog
        open={showSettingsDialog}
        onOpenChange={setShowSettingsDialog}
      />
    </div>
  )
}

// 客户卡片组件
interface CustomerCardProps {
  customer: Customer
  viewMode: ViewMode
  onEdit: (customer: Customer) => void
  onDelete: (customerId: number) => void
  onStartTraining?: (customer: Customer) => void
}

function CustomerCard({ customer, viewMode, onEdit, onDelete, onStartTraining }: CustomerCardProps) {
  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
  }



  if (viewMode === 'list') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="w-10 h-10">
                <AvatarFallback className="bg-blue-100 text-blue-600">
                  {getInitials(customer.name)}
                </AvatarFallback>
              </Avatar>

              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h3 className="font-medium text-gray-900">{customer.name}</h3>
                </div>
                <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                  {customer.company && (
                    <div className="flex items-center">
                      <Building2 className="w-4 h-4 mr-1" />
                      {customer.company}
                    </div>
                  )}
                  {customer.industry && (
                    <div className="flex items-center">
                      <Target className="w-4 h-4 mr-1" />
                      {customer.industry}
                    </div>
                  )}
                  {customer.region && (
                    <div className="flex items-center">
                      <Target className="w-4 h-4 mr-1" />
                      {customer.region}
                    </div>
                  )}
                  {customer.training_scenario && (
                    <div className="flex items-center">
                      <Target className="w-4 h-4 mr-1" />
                      {customer.training_scenario}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {onStartTraining && (
                <Button
                  size="sm"
                  onClick={(e) => { e.stopPropagation(); onStartTraining(customer) }}
                  className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white"
                >
                  <Play className="w-3 h-3 mr-1" />
                  开始训练
                </Button>
              )}

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(customer)}
                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
              >
                <Edit className="w-4 h-4 mr-1" />
                编辑
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(customer.id)}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="w-4 h-4 mr-1" />
                删除
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="hover:shadow-lg transition-all duration-300 cursor-pointer group">
      <CardContent className="p-6">
        {/* 头部区域 - 头像和姓名 */}
        <div className="flex flex-col items-center text-center mb-4">
          <Avatar className="w-12 h-12 mb-3">
            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white">
              {getInitials(customer.name)}
            </AvatarFallback>
          </Avatar>
          <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
            {customer.name}
          </h3>
        </div>

        {/* 客户信息区域 */}
        <div className="space-y-2 mb-4">
          {customer.company && (
            <div className="flex items-center text-sm text-gray-600">
              <Building2 className="w-4 h-4 mr-2 text-gray-400" />
              {customer.company}
            </div>
          )}

          {customer.industry && (
            <div className="flex items-center text-sm text-gray-600">
              <Target className="w-4 h-4 mr-2 text-gray-400" />
              {customer.industry}
            </div>
          )}

          {customer.region && (
            <div className="flex items-center text-sm text-gray-600">
              <Target className="w-4 h-4 mr-2 text-gray-400" />
              {customer.region}
            </div>
          )}

          {customer.training_scenario && (
            <div className="flex items-center text-sm text-gray-600">
              <Target className="w-4 h-4 mr-2 text-gray-400" />
              {customer.training_scenario}
            </div>
          )}

          {customer.created_at && (
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="w-4 h-4 mr-2 text-gray-400" />
              {new Date(customer.created_at).toLocaleDateString()}
            </div>
          )}
        </div>

        {/* 操作按钮区域 - 居中布局 */}
        <div className="flex flex-col items-center space-y-2">
          {onStartTraining && (
            <Button
              size="sm"
              onClick={(e) => { e.stopPropagation(); onStartTraining(customer) }}
              className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white w-full"
            >
              <Play className="w-3 h-3 mr-1" />
              开始训练
            </Button>
          )}
          <div className="flex items-center space-x-2 w-full">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit(customer)}
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 flex-1"
            >
              <Edit className="w-4 h-4 mr-1" />
              编辑
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(customer.id)}
              className="text-red-600 hover:text-red-700 hover:bg-red-50 flex-1"
            >
              <Trash2 className="w-4 h-4 mr-1" />
              删除
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}