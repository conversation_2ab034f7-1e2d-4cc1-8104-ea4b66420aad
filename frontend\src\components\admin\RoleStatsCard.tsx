/**
 * 角色统计卡片组件
 */
import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Shield, Users, Key, Activity } from 'lucide-react'

interface Permission {
  id: number
  name: string
  description: string
  category: string
}

interface Role {
  id: number
  name: string
  description: string
  is_active: boolean
  created_at: string
  permissions: Permission[]
}

interface RoleStatsCardProps {
  roles: Role[]
  permissions: Permission[]
}

export const RoleStatsCard: React.FC<RoleStatsCardProps> = ({
  roles,
  permissions,
}) => {
  // 计算统计数据
  const totalRoles = roles.length
  const activeRoles = roles.filter(role => role.is_active).length
  const inactiveRoles = totalRoles - activeRoles
  const systemRoles = roles.filter(role => ['superadmin', 'admin', 'user'].includes(role.name)).length
  const customRoles = totalRoles - systemRoles
  const totalPermissions = permissions.length
  
  // 按分类统计权限
  const permissionsByCategory = permissions.reduce((acc, permission) => {
    const category = permission.category || '其他'
    acc[category] = (acc[category] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // 计算平均每个角色的权限数量
  const avgPermissionsPerRole = totalRoles > 0 
    ? Math.round(roles.reduce((sum, role) => sum + role.permissions.length, 0) / totalRoles)
    : 0

  const stats = [
    {
      title: '总角色数',
      value: totalRoles,
      description: `${activeRoles} 个激活，${inactiveRoles} 个禁用`,
      icon: Shield,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: '系统角色',
      value: systemRoles,
      description: `${customRoles} 个自定义角色`,
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: '总权限数',
      value: totalPermissions,
      description: `${Object.keys(permissionsByCategory).length} 个分类`,
      icon: Key,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: '平均权限',
      value: avgPermissionsPerRole,
      description: '每个角色平均权限数',
      icon: Activity,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${stat.bgColor}`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        )
      })}
      
      {/* 权限分类详情 */}
      {Object.keys(permissionsByCategory).length > 0 && (
        <Card className="md:col-span-2 lg:col-span-4">
          <CardHeader>
            <CardTitle className="text-lg">权限分类分布</CardTitle>
            <CardDescription>
              各分类下的权限数量统计
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {Object.entries(permissionsByCategory)
                .sort(([,a], [,b]) => b - a)
                .map(([category, count]) => (
                  <Badge key={category} variant="outline" className="text-sm">
                    {category}: {count}
                  </Badge>
                ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
