import { useState, useEffect } from 'react'
import { Building, Plane, Home, TreePine, MapPin } from 'lucide-react'
import type { SelectorOption } from '@/components/ProductSelector'

// 默认场景选项 - 使用固定时间戳避免重新渲染问题
const FIXED_TIMESTAMP = '2024-01-01T00:00:00.000Z'

const DEFAULT_SCENARIOS: SelectorOption[] = [
  {
    id: 'scenario_1',
    value: 'office',
    label: '办公',
    icon: Building,
    iconName: 'Building',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'scenario_2',
    value: 'business-travel',
    label: '商旅',
    icon: Plane,
    iconName: 'Plane',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'scenario_3',
    value: 'home',
    label: '居家',
    icon: Home,
    iconName: 'Home',
    createdAt: FIXED_TIMESTAMP
  },
  {
    id: 'scenario_4',
    value: 'outdoor',
    label: '户外',
    icon: TreePine,
    iconName: 'TreePine',
    createdAt: FIXED_TIMESTAMP
  }
]

const STORAGE_KEY = 'group-analysis-scenario-options'

// 图标映射函数
const getIconByName = (iconName: string) => {
  switch (iconName) {
    case 'Building': return Building
    case 'Plane': return Plane
    case 'Home': return Home
    case 'TreePine': return TreePine
    case 'MapPin': return MapPin
    default: return MapPin
  }
}

export interface UseScenarioOptionsReturn {
  scenarioOptions: SelectorOption[]
  addScenarioOption: (label: string) => Promise<{ success: boolean; error?: string; value?: string }>
  removeScenarioOption: (scenarioId: string) => Promise<{ success: boolean; error?: string }>
  isLoading: boolean
}

export const useScenarioOptions = (): UseScenarioOptionsReturn => {
  const [scenarioOptions, setScenarioOptions] = useState<SelectorOption[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // 初始化加载选项
  useEffect(() => {
    const loadOptions = () => {
      try {
        const stored = localStorage.getItem(STORAGE_KEY)
        if (stored) {
          const parsedOptions = JSON.parse(stored)
          // 验证数据结构并恢复图标引用
          if (Array.isArray(parsedOptions) && parsedOptions.length > 0) {
            const optionsWithIcons = parsedOptions.map((option: any) => ({
              ...option,
              icon: getIconByName(option.iconName)
            }))
            setScenarioOptions(optionsWithIcons)
          } else {
            // 如果存储的数据无效，使用默认选项
            setScenarioOptions(DEFAULT_SCENARIOS)
            localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_SCENARIOS))
          }
        } else {
          // 首次使用，设置默认选项
          setScenarioOptions(DEFAULT_SCENARIOS)
          localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_SCENARIOS))
        }
      } catch (error) {
        console.error('加载场景选项失败:', error)
        // 出错时使用默认选项
        setScenarioOptions(DEFAULT_SCENARIOS)
        localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_SCENARIOS))
      } finally {
        setIsLoading(false)
      }
    }

    loadOptions()
  }, [])

  // 保存选项到localStorage
  const saveOptions = (options: SelectorOption[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(options))
      return true
    } catch (error) {
      console.error('保存场景选项失败:', error)
      return false
    }
  }

  // 添加新的场景选项
  const addScenarioOption = async (label: string): Promise<{ success: boolean; error?: string; value?: string }> => {
    try {
      // 检查是否已存在相同标签
      const existingOption = scenarioOptions.find(option => 
        option.label.toLowerCase() === label.toLowerCase()
      )
      
      if (existingOption) {
        return { success: false, error: '该场景选项已存在' }
      }

      // 生成新选项
      const newOption: SelectorOption = {
        id: `scenario_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        value: label.toLowerCase().replace(/\s+/g, '-'),
        label: label,
        icon: MapPin, // 新添加的使用MapPin图标
        iconName: 'MapPin',
        createdAt: new Date().toISOString()
      }

      const updatedOptions = [...scenarioOptions, newOption]
      
      if (saveOptions(updatedOptions)) {
        setScenarioOptions(updatedOptions)
        return { success: true, value: newOption.value }
      } else {
        return { success: false, error: '保存失败' }
      }
    } catch (error) {
      console.error('添加场景选项失败:', error)
      return { success: false, error: '添加过程中发生错误' }
    }
  }

  // 删除场景选项
  const removeScenarioOption = async (scenarioId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // 防止删除最后一个选项
      if (scenarioOptions.length <= 1) {
        return { success: false, error: '至少需要保留一个场景选项' }
      }

      const updatedOptions = scenarioOptions.filter(option => option.id !== scenarioId)
      
      if (saveOptions(updatedOptions)) {
        setScenarioOptions(updatedOptions)
        return { success: true }
      } else {
        return { success: false, error: '保存失败' }
      }
    } catch (error) {
      console.error('删除场景选项失败:', error)
      return { success: false, error: '删除过程中发生错误' }
    }
  }

  return {
    scenarioOptions,
    addScenarioOption,
    removeScenarioOption,
    isLoading
  }
}

export default useScenarioOptions
